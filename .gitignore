# Agent Swarm .gitignore
# Comprehensive gitignore for Python AI/ML project with modern tooling

# ============================================================================
# Python
# ============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Reports and artifacts (organized structure)
reports/
.artifacts/
*.txt.bak
*_test.py.bak
test_*.txt

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ============================================================================
# AI/ML Specific
# ============================================================================

# Model files
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5
*.pb
*.pth
*.pt
*.ckpt
*.safetensors

# Data files
*.csv
*.json
*.jsonl
*.parquet
*.feather
*.arrow
*.npy
*.npz

# Weights & Biases
wandb/

# MLflow
mlruns/
mlartifacts/

# TensorBoard
runs/
logs/
tensorboard/

# Jupyter notebook checkpoints
.ipynb_checkpoints/

# ============================================================================
# Agent Swarm Specific
# ============================================================================

# LLM model downloads
models/
*.gguf
*.bin
*.safetensors

# Agent conversation logs
conversations/
chat_logs/
agent_logs/

# MCP server data
mcp_data/
mcp_cache/

# Ollama model storage (if local)
ollama_models/

# API keys and secrets
.env.local
.env.production
secrets.json
api_keys.txt

# Agent configuration overrides
config.local.json
config.override.json

# Test files created during testing
test_*.txt
*_test_file.*
test_output/
temp_test_files/

# Demo artifacts
demo_output/
example_files/
hello.py
test.txt
test_mcp.txt
registry_test.txt
integration_test.txt
research.txt
summary.txt
feedback.txt

# ============================================================================
# Development Tools
# ============================================================================

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# ============================================================================
# CI/CD and Deployment
# ============================================================================

# Docker
Dockerfile.local
docker-compose.override.yml
.dockerignore.local

# Kubernetes
*.local.yaml
*.override.yaml

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# ============================================================================
# Package Management
# ============================================================================

# npm (if using Node.js tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ============================================================================
# Documentation
# ============================================================================

# Generated docs
docs/build/
docs/_build/
site/

# ============================================================================
# Monitoring and Logging
# ============================================================================

# Log files
*.log
logs/
log/

# Monitoring data
metrics/
traces/

# ============================================================================
# Security
# ============================================================================

# Private keys
*.pem
*.key
*.crt
*.p12
*.pfx

# OAuth tokens
.oauth_token
.refresh_token

# ============================================================================
# Performance and Profiling
# ============================================================================

# Profiling output
*.prof
*.profile
.prof/

# Memory dumps
*.hprof
*.dump

# ============================================================================
# Development Workspace
# ============================================================================

# Development workspace - track structure but not temporary files
.dev/CONSOLIDATION_NOTES.md
.dev/TESTING_STRATEGY.md
.dev/PERFORMANCE_NOTES.md
.dev/ARCHITECTURE_NOTES.md
.dev/ROADMAP.md
.dev/METRICS.md
.dev/DEPENDENCIES.md
.dev/SECURITY.md
.dev/scripts/
.dev/templates/
.dev/tools/
.dev/temp/
.dev/*.tmp
.dev/*.temp
.dev/*.bak

# ============================================================================
# Custom Project Patterns
# ============================================================================

# Add your custom patterns here
# Example:
# custom_data/
# private_configs/
# local_experiments/
