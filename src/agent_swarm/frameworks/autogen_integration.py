"""
AutoGen v0.4 integration for Agent Swarm.
Demonstrates multi-LLM agent swarm with AutoGen framework.
"""

from __future__ import annotations

import asyncio
from typing import Dict, List

from ..agents import MultiLLMAgent
from ..backends import <PERSON><PERSON>outer, LLMTier
from ..utils.logging import get_logger

logger = get_logger("frameworks.autogen")


class CodingSwarm:
    """Multi-agent swarm for coding tasks with intelligent LLM routing."""

    def __init__(self) -> None:
        self.llm_router = LLMRouter()
        self.agents: Dict[str, MultiLLMAgent] = {}
        self._setup_llm_routing()
        self._create_agents()

    async def initialize(self) -> None:
        """Initialize all LLM backends."""
        logger.info("Initializing Multi-LLM Coding Swarm...")

        # Register local LLMs
        try:
            from ..backends.ollama import create_ollama_llm

            deepseek = create_ollama_llm("deepseek-r1:7b")
            await deepseek.initialize()
            if await deepseek.is_available():
                self.llm_router.register_llm("deepseek-r1", deepseek)
                logger.info("DeepSeek-R1-7B ready")
            else:
                logger.warning("DeepSeek-R1 not available")
        except Exception as e:
            logger.warning(f"DeepSeek-R1 setup failed: {e}")

        try:
            from ..backends.ollama import create_ollama_llm

            qwen = create_ollama_llm("qwen2.5-coder:7b")
            await qwen.initialize()
            if await qwen.is_available():
                self.llm_router.register_llm("qwen-coder", qwen)
                logger.info("Qwen2.5-Coder-7B ready")
            else:
                logger.warning("Qwen2.5-Coder not available")
        except Exception as e:
            logger.warning(f"Qwen2.5-Coder setup failed: {e}")

        # Register cloud LLMs (if API keys available)
        try:
            import os

            from ..backends.cloud import create_claude_llm

            if os.getenv("ANTHROPIC_API_KEY"):
                claude = create_claude_llm()
                await claude.initialize()
                if await claude.is_available():
                    self.llm_router.register_llm("claude", claude)
                    logger.info("Claude 3.5 Sonnet ready")
                else:
                    logger.warning("Claude not available")
        except Exception as e:
            logger.warning(f"Claude setup failed: {e}")

        try:
            import os

            from ..backends.cloud import create_gemini_llm

            if os.getenv("GOOGLE_API_KEY"):
                gemini = create_gemini_llm()
                await gemini.initialize()
                if await gemini.is_available():
                    self.llm_router.register_llm("gemini", gemini)
                    logger.info("Gemini 1.5 Pro ready")
                else:
                    logger.warning("Gemini not available")
        except Exception as e:
            logger.warning(f"Gemini setup failed: {e}")

        # Set fallback chain
        available_llms = list(self.llm_router.llms.keys())
        self.llm_router.set_fallback_chain(available_llms)

        logger.info(f"Available LLMs: {', '.join(available_llms)}")

        if not available_llms:
            raise RuntimeError(
                "No LLMs available. Please set up at least one LLM backend."
            )

    def _setup_llm_routing(self) -> None:
        """Configure which LLMs to use for different task types."""

        # Task-specific routing
        routing_rules = {
            # Simple tasks → Local LLMs
            "simple_coding": "qwen-coder",
            "code_completion": "qwen-coder",
            "quick_fixes": "qwen-coder",
            "documentation": "deepseek-r1",
            # Standard tasks → Better local LLM
            "coding": "deepseek-r1",
            "code_review": "deepseek-r1",
            "debugging": "deepseek-r1",
            # Complex tasks → Cloud LLMs (if available)
            "complex_coding": "claude",
            "architecture": "claude",
            "system_design": "claude",
            "large_codebase_analysis": "gemini",
            "multimodal": "gemini",
        }

        for task_type, llm_name in routing_rules.items():
            self.llm_router.set_task_routing(task_type, llm_name)

    def _create_agents(self) -> None:
        """Create specialized agents."""
        agent_configs = [
            ("Alice", "Junior Developer"),
            ("Bob", "Senior Developer"),
            ("Carol", "Senior Architect"),
            ("Dave", "Documentation Specialist"),
        ]

        for name, role in agent_configs:
            self.agents[name] = MultiLLMAgent(name, role, self.llm_router)

    async def handle_coding_request(self, request: str) -> Dict[str, str]:
        """Handle a coding request by routing to appropriate agents."""

        logger.info(f"Processing request: {request[:100]}...")

        # Determine task complexity and route accordingly
        if any(
            keyword in request.lower()
            for keyword in ["architecture", "design", "complex", "system"]
        ):
            # Complex task → Senior Architect with premium LLM
            result = await self.agents["Carol"].process_task(
                request,
                task_type="complex_coding",
                preferred_tier=LLMTier.CLOUD_PREMIUM,
            )
            return {"agent": "Carol (Senior Architect)", "response": result}

        elif any(
            keyword in request.lower() for keyword in ["review", "check", "quality"]
        ):
            # Code review → Senior Developer with quality LLM
            result = await self.agents["Bob"].process_task(
                request, task_type="code_review", preferred_tier=LLMTier.LOCAL_QUALITY
            )
            return {"agent": "Bob (Senior Developer)", "response": result}

        elif any(
            keyword in request.lower() for keyword in ["document", "explain", "readme"]
        ):
            # Documentation → Documentation Specialist
            result = await self.agents["Dave"].process_task(
                request, task_type="documentation", preferred_tier=LLMTier.LOCAL_QUALITY
            )
            return {"agent": "Dave (Documentation Specialist)", "response": result}

        else:
            # Simple coding → Junior Developer with fast LLM
            result = await self.agents["Alice"].process_task(
                request, task_type="simple_coding", preferred_tier=LLMTier.LOCAL_FAST
            )
            return {"agent": "Alice (Junior Developer)", "response": result}

    def get_swarm_status(self) -> Dict[str, any]:
        """Get current swarm status."""
        return {
            "available_llms": self.llm_router.get_available_llms(),
            "agents": {name: agent.role for name, agent in self.agents.items()},
            "routing_rules": self.llm_router.task_routing,
        }


async def demo_autogen_swarm() -> None:
    """Demonstrate the AutoGen-style multi-LLM coding swarm."""

    swarm = CodingSwarm()
    await swarm.initialize()

    # Demo requests of varying complexity
    demo_requests = [
        "Write a simple Python function to reverse a string",
        "Design a microservices architecture for an e-commerce platform",
        "Review this code for security vulnerabilities: def login(user, pwd): return user == 'admin' and pwd == '123'",
        "Create documentation for a REST API with authentication",
    ]

    logger.info("Starting AutoGen-style Multi-LLM Swarm Demo")

    for i, request in enumerate(demo_requests, 1):
        logger.info(f"Request {i}: {request}")

        result = await swarm.handle_coding_request(request)

        logger.info(f"Handled by: {result['agent']}")
        logger.info(f"Response: {result['response'][:200]}...")

    # Show swarm status
    status = swarm.get_swarm_status()
    logger.info(f"Available LLMs: {list(status['available_llms'].keys())}")
    logger.info(f"Active Agents: {list(status['agents'].keys())}")


if __name__ == "__main__":
    asyncio.run(demo_autogen_swarm())
