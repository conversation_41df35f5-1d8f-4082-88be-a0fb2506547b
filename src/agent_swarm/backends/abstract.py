"""
Abstract LLM interface for swappable AI backends.
Supports both local (Ollama) and cloud (OpenAI, Anthropic, Google) providers.
"""

from __future__ import annotations

from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, AsyncGenerator, Dict, List, Optional

from pydantic import BaseModel, Field


class LLMTier(Enum):
    """LLM tiers based on capability and cost."""

    LOCAL_FAST = "local_fast"
    LOCAL_QUALITY = "local_quality"
    CLOUD_PREMIUM = "cloud_premium"


class Message(BaseModel):
    """Standard message format across all LLM providers."""

    role: str = Field(..., description="Message role: system, user, or assistant")
    content: str = Field(..., description="Message content")
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional metadata"
    )


class LLMResponse(BaseModel):
    """Standard response format from LLM."""

    content: str = Field(..., description="Generated content")
    model: str = Field(..., description="Model that generated the response")
    usage: Dict[str, int] = Field(..., description="Token usage statistics")
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional metadata"
    )


class LLMConfig(BaseModel):
    """Configuration for an LLM backend."""

    name: str = Field(..., description="Human-readable name")
    provider: str = Field(
        ..., description="Provider: ollama, openai, anthropic, google"
    )
    model_id: str = Field(..., description="Model identifier")
    tier: LLMTier = Field(..., description="Performance/cost tier")
    context_length: int = Field(..., description="Maximum context length")
    cost_per_1m_tokens: float = Field(
        default=0.0, description="Cost per 1M tokens (0 for local)"
    )
    recommended_for: List[str] = Field(
        default_factory=list, description="Recommended use cases"
    )
    max_concurrent: int = Field(default=1, description="Max concurrent requests")
    api_key_env: Optional[str] = Field(
        default=None, description="Environment variable for API key"
    )
    base_url: Optional[str] = Field(default=None, description="Base URL for API")


class AbstractLLM(ABC):
    """Abstract base class for all LLM providers."""

    def __init__(self, config: LLMConfig) -> None:
        self.config = config
        self._client: Any = None

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the LLM client."""
        pass

    @abstractmethod
    async def generate(
        self,
        messages: List[Message],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs: Any,
    ) -> LLMResponse:
        """Generate a response from the LLM."""
        pass

    @abstractmethod
    async def stream_generate(
        self,
        messages: List[Message],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs: Any,
    ) -> AsyncGenerator[str, None]:
        """Stream response from the LLM."""
        pass

    @abstractmethod
    async def is_available(self) -> bool:
        """Check if the LLM is available."""
        pass

    def get_cost_estimate(self, input_tokens: int, output_tokens: int) -> float:
        """Estimate cost for token usage."""
        if self.config.cost_per_1m_tokens == 0:
            return 0.0
        total_tokens = input_tokens + output_tokens
        return (total_tokens / 1_000_000) * self.config.cost_per_1m_tokens

    async def close(self) -> None:
        """Close the LLM client (optional)."""
        pass


class LLMRouter:
    """Routes requests to appropriate LLM based on task complexity and preferences."""

    def __init__(self) -> None:
        self.llms: Dict[str, AbstractLLM] = {}
        self.task_routing: Dict[str, str] = {}
        self.fallback_chain: List[str] = []

    def register_llm(self, name: str, llm: AbstractLLM) -> None:
        """Register an LLM backend."""
        self.llms[name] = llm

    def set_task_routing(self, task_type: str, llm_name: str) -> None:
        """Set which LLM to use for specific task types."""
        self.task_routing[task_type] = llm_name

    def set_fallback_chain(self, llm_names: List[str]) -> None:
        """Set fallback order if primary LLM fails."""
        self.fallback_chain = llm_names

    async def route_request(
        self,
        messages: List[Message],
        task_type: str = "general",
        preferred_tier: Optional[LLMTier] = None,
        **kwargs: Any,
    ) -> LLMResponse:
        """Route request to appropriate LLM."""

        # Determine target LLM
        target_llm_name = self._select_llm(task_type, preferred_tier)

        if not target_llm_name:
            raise ValueError("No LLMs available")

        # Try primary LLM
        try:
            llm = self.llms[target_llm_name]
            if await llm.is_available():
                return await llm.generate(messages, **kwargs)
        except Exception as e:
            print(f"Primary LLM {target_llm_name} failed: {e}")

        # Try fallback chain
        for fallback_name in self.fallback_chain:
            if fallback_name == target_llm_name:
                continue
            try:
                llm = self.llms[fallback_name]
                if await llm.is_available():
                    print(f"Using fallback LLM: {fallback_name}")
                    return await llm.generate(messages, **kwargs)
            except Exception as e:
                print(f"Fallback LLM {fallback_name} failed: {e}")

        raise RuntimeError("All LLMs failed")

    def _select_llm(
        self, task_type: str, preferred_tier: Optional[LLMTier]
    ) -> Optional[str]:
        """Select appropriate LLM based on task and preferences."""

        # Check task-specific routing
        if task_type in self.task_routing:
            return self.task_routing[task_type]

        # Select by tier preference
        if preferred_tier:
            for name, llm in self.llms.items():
                if llm.config.tier == preferred_tier:
                    return name

        # Default to first available
        return list(self.llms.keys())[0] if self.llms else None

    def get_available_llms(self) -> Dict[str, LLMConfig]:
        """Get all available LLM configurations."""
        return {name: llm.config for name, llm in self.llms.items()}

    def estimate_cost(
        self, task_type: str, input_tokens: int, output_tokens: int
    ) -> float:
        """Estimate cost for a task."""
        llm_name = self._select_llm(task_type, None)
        if llm_name and llm_name in self.llms:
            return self.llms[llm_name].get_cost_estimate(input_tokens, output_tokens)
        return 0.0

    def get_stats(self) -> Dict[str, Any]:
        """Get router statistics."""
        return {
            "registered_llms": list(self.llms.keys()),
            "fallback_chain": self.fallback_chain.copy(),
            "task_routing": self.task_routing.copy(),
            "total_llms": len(self.llms)
        }


# Predefined configurations for common models
RECOMMENDED_CONFIGS = {
    "deepseek-r1-7b": LLMConfig(
        name="DeepSeek-R1-7B",
        provider="ollama",
        model_id="deepseek-r1:7b",
        tier=LLMTier.LOCAL_QUALITY,
        context_length=32768,
        cost_per_1m_tokens=0.0,
        recommended_for=["coding", "code_review", "documentation", "debugging"],
        base_url="http://localhost:11434",
    ),
    "qwen2.5-coder-7b": LLMConfig(
        name="Qwen2.5-Coder-7B",
        provider="ollama",
        model_id="qwen2.5-coder:7b",
        tier=LLMTier.LOCAL_FAST,
        context_length=32768,
        cost_per_1m_tokens=0.0,
        recommended_for=["simple_coding", "quick_fixes", "code_completion"],
        base_url="http://localhost:11434",
    ),
    "claude-3.5-sonnet": LLMConfig(
        name="Claude 3.5 Sonnet",
        provider="anthropic",
        model_id="claude-3-5-sonnet-********",
        tier=LLMTier.CLOUD_PREMIUM,
        context_length=200000,
        cost_per_1m_tokens=3.0,
        recommended_for=[
            "complex_coding",
            "architecture",
            "system_design",
            "complex_debugging",
        ],
        api_key_env="ANTHROPIC_API_KEY",
    ),
    "gemini-1.5-pro": LLMConfig(
        name="Gemini 1.5 Pro",
        provider="google",
        model_id="gemini-1.5-pro",
        tier=LLMTier.CLOUD_PREMIUM,
        context_length=1000000,
        cost_per_1m_tokens=1.25,
        recommended_for=["large_codebase_analysis", "multimodal", "long_context"],
        api_key_env="GOOGLE_API_KEY",
    ),
}
