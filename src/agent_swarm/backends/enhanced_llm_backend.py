"""
Enhanced LLM Backend - Revolutionary Thinking-Enabled Multi-Provider System.

This module provides a unified interface for LLM providers with advanced features:
- Thinking capabilities (enable/disable for complex vs fast requests)
- Structured outputs with JSON schema validation
- Tool calling with streaming support
- Multimodal capabilities
- Integration with mathematical intent processing algorithms

Features:
- Adaptive thinking based on intent complexity (from IntentFilteringTriangle)
- Performance optimization with early stopping (from AdaptiveIntentProcessor)
- Multi-provider support (Ollama, OpenAI, Anthropic, DeepSeek)
- Intelligent model selection based on task requirements
- Real-time performance monitoring and optimization

This represents the next evolution in AI model interaction.
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Union, AsyncGenerator
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from .abstract import AbstractLLM, LLMConfig, LLMResponse, LLMTier, Message
from .ollama import OllamaLLM
from .cloud import CloudLLM

logger = logging.getLogger(__name__)


class ThinkingMode(Enum):
    """Thinking mode configuration."""
    DISABLED = "disabled"           # No thinking - fastest responses
    ADAPTIVE = "adaptive"           # Adaptive based on intent complexity
    ENABLED = "enabled"             # Always enabled - highest quality
    COMPLEX_ONLY = "complex_only"   # Only for complex requests


class ModelCapability(Enum):
    """Model capability types."""
    THINKING = "thinking"
    STRUCTURED_OUTPUT = "structured_output"
    TOOL_CALLING = "tool_calling"
    MULTIMODAL = "multimodal"
    STREAMING = "streaming"


@dataclass
class ThinkingConfig:
    """Configuration for thinking capabilities."""
    mode: ThinkingMode = ThinkingMode.ADAPTIVE
    complexity_threshold: float = 0.7  # Threshold for enabling thinking
    max_thinking_time: Optional[float] = None  # Max thinking time in seconds
    hide_thinking: bool = False  # Hide thinking process from output
    thinking_prompt: Optional[str] = None  # Custom thinking prompt


@dataclass
class EnhancedLLMRequest:
    """Enhanced LLM request with thinking and structured output support."""
    messages: List[Message]
    model_id: str
    temperature: float = 0.7
    max_tokens: Optional[int] = None

    # Thinking configuration
    thinking_config: Optional[ThinkingConfig] = None

    # Structured output
    response_schema: Optional[Dict[str, Any]] = None

    # Tool calling
    tools: Optional[List[Dict[str, Any]]] = None

    # Streaming
    stream: bool = False

    # Intent analysis (from our mathematical algorithms)
    intent_analysis: Optional[Dict[str, Any]] = None

    # Performance requirements
    priority: str = "normal"  # normal, fast, quality

    # Additional options
    **kwargs: Any


@dataclass
class EnhancedLLMResponse:
    """Enhanced LLM response with thinking and structured data."""
    content: str
    model: str
    usage: Dict[str, int]

    # Thinking process
    thinking: Optional[str] = None
    thinking_time: Optional[float] = None

    # Structured output
    structured_data: Optional[Dict[str, Any]] = None

    # Tool calls
    tool_calls: Optional[List[Dict[str, Any]]] = None

    # Performance metrics
    response_time: float = 0.0
    thinking_enabled: bool = False

    # Quality metrics
    confidence: float = 0.0
    complexity_score: float = 0.0

    # Metadata
    metadata: Dict[str, Any] = None


class EnhancedLLMBackend:
    """
    Enhanced LLM Backend with thinking capabilities and multi-provider support.

    This backend intelligently manages multiple LLM providers and automatically
    optimizes thinking capabilities based on request complexity and performance requirements.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the enhanced LLM backend."""
        self.config = config or {}
        self.providers = {}
        self.model_capabilities = {}
        self.performance_stats = {
            'total_requests': 0,
            'thinking_requests': 0,
            'average_response_time': 0.0,
            'thinking_time_saved': 0.0,
            'complexity_predictions': 0,
        }

        # Default thinking configuration
        self.default_thinking_config = ThinkingConfig(
            mode=ThinkingMode.ADAPTIVE,
            complexity_threshold=0.7,
            hide_thinking=False,
        )

        # Enhanced intent processor integration
        self.enhanced_intent_processor = None

    async def initialize(self):
        """Initialize all LLM providers and capabilities."""
        try:
            # Initialize Ollama provider
            await self._initialize_ollama_provider()

            # Initialize cloud providers
            await self._initialize_cloud_providers()

            # Detect model capabilities
            await self._detect_model_capabilities()

            # Initialize intent processor integration
            await self._initialize_intent_processor()

            logger.info("✅ Enhanced LLM Backend initialized with thinking capabilities")

        except Exception as e:
            logger.error(f"Failed to initialize Enhanced LLM Backend: {e}")
            raise

    async def _initialize_ollama_provider(self):
        """Initialize Ollama provider with latest features."""
        try:
            # Create Ollama instances for thinking-capable models
            thinking_models = {
                "deepseek-r1:8b": {
                    "config": LLMConfig(
                        name="DeepSeek-R1-8B",
                        provider="ollama",
                        model_id="deepseek-r1:8b",
                        tier=LLMTier.LOCAL_QUALITY,
                        context_length=32768,
                        cost_per_1m_tokens=0.0,
                        recommended_for=["reasoning", "complex_analysis", "coding"],
                    ),
                    "capabilities": [
                        ModelCapability.THINKING,
                        ModelCapability.STRUCTURED_OUTPUT,
                        ModelCapability.TOOL_CALLING,
                        ModelCapability.STREAMING,
                    ]
                },
                "qwen3:8b": {
                    "config": LLMConfig(
                        name="Qwen3-8B",
                        provider="ollama",
                        model_id="qwen3:8b",
                        tier=LLMTier.LOCAL_QUALITY,
                        context_length=32768,
                        cost_per_1m_tokens=0.0,
                        recommended_for=["reasoning", "analysis", "coding"],
                    ),
                    "capabilities": [
                        ModelCapability.THINKING,
                        ModelCapability.STRUCTURED_OUTPUT,
                        ModelCapability.STREAMING,
                    ]
                },
                "qwen2.5-coder:7b": {
                    "config": LLMConfig(
                        name="Qwen2.5-Coder-7B",
                        provider="ollama",
                        model_id="qwen2.5-coder:7b",
                        tier=LLMTier.LOCAL_FAST,
                        context_length=32768,
                        cost_per_1m_tokens=0.0,
                        recommended_for=["fast_coding", "quick_fixes"],
                    ),
                    "capabilities": [
                        ModelCapability.STRUCTURED_OUTPUT,
                        ModelCapability.STREAMING,
                    ]
                },
            }

            for model_id, model_info in thinking_models.items():
                try:
                    llm = OllamaLLM(model_info["config"])
                    await llm.initialize()

                    if await llm.is_available():
                        self.providers[model_id] = llm
                        self.model_capabilities[model_id] = model_info["capabilities"]
                        logger.info(f"✅ Initialized Ollama model: {model_id}")
                    else:
                        logger.warning(f"⚠️  Ollama model not available: {model_id}")

                except Exception as e:
                    logger.warning(f"Failed to initialize Ollama model {model_id}: {e}")

        except Exception as e:
            logger.error(f"Failed to initialize Ollama provider: {e}")

    async def _initialize_cloud_providers(self):
        """Initialize cloud providers with thinking support."""
        # TODO: Initialize cloud providers (OpenAI, Anthropic, DeepSeek API)
        # This would include models like GPT-4o, Claude 3.5 Sonnet, etc.
        pass

    async def _detect_model_capabilities(self):
        """Detect and validate model capabilities."""
        for model_id, provider in self.providers.items():
            capabilities = self.model_capabilities.get(model_id, [])

            # Test thinking capability
            if ModelCapability.THINKING in capabilities:
                try:
                    # Test thinking with a simple request
                    test_thinking = await self._test_thinking_capability(provider)
                    if not test_thinking:
                        capabilities.remove(ModelCapability.THINKING)
                        logger.warning(f"Thinking capability not working for {model_id}")
                except Exception as e:
                    logger.warning(f"Failed to test thinking for {model_id}: {e}")

            self.model_capabilities[model_id] = capabilities

    async def _test_thinking_capability(self, provider: AbstractLLM) -> bool:
        """Test if a provider supports thinking."""
        try:
            # This would test the thinking capability
            # For now, assume Ollama providers support it
            return isinstance(provider, OllamaLLM)
        except Exception:
            return False

    async def _initialize_intent_processor(self):
        """Initialize enhanced intent processor integration."""
        try:
            from ..core.enhanced_intent_processor import EnhancedIntentProcessor

            self.enhanced_intent_processor = EnhancedIntentProcessor(
                enable_adaptive=True,
                enable_filtering=True,
                verbose=False
            )

            await self.enhanced_intent_processor.initialize()
            logger.info("✅ Intent processor integration initialized")

        except Exception as e:
            logger.warning(f"Intent processor integration failed: {e}")
            self.enhanced_intent_processor = None

    async def generate(self, request: EnhancedLLMRequest) -> EnhancedLLMResponse:
        """Generate response with enhanced capabilities."""
        start_time = time.time()

        try:
            # Step 1: Analyze intent complexity if processor available
            intent_analysis = await self._analyze_intent_complexity(request)
            request.intent_analysis = intent_analysis

            # Step 2: Select optimal model based on requirements
            selected_model = await self._select_optimal_model(request)

            # Step 3: Configure thinking based on complexity and requirements
            thinking_config = await self._configure_thinking(request, intent_analysis)

            # Step 4: Execute request with selected provider
            response = await self._execute_request(selected_model, request, thinking_config)

            # Step 5: Post-process and enhance response
            enhanced_response = await self._enhance_response(response, request, intent_analysis)

            # Step 6: Update performance statistics
            self._update_performance_stats(enhanced_response, start_time)

            return enhanced_response

        except Exception as e:
            logger.error(f"Enhanced LLM generation failed: {e}")

            # Fallback to simple generation
            return await self._fallback_generation(request, start_time)

    async def _analyze_intent_complexity(self, request: EnhancedLLMRequest) -> Optional[Dict[str, Any]]:
        """Analyze intent complexity using our mathematical algorithms."""
        if not self.enhanced_intent_processor:
            return None

        try:
            # Extract user message for analysis
            user_messages = [msg for msg in request.messages if msg.role == "user"]
            if not user_messages:
                return None

            # Use the last user message for complexity analysis
            user_input = user_messages[-1].content

            # Process with enhanced intent processor
            enhanced_intent = await self.enhanced_intent_processor.process_intent(user_input)

            # Extract complexity metrics
            complexity_analysis = {
                'overall_confidence': enhanced_intent.processing_confidence,
                'flow_state': enhanced_intent.flow_state,
                'intent_coordinates': enhanced_intent.intent_coordinates,
                'processing_recommendations': enhanced_intent.processing_recommendations,
                'complexity_score': 0.0,
            }

            # Calculate complexity score from 3D coordinates
            if enhanced_intent.intent_coordinates:
                coords = enhanced_intent.intent_coordinates
                # Complexity = weighted combination of dimensions
                complexity_analysis['complexity_score'] = (
                    coords.get('complexity', 0) * 0.5 +
                    coords.get('context', 0) * 0.3 +
                    (1.0 - coords.get('clarity', 1)) * 0.2
                )

            return complexity_analysis

        except Exception as e:
            logger.warning(f"Intent complexity analysis failed: {e}")
            return None

    async def _select_optimal_model(self, request: EnhancedLLMRequest) -> str:
        """Select optimal model based on request requirements."""
        # Priority-based model selection
        if request.priority == "fast":
            # Prefer fast models without thinking
            fast_models = [
                "qwen2.5-coder:7b",
                "qwen3:8b",
            ]
            for model in fast_models:
                if model in self.providers:
                    return model

        elif request.priority == "quality":
            # Prefer thinking-capable models
            quality_models = [
                "deepseek-r1:8b",
                "qwen3:8b",
            ]
            for model in quality_models:
                if model in self.providers and ModelCapability.THINKING in self.model_capabilities.get(model, []):
                    return model

        # Default selection based on capabilities needed
        required_capabilities = []

        if request.thinking_config and request.thinking_config.mode != ThinkingMode.DISABLED:
            required_capabilities.append(ModelCapability.THINKING)

        if request.response_schema:
            required_capabilities.append(ModelCapability.STRUCTURED_OUTPUT)

        if request.tools:
            required_capabilities.append(ModelCapability.TOOL_CALLING)

        # Find best matching model
        best_model = None
        best_score = -1

        for model_id, capabilities in self.model_capabilities.items():
            if model_id not in self.providers:
                continue

            # Calculate capability match score
            score = sum(1 for cap in required_capabilities if cap in capabilities)

            # Bonus for thinking capability if complexity is high
            if (request.intent_analysis and
                request.intent_analysis.get('complexity_score', 0) > 0.7 and
                ModelCapability.THINKING in capabilities):
                score += 2

            if score > best_score:
                best_score = score
                best_model = model_id

        return best_model or list(self.providers.keys())[0] if self.providers else None

    async def _configure_thinking(
        self,
        request: EnhancedLLMRequest,
        intent_analysis: Optional[Dict[str, Any]]
    ) -> ThinkingConfig:
        """Configure thinking based on request and complexity analysis."""

        # Start with request thinking config or default
        thinking_config = request.thinking_config or self.default_thinking_config

        # Adaptive thinking based on intent analysis
        if thinking_config.mode == ThinkingMode.ADAPTIVE and intent_analysis:
            complexity_score = intent_analysis.get('complexity_score', 0)
            flow_state = intent_analysis.get('flow_state', 'unknown')

            # Enable thinking for complex requests
            if complexity_score >= thinking_config.complexity_threshold:
                thinking_config.mode = ThinkingMode.ENABLED
                logger.debug(f"Enabling thinking for complex request (score: {complexity_score:.2f})")

            # Enable thinking for challenging flow states
            elif flow_state in ['challenging', 'difficult']:
                thinking_config.mode = ThinkingMode.ENABLED
                logger.debug(f"Enabling thinking for {flow_state} flow state")

            # Disable thinking for simple, clear requests
            elif flow_state == 'optimal' and complexity_score < 0.3:
                thinking_config.mode = ThinkingMode.DISABLED
                logger.debug("Disabling thinking for simple, optimal request")

        # Override for priority requirements
        if request.priority == "fast":
            thinking_config.mode = ThinkingMode.DISABLED
        elif request.priority == "quality":
            thinking_config.mode = ThinkingMode.ENABLED

        return thinking_config

    async def _execute_request(
        self,
        model_id: str,
        request: EnhancedLLMRequest,
        thinking_config: ThinkingConfig
    ) -> EnhancedLLMResponse:
        """Execute request with selected model and thinking configuration."""

        if model_id not in self.providers:
            raise ValueError(f"Model {model_id} not available")

        provider = self.providers[model_id]
        start_time = time.time()

        try:
            # Prepare request for provider
            if isinstance(provider, OllamaLLM):
                response = await self._execute_ollama_request(
                    provider, request, thinking_config
                )
            else:
                # Handle other providers
                response = await self._execute_generic_request(
                    provider, request, thinking_config
                )

            response.response_time = time.time() - start_time
            response.model = model_id
            response.thinking_enabled = thinking_config.mode != ThinkingMode.DISABLED

            return response

        except Exception as e:
            logger.error(f"Request execution failed for {model_id}: {e}")
            raise

    async def _execute_ollama_request(
        self,
        provider: OllamaLLM,
        request: EnhancedLLMRequest,
        thinking_config: ThinkingConfig
    ) -> EnhancedLLMResponse:
        """Execute request with Ollama provider using latest features."""

        # Prepare Ollama-specific parameters
        ollama_params = {
            'temperature': request.temperature,
            'max_tokens': request.max_tokens,
            'stream': request.stream,
        }

        # Add thinking parameter if supported
        model_capabilities = self.model_capabilities.get(provider.config.model_id, [])
        if ModelCapability.THINKING in model_capabilities:
            ollama_params['think'] = thinking_config.mode != ThinkingMode.DISABLED

            if thinking_config.hide_thinking:
                ollama_params['hidethinking'] = True

        # Add structured output if requested
        if request.response_schema and ModelCapability.STRUCTURED_OUTPUT in model_capabilities:
            ollama_params['format'] = request.response_schema

        # Add tools if supported
        if request.tools and ModelCapability.TOOL_CALLING in model_capabilities:
            ollama_params['tools'] = request.tools

        # Execute request
        thinking_start = time.time()

        if request.stream:
            # Handle streaming response
            response_content = ""
            thinking_content = ""
            tool_calls = []

            async for chunk in provider.generate_stream(request.messages, **ollama_params):
                if hasattr(chunk, 'thinking') and chunk.thinking:
                    thinking_content += chunk.thinking
                if hasattr(chunk, 'content') and chunk.content:
                    response_content += chunk.content
                if hasattr(chunk, 'tool_calls') and chunk.tool_calls:
                    tool_calls.extend(chunk.tool_calls)

            thinking_time = time.time() - thinking_start if thinking_content else None

            return EnhancedLLMResponse(
                content=response_content,
                model=provider.config.model_id,
                usage={'prompt_tokens': 0, 'completion_tokens': 0, 'total_tokens': 0},
                thinking=thinking_content if thinking_content else None,
                thinking_time=thinking_time,
                tool_calls=tool_calls if tool_calls else None,
            )

        else:
            # Handle non-streaming response
            response = await provider.generate(request.messages, **ollama_params)

            thinking_time = time.time() - thinking_start if hasattr(response, 'thinking') and response.thinking else None

            return EnhancedLLMResponse(
                content=response.content,
                model=provider.config.model_id,
                usage=response.usage,
                thinking=getattr(response, 'thinking', None),
                thinking_time=thinking_time,
                structured_data=getattr(response, 'structured_data', None),
                tool_calls=getattr(response, 'tool_calls', None),
            )

    async def _execute_generic_request(
        self,
        provider: AbstractLLM,
        request: EnhancedLLMRequest,
        thinking_config: ThinkingConfig
    ) -> EnhancedLLMResponse:
        """Execute request with generic provider."""

        # For non-Ollama providers, use standard interface
        response = await provider.generate(
            request.messages,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
        )

        return EnhancedLLMResponse(
            content=response.content,
            model=provider.config.model_id,
            usage=response.usage,
            thinking=None,  # Generic providers don't support thinking yet
            thinking_time=None,
        )

    async def _enhance_response(
        self,
        response: EnhancedLLMResponse,
        request: EnhancedLLMRequest,
        intent_analysis: Optional[Dict[str, Any]]
    ) -> EnhancedLLMResponse:
        """Enhance response with additional analysis and metadata."""

        # Add intent analysis data
        if intent_analysis:
            response.confidence = intent_analysis.get('overall_confidence', 0.0)
            response.complexity_score = intent_analysis.get('complexity_score', 0.0)

            response.metadata = {
                'intent_analysis': intent_analysis,
                'flow_state': intent_analysis.get('flow_state', 'unknown'),
                'intent_coordinates': intent_analysis.get('intent_coordinates'),
            }

        # Validate structured output if schema was provided
        if request.response_schema and response.structured_data:
            try:
                # TODO: Add JSON schema validation
                pass
            except Exception as e:
                logger.warning(f"Structured output validation failed: {e}")

        return response

    async def _fallback_generation(
        self,
        request: EnhancedLLMRequest,
        start_time: float
    ) -> EnhancedLLMResponse:
        """Fallback generation when enhanced processing fails."""

        # Try to use any available provider
        for model_id, provider in self.providers.items():
            try:
                response = await provider.generate(
                    request.messages,
                    temperature=request.temperature,
                    max_tokens=request.max_tokens,
                )

                return EnhancedLLMResponse(
                    content=response.content,
                    model=model_id,
                    usage=response.usage,
                    response_time=time.time() - start_time,
                    metadata={'fallback': True},
                )

            except Exception as e:
                logger.warning(f"Fallback failed for {model_id}: {e}")
                continue

        # Ultimate fallback
        return EnhancedLLMResponse(
            content="I apologize, but I'm currently unable to process your request due to technical difficulties.",
            model="fallback",
            usage={'prompt_tokens': 0, 'completion_tokens': 0, 'total_tokens': 0},
            response_time=time.time() - start_time,
            metadata={'fallback': True, 'error': True},
        )

    def _update_performance_stats(self, response: EnhancedLLMResponse, start_time: float):
        """Update performance statistics."""
        self.performance_stats['total_requests'] += 1

        if response.thinking_enabled:
            self.performance_stats['thinking_requests'] += 1

        # Update average response time
        total = self.performance_stats['total_requests']
        current_avg = self.performance_stats['average_response_time']
        new_time = time.time() - start_time

        self.performance_stats['average_response_time'] = (
            (current_avg * (total - 1) + new_time) / total
        )

        # Track thinking time savings
        if not response.thinking_enabled and response.complexity_score > 0.7:
            # Estimate time saved by not thinking on complex request
            estimated_thinking_time = 2.0  # Assume 2 seconds average thinking time
            self.performance_stats['thinking_time_saved'] += estimated_thinking_time

    async def stream_generate(self, request: EnhancedLLMRequest) -> AsyncGenerator[EnhancedLLMResponse, None]:
        """Generate streaming response with enhanced capabilities."""

        # Set streaming mode
        request.stream = True

        # Use the same logic as generate but yield chunks
        try:
            # Analyze intent complexity
            intent_analysis = await self._analyze_intent_complexity(request)
            request.intent_analysis = intent_analysis

            # Select optimal model
            selected_model = await self._select_optimal_model(request)

            # Configure thinking
            thinking_config = await self._configure_thinking(request, intent_analysis)

            # Stream response
            async for chunk in self._stream_request(selected_model, request, thinking_config):
                yield chunk

        except Exception as e:
            logger.error(f"Enhanced streaming generation failed: {e}")

            # Fallback streaming
            async for chunk in self._fallback_streaming(request):
                yield chunk

    async def _stream_request(
        self,
        model_id: str,
        request: EnhancedLLMRequest,
        thinking_config: ThinkingConfig
    ) -> AsyncGenerator[EnhancedLLMResponse, None]:
        """Stream request with selected model."""

        if model_id not in self.providers:
            raise ValueError(f"Model {model_id} not available")

        provider = self.providers[model_id]

        # Execute streaming request
        if isinstance(provider, OllamaLLM):
            async for chunk in self._stream_ollama_request(provider, request, thinking_config):
                yield chunk
        else:
            async for chunk in self._stream_generic_request(provider, request):
                yield chunk

    async def _stream_ollama_request(
        self,
        provider: OllamaLLM,
        request: EnhancedLLMRequest,
        thinking_config: ThinkingConfig
    ) -> AsyncGenerator[EnhancedLLMResponse, None]:
        """Stream Ollama request with thinking support."""

        # Prepare parameters (similar to _execute_ollama_request)
        ollama_params = {
            'temperature': request.temperature,
            'max_tokens': request.max_tokens,
            'stream': True,
        }

        model_capabilities = self.model_capabilities.get(provider.config.model_id, [])
        if ModelCapability.THINKING in model_capabilities:
            ollama_params['think'] = thinking_config.mode != ThinkingMode.DISABLED

        # Stream response
        async for chunk in provider.generate_stream(request.messages, **ollama_params):
            yield EnhancedLLMResponse(
                content=getattr(chunk, 'content', ''),
                model=provider.config.model_id,
                usage={'prompt_tokens': 0, 'completion_tokens': 0, 'total_tokens': 0},
                thinking=getattr(chunk, 'thinking', None),
                thinking_enabled=thinking_config.mode != ThinkingMode.DISABLED,
            )

    async def _stream_generic_request(
        self,
        provider: AbstractLLM,
        request: EnhancedLLMRequest
    ) -> AsyncGenerator[EnhancedLLMResponse, None]:
        """Stream generic provider request."""

        # For providers that don't support streaming, yield complete response
        response = await provider.generate(
            request.messages,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
        )

        yield EnhancedLLMResponse(
            content=response.content,
            model=provider.config.model_id,
            usage=response.usage,
        )

    async def _fallback_streaming(self, request: EnhancedLLMRequest) -> AsyncGenerator[EnhancedLLMResponse, None]:
        """Fallback streaming when enhanced processing fails."""

        for model_id, provider in self.providers.items():
            try:
                async for chunk in self._stream_generic_request(provider, request):
                    chunk.metadata = {'fallback': True}
                    yield chunk
                return
            except Exception as e:
                logger.warning(f"Fallback streaming failed for {model_id}: {e}")
                continue

        # Ultimate fallback
        yield EnhancedLLMResponse(
            content="I apologize, but I'm currently unable to process your request.",
            model="fallback",
            usage={'prompt_tokens': 0, 'completion_tokens': 0, 'total_tokens': 0},
            metadata={'fallback': True, 'error': True},
        )

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        stats = self.performance_stats.copy()

        # Calculate additional metrics
        if stats['total_requests'] > 0:
            stats['thinking_usage_rate'] = stats['thinking_requests'] / stats['total_requests']
            stats['average_thinking_time_saved'] = stats['thinking_time_saved'] / stats['total_requests']

        # Add model availability
        stats['available_models'] = list(self.providers.keys())
        stats['model_capabilities'] = self.model_capabilities.copy()

        return stats

    def get_available_models(self) -> Dict[str, Dict[str, Any]]:
        """Get available models with their capabilities."""
        models = {}

        for model_id, provider in self.providers.items():
            capabilities = self.model_capabilities.get(model_id, [])

            models[model_id] = {
                'config': asdict(provider.config),
                'capabilities': [cap.value for cap in capabilities],
                'thinking_supported': ModelCapability.THINKING in capabilities,
                'structured_output_supported': ModelCapability.STRUCTURED_OUTPUT in capabilities,
                'tool_calling_supported': ModelCapability.TOOL_CALLING in capabilities,
                'streaming_supported': ModelCapability.STREAMING in capabilities,
            }

        return models
