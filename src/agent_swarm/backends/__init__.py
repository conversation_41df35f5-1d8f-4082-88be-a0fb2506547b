"""
LLM Backends Package

Multi-provider LLM abstraction layer supporting local and cloud models.
"""

from .abstract import (
    Abstract<PERSON><PERSON>,
    LLMConfig,
    LLMResponse,
    LLMRouter,
    LLMTier,
    Message,
)
from .ollama_backend import (
    OllamaLLM,
    OllamaConfig,
    create_ollama_llm,
    setup_ollama_models,
    CODING_MODELS,
    GENERAL_MODELS,
)

# Cloud backends (optional imports)
try:
    from .cloud import AnthropicLLM, GoogleLLM, create_claude_llm, create_gemini_llm

    __all__ = [
        # Core abstractions
        "AbstractLLM",
        "LLMConfig",
        "LLMRouter",
        "LLMTier",
        "LLMResponse",
        "Message",
        # Local backends
        "OllamaLLM",
        "OllamaConfig",
        "create_ollama_llm",
        "setup_ollama_models",
        "CODING_MODELS",
        "GENERAL_MODELS",
        # Cloud backends
        "AnthropicLLM",
        "GoogleLLM",
        "create_claude_llm",
        "create_gemini_llm",
    ]
except ImportError:
    # Cloud dependencies not installed
    __all__ = [
        # Core abstractions
        "AbstractLLM",
        "LLMConfig",
        "LLMRouter",
        "LLMTier",
        "LLMResponse",
        "Message",
        # Local backends
        "OllamaLLM",
        "OllamaConfig",
        "create_ollama_llm",
        "setup_ollama_models",
        "CODING_MODELS",
        "GENERAL_MODELS",
    ]
