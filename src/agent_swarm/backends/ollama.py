"""
Ollama client implementation for local LLMs.
Supports DeepSeek-R1, Qwen2.5-Coder, and other Ollama models.
"""

from __future__ import annotations

import asyncio
import json
from typing import Any, AsyncGenerator, List, Optional

import aiohttp

from .abstract import AbstractLLM, LLMConfig, LLMResponse, LLMTier, Message
from ..utils.http_client import get_http_session


class OllamaLLM(AbstractLLM):
    """Ollama implementation for local LLMs."""

    def __init__(self, config: LLMConfig) -> None:
        super().__init__(config)
        self.base_url = config.base_url or "http://localhost:11434"
        self.session: Optional[aiohttp.ClientSession] = None

    async def initialize(self) -> None:
        """Initialize the Ollama client."""
        self.session = aiohttp.ClientSession()

        # Check if model is available, pull if not
        if not await self._is_model_available():
            print(f"Pulling model {self.config.model_id}...")
            await self._pull_model()

    async def generate(
        self,
        messages: List[Message],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs: Any,
    ) -> LLMResponse:
        """Generate response using Ollama."""
        if not self.session:
            await self.initialize()

        # Convert messages to Ollama format
        ollama_messages = [
            {"role": msg.role, "content": msg.content} for msg in messages
        ]

        payload = {
            "model": self.config.model_id,
            "messages": ollama_messages,
            "stream": False,
            "options": {
                "temperature": temperature,
            },
        }

        if max_tokens:
            payload["options"]["num_predict"] = max_tokens

        async with self.session.post(
            f"{self.base_url}/api/chat", json=payload
        ) as response:
            if response.status != 200:
                raise RuntimeError(f"Ollama API error: {response.status}")

            result = await response.json()

            return LLMResponse(
                content=result["message"]["content"],
                model=self.config.model_id,
                usage={
                    "prompt_tokens": result.get("prompt_eval_count", 0),
                    "completion_tokens": result.get("eval_count", 0),
                    "total_tokens": result.get("prompt_eval_count", 0)
                    + result.get("eval_count", 0),
                },
                metadata={
                    "eval_duration": result.get("eval_duration", 0),
                    "load_duration": result.get("load_duration", 0),
                },
            )

    async def stream_generate(
        self,
        messages: List[Message],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs: Any,
    ) -> AsyncGenerator[str, None]:
        """Stream response from Ollama."""
        if not self.session:
            await self.initialize()

        ollama_messages = [
            {"role": msg.role, "content": msg.content} for msg in messages
        ]

        payload = {
            "model": self.config.model_id,
            "messages": ollama_messages,
            "stream": True,
            "options": {
                "temperature": temperature,
            },
        }

        if max_tokens:
            payload["options"]["num_predict"] = max_tokens

        async with self.session.post(
            f"{self.base_url}/api/chat", json=payload
        ) as response:
            if response.status != 200:
                raise RuntimeError(f"Ollama API error: {response.status}")

            async for line in response.content:
                if line:
                    try:
                        chunk = json.loads(line.decode("utf-8"))
                        if "message" in chunk and "content" in chunk["message"]:
                            yield chunk["message"]["content"]
                        if chunk.get("done", False):
                            break
                    except json.JSONDecodeError:
                        continue

    async def is_available(self) -> bool:
        """Check if Ollama service is available."""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()

            async with self.session.get(f"{self.base_url}/api/tags") as response:
                return response.status == 200
        except Exception:
            return False

    async def _is_model_available(self) -> bool:
        """Check if specific model is available locally."""
        try:
            if not self.session:
                return False
            async with self.session.get(f"{self.base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    models = [model["name"] for model in data.get("models", [])]
                    return self.config.model_id in models
        except Exception:
            pass
        return False

    async def _pull_model(self) -> None:
        """Pull model from Ollama registry."""
        if not self.session:
            return

        payload = {"name": self.config.model_id}

        async with self.session.post(
            f"{self.base_url}/api/pull", json=payload
        ) as response:
            if response.status != 200:
                raise RuntimeError(f"Failed to pull model: {response.status}")

            # Stream the pull progress
            async for line in response.content:
                if line:
                    try:
                        chunk = json.loads(line.decode("utf-8"))
                        if "status" in chunk:
                            print(f"Pull status: {chunk['status']}")
                        if chunk.get("status") == "success":
                            break
                    except json.JSONDecodeError:
                        continue

    async def close(self) -> None:
        """Close the session."""
        if self.session:
            await self.session.close()


def create_ollama_llm(
    model_name: str, base_url: str = "http://localhost:11434"
) -> OllamaLLM:
    """Create an Ollama LLM instance with predefined configs."""

    configs = {
        "deepseek-r1:7b": LLMConfig(
            name="DeepSeek-R1-7B",
            provider="ollama",
            model_id="deepseek-r1:7b",
            tier=LLMTier.LOCAL_QUALITY,
            context_length=32768,
            cost_per_1m_tokens=0.0,
            recommended_for=["coding", "code_review", "documentation"],
            base_url=base_url,
        ),
        "qwen2.5-coder:7b": LLMConfig(
            name="Qwen2.5-Coder-7B",
            provider="ollama",
            model_id="qwen2.5-coder:7b",
            tier=LLMTier.LOCAL_FAST,
            context_length=32768,
            cost_per_1m_tokens=0.0,
            recommended_for=["simple_coding", "quick_fixes"],
            base_url=base_url,
        ),
    }

    if model_name not in configs:
        raise ValueError(
            f"Unknown model: {model_name}. Available: {list(configs.keys())}"
        )

    return OllamaLLM(configs[model_name])


# Example usage and testing
async def test_ollama_llm() -> None:
    """Test function for Ollama LLM."""

    # Create DeepSeek-R1 instance
    llm = create_ollama_llm("deepseek-r1:7b")

    try:
        await llm.initialize()

        if await llm.is_available():
            print("✅ Ollama service is available")

            # Test generation
            messages = [
                Message(role="system", content="You are a helpful coding assistant."),
                Message(
                    role="user",
                    content="Write a Python function to calculate fibonacci numbers.",
                ),
            ]

            print("🤖 Generating response...")
            response = await llm.generate(messages, temperature=0.3)

            print(f"Model: {response.model}")
            print(f"Tokens used: {response.usage}")
            print(f"Response: {response.content[:200]}...")

        else:
            print("❌ Ollama service not available")

    except Exception as e:
        print(f"Error: {e}")
    finally:
        await llm.close()


if __name__ == "__main__":
    asyncio.run(test_ollama_llm())
