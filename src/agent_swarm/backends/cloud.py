"""
Cloud LLM clients for premium AI services.
Supports OpenAI, Anthropic (<PERSON>), and Google (Gemini).
"""

from __future__ import annotations

import os
from typing import Any, AsyncGenerator, List, Optional

from .abstract import AbstractLLM, LLMConfig, LLMResponse, LLMTier, Message


class AnthropicLLM(AbstractLLM):
    """Anthropic Claude implementation."""

    def __init__(self, config: LLMConfig) -> None:
        super().__init__(config)
        self._client: Any = None

    async def initialize(self) -> None:
        """Initialize Anthropic client."""
        try:
            import anthropic

            api_key = os.getenv(self.config.api_key_env)
            if not api_key:
                raise ValueError(f"Missing API key: {self.config.api_key_env}")
            self._client = anthropic.AsyncAnthropic(api_key=api_key)
        except ImportError as e:
            raise ImportError("Install anthropic: pip install anthropic") from e

    async def generate(
        self,
        messages: List[Message],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs: Any,
    ) -> LLMResponse:
        """Generate response using Claude."""
        if not self._client:
            await self.initialize()

        # Convert messages (Claude expects system message separately)
        system_msg = None
        claude_messages = []

        for msg in messages:
            if msg.role == "system":
                system_msg = msg.content
            else:
                claude_messages.append({"role": msg.role, "content": msg.content})

        params = {
            "model": self.config.model_id,
            "messages": claude_messages,
            "temperature": temperature,
            "max_tokens": max_tokens or 4096,
        }

        if system_msg:
            params["system"] = system_msg

        response = await self._client.messages.create(**params)

        return LLMResponse(
            content=response.content[0].text,
            model=self.config.model_id,
            usage={
                "prompt_tokens": response.usage.input_tokens,
                "completion_tokens": response.usage.output_tokens,
                "total_tokens": response.usage.input_tokens
                + response.usage.output_tokens,
            },
        )

    async def stream_generate(
        self,
        messages: List[Message],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs: Any,
    ) -> AsyncGenerator[str, None]:
        """Stream response from Claude."""
        if not self._client:
            await self.initialize()

        system_msg = None
        claude_messages = []

        for msg in messages:
            if msg.role == "system":
                system_msg = msg.content
            else:
                claude_messages.append({"role": msg.role, "content": msg.content})

        params = {
            "model": self.config.model_id,
            "messages": claude_messages,
            "temperature": temperature,
            "max_tokens": max_tokens or 4096,
            "stream": True,
        }

        if system_msg:
            params["system"] = system_msg

        async with self._client.messages.stream(**params) as stream:
            async for chunk in stream:
                if chunk.type == "content_block_delta":
                    yield chunk.delta.text

    async def is_available(self) -> bool:
        """Check if Anthropic API is available."""
        try:
            if not self._client:
                await self.initialize()
            # Simple test call
            await self._client.messages.create(
                model=self.config.model_id,
                messages=[{"role": "user", "content": "Hi"}],
                max_tokens=1,
            )
            return True
        except Exception:
            return False


class GoogleLLM(AbstractLLM):
    """Google Gemini implementation."""

    def __init__(self, config: LLMConfig) -> None:
        super().__init__(config)
        self._client: Any = None

    async def initialize(self) -> None:
        """Initialize Google client."""
        try:
            import google.generativeai as genai

            api_key = os.getenv(self.config.api_key_env)
            if not api_key:
                raise ValueError(f"Missing API key: {self.config.api_key_env}")
            genai.configure(api_key=api_key)
            self._client = genai.GenerativeModel(self.config.model_id)
        except ImportError as e:
            raise ImportError(
                "Install google-generativeai: pip install google-generativeai"
            ) from e

    async def generate(
        self,
        messages: List[Message],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs: Any,
    ) -> LLMResponse:
        """Generate response using Gemini."""
        if not self._client:
            await self.initialize()

        # Convert messages to Gemini format
        gemini_messages = []
        system_content = ""

        for msg in messages:
            if msg.role == "system":
                system_content = f"System: {msg.content}\n\n"
            elif msg.role == "user":
                gemini_messages.append({"role": "user", "parts": [msg.content]})
            elif msg.role == "assistant":
                gemini_messages.append({"role": "model", "parts": [msg.content]})

        # Add system message to first user message if exists
        if gemini_messages and system_content:
            gemini_messages[0]["parts"][0] = (
                system_content + gemini_messages[0]["parts"][0]
            )

        generation_config = {
            "temperature": temperature,
            "max_output_tokens": max_tokens or 8192,
        }

        response = await self._client.generate_content_async(
            gemini_messages, generation_config=generation_config
        )

        return LLMResponse(
            content=response.text,
            model=self.config.model_id,
            usage={
                "prompt_tokens": response.usage_metadata.prompt_token_count,
                "completion_tokens": response.usage_metadata.candidates_token_count,
                "total_tokens": response.usage_metadata.total_token_count,
            },
        )

    async def stream_generate(
        self,
        messages: List[Message],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs: Any,
    ) -> AsyncGenerator[str, None]:
        """Stream response from Gemini."""
        if not self._client:
            await self.initialize()

        # Convert messages (similar to generate)
        gemini_messages = []
        system_content = ""

        for msg in messages:
            if msg.role == "system":
                system_content = f"System: {msg.content}\n\n"
            elif msg.role == "user":
                gemini_messages.append({"role": "user", "parts": [msg.content]})
            elif msg.role == "assistant":
                gemini_messages.append({"role": "model", "parts": [msg.content]})

        if gemini_messages and system_content:
            gemini_messages[0]["parts"][0] = (
                system_content + gemini_messages[0]["parts"][0]
            )

        generation_config = {
            "temperature": temperature,
            "max_output_tokens": max_tokens or 8192,
        }

        response = await self._client.generate_content_async(
            gemini_messages, generation_config=generation_config, stream=True
        )

        async for chunk in response:
            if chunk.text:
                yield chunk.text

    async def is_available(self) -> bool:
        """Check if Google API is available."""
        try:
            if not self._client:
                await self.initialize()
            # Simple test
            response = await self._client.generate_content_async("Hi")
            return bool(response.text)
        except Exception:
            return False


# Factory functions
def create_claude_llm(model_id: str = "claude-3-5-sonnet-20241022") -> AnthropicLLM:
    """Create Claude LLM instance."""
    config = LLMConfig(
        name="Claude 3.5 Sonnet",
        provider="anthropic",
        model_id=model_id,
        tier=LLMTier.CLOUD_PREMIUM,
        context_length=200000,
        cost_per_1m_tokens=3.0,
        recommended_for=["complex_coding", "architecture", "system_design"],
        api_key_env="ANTHROPIC_API_KEY",
    )
    return AnthropicLLM(config)


def create_gemini_llm(model_id: str = "gemini-1.5-pro") -> GoogleLLM:
    """Create Gemini LLM instance."""
    config = LLMConfig(
        name="Gemini 1.5 Pro",
        provider="google",
        model_id=model_id,
        tier=LLMTier.CLOUD_PREMIUM,
        context_length=1000000,
        cost_per_1m_tokens=1.25,
        recommended_for=["large_codebase_analysis", "multimodal", "long_context"],
        api_key_env="GOOGLE_API_KEY",
    )
    return GoogleLLM(config)
