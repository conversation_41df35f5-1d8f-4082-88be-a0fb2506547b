"""
Ollama LLM Backend for Agent Swarm.
Provides integration with local Ollama models.
"""

from __future__ import annotations

import asyncio
import json
from typing import Any, Dict, List, Optional

import aiohttp
from pydantic import BaseModel, Field

from .abstract import AbstractLLM, LLMConfig, LLMResponse, Message
from ..utils.logging import get_logger
from ..utils.http_client import get_http_session

logger = get_logger("backends.ollama")


class OllamaConfig(LLMConfig):
    """Configuration for Ollama LLM."""

    # Required LLMConfig fields
    name: str = Field(..., description="LLM name")
    provider: str = Field(default="ollama", description="Provider name")
    model_id: str = Field(..., description="Model identifier")
    tier: str = Field(default="LOCAL_FAST", description="LLM tier")
    context_length: int = Field(default=4096, description="Context length")

    # Ollama-specific fields
    base_url: str = Field(default="http://localhost:11434", description="Ollama server URL")
    model: str = Field(..., description="Model name (e.g., 'deepseek-r1:7b')")
    timeout: int = Field(default=120, description="Request timeout in seconds")
    stream: bool = Field(default=False, description="Enable streaming responses")

    # Ollama-specific parameters
    num_predict: Optional[int] = Field(default=None, description="Maximum tokens to generate")
    top_k: Optional[int] = Field(default=None, description="Top-k sampling")
    top_p: Optional[float] = Field(default=None, description="Top-p sampling")
    repeat_penalty: Optional[float] = Field(default=None, description="Repetition penalty")
    seed: Optional[int] = Field(default=None, description="Random seed")


class OllamaLLM(AbstractLLM):
    """Ollama LLM implementation."""

    def __init__(self, config: OllamaConfig):
        super().__init__(config)
        self.config = config

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get HTTP session with connection pooling."""
        return await get_http_session(f"ollama_{self.config.model}")

    async def generate(
        self,
        messages: List[Message],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate response using Ollama."""

        try:
            session = await self._get_session()

            # Convert messages to Ollama format
            prompt = self._messages_to_prompt(messages)

            # Prepare request payload
            payload = {
                "model": self.config.model,
                "prompt": prompt,
                "stream": self.config.stream,
                "options": self._build_options(temperature, max_tokens, **kwargs)
            }

            # Remove None values
            payload = {k: v for k, v in payload.items() if v is not None}

            logger.debug(f"Ollama request: {payload}")

            # Make request
            async with session.post(
                f"{self.config.base_url}/api/generate",
                json=payload
            ) as response:

                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Ollama API error {response.status}: {error_text}")

                if self.config.stream:
                    content = await self._handle_streaming_response(response)
                else:
                    result = await response.json()
                    content = result.get("response", "")

                return LLMResponse(
                    content=content,
                    model=self.config.model,
                    usage={
                        "prompt_tokens": 0,  # Ollama doesn't provide token counts
                        "completion_tokens": 0,
                        "total_tokens": 0
                    },
                    metadata={
                        "model": self.config.model,
                        "llm_used": f"ollama/{self.config.model}",
                        "backend": "ollama",
                        "temperature": temperature,
                        "max_tokens": max_tokens,
                        "cost": 0.0  # Local models are free
                    }
                )

        except Exception as e:
            logger.error(f"Ollama generation failed: {e}")
            raise Exception(f"Ollama generation failed: {e}")

    def _messages_to_prompt(self, messages: List[Message]) -> str:
        """Convert messages to a single prompt string."""

        prompt_parts = []

        for message in messages:
            if message.role == "system":
                prompt_parts.append(f"System: {message.content}")
            elif message.role == "user":
                prompt_parts.append(f"User: {message.content}")
            elif message.role == "assistant":
                prompt_parts.append(f"Assistant: {message.content}")

        # Add final assistant prompt
        prompt_parts.append("Assistant:")

        return "\n\n".join(prompt_parts)

    def _build_options(
        self,
        temperature: float,
        max_tokens: Optional[int],
        **kwargs
    ) -> Dict[str, Any]:
        """Build Ollama options dictionary."""

        options = {
            "temperature": temperature,
        }

        # Add max tokens if specified
        if max_tokens is not None:
            options["num_predict"] = max_tokens
        elif self.config.num_predict is not None:
            options["num_predict"] = self.config.num_predict

        # Add Ollama-specific parameters
        if self.config.top_k is not None:
            options["top_k"] = self.config.top_k

        if self.config.top_p is not None:
            options["top_p"] = self.config.top_p

        if self.config.repeat_penalty is not None:
            options["repeat_penalty"] = self.config.repeat_penalty

        if self.config.seed is not None:
            options["seed"] = self.config.seed

        # Add any additional kwargs
        options.update(kwargs)

        return options

    async def _handle_streaming_response(self, response: aiohttp.ClientResponse) -> str:
        """Handle streaming response from Ollama."""

        content_parts = []

        async for line in response.content:
            if line:
                try:
                    data = json.loads(line.decode('utf-8'))
                    if "response" in data:
                        content_parts.append(data["response"])

                    # Check if done
                    if data.get("done", False):
                        break

                except json.JSONDecodeError:
                    continue

        return "".join(content_parts)

    async def is_available(self) -> bool:
        """Check if Ollama is available."""

        try:
            session = await self._get_session()

            async with session.get(f"{self.config.base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    models = [model["name"] for model in data.get("models", [])]
                    return self.config.model in models
                return False

        except Exception as e:
            logger.debug(f"Ollama availability check failed: {e}")
            return False

    def get_cost_estimate(self, input_tokens: int, output_tokens: int) -> float:
        """Get cost estimate (free for local models)."""
        return 0.0

    async def list_models(self) -> List[str]:
        """List available models in Ollama."""

        try:
            session = await self._get_session()

            async with session.get(f"{self.config.base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    return [model["name"] for model in data.get("models", [])]
                return []

        except Exception as e:
            logger.error(f"Failed to list Ollama models: {e}")
            return []

    async def pull_model(self, model_name: str) -> bool:
        """Pull a model in Ollama."""

        try:
            session = await self._get_session()

            payload = {"name": model_name}

            async with session.post(
                f"{self.config.base_url}/api/pull",
                json=payload
            ) as response:

                if response.status == 200:
                    # Handle streaming pull response
                    async for line in response.content:
                        if line:
                            try:
                                data = json.loads(line.decode('utf-8'))
                                status = data.get("status", "")
                                logger.info(f"Pull {model_name}: {status}")

                                if data.get("error"):
                                    logger.error(f"Pull error: {data['error']}")
                                    return False

                            except json.JSONDecodeError:
                                continue

                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"Pull failed: {error_text}")
                    return False

        except Exception as e:
            logger.error(f"Failed to pull model {model_name}: {e}")
            return False

    async def initialize(self) -> None:
        """Initialize the Ollama LLM."""
        # Ensure session is created
        await self._get_session()
        logger.info(f"Ollama LLM initialized: {self.config.model}")

    async def stream_generate(
        self,
        messages: List[Message],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ):
        """Stream generate responses (async generator)."""
        # For now, just yield the full response
        response = await self.generate(messages, temperature, max_tokens, **kwargs)
        yield response.content

    async def close(self):
        """Close the HTTP session."""
        if self.session and not self.session.closed:
            await self.session.close()


# Factory functions
def create_ollama_llm(
    model: str,
    base_url: str = "http://localhost:11434",
    name: Optional[str] = None,
    **kwargs
) -> OllamaLLM:
    """Create an Ollama LLM instance."""

    if name is None:
        name = f"ollama_{model.replace(':', '_').replace('.', '_')}"

    config = OllamaConfig(
        name=name,
        model_id=model,
        model=model,
        base_url=base_url,
        **kwargs
    )

    return OllamaLLM(config)


async def setup_ollama_models(
    models: List[str],
    base_url: str = "http://localhost:11434",
    auto_pull: bool = True
) -> List[OllamaLLM]:
    """Set up multiple Ollama models."""

    llms = []

    for model in models:
        llm = create_ollama_llm(model, base_url)

        # Check if model is available
        if not await llm.is_available():
            if auto_pull:
                logger.info(f"Pulling model: {model}")
                success = await llm.pull_model(model)
                if not success:
                    logger.warning(f"Failed to pull model: {model}")
                    continue
            else:
                logger.warning(f"Model not available: {model}")
                continue

        llms.append(llm)
        logger.info(f"Ollama model ready: {model}")

    return llms


# Recommended models for coding
CODING_MODELS = [
    "deepseek-r1:7b",      # Primary coding model
    "qwen2.5-coder:7b",    # Fast backup model
    "codellama:7b",        # Alternative coding model
]

GENERAL_MODELS = [
    "llama3.2:3b",         # Fast general model
    "mistral:7b",          # Quality general model
]
