"""
Agent Swarm: Multi-LLM Agent Development Framework

A modern, flexible framework for building agent swarms with interchangeable LLM backends.
Optimized for local + cloud LLM combinations with intelligent routing.
"""

from ._version import __version__, __version_info__

from .agents import MultiLLMAgent, CodingAgent, create_coding_agent
from .backends import (
    AbstractLLM,
    LLMConfig,
    LLMResponse,
    LLMRouter,
    LLMTier,
    Message,
)
from .utils import setup_logging

# MCP integration (optional import)
try:
    from .mcp import (
        MCPClient,
        MCPToolRegistry,
        MCPEnabledAgent,
        MCPRouter,
    )
    _MCP_AVAILABLE = True
except ImportError:
    _MCP_AVAILABLE = False

# CLI Tools and Context Engine (optional import)
try:
    from .tools import (
        LinuxCLITool,
        FileSystemTool,
        ProcessTool,
        NetworkTool,
        SystemInfoTool,
        DevelopmentTool,
    )
    from .context import (
        RAGSystem,
        ContextEngine,
        create_rag_system,
    )
    from .agents.cli_agent import CLIAgent, create_cli_agent
    _TOOLS_AVAILABLE = True
except ImportError:
    _TOOLS_AVAILABLE = False

__author__ = "Agent Swarm Contributors"

__all__ = [
    # Core abstractions
    "AbstractLLM",
    "LLMConfig",
    "LLMRouter",
    "LLMTier",
    "Message",
    "LLMResponse",
    # Agents
    "MultiLLMAgent",
    "CodingAgent",
    "create_coding_agent",
    # Utils
    "setup_logging",
    # Metadata
    "__version__",
    "__author__",
]

# Add MCP components if available
if _MCP_AVAILABLE:
    __all__.extend([
        "MCPClient",
        "MCPToolRegistry",
        "MCPEnabledAgent",
        "MCPRouter",
    ])

# Add CLI Tools and Context components if available
if _TOOLS_AVAILABLE:
    __all__.extend([
        "LinuxCLITool",
        "FileSystemTool",
        "ProcessTool",
        "NetworkTool",
        "SystemInfoTool",
        "DevelopmentTool",
        "RAGSystem",
        "ContextEngine",
        "create_rag_system",
        "CLIAgent",
        "create_cli_agent",
    ])
