"""
Context Manager for @ Commands

Handles @file and @folder commands to add files/folders to request context.
Similar to modern AI tools like Claude, ChatGPT, etc.
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import mimetypes


@dataclass
class ContextItem:
    """An item added to context via @ command."""
    path: Path
    type: str  # "file" or "folder"
    content: Optional[str] = None
    metadata: Dict[str, Any] = None
    size: int = 0
    error: Optional[str] = None


class ContextManager:
    """
    Manages @ commands for adding files/folders to request context.

    Features:
    - @file.py - Add specific file to context
    - @folder/ - Add folder contents to context
    - @*.py - Add files matching pattern
    - Smart content extraction
    - Size limits and safety checks
    - Rich metadata
    """

    def __init__(self, project_path: Optional[Path] = None):
        """Initialize context manager."""
        self.project_path = project_path or Path.cwd()
        self.context_items: List[ContextItem] = []
        self.persistent_context: List[ContextItem] = []  # For subsequent queries
        self.last_at_command_files: List[str] = []  # Track recent @ command usage
        self.max_file_size = 1024 * 1024  # 1MB per file
        self.max_total_size = 10 * 1024 * 1024  # 10MB total
        self.max_files = 50  # Maximum files in context

        # Supported file types for content extraction
        self.text_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.h',
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
            '.html', '.css', '.scss', '.less', '.xml', '.json', '.yaml', '.yml',
            '.toml', '.ini', '.cfg', '.conf', '.md', '.rst', '.txt', '.log',
            '.sql', '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd'
        }

        # Binary file types to skip
        self.binary_extensions = {
            '.exe', '.dll', '.so', '.dylib', '.bin', '.dat', '.db', '.sqlite',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.ico', '.webp',
            '.mp3', '.mp4', '.avi', '.mov', '.wav', '.flac', '.ogg',
            '.zip', '.tar', '.gz', '.bz2', '.xz', '.7z', '.rar',
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'
        }

    def parse_at_commands(self, text: str) -> Tuple[str, List[str]]:
        """
        Parse @ commands from text and return cleaned text + @ commands.

        Args:
            text: Input text with potential @ commands

        Returns:
            Tuple of (cleaned_text, at_commands)
        """
        # Find all @ commands
        at_pattern = r'@([^\s]+)'
        at_commands = re.findall(at_pattern, text)

        # Remove @ commands from text
        cleaned_text = re.sub(at_pattern, '', text).strip()

        # Clean up extra whitespace
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)

        return cleaned_text, at_commands

    def process_at_commands(self, at_commands: List[str]) -> List[ContextItem]:
        """
        Process @ commands and add items to context.

        Args:
            at_commands: List of @ command arguments

        Returns:
            List of processed context items
        """
        new_items = []

        for command in at_commands:
            items = self._process_single_at_command(command)
            new_items.extend(items)

        # Add to context with size limits
        for item in new_items:
            if self._can_add_to_context(item):
                self.context_items.append(item)

        # Track @ command usage for persistence
        self._update_at_command_tracking(at_commands, new_items)

        return new_items

    def _process_single_at_command(self, command: str) -> List[ContextItem]:
        """Process a single @ command."""
        items = []

        # Resolve path relative to project
        if command.startswith('/'):
            path = Path(command)
        else:
            path = self.project_path / command

        try:
            if '*' in command or '?' in command:
                # Pattern matching
                items.extend(self._process_pattern(command))
            elif path.is_file():
                # Single file
                item = self._create_file_item(path)
                if item:
                    items.append(item)
            elif path.is_dir():
                # Directory
                items.extend(self._process_directory(path))
            else:
                # File doesn't exist
                items.append(ContextItem(
                    path=path,
                    type="file",
                    error=f"File not found: {path}"
                ))

        except Exception as e:
            items.append(ContextItem(
                path=path,
                type="file",
                error=f"Error processing {command}: {e}"
            ))

        return items

    def _process_pattern(self, pattern: str) -> List[ContextItem]:
        """Process wildcard patterns like *.py."""
        items = []

        try:
            # Use pathlib glob
            if pattern.startswith('/'):
                base_path = Path('/')
                glob_pattern = pattern[1:]
            else:
                base_path = self.project_path
                glob_pattern = pattern

            for path in base_path.glob(glob_pattern):
                if path.is_file():
                    item = self._create_file_item(path)
                    if item:
                        items.append(item)

                        # Limit pattern matches
                        if len(items) >= 20:
                            break

        except Exception as e:
            items.append(ContextItem(
                path=Path(pattern),
                type="file",
                error=f"Pattern error: {e}"
            ))

        return items

    def _process_directory(self, dir_path: Path) -> List[ContextItem]:
        """Process directory contents."""
        items = []

        try:
            # Add directory info
            dir_item = ContextItem(
                path=dir_path,
                type="folder",
                metadata={
                    "file_count": len(list(dir_path.iterdir())),
                    "subdirs": len([p for p in dir_path.iterdir() if p.is_dir()]),
                    "files": len([p for p in dir_path.iterdir() if p.is_file()])
                }
            )
            items.append(dir_item)

            # Add text files from directory (non-recursive)
            for file_path in dir_path.iterdir():
                if file_path.is_file() and self._is_text_file(file_path):
                    item = self._create_file_item(file_path)
                    if item:
                        items.append(item)

                        # Limit directory files
                        if len(items) >= 15:
                            break

        except Exception as e:
            items.append(ContextItem(
                path=dir_path,
                type="folder",
                error=f"Directory error: {e}"
            ))

        return items

    def _create_file_item(self, file_path: Path) -> Optional[ContextItem]:
        """Create a context item for a file."""
        try:
            # Check file size
            size = file_path.stat().st_size
            if size > self.max_file_size:
                return ContextItem(
                    path=file_path,
                    type="file",
                    size=size,
                    error=f"File too large: {size} bytes (max: {self.max_file_size})"
                )

            # Check if it's a text file
            if not self._is_text_file(file_path):
                return ContextItem(
                    path=file_path,
                    type="file",
                    size=size,
                    metadata={"file_type": "binary"},
                    error="Binary file - content not extracted"
                )

            # Read content
            try:
                content = file_path.read_text(encoding='utf-8')
            except UnicodeDecodeError:
                try:
                    content = file_path.read_text(encoding='latin-1')
                except:
                    content = None

            # Create metadata
            metadata = {
                "file_type": self._get_file_type(file_path),
                "extension": file_path.suffix,
                "lines": len(content.splitlines()) if content else 0,
                "modified": file_path.stat().st_mtime
            }

            return ContextItem(
                path=file_path,
                type="file",
                content=content,
                size=size,
                metadata=metadata
            )

        except Exception as e:
            return ContextItem(
                path=file_path,
                type="file",
                error=f"Error reading file: {e}"
            )

    def _is_text_file(self, file_path: Path) -> bool:
        """Check if file is a text file."""
        # Check extension
        if file_path.suffix.lower() in self.binary_extensions:
            return False

        if file_path.suffix.lower() in self.text_extensions:
            return True

        # Use mimetypes as fallback
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if mime_type:
            return mime_type.startswith('text/') or mime_type in [
                'application/json', 'application/xml', 'application/javascript'
            ]

        # Default to text for unknown extensions
        return True

    def _get_file_type(self, file_path: Path) -> str:
        """Get file type description."""
        ext = file_path.suffix.lower()

        type_map = {
            '.py': 'Python',
            '.js': 'JavaScript',
            '.ts': 'TypeScript',
            '.jsx': 'React JSX',
            '.tsx': 'React TSX',
            '.java': 'Java',
            '.cpp': 'C++',
            '.c': 'C',
            '.h': 'C Header',
            '.cs': 'C#',
            '.php': 'PHP',
            '.rb': 'Ruby',
            '.go': 'Go',
            '.rs': 'Rust',
            '.html': 'HTML',
            '.css': 'CSS',
            '.json': 'JSON',
            '.yaml': 'YAML',
            '.yml': 'YAML',
            '.md': 'Markdown',
            '.txt': 'Text',
            '.sql': 'SQL',
            '.sh': 'Shell Script'
        }

        return type_map.get(ext, 'Text')

    def _can_add_to_context(self, item: ContextItem) -> bool:
        """Check if item can be added to context."""
        # Check file count limit
        if len(self.context_items) >= self.max_files:
            return False

        # Check total size limit
        current_size = sum(item.size for item in self.context_items)
        if current_size + item.size > self.max_total_size:
            return False

        return True

    def get_context_summary(self) -> str:
        """Get summary of current context."""
        if not self.context_items:
            return "No files in context"

        summary = f"Context: {len(self.context_items)} items\n"

        # Group by type
        files = [item for item in self.context_items if item.type == "file" and not item.error]
        folders = [item for item in self.context_items if item.type == "folder"]
        errors = [item for item in self.context_items if item.error]

        if files:
            summary += f"📄 Files ({len(files)}):\n"
            for item in files[:10]:  # Show first 10
                lines = item.metadata.get('lines', 0) if item.metadata else 0
                summary += f"  • {item.path.name} ({lines} lines)\n"

            if len(files) > 10:
                summary += f"  ... and {len(files) - 10} more files\n"

        if folders:
            summary += f"📁 Folders ({len(folders)}):\n"
            for item in folders:
                file_count = item.metadata.get('file_count', 0) if item.metadata else 0
                summary += f"  • {item.path.name}/ ({file_count} items)\n"

        if errors:
            summary += f"❌ Errors ({len(errors)}):\n"
            for item in errors[:5]:  # Show first 5 errors
                summary += f"  • {item.path.name}: {item.error}\n"

        # Size info
        total_size = sum(item.size for item in self.context_items)
        summary += f"\n📊 Total size: {total_size:,} bytes"

        return summary

    def get_context_for_llm(self, priority_level: str = "high") -> str:
        """Get context formatted for LLM consumption with explicit priority weighting."""
        if not self.context_items:
            return ""

        context = ""

        # High priority header for @ commands
        if priority_level == "high":
            context += "🎯 PRIMARY FOCUS FILES (User explicitly requested with @ commands)\n"
            context += "=" * 70 + "\n"
            context += "⚠️  CRITICAL: These files are the MAIN FOCUS of the user's request.\n"
            context += "⚠️  Give these files HIGHEST PRIORITY in your analysis and response.\n"
            context += "⚠️  The user explicitly selected these files for attention.\n"
            context += "=" * 70 + "\n\n"
        else:
            context += "📚 REFERENCE CONTEXT FILES\n"
            context += "=" * 40 + "\n\n"

        for item in self.context_items:
            if item.error:
                context += f"❌ ERROR - {item.path}: {item.error}\n\n"
                continue

            if item.type == "folder":
                file_count = item.metadata.get('file_count', 0) if item.metadata else 0
                if priority_level == "high":
                    context += f"🎯 PRIMARY FOLDER: {item.path} ({file_count} items)\n"
                    context += "📁 This directory was explicitly requested by the user.\n\n"
                else:
                    context += f"📁 FOLDER: {item.path} ({file_count} items)\n\n"
                continue

            # File content with priority indicators
            if priority_level == "high":
                context += f"🎯 PRIMARY FILE: {item.path}\n"
                context += "🔍 USER EXPLICITLY REQUESTED THIS FILE - FOCUS ON IT!\n"
            else:
                context += f"📄 Reference File: {item.path}\n"

            if item.metadata:
                file_type = item.metadata.get('file_type', 'Unknown')
                lines = item.metadata.get('lines', 0)
                context += f"📊 Type: {file_type}, Lines: {lines}\n"

            if priority_level == "high":
                context += "🔍 CONTENT (ANALYZE THIS CAREFULLY):\n"
                context += "=" * 50 + "\n"
            else:
                context += "---\n"

            if item.content:
                # Limit content size for very large files
                if len(item.content) > 10000:
                    context += item.content[:10000] + "\n... (truncated)\n"
                else:
                    context += item.content + "\n"

            if priority_level == "high":
                context += "=" * 50 + "\n"
                context += "🎯 END OF PRIMARY FILE CONTENT\n\n"
            else:
                context += "---\n\n"

        if priority_level == "high":
            context += "🎯 END OF PRIMARY FOCUS FILES\n"
            context += "💡 REMINDER: Focus your response on these explicitly requested files!\n"
            context += "💡 The user used @ commands to specifically select these files.\n"
            context += "=" * 70 + "\n\n"

        return context

    def clear_context(self):
        """Clear all context items."""
        self.context_items.clear()

    def remove_context_item(self, path: str) -> bool:
        """Remove specific item from context."""
        for i, item in enumerate(self.context_items):
            if str(item.path) == path or item.path.name == path:
                del self.context_items[i]
                return True
        return False

    def _update_at_command_tracking(self, at_commands: List[str], new_items: List[ContextItem]):
        """Update tracking for @ command persistence."""
        # Track recent @ command files
        for item in new_items:
            if not item.error and item.type == "file":
                file_path = str(item.path)
                if file_path not in self.last_at_command_files:
                    self.last_at_command_files.append(file_path)

        # Keep only last 5 @ command files
        self.last_at_command_files = self.last_at_command_files[-5:]

        # Move current context to persistent for next query
        self.persistent_context = [item for item in self.context_items if not item.error]

    def get_context_for_llm_with_persistence(self, has_at_commands: bool = True) -> str:
        """Get context with persistence logic for subsequent queries."""
        if has_at_commands:
            # High priority for explicit @ commands
            return self.get_context_for_llm(priority_level="high")
        elif self.persistent_context:
            # Lower priority for persistent context from previous @ commands
            return self._format_persistent_context()
        else:
            return ""

    def _format_persistent_context(self) -> str:
        """Format persistent context with lower priority."""
        if not self.persistent_context:
            return ""

        context = "📚 REFERENCE CONTEXT (from recent @ commands)\n"
        context += "=" * 50 + "\n"
        context += "💡 These files were recently accessed with @ commands.\n"
        context += "💡 They provide background context for your current request.\n"
        context += "=" * 50 + "\n\n"

        for item in self.persistent_context[:3]:  # Limit to 3 most recent
            if item.type == "file" and not item.error:
                context += f"📄 Reference File: {item.path}\n"
                if item.metadata:
                    file_type = item.metadata.get('file_type', 'Unknown')
                    lines = item.metadata.get('lines', 0)
                    context += f"📊 Type: {file_type}, Lines: {lines}\n"

                context += "---\n"
                if item.content:
                    # Truncate persistent context more aggressively
                    if len(item.content) > 3000:
                        context += item.content[:3000] + "\n... (truncated for reference)\n"
                    else:
                        context += item.content + "\n"
                context += "---\n\n"

        context += "📚 END OF REFERENCE CONTEXT\n\n"
        return context

    def should_include_persistent_context(self, user_input: str) -> bool:
        """Determine if persistent context should be included."""
        # Include if no @ commands and we have persistent context
        has_at_commands = '@' in user_input
        has_persistent = len(self.persistent_context) > 0

        # Also include if user mentions files from recent @ commands
        mentions_recent_files = any(
            file_path.split('/')[-1].lower() in user_input.lower()
            for file_path in self.last_at_command_files
        )

        return not has_at_commands and (has_persistent or mentions_recent_files)
