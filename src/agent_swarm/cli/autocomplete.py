"""
Advanced Autocomplete System for Agent Swarm CLI

Provides intelligent autocomplete with:
- Command completion
- Path completion
- Context-aware suggestions
- File type recognition
"""

import os
import re
import readline
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Any
from dataclasses import dataclass


@dataclass
class CompletionSuggestion:
    """A completion suggestion."""
    text: str
    description: str
    type: str  # "command", "file", "directory", "option"
    priority: int = 0


class AdvancedCompleter:
    """
    Advanced autocomplete system for Agent Swarm CLI.

    Features:
    - Command completion with descriptions
    - Intelligent path completion
    - Context-aware suggestions
    - File type recognition
    - Parameter completion
    """

    def __init__(self, shell_instance=None):
        """Initialize the completer."""
        self.shell = shell_instance
        self.project_path = None
        self.commands = {}
        self.command_indexer = None

        # File type patterns for intelligent suggestions
        self.file_patterns = {
            'python': ['.py', '.pyx', '.pyi'],
            'javascript': ['.js', '.jsx', '.ts', '.tsx'],
            'config': ['.json', '.yaml', '.yml', '.toml', '.ini', '.cfg'],
            'docs': ['.md', '.rst', '.txt', '.doc'],
            'data': ['.csv', '.json', '.xml', '.sql'],
            'image': ['.png', '.jpg', '.jpeg', '.gif', '.svg'],
            'archive': ['.zip', '.tar', '.gz', '.bz2']
        }

        # Commands that expect file paths
        self.file_commands = {
            'edit', 'smart-edit', 'file', 'create', 'delete',
            'diff', 'analyze', 'review', 'explain'
        }

        # Commands that expect directory paths
        self.dir_commands = {'project', 'cd', 'index'}

        # Setup readline
        self._setup_readline()

    def _setup_readline(self):
        """Setup readline for autocomplete."""
        try:
            # Set completer
            readline.set_completer(self.complete)

            # Enable tab completion
            readline.parse_and_bind("tab: complete")

            # Set completion display hook
            readline.set_completion_display_matches_hook(self.display_matches)

            # Configure completion behavior - remove @ from delimiters to support @ commands
            readline.set_completer_delims(' \t\n`!#$%^&*()=+[{]}\\|;:\'",<>?')

        except ImportError:
            # readline not available (Windows without pyreadline)
            pass

    def set_shell(self, shell_instance):
        """Set the shell instance for context."""
        self.shell = shell_instance
        if shell_instance:
            self.project_path = getattr(shell_instance, 'project_path', None)
            self.commands = getattr(shell_instance, 'commands', {})

            # Initialize command indexer
            try:
                from ..context.cli_commands_indexer import CLICommandsIndexer
                self.command_indexer = CLICommandsIndexer()
                self.command_indexer.index_shell_commands(shell_instance)
            except ImportError:
                pass

    def complete(self, text: str, state: int) -> Optional[str]:
        """Main completion function called by readline."""
        try:
            # Get current line
            line = readline.get_line_buffer()

            # Generate completions
            completions = self.get_completions(line, text)

            # Return completion at state index
            if state < len(completions):
                return completions[state].text

        except Exception:
            # Fail silently to avoid breaking the shell
            pass

        return None

    def get_completions(self, line: str, text: str) -> List[CompletionSuggestion]:
        """Get all possible completions for the current context."""
        completions = []

        # Check if we're completing an @ command (file/folder context)
        if self._is_at_command_context(line, text):
            completions.extend(self._complete_at_command(line, text))
            return completions

        # Parse the current line for regular command completion
        parts = line.split()

        if not parts or (len(parts) == 1 and not line.endswith(' ')):
            # Completing command name
            completions.extend(self._complete_command(text))
        else:
            # Completing command arguments
            command = parts[0].lstrip('/')
            arg_index = len(parts) - 1 if not line.endswith(' ') else len(parts)

            completions.extend(self._complete_command_args(command, text, arg_index, parts))

        # Sort by priority and text
        completions.sort(key=lambda x: (-x.priority, x.text))

        return completions

    def _complete_command(self, text: str) -> List[CompletionSuggestion]:
        """Complete command names."""
        completions = []
        prefix = text.lstrip('/')

        if self.command_indexer:
            # Use indexed commands
            suggestions = self.command_indexer.get_command_suggestions(text)
            for cmd_info in suggestions:
                completions.append(CompletionSuggestion(
                    text=f"/{cmd_info.name}",
                    description=cmd_info.description,
                    type="command",
                    priority=100
                ))
        else:
            # Fallback to basic command completion
            basic_commands = [
                'help', 'exit', 'clear', 'history', 'project', 'model', 'models',
                'search', 'edit', 'smart-edit', 'diff', 'revert', 'analyze', 'index'
            ]

            for cmd in basic_commands:
                if cmd.startswith(prefix):
                    completions.append(CompletionSuggestion(
                        text=f"/{cmd}",
                        description=f"Execute {cmd} command",
                        type="command",
                        priority=90
                    ))

        return completions

    def _is_at_command_context(self, line: str, text: str) -> bool:
        """Check if we're in an @ command context for file/folder completion."""
        # Look for @ followed by partial path in the current line
        import re

        # Find the position of the cursor (end of text)
        cursor_pos = len(line)

        # Look for @ commands before the cursor
        at_pattern = r'@([^\s]*)'
        matches = list(re.finditer(at_pattern, line))

        for match in matches:
            start, end = match.span()
            # Check if cursor is within or right after this @ command
            if start <= cursor_pos <= end + 1:
                return True

        # Also check if the current text starts with @ or we're right after @
        if text.startswith('@') or (len(line) > 0 and line[-1] == '@'):
            return True

        return False

    def _complete_at_command(self, line: str, text: str) -> List[CompletionSuggestion]:
        """Complete @ commands with file/folder paths."""
        import re
        completions = []

        # Extract the @ command part
        at_pattern = r'@([^\s]*)'
        matches = list(re.finditer(at_pattern, line))

        # Find the @ command we're currently completing
        cursor_pos = len(line)
        current_at_command = None

        for match in matches:
            start, end = match.span()
            if start <= cursor_pos <= end + 1:
                current_at_command = match.group(1)  # The part after @
                break

        # If text starts with @, extract the path part
        if text.startswith('@'):
            path_part = text[1:]  # Remove the @
        elif current_at_command is not None:
            path_part = current_at_command
        else:
            path_part = text

        # Get file/folder completions
        file_completions = self._complete_file_path(path_part)

        # Convert to @ command format
        for completion in file_completions:
            # Add @ prefix if not already present
            if not completion.text.startswith('@'):
                at_text = f"@{completion.text}"
            else:
                at_text = completion.text

            completions.append(CompletionSuggestion(
                text=at_text,
                description=f"@ command: {completion.description}",
                type="at_command",
                priority=completion.priority + 10  # Higher priority for @ commands
            ))

        return completions

    def _complete_command_args(self, command: str, text: str, arg_index: int, parts: List[str]) -> List[CompletionSuggestion]:
        """Complete command arguments based on command type."""
        completions = []

        # File path completion for file commands
        if command in self.file_commands:
            completions.extend(self._complete_file_path(text, files_only=True))

        # Directory path completion for directory commands
        elif command in self.dir_commands:
            completions.extend(self._complete_file_path(text, dirs_only=True))

        # Special command-specific completions
        elif command == 'model':
            completions.extend(self._complete_model_names(text))

        elif command == 'config':
            completions.extend(self._complete_config_options(text, arg_index))

        elif command == 'search':
            completions.extend(self._complete_search_terms(text))

        # Generic file path completion as fallback
        if not completions:
            completions.extend(self._complete_file_path(text))

        return completions

    def _complete_file_path(self, text: str, files_only: bool = False, dirs_only: bool = False) -> List[CompletionSuggestion]:
        """Complete file and directory paths."""
        completions = []

        # Determine base directory
        if text.startswith('/'):
            # Absolute path
            base_dir = Path('/')
            relative_path = text[1:]
        elif text.startswith('~'):
            # Home directory
            base_dir = Path.home()
            relative_path = text[2:] if text.startswith('~/') else ''
        else:
            # Relative path
            base_dir = self.project_path or Path.cwd()
            relative_path = text

        # Split path into directory and filename parts
        if '/' in relative_path:
            dir_part, file_part = relative_path.rsplit('/', 1)
            search_dir = base_dir / dir_part
        else:
            file_part = relative_path
            search_dir = base_dir

        # Get completions from directory
        if search_dir.exists() and search_dir.is_dir():
            try:
                for item in search_dir.iterdir():
                    if item.name.startswith('.') and not file_part.startswith('.'):
                        continue  # Skip hidden files unless explicitly requested

                    if item.name.startswith(file_part):
                        if item.is_dir():
                            if not files_only:
                                completions.append(CompletionSuggestion(
                                    text=str(item.relative_to(base_dir)) + '/',
                                    description=f"Directory: {item.name}",
                                    type="directory",
                                    priority=80
                                ))
                        else:
                            if not dirs_only:
                                file_type = self._get_file_type(item)
                                completions.append(CompletionSuggestion(
                                    text=str(item.relative_to(base_dir)),
                                    description=f"{file_type.title()} file: {item.name}",
                                    type="file",
                                    priority=70
                                ))
            except PermissionError:
                pass

        return completions

    def _complete_model_names(self, text: str) -> List[CompletionSuggestion]:
        """Complete AI model names."""
        completions = []

        # Common model names
        models = [
            'llama3.2:3b', 'llama3.2:1b', 'llama3.1:8b', 'llama3.1:70b',
            'codellama:7b', 'codellama:13b', 'codellama:34b',
            'mistral:7b', 'mixtral:8x7b', 'phi3:mini', 'qwen2.5:7b'
        ]

        for model in models:
            if model.startswith(text):
                completions.append(CompletionSuggestion(
                    text=model,
                    description=f"AI model: {model}",
                    type="option",
                    priority=60
                ))

        return completions

    def _complete_config_options(self, text: str, arg_index: int) -> List[CompletionSuggestion]:
        """Complete configuration options."""
        completions = []

        if arg_index == 1:
            # Config actions
            actions = ['show', 'set', 'get', 'delete', 'list']
            for action in actions:
                if action.startswith(text):
                    completions.append(CompletionSuggestion(
                        text=action,
                        description=f"Config action: {action}",
                        type="option",
                        priority=60
                    ))

        elif arg_index == 2:
            # Config keys
            keys = ['api_key', 'default_model', 'default_provider', 'verbose', 'auto_index']
            for key in keys:
                if key.startswith(text):
                    completions.append(CompletionSuggestion(
                        text=key,
                        description=f"Config key: {key}",
                        type="option",
                        priority=60
                    ))

        return completions

    def _complete_search_terms(self, text: str) -> List[CompletionSuggestion]:
        """Complete search terms based on project context."""
        completions = []

        # Common search patterns
        patterns = [
            'async def', 'class ', 'import ', 'from ', 'def ', 'function',
            'error', 'exception', 'TODO', 'FIXME', 'config', 'settings'
        ]

        for pattern in patterns:
            if pattern.startswith(text.lower()):
                completions.append(CompletionSuggestion(
                    text=pattern,
                    description=f"Search pattern: {pattern}",
                    type="option",
                    priority=50
                ))

        return completions

    def _get_file_type(self, file_path: Path) -> str:
        """Get file type description."""
        suffix = file_path.suffix.lower()

        for file_type, extensions in self.file_patterns.items():
            if suffix in extensions:
                return file_type

        return "file"

    def display_matches(self, substitution: str, matches: List[str], longest_match_length: int):
        """Custom display for completion matches."""
        if not matches:
            return

        print()  # New line

        # Group matches by type
        by_type = {}
        for match in matches:
            # Find the corresponding suggestion
            suggestion = None
            for comp in self.get_completions(readline.get_line_buffer(), substitution):
                if comp.text == match:
                    suggestion = comp
                    break

            if suggestion:
                if suggestion.type not in by_type:
                    by_type[suggestion.type] = []
                by_type[suggestion.type].append(suggestion)

        # Display by type
        type_order = ["at_command", "command", "directory", "file", "option"]
        for comp_type in type_order:
            if comp_type in by_type:
                suggestions = by_type[comp_type]
                print(f"\n{comp_type.title()}s:")
                for suggestion in suggestions[:10]:  # Limit display
                    print(f"  {suggestion.text:<30} {suggestion.description}")

        # Redisplay prompt
        print(f"\n🤖 [llama3.2:3b] > ", end="", flush=True)

    def get_path_suggestions(self, partial_path: str, context: str = "") -> List[str]:
        """Get path suggestions for @ command."""
        suggestions = []

        # Determine base directory
        if partial_path.startswith('/'):
            base_dir = Path('/')
            relative_path = partial_path[1:]
        else:
            base_dir = self.project_path or Path.cwd()
            relative_path = partial_path

        # Split path
        if '/' in relative_path:
            dir_part, file_part = relative_path.rsplit('/', 1)
            search_dir = base_dir / dir_part
        else:
            file_part = relative_path
            search_dir = base_dir

        # Get suggestions
        if search_dir.exists() and search_dir.is_dir():
            try:
                for item in search_dir.iterdir():
                    if item.name.startswith(file_part):
                        if item.is_dir():
                            suggestions.append(str(item.relative_to(base_dir)) + '/')
                        else:
                            suggestions.append(str(item.relative_to(base_dir)))
            except PermissionError:
                pass

        return suggestions[:20]  # Limit suggestions
