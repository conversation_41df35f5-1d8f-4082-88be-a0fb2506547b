#!/usr/bin/env python3
"""
Interactive Shell for Agent Swarm
A powerful command-line interface for AI-powered development.
"""

import asyncio
import os
import sys
import time
from pathlib import Path
from typing import Optional, Dict, Any, List
import readline
import atexit
from datetime import datetime

# Add src to path for development (only if needed)
try:
    import agent_swarm
except ImportError:
    # Only add to path if we can't import (development mode)
    src_path = Path(__file__).parent.parent.parent
    if src_path.name == "src" or (src_path / "src").exists():
        sys.path.insert(0, str(src_path if src_path.name == "src" else src_path / "src"))

from agent_swarm import setup_logging, create_coding_agent, __version__
from agent_swarm.backends import create_ollama_llm, LLMRouter, Message
from agent_swarm.utils.logging import get_logger
from agent_swarm.context import (
    UnifiedContext, QueryIntent, ResponseMode, ContextDepth
)
from agent_swarm.cli.tools import (
    FileSystemTools, DevelopmentTools, NetworkTools, SystemTools
)
from agent_swarm.core.enhanced_intent_processor import EnhancedIntentProcessor

# Rich imports for beautiful CLI
try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.text import Text
    from rich.syntax import Syntax
    from rich.table import Table
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.prompt import Prompt
    from rich.markdown import Markdown
    from rich.columns import Columns
    from rich.align import Align
    from rich.live import Live
    from rich.layout import Layout
    from rich.rule import Rule
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    Console = None

logger = get_logger("interactive_shell")


class AgentSwarmShell:
    """Interactive shell for Agent Swarm with beautiful Rich UI."""

    def __init__(self, verbose=False, auto_index=None):
        self.agent = None
        self.llm_router = None
        self.context_engine = None
        self.conversation_history = []
        self.project_path = None
        self.current_model = "llama3.2:3b"
        self.current_task = None
        self.session_start = datetime.now()
        self.verbose = verbose
        self.auto_index = auto_index  # None = ask user, True = auto-index, False = skip

        # Double Ctrl+C handling
        self._last_interrupt_time = 0
        self._interrupt_threshold = 2.0  # 2 seconds for double Ctrl+C

        # Agent routing system
        self.agent_router = None
        self.show_agent_info = True  # Show agent selection info

        # Advanced features
        self.context_manager = None
        self.autocompleter = None
        self.commands_indexer = None
        self.orchestrator = None  # Multi-step execution orchestrator
        self.enhanced_intent_processor = None  # Revolutionary intent processing

        # Context preferences
        self.response_mode = ResponseMode.DETAILED
        self.context_depth = ContextDepth.MEDIUM
        self.include_tests = True
        self.include_docs = True

        # Initialize Rich console
        if RICH_AVAILABLE:
            self.console = Console()
            self.use_rich = True
        else:
            self.console = None
            self.use_rich = False

        self.commands = {
            # Core commands
            'help': self.cmd_help,
            'version': self.cmd_version,
            'exit': self.cmd_exit,
            'quit': self.cmd_exit,
            'clear': self.cmd_clear,
            'history': self.cmd_history,

            # Project and model commands
            'project': self.cmd_project,
            'model': self.cmd_model,
            'models': self.cmd_list_models,

            # Analysis commands
            'search': self.cmd_search,
            'analyze': self.cmd_analyze,
            'stats': self.cmd_stats,
            'files': self.cmd_files,
            'tree': self.cmd_files,  # Alias for files
            'context': self.cmd_context,
            'index': self.cmd_index,  # Index project files
            'update': self.cmd_update,  # Update Agent Swarm
            'config': self.cmd_config,  # Configuration management

            # File editing commands
            'edit': self.cmd_edit,  # Advanced file editing
            'smart-edit': self.cmd_smart_edit,  # AI-powered editing
            'diff': self.cmd_diff,  # Show file diffs
            'revert': self.cmd_revert,  # Revert changes

            # Development commands
            'implement': self.cmd_implement,
            'fix': self.cmd_fix,
            'review': self.cmd_review,
            'explain': self.cmd_explain,

            # File system commands (NEW)
            'find': self.cmd_find,
            'grep': self.cmd_grep,
            'info': self.cmd_info,

            # Development tools (NEW)
            'projectinfo': self.cmd_project_info,  # Enhanced project info

            # Network commands (NEW)
            'ping': self.cmd_ping,
            'services': self.cmd_services,

            # System commands (NEW)
            'system': self.cmd_system,

            # Enhanced Intent Processing commands (REVOLUTIONARY)
            'intent-analytics': self.cmd_intent_analytics,
            'intent-test': self.cmd_intent_test,
        }

        # Setup readline for command history
        self.setup_readline()

    def setup_readline(self):
        """Setup readline for command history and completion."""
        try:
            # History file
            history_file = Path.home() / ".agent_swarm_history"

            # Load existing history
            if history_file.exists():
                readline.read_history_file(str(history_file))

            # Set history length
            readline.set_history_length(1000)

            # Save history on exit
            atexit.register(readline.write_history_file, str(history_file))

            # Tab completion
            readline.set_completer(self.complete)
            readline.parse_and_bind("tab: complete")

        except ImportError:
            # readline not available on all systems
            pass

    def complete(self, text, state):
        """Tab completion for commands."""
        options = [cmd for cmd in self.commands.keys() if cmd.startswith(text)]

        if state < len(options):
            return options[state]
        return None

    def print_rich(self, content, style="default"):
        """Print content with Rich formatting if available."""
        if self.use_rich:
            if isinstance(content, str):
                self.console.print(content)
            else:
                self.console.print(content)
        else:
            # Fallback to regular print
            print(str(content))

    def print_panel(self, content, title=None, style="blue"):
        """Print content in a panel."""
        if self.use_rich:
            panel = Panel(content, title=title, border_style=style)
            self.console.print(panel)
        else:
            if title:
                print(f"\n=== {title} ===")
            print(content)
            if title:
                print("=" * (len(title) + 8))

    def print_code(self, code, language="python"):
        """Print code with syntax highlighting."""
        if self.use_rich:
            syntax = Syntax(code, language, theme="monokai", line_numbers=True)
            self.console.print(syntax)
        else:
            print(code)

    def print_status(self, message, status="info"):
        """Print status message with appropriate styling."""
        if self.use_rich:
            if status == "success":
                self.console.print(f"✅ {message}", style="green")
            elif status == "error":
                self.console.print(f"❌ {message}", style="red")
            elif status == "warning":
                self.console.print(f"⚠️ {message}", style="yellow")
            elif status == "info":
                self.console.print(f"ℹ️ {message}", style="blue")
            elif status == "thinking":
                self.console.print(f"🤖 {message}", style="cyan")
            else:
                self.console.print(message)
        else:
            icons = {
                "success": "✅",
                "error": "❌",
                "warning": "⚠️",
                "info": "ℹ️",
                "thinking": "🤖"
            }
            icon = icons.get(status, "")
            print(f"{icon} {message}")

    def show_progress(self, description="Working..."):
        """Show a progress spinner."""
        if self.use_rich:
            return Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            )
        else:
            print(f"⏳ {description}")
            return None

    def get_status_bar(self):
        """Get current status information."""
        uptime = datetime.now() - self.session_start
        uptime_str = str(uptime).split('.')[0]  # Remove microseconds

        status_items = [
            f"🤖 {self.current_model}",
            f"📁 {self.project_path.name if self.project_path else 'No project'}",
            f"💬 {len(self.conversation_history)} messages",
            f"⏱️ {uptime_str}"
        ]

        if self.current_task:
            status_items.insert(0, f"🔄 {self.current_task}")

        return " | ".join(status_items)

    async def initialize(self, project_path: Optional[str] = None):
        """Initialize the shell with a project."""
        print("🚀 Initializing Agent Swarm Interactive Shell...")

        # Set project path
        if project_path:
            self.project_path = Path(project_path).resolve()
        else:
            self.project_path = Path.cwd()

        print(f"📁 Project: {self.project_path}")

        # Check if Ollama is available
        test_llm = create_ollama_llm(self.current_model)
        models = await test_llm.list_models()
        await test_llm.close()

        if not models:
            print("❌ No Ollama models available!")
            print("💡 Please install a model first:")
            print("   ollama pull llama3.2:3b")
            return False

        print(f"✅ Available models: {', '.join(models[:3])}{'...' if len(models) > 3 else ''}")

        # Create LLM router
        self.llm_router = LLMRouter()
        ollama_llm = create_ollama_llm(self.current_model)
        self.llm_router.register_llm("primary", ollama_llm)
        self.llm_router.set_task_routing("coding", "primary")
        self.llm_router.set_task_routing("analysis", "primary")
        self.llm_router.set_task_routing("general", "primary")

        # Create coding agent
        self.agent = await create_coding_agent(
            name="InteractiveAgent",
            project_path=str(self.project_path),
            enable_rag=True,
            llm_router=self.llm_router
        )

        print("✅ Agent initialized with RAG context")

        # Initialize unified context engine
        self.context_engine = UnifiedContext(str(self.project_path))

        # Ask user about auto-indexing on first run
        should_auto_index = await self._ask_about_indexing()
        await self.context_engine.initialize(enable_rag=True, enable_dev_features=True, auto_index=should_auto_index)

        # Initialize CLI tools
        self.fs_tools = FileSystemTools()
        self.dev_tools = DevelopmentTools()
        self.network_tools = NetworkTools()
        self.system_tools = SystemTools()

        # Initialize agent router for revolutionary agent-swarm functionality
        try:
            from ..core.agent_router import AgentRouter
            self.agent_router = AgentRouter()

            # Register routing callback for visibility
            self.agent_router.register_routing_callback(self._on_agent_routing)

            if self.verbose:
                print("🤖 Agent router initialized with smart routing")
        except ImportError:
            if self.verbose:
                print("⚠️  Agent router not available")

        # Initialize advanced features
        self._initialize_advanced_features()

        # Initialize revolutionary enhanced intent processor
        await self._initialize_enhanced_intent_processor()

        # The unified context engine automatically handles RAG integration

        if self.verbose:
            print("🧠 Unified context engine initialized")
            project_analysis = await self.context_engine.get_project_analysis()
            if project_analysis:
                print(f"   Project type: {project_analysis.project_type.value}")
                print(f"   Frameworks: {len(project_analysis.frameworks)} detected")
                print(f"   Confidence: {project_analysis.confidence_score:.2f}")

        if self.agent.dev_rag:
            stats = await self.agent.dev_rag.get_project_stats()
            print(f"📊 Indexed {stats['total_files_indexed']} files")

        return True

    async def _ask_about_indexing(self) -> bool:
        """Ask user if they want to auto-index the project."""
        # If auto_index was set via command line, use that
        if self.auto_index is not None:
            if self.auto_index:
                print("🚀 Auto-indexing enabled via command line...")
            else:
                print("⏭️ Auto-indexing disabled via command line. Use '/index' to index manually.")
            return self.auto_index

        # Otherwise, ask the user
        print("\n🤔 Project Indexing")
        print("=" * 50)
        print("📚 To provide smart context-aware responses, the AI needs to index your project files.")
        print("🔍 This analyzes your codebase to understand structure, functions, and patterns.")
        print("⚡ Indexing takes a few seconds but greatly improves response quality.")
        print("💡 You can always run '/index' later to index manually.")
        print()

        if self.use_rich:
            # Use Rich prompt for better UX
            try:
                response = Prompt.ask(
                    "[bold cyan]🚀 Index project files now?[/bold cyan]",
                    choices=["y", "n", "yes", "no"],
                    default="y",
                    show_default=True
                )
                return response.lower() in ["y", "yes"]
            except (KeyboardInterrupt, EOFError):
                print("\n⏭️ Skipping indexing. Use '/index' command later.")
                return False
        else:
            # Fallback for no Rich
            try:
                response = input("🚀 Index project files now? [Y/n]: ").strip().lower()
                if not response:
                    response = "y"
                return response in ["y", "yes"]
            except (KeyboardInterrupt, EOFError):
                print("\n⏭️ Skipping indexing. Use '/index' command later.")
                return False

    async def _initialize_enhanced_intent_processor(self):
        """Initialize the revolutionary enhanced intent processor."""
        try:
            self.enhanced_intent_processor = EnhancedIntentProcessor(
                enable_adaptive=True,
                enable_filtering=True,
                verbose=self.verbose
            )

            await self.enhanced_intent_processor.initialize()

            if self.verbose:
                print("🔺 Enhanced Intent Processor initialized with mathematical algorithms")
                print("   ⚡ AdaptiveIntentProcessor: 5-stage processing with early stopping")
                print("   🧠 IntentFilteringTriangle: 3D intent space analysis")

        except Exception as e:
            if self.verbose:
                print(f"⚠️  Enhanced Intent Processor initialization failed: {e}")
                print("   Falling back to traditional intent detection")
            self.enhanced_intent_processor = None

    def print_banner(self):
        """Print the welcome banner."""
        if self.use_rich:
            # Create a beautiful banner with Rich
            banner_text = Text()
            banner_text.append("🤖 Agent Swarm Interactive Shell\n", style="bold cyan")
            banner_text.append("🎯 AI-Powered Development Assistant", style="bold blue")

            examples = Text()
            examples.append("💡 Type '/help' for commands or just ask questions!\n\n", style="yellow")
            examples.append("🔍 Examples:\n", style="bold green")
            examples.append("   • 'search async def' - Find async functions\n", style="dim")
            examples.append("   • 'explain this code: def foo():' - Get explanations\n", style="dim")
            examples.append("   • 'implement a logger utility' - Generate code\n", style="dim")
            examples.append("   • 'fix this bug: ZeroDivisionError' - Debug issues\n", style="dim")

            # Status bar
            status = Text(self.get_status_bar(), style="dim blue")

            # Create panels
            self.console.print()
            self.print_panel(banner_text, title="Welcome", style="cyan")
            self.print_panel(examples, title="Quick Start", style="green")
            self.console.print(Panel(status, title="Status", border_style="blue"))
            self.console.print()
        else:
            # Fallback for no Rich
            print("\n" + "="*60)
            print("🤖 Agent Swarm Interactive Shell")
            print("🎯 AI-Powered Development Assistant")
            print("="*60)
            print("💡 Type '/help' for commands or just ask questions!")
            print("🔍 Examples:")
            print("   • 'search async def' - Find async functions")
            print("   • 'explain this code: def foo():' - Get explanations")
            print("   • 'implement a logger utility' - Generate code")
            print("   • 'fix this bug: ZeroDivisionError' - Debug issues")
            print("="*60 + "\n")

    async def run(self):
        """Run the interactive shell."""
        self.print_banner()

        while True:
            try:
                # Show status bar if Rich is available
                if self.use_rich:
                    status_text = Text(self.get_status_bar(), style="dim")
                    self.console.print(Rule(title="Status", style="dim"))
                    self.console.print(status_text)
                    self.console.print()

                # Get user input - use regular input() to enable readline/autocomplete
                prompt = f"🤖 [{self.current_model}] > "
                user_input = input(prompt).strip()

                if not user_input:
                    continue

                # Show user input in a nice format
                if self.use_rich:
                    user_panel = Panel(
                        Text(user_input, style="white"),
                        title="👤 You",
                        border_style="green",
                        padding=(0, 1)
                    )
                    self.console.print(user_panel)

                # Check for commands
                if user_input.startswith('/'):
                    await self.handle_command(user_input[1:])
                else:
                    await self.handle_chat(user_input)

            except KeyboardInterrupt:
                # Handle double Ctrl+C for graceful quit
                current_time = time.time()
                if current_time - self._last_interrupt_time < self._interrupt_threshold:
                    # Double Ctrl+C detected - graceful quit
                    self.print_status("Double Ctrl+C detected. Goodbye! 👋", "info")
                    await self.cmd_exit([])
                else:
                    # Single Ctrl+C - show help message
                    self._last_interrupt_time = current_time
                    self.print_status("Press Ctrl+C again within 2 seconds to quit, or use '/exit'", "warning")
                continue
            except EOFError:
                self.print_status("Goodbye!", "info")
                break
            except Exception as e:
                self.print_status(f"Error: {e}", "error")
                logger.error(f"Shell error: {e}")

    async def handle_command(self, command_line: str):
        """Handle shell commands."""
        parts = command_line.split()
        if not parts:
            return

        cmd = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []

        if cmd in self.commands:
            try:
                await self.commands[cmd](args)
            except Exception as e:
                print(f"❌ Command failed: {e}")
        else:
            print(f"❌ Unknown command: {cmd}")
            print("💡 Type '/help' for available commands")

    async def handle_chat(self, user_input: str):
        """Handle chat/AI interactions with revolutionary intent processing and agent routing."""
        if not self.agent:
            self.print_status("Agent not initialized. Use '/project <path>' first.", "error")
            return

        # 🔺 REVOLUTIONARY INTENT PROCESSING
        enhanced_intent = None
        if self.enhanced_intent_processor:
            try:
                # Process intent with mathematical algorithms
                enhanced_intent = await self.enhanced_intent_processor.process_intent(
                    user_input,
                    context={'conversation_history': len(self.conversation_history)}
                )

                if self.verbose:
                    # Show enhanced intent analysis
                    intent_explanation = self.enhanced_intent_processor.get_intent_explanation(enhanced_intent)
                    self.print_panel(intent_explanation, title="🔺 Enhanced Intent Analysis", style="magenta")

            except Exception as e:
                if self.verbose:
                    self.print_status(f"Enhanced intent processing failed: {e}", "warning")

        # Process @ commands for file/folder context
        original_input = user_input
        context_items = []

        if self.context_manager and '@' in user_input:
            try:
                if self.verbose:
                    self.print_status(f"Processing @ commands in: '{user_input}'", "info")

                cleaned_input, at_commands = self.context_manager.parse_at_commands(user_input)

                if self.verbose:
                    self.print_status(f"Found @ commands: {at_commands}", "info")

                if at_commands:
                    context_items = self.context_manager.process_at_commands(at_commands)

                    # Handle case where input is just @ commands (empty cleaned input)
                    if not cleaned_input.strip():
                        # Generate a helpful default prompt based on the files
                        file_names = [item.path.name for item in context_items if not item.error]
                        if file_names:
                            if len(file_names) == 1:
                                user_input = f"Please explain the contents of {file_names[0]}"
                            else:
                                user_input = f"Please explain these files: {', '.join(file_names)}"
                        else:
                            user_input = "Please help me understand the provided context"
                    else:
                        user_input = cleaned_input

                    if self.verbose:
                        summary = self.context_manager.get_context_summary()
                        self.print_status(f"Added context: {summary}", "info")
                        self.print_status(f"Final input: '{user_input}'", "info")

            except Exception as e:
                if self.verbose:
                    self.print_status(f"@ command processing failed: {e}", "warning")
                    import traceback
                    traceback.print_exc()
        elif '@' in user_input and self.verbose:
            self.print_status("@ commands detected but context manager not available", "warning")

        # Check if this requires multi-step execution (temporarily disabled for debugging)
        if False and self.orchestrator:
            try:
                # Analyze if multi-step execution is needed
                analysis = await self.orchestrator._analyze_request(user_input)

                if analysis["needs_multi_step"]:
                    if self.verbose:
                        self.print_status(f"Multi-step execution detected: {analysis['request_type']}", "info")

                        # Display query analysis if available
                        if "chunks" in analysis and analysis["chunks"]:
                            self._display_query_analysis(analysis)

                    # Execute multi-step workflow
                    result = await self.orchestrator.execute_multi_step(user_input)

                    # Display multi-step results
                    await self._display_multi_step_result(result)
                    return

            except Exception as e:
                if self.verbose:
                    self.print_status(f"Multi-step analysis failed: {e}, using single-step", "warning")

        # Revolutionary Agent-Swarm: Use agent router for intelligent routing
        if self.agent_router:
            try:
                # Show agent recommendations if enabled
                if self.show_agent_info:
                    recommendations = self.agent_router.get_agent_recommendations(user_input)
                    if recommendations and self.verbose:
                        self._show_agent_recommendations(recommendations[:3])

                # Route the request
                routing_decision = await self.agent_router.route_request(user_input)

                if self.show_agent_info:
                    self._show_routing_decision(routing_decision)

            except Exception as e:
                if self.verbose:
                    self.print_status(f"Agent routing failed: {e}, using fallback", "warning")

        # Set current task
        self.current_task = "Generating response"

        # Show thinking indicator
        if self.use_rich:
            with self.show_progress() as progress:
                if progress:
                    task = progress.add_task("🤖 Thinking...", total=None)

                try:
                    # Add to conversation history
                    self.conversation_history.append(Message(role="user", content=user_input))

                    # Try unified context engine first, then fallback to basic RAG
                    enhanced_prompt = user_input
                    context_used = "none"

                    # Try unified context engine first
                    if self.context_engine:
                        try:
                            # Detect intent from user input
                            intent = self._detect_intent(user_input)

                            if self.verbose:
                                self.print_status(f"Detected intent: {intent.value}", "info")

                            # Build enhanced query with chat history context
                            enhanced_query = self._build_context_aware_query(user_input)

                            # Get smart context with unified intelligence
                            smart_context = await self.context_engine.get_context(
                                query=enhanced_query,
                                intent=intent,
                                response_mode=self.response_mode,
                                context_depth=self.context_depth,
                                include_tests=self.include_tests,
                                include_docs=self.include_docs
                            )

                            if smart_context and len(smart_context) > 0:
                                # Build enhanced prompt with smart context
                                enhanced_prompt = self._build_smart_prompt(user_input, smart_context)
                                context_used = f"smart ({len(smart_context)} results)"

                                if self.verbose:
                                    self.print_status(f"Using {len(smart_context)} smart context results", "info")
                            else:
                                if self.verbose:
                                    self.print_status("Smart context returned no results, trying basic RAG", "warning")
                                # Don't raise exception, just fall through to basic RAG
                                smart_context = None

                        except Exception as smart_error:
                            if self.verbose:
                                self.print_status(f"Smart context failed: {smart_error}, falling back to basic RAG", "warning")
                            smart_context = None

                    # Fallback to basic RAG if smart context didn't work or returned no results
                    if context_used == "none" and self.agent and self.agent.dev_rag:
                        try:
                            context_results = await self.agent.dev_rag.search_code(user_input)
                            if context_results and len(context_results) > 0:
                                enhanced_prompt = self._build_basic_prompt(user_input, context_results)
                                context_used = f"basic RAG ({len(context_results)} results)"

                                if self.verbose:
                                    self.print_status(f"Using {len(context_results)} basic RAG results", "info")
                            else:
                                if self.verbose:
                                    self.print_status("Basic RAG returned no results", "warning")
                        except Exception as rag_error:
                            if self.verbose:
                                self.print_status(f"Basic RAG failed: {rag_error}", "error")

                    # If no context was found at all, inform user but continue
                    if context_used == "none" and self.verbose:
                        self.print_status("No context available, using query without context", "warning")

                    # Generate response with context
                    response = await self.llm_router.route_request(
                        messages=[Message(role="user", content=enhanced_prompt)],
                        task_type="general",
                        temperature=0.7
                    )

                    # Add context info to response metadata
                    if not hasattr(response, 'metadata'):
                        response.metadata = {}
                    response.metadata['context_used'] = context_used

                    if progress:
                        progress.update(task, completed=True)

                except Exception as e:
                    if progress:
                        progress.update(task, completed=True)
                    raise e
        else:
            self.print_status("Thinking...", "thinking")

            try:
                # Add to conversation history
                self.conversation_history.append(Message(role="user", content=user_input))

                # Use the same improved context logic as Rich UI
                enhanced_prompt = user_input
                context_used = "none"

                # Try unified context engine first
                if self.context_engine:
                    try:
                        # Detect intent from user input
                        intent = self._detect_intent(user_input)

                        # Get smart context with unified intelligence
                        smart_context = await self.context_engine.get_context(
                            query=user_input,
                            intent=intent,
                            response_mode=self.response_mode,
                            context_depth=self.context_depth,
                            include_tests=self.include_tests,
                            include_docs=self.include_docs
                        )

                        if smart_context and len(smart_context) > 0:
                            # Build enhanced prompt with smart context
                            enhanced_prompt = self._build_smart_prompt(user_input, smart_context)
                            context_used = f"smart ({len(smart_context)} results)"

                            # Add @ command context with smart priority weighting
                            if self.context_manager:
                                has_at_commands = '@' in user_input

                                if has_at_commands and self.context_manager.context_items:
                                    # High priority for explicit @ commands
                                    file_context = self.context_manager.get_context_for_llm(priority_level="high")
                                    enhanced_prompt = f"{enhanced_prompt}\n\n{file_context}"
                                    context_used += f" + PRIMARY files ({len(self.context_manager.context_items)})"
                                elif self.context_manager.should_include_persistent_context(user_input):
                                    # Lower priority for persistent context
                                    persistent_context = self.context_manager._format_persistent_context()
                                    if persistent_context:
                                        enhanced_prompt = f"{enhanced_prompt}\n\n{persistent_context}"
                                        context_used += f" + reference files ({len(self.context_manager.persistent_context)})"

                            # Add CLI commands context for better agent awareness
                            if self.commands_indexer:
                                cli_context = self.commands_indexer.get_commands_for_rag()
                                enhanced_prompt = f"{enhanced_prompt}\n\n=== AVAILABLE CLI COMMANDS ===\n{cli_context[:2000]}..."  # Limit size
                                context_used += " + commands"
                        else:
                            # Don't raise exception, just fall through to basic RAG
                            smart_context = None

                    except Exception:
                        # Smart context failed, fall through to basic RAG
                        smart_context = None

                # Fallback to basic RAG if smart context didn't work or returned no results
                if context_used == "none" and self.agent and self.agent.dev_rag:
                    try:
                        context_results = await self.agent.dev_rag.search_code(user_input)
                        if context_results and len(context_results) > 0:
                            enhanced_prompt = self._build_basic_prompt(user_input, context_results)
                            context_used = f"basic RAG ({len(context_results)} results)"
                    except Exception:
                        pass

                # Generate response with context
                response = await self.llm_router.route_request(
                    messages=[Message(role="user", content=enhanced_prompt)],
                    task_type="general",
                    temperature=0.7
                )

                # Add context info to response metadata
                if not hasattr(response, 'metadata'):
                    response.metadata = {}
                response.metadata['context_used'] = context_used

            except Exception as e:
                self.print_status(f"AI response failed: {e}", "error")
                self.current_task = None
                return

        # Clear current task
        self.current_task = None

        try:
            # Display AI response with Rich formatting
            if self.use_rich:
                # Check if response contains code
                if "```" in response.content:
                    # Split content by code blocks
                    parts = response.content.split("```")

                    ai_content = Text()
                    for i, part in enumerate(parts):
                        if i % 2 == 0:
                            # Regular text
                            ai_content.append(part)
                        else:
                            # Code block
                            lines = part.split('\n')
                            if lines and lines[0].strip():
                                # First line might be language
                                language = lines[0].strip()
                                code = '\n'.join(lines[1:])
                            else:
                                language = "python"
                                code = part

                            ai_content.append(f"\n[Code: {language}]\n", style="dim yellow")
                            ai_content.append(code, style="cyan")
                            ai_content.append("\n", style="dim")

                    ai_panel = Panel(
                        ai_content,
                        title="🤖 Assistant",
                        border_style="cyan",
                        padding=(0, 1)
                    )
                else:
                    # Regular text response
                    ai_panel = Panel(
                        Text(response.content, style="white"),
                        title="🤖 Assistant",
                        border_style="cyan",
                        padding=(0, 1)
                    )

                self.console.print(ai_panel)

                # Show metadata
                metadata_text = Text()
                metadata_text.append(f"Model: {response.metadata.get('llm_used', 'unknown')}", style="dim")
                if 'cost' in response.metadata:
                    metadata_text.append(f" | Cost: ${response.metadata['cost']:.4f}", style="dim green")
                if 'context_used' in response.metadata:
                    metadata_text.append(f" | Context: {response.metadata['context_used']}", style="dim cyan")

                self.console.print(metadata_text)
                self.console.print()
            else:
                # Fallback formatting
                print(f"🤖 {response.content}")
                metadata_parts = [f"Model: {response.metadata.get('llm_used', 'unknown')}"]
                if 'cost' in response.metadata:
                    metadata_parts.append(f"Cost: ${response.metadata['cost']:.4f}")
                if 'context_used' in response.metadata:
                    metadata_parts.append(f"Context: {response.metadata['context_used']}")
                print(f"   {' | '.join(metadata_parts)}")

            # Add to history
            self.conversation_history.append(Message(role="assistant", content=response.content))

        except Exception as e:
            self.print_status(f"AI response failed: {e}", "error")

    def _detect_intent(self, user_input: str) -> QueryIntent:
        """Detect user intent from input text."""
        user_input_lower = user_input.lower()

        # Debug intent keywords
        debug_keywords = ['error', 'bug', 'fix', 'debug', 'exception', 'crash', 'fail', 'broken']
        if any(keyword in user_input_lower for keyword in debug_keywords):
            return QueryIntent.DEBUG

        # Feature intent keywords
        feature_keywords = ['implement', 'create', 'build', 'add', 'develop', 'generate', 'make']
        if any(keyword in user_input_lower for keyword in feature_keywords):
            return QueryIntent.FEATURE

        # Understand intent keywords
        understand_keywords = ['explain', 'understand', 'what', 'how', 'why', 'overview', 'architecture']
        if any(keyword in user_input_lower for keyword in understand_keywords):
            return QueryIntent.UNDERSTAND

        # Refactor intent keywords
        refactor_keywords = ['refactor', 'improve', 'optimize', 'clean', 'restructure', 'reorganize']
        if any(keyword in user_input_lower for keyword in refactor_keywords):
            return QueryIntent.REFACTOR

        # Document intent keywords
        document_keywords = ['document', 'docs', 'documentation', 'example', 'usage', 'guide']
        if any(keyword in user_input_lower for keyword in document_keywords):
            return QueryIntent.DOCUMENT

        # Default to general
        return QueryIntent.GENERAL

    def _build_smart_prompt(self, user_input: str, smart_context) -> str:
        """Build enhanced prompt with smart context."""
        if not smart_context:
            return user_input

        context_info = "\n\nRelevant code context from the project:\n"

        for i, result in enumerate(smart_context, 1):
            file_path = result.file_path
            priority = result.priority.value
            context_type = result.context_type
            summary = result.summary

            context_info += f"{i}. [{priority.upper()}] {file_path} ({context_type}):\n"
            context_info += f"   Summary: {summary}\n"

            if result.key_concepts:
                context_info += f"   Key concepts: {', '.join(result.key_concepts[:5])}\n"

            # Include content preview
            content_preview = result.content[:200].replace('\n', ' ')
            context_info += f"   Content: {content_preview}...\n\n"

        return f"{user_input}{context_info}"

    def _build_basic_prompt(self, user_input: str, context_results) -> str:
        """Build basic prompt with RAG context (fallback)."""
        if not context_results:
            return user_input

        context_info = "\n\nRelevant code context from the project:\n"

        for i, result in enumerate(context_results[:3], 1):
            file_path = result.metadata.get('file_path', 'unknown')
            content_preview = result.content[:300].replace('\n', ' ')
            context_info += f"{i}. {file_path}: {content_preview}...\n"

        return f"{user_input}{context_info}"

    def _build_context_aware_query(self, user_input: str) -> str:
        """Build a context-aware query that includes relevant chat history."""
        # Get recent conversation history for context
        recent_messages = self.conversation_history[-6:]  # Last 6 messages (3 exchanges)

        if not recent_messages:
            return user_input

        # Build context from recent conversation
        context_parts = []

        for msg in recent_messages:
            if hasattr(msg, 'role') and hasattr(msg, 'content'):
                if msg.role == "user":
                    context_parts.append(f"Previous question: {msg.content}")
                elif msg.role == "assistant":
                    # Extract key information from assistant responses
                    content = msg.content[:200]  # First 200 chars
                    if "search_code" in content.lower() or "async def" in content.lower():
                        context_parts.append(f"Previous context: {content}")

        if context_parts:
            # Combine context with current query
            context_str = " | ".join(context_parts[-2:])  # Last 2 context items
            enhanced_query = f"{user_input}\n\nConversation context: {context_str}"

            if self.verbose:
                self.print_status(f"Enhanced query with conversation context", "info")

            return enhanced_query

        return user_input

    def _initialize_advanced_features(self):
        """Initialize advanced CLI features."""
        try:
            # Initialize context manager for @ commands
            from .context_manager import ContextManager
            self.context_manager = ContextManager(self.project_path)

            if self.verbose:
                print(f"✅ Context manager initialized with path: {self.project_path}")

            # Initialize autocompleter
            from .autocomplete import AdvancedCompleter
            self.autocompleter = AdvancedCompleter(self)

            # Initialize commands indexer for RAG
            from ..context.cli_commands_indexer import CLICommandsIndexer
            self.commands_indexer = CLICommandsIndexer()
            self.commands_indexer.index_shell_commands(self)

            # Initialize multi-step orchestrator
            from ..core.orchestrator import MultiStepOrchestrator
            self.orchestrator = MultiStepOrchestrator(self)

            if self.verbose:
                print("🚀 Advanced features initialized:")
                print("   ✅ @ commands for file context")
                print("   ✅ Intelligent autocomplete")
                print("   ✅ CLI commands in RAG context")
                print("   ✅ Multi-step orchestrator")

        except ImportError as e:
            if self.verbose:
                print(f"⚠️  Some advanced features not available: {e}")
        except Exception as e:
            if self.verbose:
                print(f"❌ Error initializing advanced features: {e}")
                import traceback
                traceback.print_exc()

    # Agent routing callbacks and helpers
    async def _on_agent_routing(self, intent, routing_decision):
        """Callback for agent routing decisions."""
        if self.verbose:
            self.print_status(f"Intent: {intent.intent_type.value} -> Agent: {routing_decision.selected_agent}", "info")

    def _show_agent_recommendations(self, recommendations):
        """Show agent recommendations to user."""
        if not recommendations:
            return

        if self.use_rich:
            from rich.table import Table

            table = Table(title="🤖 Agent Recommendations")
            table.add_column("Agent", style="cyan")
            table.add_column("Score", style="green")
            table.add_column("Reasoning", style="dim")

            for rec in recommendations:
                score_str = f"{rec['score']:.1%}"
                table.add_row(rec['agent'], score_str, rec['reasoning'])

            self.console.print(table)
        else:
            print("🤖 Agent Recommendations:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec['agent']} ({rec['score']:.1%}) - {rec['reasoning']}")

    def _show_routing_decision(self, routing_decision):
        """Show routing decision to user."""
        if self.use_rich:
            decision_text = f"🎯 Selected: [bold cyan]{routing_decision.selected_agent}[/bold cyan] "
            decision_text += f"([green]{routing_decision.confidence:.1%}[/green])"

            if routing_decision.selected_tools:
                tools_str = ", ".join(routing_decision.selected_tools[:3])
                decision_text += f"\n🛠️  Tools: {tools_str}"

            self.console.print(decision_text)
        else:
            print(f"🎯 Selected: {routing_decision.selected_agent} ({routing_decision.confidence:.1%})")
            if routing_decision.selected_tools:
                tools_str = ", ".join(routing_decision.selected_tools[:3])
                print(f"🛠️  Tools: {tools_str}")

    async def _display_multi_step_result(self, result: Dict[str, Any]):
        """Display results from multi-step execution."""
        if self.use_rich:
            from rich.panel import Panel
            from rich.table import Table

            # Display the actual response content first if available
            if result.get("response") and result["response"].get("content"):
                response_content = result["response"]["content"]

                # Display AI response with Rich formatting
                if "```" in response_content:
                    # Split content by code blocks
                    parts = response_content.split("```")

                    ai_content = Text()
                    for i, part in enumerate(parts):
                        if i % 2 == 0:
                            # Regular text
                            ai_content.append(part)
                        else:
                            # Code block
                            lines = part.split('\n')
                            if lines and lines[0].strip():
                                # First line might be language
                                language = lines[0].strip()
                                code = '\n'.join(lines[1:])
                            else:
                                language = "python"
                                code = part

                            ai_content.append(f"\n[Code: {language}]\n", style="dim yellow")
                            ai_content.append(code, style="cyan")
                            ai_content.append("\n", style="dim")

                    ai_panel = Panel(
                        ai_content,
                        title="🤖 Assistant",
                        border_style="cyan",
                        padding=(0, 1)
                    )
                else:
                    # Regular text response
                    ai_panel = Panel(
                        Text(response_content, style="white"),
                        title="🤖 Assistant",
                        border_style="cyan",
                        padding=(0, 1)
                    )

                self.console.print(ai_panel)

            # Create summary panel
            if result["success"]:
                title = f"✅ Multi-Step Execution Complete"
                style = "green"
            else:
                title = f"⚠️  Multi-Step Execution Partial"
                style = "yellow"

            summary = f"Goal: {result['goal']}\n"
            summary += f"Steps: {result['steps_completed']}/{result['total_steps']} completed\n"
            summary += f"Type: {result['type']}"

            panel = Panel(summary, title=title, border_style=style)
            self.console.print(panel)

            # Create steps table
            if self.verbose and result.get("steps"):
                table = Table(title="Execution Steps")
                table.add_column("Step", style="cyan")
                table.add_column("Status", style="green")
                table.add_column("Description", style="dim")

                for step in result["steps"]:
                    status_icon = {
                        "completed": "✅",
                        "failed": "❌",
                        "running": "🔄",
                        "pending": "⏳"
                    }.get(step["status"], "❓")

                    table.add_row(
                        step["step_id"],
                        f"{status_icon} {step['status']}",
                        step["description"]
                    )

                self.console.print(table)
        else:
            # Plain text display
            if result["success"]:
                print(f"✅ Multi-Step Execution Complete")
            else:
                print(f"⚠️  Multi-Step Execution Partial")

            print(f"Goal: {result['goal']}")
            print(f"Steps: {result['steps_completed']}/{result['total_steps']} completed")

            if self.verbose and result.get("steps"):
                print("\nExecution Steps:")
                for step in result["steps"]:
                    status_icon = {
                        "completed": "✅",
                        "failed": "❌",
                        "running": "🔄",
                        "pending": "⏳"
                    }.get(step["status"], "❓")

                    print(f"  {status_icon} {step['step_id']}: {step['description']}")
                    if step.get("error"):
                        print(f"    ❌ Error: {step['error']}")

    def _display_query_analysis(self, analysis: Dict[str, Any]):
        """Display query chunking and analysis information."""
        if self.use_rich:
            from rich.panel import Panel
            from rich.table import Table

            # Create analysis summary
            summary = f"Complexity: {analysis['complexity']}\n"
            summary += f"Estimated Steps: {analysis['estimated_steps']}\n"
            summary += f"Request Type: {analysis['request_type']}"

            if analysis.get('mentioned_files'):
                summary += f"\nFiles: {', '.join(analysis['mentioned_files'][:3])}"
                if len(analysis['mentioned_files']) > 3:
                    summary += f" +{len(analysis['mentioned_files']) - 3} more"

            panel = Panel(summary, title="🧠 Query Analysis", border_style="blue")
            self.console.print(panel)

            # Create chunks table
            if analysis.get("chunks"):
                table = Table(title="Query Chunks")
                table.add_column("ID", style="cyan", width=8)
                table.add_column("Type", style="green", width=10)
                table.add_column("Priority", style="yellow", width=8)
                table.add_column("Text", style="dim")

                for chunk in analysis["chunks"]:
                    # Truncate long text
                    text = chunk["text"]
                    if len(text) > 50:
                        text = text[:47] + "..."

                    table.add_row(
                        chunk["id"],
                        chunk["type"],
                        chunk["priority"],
                        text
                    )

                self.console.print(table)
        else:
            # Plain text display
            print(f"🧠 Query Analysis:")
            print(f"   Complexity: {analysis['complexity']}")
            print(f"   Estimated Steps: {analysis['estimated_steps']}")
            print(f"   Request Type: {analysis['request_type']}")

            if analysis.get('mentioned_files'):
                files_str = ', '.join(analysis['mentioned_files'][:3])
                if len(analysis['mentioned_files']) > 3:
                    files_str += f" +{len(analysis['mentioned_files']) - 3} more"
                print(f"   Files: {files_str}")

            if analysis.get("chunks"):
                print(f"   Query Chunks ({len(analysis['chunks'])}):")
                for chunk in analysis["chunks"]:
                    text = chunk["text"]
                    if len(text) > 40:
                        text = text[:37] + "..."
                    print(f"     {chunk['id']}: {text} ({chunk['type']}, {chunk['priority']})")

    # Command implementations
    async def cmd_version(self, args):
        """Show version information."""
        from agent_swarm import __version__

        print(f"\n🤖 Agent Swarm v{__version__}")
        print("=" * 40)
        print("🚀 Multi-LLM Agent Development Framework")
        print("🔗 https://github.com/your-org/agent-swarm")
        print()
        print("✨ Features:")
        print("   • Smart context-aware AI responses")
        print("   • Multi-LLM routing and orchestration")
        print("   • Interactive development shell")
        print("   • Project indexing and analysis")
        print("   • Extensible plugin architecture")
        print()
        print("💡 Use '/help' for available commands")
        print("=" * 40 + "\n")

    async def cmd_help(self, args):
        """Show help information."""
        print("\n📚 Agent Swarm Shell Commands:")
        print("="*40)
        print("🔧 Setup Commands:")
        print("  /project <path>     - Set project directory")
        print("  /model <name>       - Switch LLM model")
        print("  /models             - List available models")
        print()
        print("🔍 Analysis Commands:")
        print("  /search <query>     - Search codebase")
        print("  /analyze            - Analyze project")
        print("  /stats              - Show project stats")
        print("  /files [depth]      - Show project file tree")
        print("  /tree [depth]       - Alias for /files")
        print("  /context <file>     - Show file context")
        print("  /index              - Index project files for better context")
        print("  /update             - Update Agent Swarm to latest version")
        print("  /config             - Configuration management")
        print()
        print("✏️  File Editing Commands:")
        print("  /edit <file>        - Advanced file editing with diff preview")
        print("  /smart-edit <file>  - AI-powered smart editing")
        print("  /diff <file|id>     - Show file differences or change diff")
        print("  /revert <change_id> - Revert specific change")
        print()
        print("🚀 Development Commands:")
        print("  /implement <desc>   - Implement feature")
        print("  /fix <description>  - Fix bug")
        print("  /review <file>      - Review code")
        print("  /explain <code>     - Explain code")
        print("  /projectinfo        - Show project information")
        print("  /install            - Install dependencies")
        print("  /run <command>      - Run project command")
        print()
        print("📁 File System Commands:")
        print("  /find <pattern>     - Find files matching pattern")
        print("  /grep <pattern>     - Search for pattern in files")
        print("  /info <path>        - Get file/directory information")
        print()
        print("🌐 Network Commands:")
        print("  /ping <host>        - Ping a host")
        print("  /port <host> <port> - Check if port is open")
        print("  /services           - Check local services")
        print()
        print("💻 System Commands:")
        print("  /system             - Show system information")
        print()
        print("🔺 Enhanced Intent Processing (REVOLUTIONARY):")
        print("  /intent-analytics   - Show intent processing analytics")
        print("  /intent-test [n]    - Test intent processing with samples")
        print()
        print("💬 Chat Commands:")
        print("  /history            - Show conversation")
        print("  /clear              - Clear conversation")
        print("  /version            - Show version information")
        print("  /help               - Show this help")
        print("  /exit, /quit        - Exit shell")
        print()
        print("💡 You can also just type questions directly!")
        print("🔺 Enhanced with mathematical intent processing algorithms!")
        print("="*40 + "\n")

    async def cmd_exit(self, args):
        """Exit the shell."""
        print("👋 Goodbye!")
        if self.agent and hasattr(self.agent, 'llm_router'):
            # Clean up LLM connections
            for llm in self.agent.llm_router.llms.values():
                if hasattr(llm, 'close'):
                    await llm.close()
        sys.exit(0)

    async def cmd_clear(self, args):
        """Clear conversation history."""
        self.conversation_history.clear()
        print("🧹 Conversation history cleared")

    async def cmd_history(self, args):
        """Show conversation history."""
        if not self.conversation_history:
            print("📭 No conversation history")
            return

        print("\n📜 Conversation History:")
        print("="*40)
        for i, msg in enumerate(self.conversation_history[-10:], 1):
            role_icon = "👤" if msg.role == "user" else "🤖"
            content_preview = msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
            print(f"{i}. {role_icon} {content_preview}")
        print("="*40 + "\n")

    async def cmd_project(self, args):
        """Set or show project directory."""
        if not args:
            print(f"📁 Current project: {self.project_path}")
            return

        new_path = Path(args[0]).resolve()
        if not new_path.exists():
            print(f"❌ Path does not exist: {new_path}")
            return

        print(f"🔄 Switching to project: {new_path}")
        await self.initialize(str(new_path))

    async def cmd_model(self, args):
        """Switch LLM model."""
        if not args:
            print(f"🤖 Current model: {self.current_model}")
            return

        new_model = args[0]

        # Test if model is available
        test_llm = create_ollama_llm(new_model)
        if not await test_llm.is_available():
            print(f"❌ Model not available: {new_model}")
            print("💡 Use '/models' to see available models")
            await test_llm.close()
            return
        await test_llm.close()

        # Switch model
        self.current_model = new_model
        print(f"✅ Switched to model: {new_model}")

        # Reinitialize with new model
        if self.project_path:
            await self.initialize(str(self.project_path))

    async def cmd_list_models(self, args):
        """List available Ollama models."""
        test_llm = create_ollama_llm("dummy")
        models = await test_llm.list_models()
        await test_llm.close()

        if models:
            print("\n🤖 Available Models:")
            print("="*30)
            for i, model in enumerate(models, 1):
                current = " (current)" if model == self.current_model else ""
                print(f"  {i}. {model}{current}")
            print("="*30 + "\n")
        else:
            print("❌ No models available")

    async def cmd_search(self, args):
        """Search the codebase."""
        if not args:
            print("❌ Usage: /search <query>")
            return

        if not self.agent or not self.agent.dev_rag:
            print("❌ RAG not available")
            return

        query = " ".join(args)
        print(f"🔍 Searching for: '{query}'")

        results = await self.agent.dev_rag.search_code(query)

        if results:
            print(f"\n📊 Found {len(results)} matches:")
            print("="*40)
            for i, result in enumerate(results[:5], 1):
                file_path = result.metadata.get('file_path', 'unknown')
                score = result.score
                content_preview = result.content.replace('\n', ' ')[:80]
                print(f"{i}. {Path(file_path).name} (score: {score:.2f})")
                print(f"   {content_preview}...")
                print()
        else:
            print("📭 No matches found")

    async def cmd_analyze(self, args):
        """Analyze the current project."""
        if not self.agent:
            print("❌ Agent not initialized")
            return

        print("📊 Analyzing project...")

        if self.agent.dev_rag:
            stats = await self.agent.dev_rag.get_project_stats()

            print("\n📈 Project Analysis:")
            print("="*30)
            print(f"Files indexed: {stats['total_files_indexed']}")
            print(f"Languages: {list(stats.get('languages', {}).keys())}")
            print(f"File types: {dict(list(stats.get('file_types', {}).items())[:5])}")
            print("="*30 + "\n")
        else:
            print("❌ RAG not available for analysis")

    async def cmd_stats(self, args):
        """Show detailed project statistics."""
        await self.cmd_analyze(args)

    async def cmd_index(self, args):
        """Index project files for better context retrieval."""
        if not self.project_path:
            print("❌ No project set. Use '/project <path>' first.")
            return

        print("🔄 Indexing project files...")

        try:
            # Index both agent's RAG and context engine's RAG
            indexed_count = 0

            if self.agent and self.agent.dev_rag:
                print("📚 Indexing agent's RAG system...")
                await self.agent.dev_rag.index_project()
                agent_stats = await self.agent.dev_rag.get_project_stats()
                agent_count = agent_stats.get('total_files_indexed', 0)
                print(f"   ✅ Agent RAG: {agent_count} files indexed")
                indexed_count += agent_count

            if self.context_engine and self.context_engine.dev_rag:
                print("🧠 Indexing context engine's RAG system...")
                await self.context_engine.dev_rag.index_project()
                context_stats = await self.context_engine.dev_rag.get_project_stats()
                context_count = context_stats.get('total_files_indexed', 0)
                print(f"   ✅ Context RAG: {context_count} files indexed")
                indexed_count += context_count

            if indexed_count > 0:
                print(f"🎉 Indexing complete! Total files processed: {indexed_count}")
                print("💡 You can now use smart context retrieval for better AI responses.")
            else:
                print("⚠️ No RAG systems available to index.")

        except Exception as e:
            print(f"❌ Indexing failed: {e}")
            if self.verbose:
                import traceback
                traceback.print_exc()

    async def cmd_update(self, args):
        """Update Agent Swarm to the latest version."""
        import subprocess
        import sys
        from agent_swarm import __version__

        print(f"🔄 Updating Agent Swarm from v{__version__}...")
        print("=" * 50)

        try:
            # Check if we're in development mode
            import agent_swarm
            install_path = str(Path(agent_swarm.__file__).parent.parent.parent)
            is_dev_install = install_path.endswith('src')

            if is_dev_install:
                print("🔧 Development installation detected")
                print("💡 To update a development installation:")
                print("   cd /path/to/agent-swarm")
                print("   git pull origin main")
                print("   pip install -e .")
                return

            # Try to update via pip
            print("📦 Updating via pip...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "--upgrade", "agent-swarm"
            ], capture_output=True, text=True)

            if result.returncode == 0:
                print("✅ Update successful!")
                print("🔄 Please restart the shell to use the new version.")
                print("💡 Use '/version' to check the current version after restart.")
            else:
                print("❌ Update failed!")
                print(f"Error: {result.stderr}")
                print("\n💡 Manual update options:")
                print("   pip install --upgrade agent-swarm")
                print("   pip install --upgrade --force-reinstall agent-swarm")

        except Exception as e:
            print(f"❌ Update failed: {e}")
            print("\n💡 Manual update:")
            print("   pip install --upgrade agent-swarm")

    async def cmd_config(self, args):
        """Configuration management commands."""
        from agent_swarm.utils.config import (
            load_config, save_config, get_config_file, create_example_config,
            validate_api_key, get_api_key, ProviderType
        )

        if not args:
            # Show current configuration
            try:
                config = load_config()
                print("🔧 Current Agent Swarm Configuration")
                print("=" * 50)
                print(f"📍 Config file: {get_config_file()}")
                print(f"📦 Version: {config.version}")
                print(f"🤖 Default provider: {config.default_provider}")
                print(f"🎯 Default model: {config.default_model}")
                print(f"🔄 Fallback model: {config.fallback_model}")
                print(f"🌡️  Temperature: {config.default_temperature}")
                print(f"📊 Max tokens: {config.default_max_tokens}")
                print(f"⚡ Max concurrent: {config.max_concurrent_requests}")

                print("\n🤖 Available Models:")
                for name, model in config.models.items():
                    status = "✅" if model.enabled else "❌"
                    api_key_status = ""
                    if model.api_key_env:
                        api_key = get_api_key(model.provider, model)
                        if api_key and validate_api_key(model.provider, api_key):
                            api_key_status = " 🔑"
                        else:
                            api_key_status = " ❌🔑"
                    print(f"  {status} {name} ({model.provider}){api_key_status}")

                print("\n💡 Commands:")
                print("  /config set <key> <value>  - Update configuration")
                print("  /config reset              - Reset to defaults")
                print("  /config export             - Export configuration")
                print("  /config wizard             - Interactive setup")
                print("  /config example            - Create example files")

            except Exception as e:
                print(f"❌ Error loading configuration: {e}")

        elif args[0] == "set" and len(args) >= 3:
            # Set configuration value
            key = args[1]
            value = " ".join(args[2:])

            try:
                config = load_config()

                # Handle nested keys like "rag.chunk_size"
                if "." in key:
                    parts = key.split(".")
                    if parts[0] == "rag" and hasattr(config.rag, parts[1]):
                        setattr(config.rag, parts[1], value)
                    elif parts[0] == "cli" and hasattr(config.cli, parts[1]):
                        setattr(config.cli, parts[1], value)
                    else:
                        print(f"❌ Unknown configuration key: {key}")
                        return
                else:
                    if hasattr(config, key):
                        setattr(config, key, value)
                    else:
                        print(f"❌ Unknown configuration key: {key}")
                        return

                save_config(config)
                print(f"✅ Configuration updated: {key} = {value}")

            except Exception as e:
                print(f"❌ Error updating configuration: {e}")

        elif args[0] == "reset":
            # Reset to defaults
            try:
                from agent_swarm.utils.config import get_default_config
                config = get_default_config()
                save_config(config)
                print("✅ Configuration reset to defaults")

            except Exception as e:
                print(f"❌ Error resetting configuration: {e}")

        elif args[0] == "export":
            # Export configuration
            try:
                config = load_config()
                config_file = get_config_file()
                print(f"📄 Configuration exported to: {config_file}")
                print("\n📋 Current configuration:")
                with open(config_file, 'r') as f:
                    print(f.read())

            except Exception as e:
                print(f"❌ Error exporting configuration: {e}")

        elif args[0] == "wizard":
            # Interactive configuration wizard
            await self._config_wizard()

        elif args[0] == "example":
            # Create example files
            try:
                create_example_config()

            except Exception as e:
                print(f"❌ Error creating example files: {e}")

        else:
            print("❌ Unknown config command")
            print("💡 Usage: /config [set|reset|export|wizard|example]")

    async def _config_wizard(self):
        """Interactive configuration wizard."""
        from agent_swarm.utils.config import (
            load_config, save_config, validate_api_key, ProviderType, get_config_file
        )

        print("🧙 Agent Swarm Configuration Wizard")
        print("=" * 50)
        print("This wizard will help you configure Agent Swarm for cloud AI providers.")
        print()

        try:
            config = load_config()

            # Ask about DeepSeek API key
            print("🤖 DeepSeek Configuration (Recommended for coding)")
            print("DeepSeek offers excellent coding capabilities at competitive prices.")
            deepseek_key = input("Enter your DeepSeek API key (or press Enter to skip): ").strip()

            if deepseek_key:
                if validate_api_key(ProviderType.DEEPSEEK, deepseek_key):
                    print("✅ DeepSeek API key looks valid")
                    print("💡 Set environment variable: export DEEPSEEK_API_KEY=your_key")

                    # Ask if they want to use DeepSeek as default
                    use_deepseek = input("Use DeepSeek as default provider? (y/N): ").strip().lower()
                    if use_deepseek in ['y', 'yes']:
                        config.default_provider = ProviderType.DEEPSEEK
                        config.default_model = "deepseek-chat"
                        print("✅ DeepSeek set as default provider")
                else:
                    print("❌ DeepSeek API key format appears invalid")

            # Ask about other providers
            print("\n🌐 Other Cloud Providers")

            # OpenAI
            openai_key = input("Enter OpenAI API key (optional): ").strip()
            if openai_key and validate_api_key(ProviderType.OPENAI, openai_key):
                print("✅ OpenAI API key looks valid")
                print("💡 Set environment variable: export OPENAI_API_KEY=your_key")

            # Anthropic
            anthropic_key = input("Enter Anthropic API key (optional): ").strip()
            if anthropic_key and validate_api_key(ProviderType.ANTHROPIC, anthropic_key):
                print("✅ Anthropic API key looks valid")
                print("💡 Set environment variable: export ANTHROPIC_API_KEY=your_key")

            # Configuration preferences
            print("\n⚙️ Configuration Preferences")

            # Temperature
            temp_input = input(f"Temperature (0.0-1.0, current: {config.default_temperature}): ").strip()
            if temp_input:
                try:
                    temp = float(temp_input)
                    if 0.0 <= temp <= 1.0:
                        config.default_temperature = temp
                        print(f"✅ Temperature set to {temp}")
                    else:
                        print("❌ Temperature must be between 0.0 and 1.0")
                except ValueError:
                    print("❌ Invalid temperature value")

            # Max tokens
            tokens_input = input(f"Max tokens (current: {config.default_max_tokens}): ").strip()
            if tokens_input:
                try:
                    tokens = int(tokens_input)
                    if tokens > 0:
                        config.default_max_tokens = tokens
                        print(f"✅ Max tokens set to {tokens}")
                    else:
                        print("❌ Max tokens must be positive")
                except ValueError:
                    print("❌ Invalid max tokens value")

            # RAG settings
            print("\n🧠 RAG (Context) Settings")
            chunk_input = input(f"Chunk size (current: {config.rag.chunk_size}): ").strip()
            if chunk_input:
                try:
                    chunk_size = int(chunk_input)
                    if chunk_size > 0:
                        config.rag.chunk_size = chunk_size
                        print(f"✅ Chunk size set to {chunk_size}")
                except ValueError:
                    print("❌ Invalid chunk size")

            # Save configuration
            save_config(config)
            print("\n✅ Configuration saved successfully!")
            print(f"📍 Config file: {get_config_file()}")

            print("\n💡 Next steps:")
            print("1. Set your API keys as environment variables")
            print("2. Use '/config' to view your configuration")
            print("3. Try asking a coding question to test the setup")

        except Exception as e:
            print(f"❌ Configuration wizard failed: {e}")
            if self.verbose:
                import traceback
                traceback.print_exc()

    async def cmd_files(self, args):
        """List project files with enhanced tree view."""
        if not self.project_path:
            print("❌ No project set")
            return

        # Check for specific options
        show_all = "--all" in args if args else False
        max_depth = 3

        if args and args[0].isdigit():
            max_depth = int(args[0])

        print(f"📁 Project Structure: {self.project_path.name}")
        print("="*60)

        # Enhanced file tree with better formatting
        def print_tree(path, prefix="", max_depth=3, current_depth=0):
            if current_depth >= max_depth:
                return

            try:
                items = []
                for item in sorted(path.iterdir()):
                    # Skip hidden files and common ignore patterns
                    if item.name.startswith('.') and not show_all:
                        continue
                    if item.name in ['__pycache__', 'node_modules', 'venv', 'env', '.git'] and not show_all:
                        continue
                    items.append(item)

                for i, item in enumerate(items):
                    is_last = i == len(items) - 1
                    current_prefix = "└── " if is_last else "├── "

                    if item.is_dir():
                        print(f"{prefix}{current_prefix}📁 {item.name}/")
                        extension = "    " if is_last else "│   "
                        print_tree(item, prefix + extension, max_depth, current_depth + 1)
                    else:
                        # Add file type icons
                        icon = self._get_file_icon(item.name)
                        print(f"{prefix}{current_prefix}{icon} {item.name}")

            except PermissionError:
                print(f"{prefix}❌ Permission denied")

        print_tree(self.project_path, max_depth=max_depth)

        # Show summary
        if self.context_engine and self.context_engine.project_structure:
            structure = self.context_engine.project_structure
            print("\n" + "="*60)
            print("📊 Project Summary:")
            print(f"   Type: {structure.project_type.value}")
            print(f"   Language: {structure.main_language}")
            print(f"   Frameworks: {', '.join(structure.frameworks[:5])}{'...' if len(structure.frameworks) > 5 else ''}")
            print(f"   Core modules: {len(structure.core_modules)}")
            print(f"   Entry points: {', '.join(structure.entry_points)}")

        print("\n💡 Usage:")
        print("   /files          - Show tree (depth 3)")
        print("   /files 5        - Show tree (depth 5)")
        print("   /files --all    - Show hidden files")
        print("="*60 + "\n")

    def _get_file_icon(self, filename):
        """Get icon for file type."""
        ext = filename.lower().split('.')[-1] if '.' in filename else ''

        icons = {
            'py': '🐍',
            'js': '📜',
            'ts': '📘',
            'html': '🌐',
            'css': '🎨',
            'md': '📝',
            'txt': '📄',
            'json': '📋',
            'yaml': '⚙️',
            'yml': '⚙️',
            'toml': '⚙️',
            'cfg': '⚙️',
            'ini': '⚙️',
            'sh': '🔧',
            'bat': '🔧',
            'dockerfile': '🐳',
            'gitignore': '🚫',
            'requirements': '📦',
            'makefile': '🔨',
        }

        # Special filenames
        special_files = {
            'readme.md': '📖',
            'license': '📜',
            'changelog.md': '📅',
            'contributing.md': '🤝',
            'setup.py': '📦',
            'pyproject.toml': '📦',
            'package.json': '📦',
            'makefile': '🔨',
        }

        filename_lower = filename.lower()
        if filename_lower in special_files:
            return special_files[filename_lower]
        elif ext in icons:
            return icons[ext]
        else:
            return '📄'

    async def cmd_context(self, args):
        """Show context information and debug context engine."""
        if not args:
            # Show general context engine status
            print("🧠 Unified Context Engine Status:")
            print("="*50)

            if self.context_engine:
                # Get project analysis
                project_analysis = await self.context_engine.get_project_analysis()
                if project_analysis:
                    print(f"✅ Project analyzed: {project_analysis.project_type.value}")
                    print(f"   Language: {project_analysis.main_language}")
                    print(f"   Frameworks: {', '.join(project_analysis.frameworks[:5])}{'...' if len(project_analysis.frameworks) > 5 else ''}")
                    print(f"   Core modules: {len(project_analysis.core_modules)}")
                    print(f"   Confidence: {project_analysis.confidence_score:.2f}")
                    print(f"   Entry points: {', '.join(project_analysis.entry_points[:3])}")
                else:
                    print("❌ Project not analyzed")

                # Get system stats
                stats = await self.context_engine.get_stats()
                print(f"\n🔧 System Components:")
                components = stats.get('components', {})
                for component, enabled in components.items():
                    status = "✅" if enabled else "❌"
                    print(f"   {status} {component.replace('_', ' ').title()}")
            else:
                print("❌ Unified context engine not initialized")

            print(f"\n⚙️ Current Settings:")
            print(f"   Response mode: {self.response_mode.value}")
            print(f"   Context depth: {self.context_depth.value}")
            print(f"   Include tests: {self.include_tests}")
            print(f"   Include docs: {self.include_docs}")
            print(f"   Verbose: {self.verbose}")

            if self.agent and self.agent.dev_rag:
                stats = await self.agent.dev_rag.get_project_stats()
                print(f"\n📊 RAG System:")
                print(f"   Files indexed: {stats['total_files_indexed']}")
                print(f"   Total chunks: {stats.get('total_chunks', 'unknown')}")

            print(f"\n💡 Usage:")
            print(f"   /context <query>     - Test context for a query")
            print(f"   /context file <path> - Show context for a file")
            return

        if args[0] == "file" and len(args) > 1:
            # Show context for a specific file
            file_path = args[1]
            full_path = self.project_path / file_path

            if not full_path.exists():
                print(f"❌ File not found: {file_path}")
                return

            print(f"📄 Context for {file_path}:")
            print("="*40)
        else:
            # Test context for a query
            query = " ".join(args)
            print(f"🔍 Testing context for query: '{query}'")
            print("="*50)

            if self.context_engine:
                # Detect intent
                intent = self._detect_intent(query)
                print(f"🎯 Detected intent: {intent.value}")

                # Get smart context
                try:
                    smart_context = await self.context_engine.get_context(
                        query=query,
                        intent=intent,
                        response_mode=self.response_mode,
                        context_depth=self.context_depth,
                        include_tests=self.include_tests,
                        include_docs=self.include_docs
                    )

                    print(f"📊 Found {len(smart_context)} smart context results:")
                    print()

                    for i, result in enumerate(smart_context, 1):
                        print(f"{i}. [{result.priority.value.upper()}] {result.file_path}")
                        print(f"   Type: {result.context_type}")
                        print(f"   Relevance: {result.relevance_score:.2f}")
                        print(f"   Summary: {result.summary}")
                        if result.key_concepts:
                            print(f"   Concepts: {', '.join(result.key_concepts[:5])}")
                        print()

                except Exception as e:
                    print(f"❌ Smart context failed: {e}")

            elif self.agent and self.agent.dev_rag:
                # Fallback to basic RAG
                print("⚠️ Using basic RAG (enhanced context not available)")
                try:
                    context_results = await self.agent.dev_rag.search_code(query)
                    print(f"📊 Found {len(context_results)} basic RAG results:")
                    print()

                    for i, result in enumerate(context_results[:5], 1):
                        file_path = result.metadata.get('file_path', 'unknown')
                        print(f"{i}. {file_path}")
                        print(f"   Content preview: {result.content[:100]}...")
                        print()

                except Exception as e:
                    print(f"❌ Basic RAG failed: {e}")
            else:
                print("❌ No context system available")

    async def cmd_implement(self, args):
        """Implement a feature."""
        if not args:
            print("❌ Usage: /implement <description>")
            return

        if not self.agent:
            print("❌ Agent not initialized")
            return

        description = " ".join(args)
        print(f"🚀 Implementing: {description}")
        print("⏳ This may take a moment...")

        try:
            # Use the agent to implement the feature
            session = await self.agent.implement_feature(
                feature_description=description,
                file_path=f"src/generated_feature_{len(self.conversation_history)}.py",
                test_required=True
            )

            print(f"\n📊 Implementation Results:")
            print(f"   Success: {'✅' if session.get('success', False) else '❌'}")
            print(f"   Files created: {len(session.get('files_created', []))}")

            for file_path in session.get('files_created', []):
                print(f"   📄 Created: {file_path}")

        except Exception as e:
            print(f"❌ Implementation failed: {e}")

    async def cmd_fix(self, args):
        """Fix a bug."""
        if not args:
            print("❌ Usage: /fix <bug_description>")
            return

        description = " ".join(args)
        print(f"🐛 Analyzing bug: {description}")

        # For now, just use chat to help with debugging
        await self.handle_chat(f"Help me fix this bug: {description}")

    async def cmd_review(self, args):
        """Review code."""
        if not args:
            print("❌ Usage: /review <file_path>")
            return

        file_path = args[0]
        print(f"👀 Reviewing: {file_path}")

        # For now, just use chat for code review
        await self.handle_chat(f"Please review the code in {file_path}")

    async def cmd_explain(self, args):
        """Explain code."""
        if not args:
            print("❌ Usage: /explain <code_or_concept>")
            return

        code_or_concept = " ".join(args)
        await self.handle_chat(f"Please explain: {code_or_concept}")

    # New CLI tool commands
    async def cmd_find(self, args):
        """Find files matching pattern."""
        if not args:
            print("❌ Usage: /find <pattern>")
            return

        pattern = args[0]
        print(f"🔍 Finding files matching: {pattern}")

        result = await self.fs_tools.find_files(pattern, str(self.project_path))
        if result["success"]:
            files = result["stdout"].strip().split('\n') if result["stdout"].strip() else []
            if files:
                print(f"📁 Found {len(files)} files:")
                for file in files[:20]:  # Show first 20 files
                    if file.strip():
                        print(f"  • {file.strip()}")
                if len(files) > 20:
                    print(f"  ... and {len(files) - 20} more files")
            else:
                print("📭 No files found")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")

    async def cmd_grep(self, args):
        """Search for pattern in files."""
        if not args:
            print("❌ Usage: /grep <pattern>")
            return

        pattern = args[0]
        print(f"🔍 Searching for pattern: {pattern}")

        result = await self.fs_tools.grep_in_files(pattern, str(self.project_path))
        if result["success"]:
            matches = result["stdout"].strip().split('\n') if result["stdout"].strip() else []
            if matches:
                print(f"📊 Found {len(matches)} matches:")
                for match in matches[:15]:  # Show first 15 matches
                    if match.strip():
                        print(f"  {match.strip()}")
                if len(matches) > 15:
                    print(f"  ... and {len(matches) - 15} more matches")
            else:
                print("📭 No matches found")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")

    async def cmd_info(self, args):
        """Get file/directory information."""
        if not args:
            print("❌ Usage: /info <path>")
            return

        path = args[0]
        print(f"ℹ️  Getting information for: {path}")

        result = await self.fs_tools.get_file_info(path)
        if result["success"]:
            print(f"📁 Path: {result['path']}")
            print(f"📏 Size: {result['size']} bytes")
            print(f"🔒 Permissions: {result['permissions']}")
            print(f"📂 Type: {'Directory' if result['is_directory'] else 'File'}")
            if result['is_symlink']:
                print("🔗 Symlink: Yes")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")

    async def cmd_edit(self, args):
        """Advanced file editing with diff preview and change tracking."""
        try:
            from .commands.professional_edit import ProfessionalEditCommands
            if not hasattr(self, '_professional_edit'):
                self._professional_edit = ProfessionalEditCommands(self)
            await self._professional_edit.cmd_edit(args)
        except ImportError as e:
            print("❌ Professional editing not available. Install editing dependencies:")
            print("   pip install agent-swarm[editing]")
            if self.verbose:
                print(f"   Import error: {e}")
        except Exception as e:
            print(f"❌ Edit command failed: {e}")
            if self.verbose:
                import traceback
                traceback.print_exc()

    async def cmd_smart_edit(self, args):
        """AI-powered smart file editing."""
        try:
            from .commands.professional_edit import ProfessionalEditCommands
            if not hasattr(self, '_professional_edit'):
                self._professional_edit = ProfessionalEditCommands(self)
            # Use the same command handler but with smart editing flag
            await self._professional_edit.cmd_edit(["smart"] + args)
        except ImportError as e:
            print("❌ Smart editing not available. Install editing dependencies:")
            print("   pip install agent-swarm[editing]")
            if self.verbose:
                print(f"   Import error: {e}")
        except Exception as e:
            print(f"❌ Smart edit command failed: {e}")
            if self.verbose:
                import traceback
                traceback.print_exc()

    async def cmd_diff(self, args):
        """Show file differences and changes."""
        try:
            from .commands.professional_edit import ProfessionalEditCommands
            if not hasattr(self, '_professional_edit'):
                self._professional_edit = ProfessionalEditCommands(self)
            await self._professional_edit.cmd_edit(["diff"] + args)
        except ImportError as e:
            print("❌ Diff functionality not available. Install editing dependencies:")
            print("   pip install agent-swarm[editing]")
            if self.verbose:
                print(f"   Import error: {e}")
        except Exception as e:
            print(f"❌ Diff command failed: {e}")
            if self.verbose:
                import traceback
                traceback.print_exc()

    async def cmd_revert(self, args):
        """Revert file changes."""
        try:
            from .commands.professional_edit import ProfessionalEditCommands
            if not hasattr(self, '_professional_edit'):
                self._professional_edit = ProfessionalEditCommands(self)
            await self._professional_edit.cmd_edit(["revert"] + args)
        except ImportError as e:
            print("❌ Revert functionality not available. Install editing dependencies:")
            print("   pip install agent-swarm[editing]")
            if self.verbose:
                print(f"   Import error: {e}")
        except Exception as e:
            print(f"❌ Revert command failed: {e}")
            if self.verbose:
                import traceback
                traceback.print_exc()

    async def cmd_project_info(self, args):
        """Show project information."""
        print("📁 Analyzing project...")

        result = await self.dev_tools.get_project_info(str(self.project_path))
        if result["success"]:
            print(f"📂 Directory: {result['directory']}")
            print(f"🏷️  Project types: {', '.join(result['project_types']) or 'Unknown'}")

            if result.get("python"):
                python_info = result["python"]
                print(f"🐍 Python:")
                for key, value in python_info.items():
                    print(f"   {key}: {value}")

            if result.get("node"):
                node_info = result["node"]
                print(f"📦 Node.js:")
                for key, value in node_info.items():
                    print(f"   {key}: {value}")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")

    async def cmd_ping(self, args):
        """Ping a host."""
        if not args:
            print("❌ Usage: /ping <host>")
            return

        host = args[0]
        print(f"🌐 Pinging {host}...")

        result = await self.network_tools.ping_host(host, count=3)
        if result["success"]:
            print("✅ Ping successful")
            print(result["stdout"])
        else:
            print(f"❌ Ping failed: {result.get('error', 'Unknown error')}")

    async def cmd_services(self, args):
        """Check local development services."""
        print("🔧 Checking local development services...")

        result = await self.network_tools.check_local_services()
        if result["success"]:
            available = result["available_services"]
            total = result["services_checked"]
            print(f"📊 Found {len(available)} of {total} services:")

            for service_result in result["results"]:
                status = "✅" if service_result["available"] else "❌"
                print(f"  {status} {service_result['name']} ({service_result['host']}:{service_result['port']})")
        else:
            print(f"❌ Error checking services: {result.get('error', 'Unknown error')}")

    async def cmd_system(self, args):
        """Show system information."""
        print("💻 Getting system information...")

        result = await self.system_tools.get_development_environment_info()
        if result["success"]:
            print(f"🖥️  Platform: {result['platform']}")
            print(f"🐍 Python: {result.get('python', {}).get('version', 'Not found')}")
            print(f"📦 Node.js: {result.get('node', {}).get('version', 'Not found')}")
            print(f"🔧 Git: {result.get('git', {}).get('version', 'Not found')}")
            print(f"🐳 Docker: {result.get('docker', {}).get('version', 'Not found')}")
            print(f"💾 Memory: {result['memory_total'] / (1024**3):.1f} GB")
            print(f"💽 CPU cores: {result['cpu_count']}")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")

    # ========================================
    # REVOLUTIONARY INTENT PROCESSING COMMANDS
    # ========================================

    async def cmd_intent_analytics(self, args):
        """Show enhanced intent processing analytics."""
        if not self.enhanced_intent_processor:
            self.print_status("Enhanced Intent Processor not available", "error")
            return

        try:
            analytics = self.enhanced_intent_processor.get_processing_analytics()

            if 'message' in analytics:
                self.print_status(analytics['message'], "info")
                return

            # Display comprehensive analytics
            if self.use_rich:
                # Create analytics table
                table = Table(title="🔺 Enhanced Intent Processing Analytics")
                table.add_column("Metric", style="cyan")
                table.add_column("Value", style="white")
                table.add_column("Details", style="dim")

                # Core metrics
                table.add_row(
                    "Total Processed",
                    str(analytics['total_processed']),
                    "Total intents processed"
                )

                table.add_row(
                    "Confidence Improvement Rate",
                    f"{analytics['confidence_improvement_rate']:.1%}",
                    "Rate of confidence improvements"
                )

                table.add_row(
                    "Average Processing Time",
                    f"{analytics['average_processing_time']:.4f}s",
                    "Average time per intent"
                )

                table.add_row(
                    "Recent Average Confidence",
                    f"{analytics['recent_average_confidence']:.2f}",
                    "Last 10 intents"
                )

                self.console.print(table)

                # Algorithm usage
                if analytics['algorithm_usage']:
                    usage_table = Table(title="🧠 Algorithm Usage")
                    usage_table.add_column("Algorithm", style="cyan")
                    usage_table.add_column("Usage Count", style="white")
                    usage_table.add_column("Usage Rate", style="green")

                    total = analytics['total_processed']
                    for algorithm, count in analytics['algorithm_usage'].items():
                        rate = f"{count / max(1, total):.1%}"
                        usage_table.add_row(algorithm, str(count), rate)

                    self.console.print(usage_table)

                # Flow state distribution
                if analytics['flow_state_distribution']:
                    flow_table = Table(title="🌊 Flow State Distribution")
                    flow_table.add_column("Flow State", style="cyan")
                    flow_table.add_column("Count", style="white")
                    flow_table.add_column("Percentage", style="blue")

                    total_flow = sum(analytics['flow_state_distribution'].values())
                    for state, count in analytics['flow_state_distribution'].items():
                        percentage = f"{count / max(1, total_flow):.1%}"
                        flow_table.add_row(state, str(count), percentage)

                    self.console.print(flow_table)

            else:
                # Fallback text display
                print("🔺 Enhanced Intent Processing Analytics")
                print("=" * 50)
                print(f"Total Processed: {analytics['total_processed']}")
                print(f"Confidence Improvement Rate: {analytics['confidence_improvement_rate']:.1%}")
                print(f"Average Processing Time: {analytics['average_processing_time']:.4f}s")
                print(f"Recent Average Confidence: {analytics['recent_average_confidence']:.2f}")

                if analytics['algorithm_usage']:
                    print("\nAlgorithm Usage:")
                    for algorithm, count in analytics['algorithm_usage'].items():
                        print(f"  {algorithm}: {count} times")

        except Exception as e:
            self.print_status(f"Failed to get intent analytics: {e}", "error")

    async def cmd_intent_test(self, args):
        """Test enhanced intent processing with sample intents."""
        if not self.enhanced_intent_processor:
            self.print_status("Enhanced Intent Processor not available", "error")
            return

        if not args:
            # Show test menu
            test_intents = [
                "/help config",
                "create a Python function",
                "I need help with something",
                "analyze the performance data",
                "what do you think about this?",
                "fix this bug in the authentication system",
                "implement a machine learning model for predictions",
            ]

            self.print_panel(
                "Available test intents:\n" +
                "\n".join(f"{i+1}. {intent}" for i, intent in enumerate(test_intents)) +
                "\n\nUsage: /intent-test <number> or /intent-test \"custom intent\"",
                title="🧪 Intent Testing",
                style="yellow"
            )
            return

        try:
            # Parse argument
            if args[0].isdigit():
                # Test predefined intent
                test_intents = [
                    "/help config",
                    "create a Python function",
                    "I need help with something",
                    "analyze the performance data",
                    "what do you think about this?",
                    "fix this bug in the authentication system",
                    "implement a machine learning model for predictions",
                ]

                intent_index = int(args[0]) - 1
                if 0 <= intent_index < len(test_intents):
                    test_intent = test_intents[intent_index]
                else:
                    self.print_status(f"Invalid test number. Use 1-{len(test_intents)}", "error")
                    return
            else:
                # Custom intent
                test_intent = " ".join(args)

            self.print_status(f"Testing intent: '{test_intent}'", "info")

            # Process the intent
            start_time = time.time()
            enhanced_intent = await self.enhanced_intent_processor.process_intent(test_intent)
            processing_time = time.time() - start_time

            # Display results
            explanation = self.enhanced_intent_processor.get_intent_explanation(enhanced_intent)
            self.print_panel(explanation, title="🔺 Intent Analysis Results", style="magenta")

            # Show performance
            self.print_status(f"Processing completed in {processing_time:.4f}s", "success")

        except Exception as e:
            self.print_status(f"Intent test failed: {e}", "error")


async def main():
    """Main function to run the interactive shell."""
    import argparse

    parser = argparse.ArgumentParser(description="Agent Swarm Interactive Shell")
    parser.add_argument("--project", "-p", help="Project directory path")
    parser.add_argument("--model", "-m", default="llama3.2:3b", help="Ollama model to use")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    parser.add_argument("--auto-index", action="store_true", help="Automatically index project files without asking")
    parser.add_argument("--no-index", action="store_true", help="Skip indexing project files")
    parser.add_argument("--version", action="version", version=f"Agent Swarm {__version__}")

    args = parser.parse_args()

    # Validate indexing arguments
    if args.auto_index and args.no_index:
        print("❌ Error: Cannot use both --auto-index and --no-index")
        return

    # Determine auto_index setting
    auto_index = None
    if args.auto_index:
        auto_index = True
    elif args.no_index:
        auto_index = False

    # Setup logging
    log_level = "DEBUG" if args.verbose else "WARNING"
    setup_logging(level=log_level)

    # Create and run shell
    shell = AgentSwarmShell(verbose=args.verbose, auto_index=auto_index)
    shell.current_model = args.model

    # Initialize with project
    project_path = args.project or os.getcwd()
    success = await shell.initialize(project_path)

    if success:
        await shell.run()
    else:
        print("❌ Failed to initialize shell")
        sys.exit(1)


def cli_main():
    """Synchronous entry point for CLI."""
    asyncio.run(main())


if __name__ == "__main__":
    cli_main()
