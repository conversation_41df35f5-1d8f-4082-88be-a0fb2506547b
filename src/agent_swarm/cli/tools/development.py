"""
Development tools for CLI operations.
Enhanced development utilities with project-aware features.
"""

from typing import Any, Dict, List, Optional
from pathlib import Path

# Import the existing implementation
from ...tools.cli_tools import DevelopmentTool as BaseDevelopmentTool


class DevelopmentTools(BaseDevelopmentTool):
    """Enhanced development tools with additional convenience methods."""
    
    async def detect_project_type(self, directory: str = ".") -> Dict[str, Any]:
        """Detect the type of project in directory."""
        path = Path(directory)
        
        project_indicators = {
            "python": ["setup.py", "pyproject.toml", "requirements.txt", "Pipfile"],
            "node": ["package.json", "yarn.lock", "package-lock.json"],
            "rust": ["Cargo.toml", "Cargo.lock"],
            "go": ["go.mod", "go.sum"],
            "java": ["pom.xml", "build.gradle", "build.gradle.kts"],
            "docker": ["Dockerfile", "docker-compose.yml", "docker-compose.yaml"],
            "git": [".git"],
        }
        
        detected_types = []
        for project_type, indicators in project_indicators.items():
            for indicator in indicators:
                if (path / indicator).exists():
                    detected_types.append(project_type)
                    break
        
        return {
            "success": True,
            "directory": str(path.absolute()),
            "detected_types": detected_types,
            "primary_type": detected_types[0] if detected_types else "unknown"
        }
    
    async def get_project_info(self, directory: str = ".") -> Dict[str, Any]:
        """Get comprehensive project information."""
        project_type_result = await self.detect_project_type(directory)
        
        info = {
            "success": True,
            "directory": directory,
            "project_types": project_type_result.get("detected_types", []),
            "files": {},
            "dependencies": {},
            "scripts": {}
        }
        
        path = Path(directory)
        
        # Check for common files
        common_files = [
            "README.md", "README.rst", "README.txt",
            "LICENSE", "LICENSE.txt", "LICENSE.md",
            ".gitignore", ".dockerignore",
            "Makefile", "makefile"
        ]
        
        for file in common_files:
            if (path / file).exists():
                info["files"][file] = True
        
        # Get language-specific info
        if "python" in info["project_types"]:
            python_info = await self._get_python_info(path)
            info.update(python_info)
        
        if "node" in info["project_types"]:
            node_info = await self._get_node_info(path)
            info.update(node_info)
        
        return info
    
    async def _get_python_info(self, path: Path) -> Dict[str, Any]:
        """Get Python project specific information."""
        info = {"python": {}}
        
        # Check for virtual environment
        venv_paths = [".venv", "venv", "env", ".env"]
        for venv_path in venv_paths:
            if (path / venv_path).exists():
                info["python"]["virtual_env"] = venv_path
                break
        
        # Check for requirements
        if (path / "requirements.txt").exists():
            try:
                requirements = (path / "requirements.txt").read_text().strip().split('\n')
                info["python"]["requirements_count"] = len([r for r in requirements if r.strip() and not r.startswith('#')])
            except Exception:
                pass
        
        # Check for pyproject.toml
        if (path / "pyproject.toml").exists():
            info["python"]["has_pyproject"] = True
        
        return info
    
    async def _get_node_info(self, path: Path) -> Dict[str, Any]:
        """Get Node.js project specific information."""
        info = {"node": {}}
        
        # Check for node_modules
        if (path / "node_modules").exists():
            info["node"]["has_node_modules"] = True
        
        # Check for package.json
        if (path / "package.json").exists():
            try:
                import json
                package_data = json.loads((path / "package.json").read_text())
                info["node"]["name"] = package_data.get("name")
                info["node"]["version"] = package_data.get("version")
                info["node"]["scripts"] = list(package_data.get("scripts", {}).keys())
                info["node"]["dependencies_count"] = len(package_data.get("dependencies", {}))
                info["node"]["dev_dependencies_count"] = len(package_data.get("devDependencies", {}))
            except Exception:
                pass
        
        return info
    
    async def run_project_command(self, command: str, directory: str = ".") -> Dict[str, Any]:
        """Run a project-specific command."""
        project_type_result = await self.detect_project_type(directory)
        project_types = project_type_result.get("detected_types", [])
        
        # Map common commands to project-specific implementations
        command_mappings = {
            "install": {
                "python": "pip install -r requirements.txt",
                "node": "npm install",
                "rust": "cargo build",
                "go": "go mod download"
            },
            "test": {
                "python": "pytest",
                "node": "npm test",
                "rust": "cargo test",
                "go": "go test ./..."
            },
            "build": {
                "python": "python -m build",
                "node": "npm run build",
                "rust": "cargo build --release",
                "go": "go build"
            },
            "clean": {
                "python": "find . -name '__pycache__' -exec rm -rf {} +",
                "node": "rm -rf node_modules",
                "rust": "cargo clean",
                "go": "go clean"
            }
        }
        
        if command in command_mappings:
            for project_type in project_types:
                if project_type in command_mappings[command]:
                    actual_command = command_mappings[command][project_type]
                    return await self.execute_command(actual_command, cwd=directory)
        
        # Fallback to direct command execution
        return await self.execute_command(command, cwd=directory)
    
    async def setup_development_environment(self, directory: str = ".") -> Dict[str, Any]:
        """Set up development environment for project."""
        project_info = await self.get_project_info(directory)
        project_types = project_info.get("project_types", [])
        
        setup_results = []
        
        for project_type in project_types:
            if project_type == "python":
                # Set up Python environment
                result = await self._setup_python_env(directory)
                setup_results.append({"type": "python", "result": result})
            
            elif project_type == "node":
                # Set up Node environment
                result = await self._setup_node_env(directory)
                setup_results.append({"type": "node", "result": result})
        
        return {
            "success": True,
            "directory": directory,
            "project_types": project_types,
            "setup_results": setup_results
        }
    
    async def _setup_python_env(self, directory: str) -> Dict[str, Any]:
        """Set up Python development environment."""
        path = Path(directory)
        
        # Check if virtual environment exists
        venv_path = path / ".venv"
        if not venv_path.exists():
            result = await self.execute_command("python -m venv .venv", cwd=directory)
            if not result["success"]:
                return result
        
        # Install dependencies if requirements.txt exists
        if (path / "requirements.txt").exists():
            result = await self.execute_command(".venv/bin/pip install -r requirements.txt", cwd=directory)
            return result
        
        return {"success": True, "message": "Python environment ready"}
    
    async def _setup_node_env(self, directory: str) -> Dict[str, Any]:
        """Set up Node.js development environment."""
        path = Path(directory)
        
        # Install dependencies if package.json exists
        if (path / "package.json").exists():
            result = await self.execute_command("npm install", cwd=directory)
            return result
        
        return {"success": True, "message": "Node environment ready"}


# Convenience instance
dev_tools = DevelopmentTools()
