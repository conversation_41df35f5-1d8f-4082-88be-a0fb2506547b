"""
System tools for CLI operations.
Enhanced system utilities and diagnostics.
"""

from typing import Any, Dict, List, Optional
import platform
import shutil

# Import the existing implementation
from ...tools.cli_tools import SystemInfoTool as BaseSystemInfoTool, ProcessTool


class SystemTools(BaseSystemInfoTool):
    """Enhanced system tools with additional convenience methods."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.process_tool = ProcessTool()
    
    async def get_development_environment_info(self) -> Dict[str, Any]:
        """Get comprehensive development environment information."""
        base_info = await self.get_system_info()
        
        if not base_info["success"]:
            return base_info
        
        # Check for development tools
        dev_tools = [
            "python", "python3", "node", "npm", "git", "docker", 
            "make", "gcc", "rustc", "go", "java", "mvn"
        ]
        
        tool_check = await self.check_dependencies(dev_tools)
        
        # Get Python version info
        python_info = await self._get_python_info()
        
        # Get Node.js version info
        node_info = await self._get_node_info()
        
        # Get Git info
        git_info = await self._get_git_info()
        
        # Get Docker info
        docker_info = await self._get_docker_info()
        
        return {
            **base_info,
            "development_tools": tool_check.get("dependencies", {}),
            "python": python_info,
            "node": node_info,
            "git": git_info,
            "docker": docker_info
        }
    
    async def _get_python_info(self) -> Dict[str, Any]:
        """Get Python environment information."""
        info = {}
        
        # Python version
        python_result = await self.execute_command("python --version")
        if python_result["success"]:
            info["version"] = python_result["stdout"].strip()
        
        # Python3 version
        python3_result = await self.execute_command("python3 --version")
        if python3_result["success"]:
            info["python3_version"] = python3_result["stdout"].strip()
        
        # Pip version
        pip_result = await self.execute_command("pip --version")
        if pip_result["success"]:
            info["pip_version"] = pip_result["stdout"].strip()
        
        # Virtual environment
        venv_result = await self.execute_command("echo $VIRTUAL_ENV")
        if venv_result["success"] and venv_result["stdout"].strip():
            info["virtual_env"] = venv_result["stdout"].strip()
        
        return info
    
    async def _get_node_info(self) -> Dict[str, Any]:
        """Get Node.js environment information."""
        info = {}
        
        # Node version
        node_result = await self.execute_command("node --version")
        if node_result["success"]:
            info["version"] = node_result["stdout"].strip()
        
        # NPM version
        npm_result = await self.execute_command("npm --version")
        if npm_result["success"]:
            info["npm_version"] = npm_result["stdout"].strip()
        
        # Yarn version
        yarn_result = await self.execute_command("yarn --version")
        if yarn_result["success"]:
            info["yarn_version"] = yarn_result["stdout"].strip()
        
        return info
    
    async def _get_git_info(self) -> Dict[str, Any]:
        """Get Git environment information."""
        info = {}
        
        # Git version
        git_result = await self.execute_command("git --version")
        if git_result["success"]:
            info["version"] = git_result["stdout"].strip()
        
        # Git config
        name_result = await self.execute_command("git config --global user.name")
        if name_result["success"]:
            info["user_name"] = name_result["stdout"].strip()
        
        email_result = await self.execute_command("git config --global user.email")
        if email_result["success"]:
            info["user_email"] = email_result["stdout"].strip()
        
        return info
    
    async def _get_docker_info(self) -> Dict[str, Any]:
        """Get Docker environment information."""
        info = {}
        
        # Docker version
        docker_result = await self.execute_command("docker --version")
        if docker_result["success"]:
            info["version"] = docker_result["stdout"].strip()
        
        # Docker compose version
        compose_result = await self.execute_command("docker-compose --version")
        if compose_result["success"]:
            info["compose_version"] = compose_result["stdout"].strip()
        
        # Docker status
        status_result = await self.execute_command("docker info")
        info["daemon_running"] = status_result["success"]
        
        return info
    
    async def check_agent_swarm_environment(self) -> Dict[str, Any]:
        """Check Agent Swarm specific environment requirements."""
        checks = []
        
        # Check Python version (3.8+)
        python_check = await self._check_python_version()
        checks.append(python_check)
        
        # Check required Python packages
        packages_check = await self._check_python_packages()
        checks.append(packages_check)
        
        # Check Ollama
        ollama_check = await self._check_ollama()
        checks.append(ollama_check)
        
        # Check system resources
        resources_check = await self._check_system_resources()
        checks.append(resources_check)
        
        # Calculate overall status
        passed_checks = sum(1 for check in checks if check["status"] == "pass")
        total_checks = len(checks)
        
        return {
            "success": True,
            "overall_status": "ready" if passed_checks == total_checks else "issues_found",
            "checks_passed": passed_checks,
            "total_checks": total_checks,
            "checks": checks
        }
    
    async def _check_python_version(self) -> Dict[str, Any]:
        """Check Python version requirement."""
        result = await self.execute_command("python3 --version")
        
        if not result["success"]:
            return {
                "name": "Python 3.8+",
                "status": "fail",
                "message": "Python 3 not found",
                "required": True
            }
        
        try:
            version_str = result["stdout"].strip()
            version_parts = version_str.split()[1].split('.')
            major, minor = int(version_parts[0]), int(version_parts[1])
            
            if major >= 3 and minor >= 8:
                return {
                    "name": "Python 3.8+",
                    "status": "pass",
                    "message": f"Found {version_str}",
                    "required": True
                }
            else:
                return {
                    "name": "Python 3.8+",
                    "status": "fail",
                    "message": f"Found {version_str}, need 3.8+",
                    "required": True
                }
        except (ValueError, IndexError):
            return {
                "name": "Python 3.8+",
                "status": "fail",
                "message": "Could not parse Python version",
                "required": True
            }
    
    async def _check_python_packages(self) -> Dict[str, Any]:
        """Check required Python packages."""
        required_packages = ["aiohttp", "pydantic", "typer", "rich"]
        
        missing_packages = []
        for package in required_packages:
            result = await self.execute_command(f"python3 -c 'import {package}'")
            if not result["success"]:
                missing_packages.append(package)
        
        if not missing_packages:
            return {
                "name": "Python Packages",
                "status": "pass",
                "message": "All required packages found",
                "required": True
            }
        else:
            return {
                "name": "Python Packages",
                "status": "fail",
                "message": f"Missing packages: {', '.join(missing_packages)}",
                "required": True
            }
    
    async def _check_ollama(self) -> Dict[str, Any]:
        """Check Ollama installation and status."""
        # Check if Ollama is installed
        which_result = await self.execute_command("which ollama")
        if not which_result["success"]:
            return {
                "name": "Ollama",
                "status": "warn",
                "message": "Ollama not found in PATH",
                "required": False
            }
        
        # Check if Ollama service is running
        status_result = await self.execute_command("ollama list")
        if status_result["success"]:
            return {
                "name": "Ollama",
                "status": "pass",
                "message": "Ollama installed and running",
                "required": False
            }
        else:
            return {
                "name": "Ollama",
                "status": "warn",
                "message": "Ollama installed but not running",
                "required": False
            }
    
    async def _check_system_resources(self) -> Dict[str, Any]:
        """Check system resources for Agent Swarm."""
        resources = await self.get_system_resources()
        
        if not resources["success"]:
            return {
                "name": "System Resources",
                "status": "fail",
                "message": "Could not check system resources",
                "required": True
            }
        
        memory_gb = resources["memory"]["total"] / (1024**3)
        disk_gb = resources["disk"]["total"] / (1024**3)
        
        issues = []
        if memory_gb < 4:
            issues.append(f"Low memory: {memory_gb:.1f}GB (recommend 8GB+)")
        if disk_gb < 10:
            issues.append(f"Low disk space: {disk_gb:.1f}GB (recommend 20GB+)")
        
        if not issues:
            return {
                "name": "System Resources",
                "status": "pass",
                "message": f"Memory: {memory_gb:.1f}GB, Disk: {disk_gb:.1f}GB",
                "required": True
            }
        else:
            return {
                "name": "System Resources",
                "status": "warn",
                "message": "; ".join(issues),
                "required": True
            }


# Convenience instance
system_tools = SystemTools()
