"""
File system tools for CLI operations.
Enhanced file system utilities with better organization.
"""

from typing import Any, Dict, List, Optional
from pathlib import Path

# Import the existing implementation
from ...tools.cli_tools import FileSystemTool as BaseFileSystemTool


class FileSystemTools(BaseFileSystemTool):
    """Enhanced file system tools with additional convenience methods."""
    
    async def find_code_files(
        self, 
        directory: str = ".", 
        extensions: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Find code files in directory."""
        if extensions is None:
            extensions = [".py", ".js", ".ts", ".java", ".cpp", ".c", ".rs", ".go"]
        
        results = []
        for ext in extensions:
            result = await self.find_files(f"*{ext}", directory)
            if result["success"]:
                results.extend(result["stdout"].strip().split('\n') if result["stdout"].strip() else [])
        
        return {
            "success": True,
            "files": [f for f in results if f],
            "count": len([f for f in results if f])
        }
    
    async def search_in_code(
        self, 
        pattern: str, 
        directory: str = ".",
        extensions: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Search for pattern in code files."""
        if extensions is None:
            extensions = [".py", ".js", ".ts", ".java", ".cpp", ".c", ".rs", ".go"]
        
        results = []
        for ext in extensions:
            result = await self.grep_in_files(pattern, directory, f"*{ext}")
            if result["success"] and result["stdout"]:
                results.append({
                    "extension": ext,
                    "matches": result["stdout"].strip().split('\n')
                })
        
        return {
            "success": True,
            "pattern": pattern,
            "results": results,
            "total_matches": sum(len(r["matches"]) for r in results)
        }
    
    async def get_project_structure(self, directory: str = ".", max_depth: int = 3) -> Dict[str, Any]:
        """Get project directory structure."""
        command = f"find {directory} -maxdepth {max_depth} -type d | head -50"
        result = await self.execute_command(command)
        
        if result["success"]:
            directories = result["stdout"].strip().split('\n') if result["stdout"].strip() else []
            return {
                "success": True,
                "directories": directories,
                "count": len(directories)
            }
        
        return result
    
    async def get_file_tree(self, directory: str = ".", max_depth: int = 2) -> Dict[str, Any]:
        """Get file tree structure."""
        # Try tree command first, fallback to find
        tree_result = await self.execute_command(f"tree -L {max_depth} {directory}")
        if tree_result["success"]:
            return {
                "success": True,
                "tree": tree_result["stdout"],
                "method": "tree"
            }
        
        # Fallback to find
        find_result = await self.execute_command(f"find {directory} -maxdepth {max_depth} | head -100")
        if find_result["success"]:
            files = find_result["stdout"].strip().split('\n') if find_result["stdout"].strip() else []
            return {
                "success": True,
                "files": files,
                "count": len(files),
                "method": "find"
            }
        
        return find_result
    
    async def count_lines_of_code(self, directory: str = ".") -> Dict[str, Any]:
        """Count lines of code in project."""
        # Try cloc first
        cloc_result = await self.execute_command(f"cloc {directory}")
        if cloc_result["success"]:
            return {
                "success": True,
                "output": cloc_result["stdout"],
                "method": "cloc"
            }
        
        # Fallback to wc
        extensions = ["*.py", "*.js", "*.ts", "*.java", "*.cpp", "*.c", "*.rs", "*.go"]
        total_lines = 0
        file_counts = {}
        
        for ext in extensions:
            result = await self.execute_command(f"find {directory} -name '{ext}' -exec wc -l {{}} +")
            if result["success"] and result["stdout"]:
                lines = result["stdout"].strip().split('\n')
                if lines and lines[-1].strip():
                    try:
                        count = int(lines[-1].strip().split()[0])
                        file_counts[ext] = count
                        total_lines += count
                    except (ValueError, IndexError):
                        pass
        
        return {
            "success": True,
            "total_lines": total_lines,
            "by_extension": file_counts,
            "method": "wc"
        }


# Convenience instance
fs_tools = FileSystemTools()
