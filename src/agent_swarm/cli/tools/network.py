"""
Network tools for CLI operations.
Enhanced network utilities for development and diagnostics.
"""

from typing import Any, Dict, List, Optional
import asyncio

# Import the existing implementation
from ...tools.cli_tools import NetworkTool as BaseNetworkTool


class NetworkTools(BaseNetworkTool):
    """Enhanced network tools with additional convenience methods."""
    
    async def check_internet_connectivity(self) -> Dict[str, Any]:
        """Check internet connectivity with multiple hosts."""
        test_hosts = ["*******", "*******", "google.com"]
        results = []
        
        for host in test_hosts:
            result = await self.ping_host(host, count=1)
            results.append({
                "host": host,
                "reachable": result["success"],
                "response": result.get("stdout", "").strip()
            })
        
        successful_pings = sum(1 for r in results if r["reachable"])
        
        return {
            "success": True,
            "internet_available": successful_pings > 0,
            "connectivity_score": successful_pings / len(test_hosts),
            "test_results": results
        }
    
    async def check_local_services(self, services: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """Check common local development services."""
        if services is None:
            services = [
                {"name": "Ollama", "host": "localhost", "port": 11434},
                {"name": "PostgreSQL", "host": "localhost", "port": 5432},
                {"name": "Redis", "host": "localhost", "port": 6379},
                {"name": "MongoDB", "host": "localhost", "port": 27017},
                {"name": "MySQL", "host": "localhost", "port": 3306},
                {"name": "Docker", "host": "localhost", "port": 2376},
            ]
        
        results = []
        for service in services:
            result = await self.check_port(service["host"], service["port"])
            results.append({
                "name": service["name"],
                "host": service["host"],
                "port": service["port"],
                "available": result.get("port_open", False),
                "response": result.get("stderr", "").strip()
            })
        
        available_services = [r for r in results if r["available"]]
        
        return {
            "success": True,
            "services_checked": len(services),
            "services_available": len(available_services),
            "available_services": [s["name"] for s in available_services],
            "results": results
        }
    
    async def test_api_endpoint(
        self, 
        url: str, 
        method: str = "GET",
        expected_status: int = 200,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Test an API endpoint."""
        result = await self.curl_request(url, method, headers)
        
        if result["success"]:
            # Extract status code from curl output
            output = result["stdout"]
            try:
                # Curl with -w '%{http_code}' puts status code at the end
                status_code = int(output[-3:]) if len(output) >= 3 else 0
                response_body = output[:-3] if len(output) > 3 else ""
                
                return {
                    "success": True,
                    "url": url,
                    "method": method,
                    "status_code": status_code,
                    "expected_status": expected_status,
                    "status_ok": status_code == expected_status,
                    "response_body": response_body.strip(),
                    "response_length": len(response_body.strip())
                }
            except ValueError:
                return {
                    "success": False,
                    "error": "Could not parse status code",
                    "raw_output": output
                }
        
        return result
    
    async def scan_local_network(self, network: str = "192.168.1") -> Dict[str, Any]:
        """Scan local network for active hosts."""
        # Use nmap if available, otherwise ping sweep
        nmap_result = await self.execute_command(f"nmap -sn {network}.0/24")
        
        if nmap_result["success"]:
            return {
                "success": True,
                "method": "nmap",
                "output": nmap_result["stdout"],
                "network": f"{network}.0/24"
            }
        
        # Fallback to ping sweep
        active_hosts = []
        tasks = []
        
        # Create ping tasks for common host IPs
        for i in range(1, 255, 10):  # Sample every 10th IP for speed
            tasks.append(self._ping_host_quick(f"{network}.{i}"))
        
        # Wait for all pings with timeout
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=10
            )
            
            for i, result in enumerate(results):
                if isinstance(result, dict) and result.get("success"):
                    active_hosts.append(f"{network}.{i*10 + 1}")
        
        except asyncio.TimeoutError:
            pass
        
        return {
            "success": True,
            "method": "ping_sweep",
            "network": f"{network}.0/24",
            "active_hosts": active_hosts,
            "hosts_found": len(active_hosts)
        }
    
    async def _ping_host_quick(self, host: str) -> Dict[str, Any]:
        """Quick ping with short timeout."""
        return await self.ping_host(host, count=1)
    
    async def get_public_ip(self) -> Dict[str, Any]:
        """Get public IP address."""
        services = [
            "https://ipinfo.io/ip",
            "https://api.ipify.org",
            "https://checkip.amazonaws.com"
        ]
        
        for service in services:
            result = await self.curl_request(service)
            if result["success"]:
                ip = result["stdout"].strip()
                if ip and "." in ip:  # Basic IP validation
                    return {
                        "success": True,
                        "public_ip": ip,
                        "service": service
                    }
        
        return {
            "success": False,
            "error": "Could not determine public IP",
            "services_tried": services
        }
    
    async def test_dns_resolution(self, domains: Optional[List[str]] = None) -> Dict[str, Any]:
        """Test DNS resolution for common domains."""
        if domains is None:
            domains = ["google.com", "github.com", "stackoverflow.com", "python.org"]
        
        results = []
        for domain in domains:
            result = await self.execute_command(f"nslookup {domain}")
            results.append({
                "domain": domain,
                "resolved": result["success"],
                "response": result.get("stdout", "").strip()
            })
        
        successful_resolutions = sum(1 for r in results if r["resolved"])
        
        return {
            "success": True,
            "domains_tested": len(domains),
            "successful_resolutions": successful_resolutions,
            "dns_working": successful_resolutions > 0,
            "results": results
        }


# Convenience instance
network_tools = NetworkTools()
