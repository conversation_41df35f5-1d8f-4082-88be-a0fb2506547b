"""
Built-in MCP tools for Agent Swarm.
"""

from __future__ import annotations

import os
import subprocess
from pathlib import Path
from typing import Any, Dict

from ..utils.logging import get_logger

logger = get_logger("mcp.tools")


class FileSystemTool:
    """MCP tool for file system operations."""
    
    @staticmethod
    def get_schema() -> Dict[str, Any]:
        """Get the MCP schema for filesystem tools."""
        return {
            "read_file": {
                "description": "Read contents of a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "File path to read"}
                    },
                    "required": ["path"]
                }
            },
            "write_file": {
                "description": "Write contents to a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "File path to write"},
                        "content": {"type": "string", "description": "Content to write"}
                    },
                    "required": ["path", "content"]
                }
            },
            "list_directory": {
                "description": "List contents of a directory",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Directory path to list"}
                    },
                    "required": ["path"]
                }
            }
        }
    
    @staticmethod
    async def execute(tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a filesystem tool."""
        if tool_name == "read_file":
            return await FileSystemTool._read_file(arguments["path"])
        elif tool_name == "write_file":
            return await FileSystemTool._write_file(arguments["path"], arguments["content"])
        elif tool_name == "list_directory":
            return await FileSystemTool._list_directory(arguments["path"])
        else:
            raise ValueError(f"Unknown filesystem tool: {tool_name}")
    
    @staticmethod
    async def _read_file(path: str) -> Dict[str, Any]:
        """Read a file."""
        try:
            file_path = Path(path)
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {path}")
            
            content = file_path.read_text(encoding="utf-8")
            return {
                "content": content,
                "path": str(file_path),
                "size": len(content),
                "encoding": "utf-8"
            }
        except Exception as e:
            raise Exception(f"Failed to read file {path}: {e}")
    
    @staticmethod
    async def _write_file(path: str, content: str) -> Dict[str, Any]:
        """Write to a file."""
        try:
            file_path = Path(path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            file_path.write_text(content, encoding="utf-8")
            
            return {
                "success": True,
                "path": str(file_path),
                "bytes_written": len(content.encode("utf-8")),
                "encoding": "utf-8"
            }
        except Exception as e:
            raise Exception(f"Failed to write file {path}: {e}")
    
    @staticmethod
    async def _list_directory(path: str) -> Dict[str, Any]:
        """List directory contents."""
        try:
            dir_path = Path(path)
            if not dir_path.exists():
                raise FileNotFoundError(f"Directory not found: {path}")
            if not dir_path.is_dir():
                raise NotADirectoryError(f"Not a directory: {path}")
            
            items = []
            for item in dir_path.iterdir():
                items.append({
                    "name": item.name,
                    "path": str(item),
                    "type": "directory" if item.is_dir() else "file",
                    "size": item.stat().st_size if item.is_file() else None
                })
            
            return {
                "path": str(dir_path),
                "items": items,
                "count": len(items)
            }
        except Exception as e:
            raise Exception(f"Failed to list directory {path}: {e}")


class WebSearchTool:
    """MCP tool for web search operations."""
    
    @staticmethod
    def get_schema() -> Dict[str, Any]:
        """Get the MCP schema for web search tools."""
        return {
            "search_web": {
                "description": "Search the web for information",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Search query"},
                        "num_results": {"type": "integer", "description": "Number of results", "default": 5}
                    },
                    "required": ["query"]
                }
            }
        }
    
    @staticmethod
    async def execute(tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a web search tool."""
        if tool_name == "search_web":
            return await WebSearchTool._search_web(
                arguments["query"], 
                arguments.get("num_results", 5)
            )
        else:
            raise ValueError(f"Unknown web search tool: {tool_name}")
    
    @staticmethod
    async def _search_web(query: str, num_results: int) -> Dict[str, Any]:
        """Perform web search (simulated for demo)."""
        # In a real implementation, this would use a search API
        # For demo purposes, we'll return simulated results
        
        results = []
        for i in range(num_results):
            results.append({
                "title": f"Search Result {i+1} for '{query}'",
                "url": f"https://example.com/result{i+1}",
                "snippet": f"This is a simulated search result {i+1} for the query '{query}'. It contains relevant information about the topic.",
                "source": "example.com"
            })
        
        return {
            "query": query,
            "results": results,
            "total_results": num_results,
            "search_time": "0.1s"
        }


class CodeExecutionTool:
    """MCP tool for code execution."""
    
    @staticmethod
    def get_schema() -> Dict[str, Any]:
        """Get the MCP schema for code execution tools."""
        return {
            "execute_python": {
                "description": "Execute Python code safely",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "Python code to execute"},
                        "timeout": {"type": "integer", "description": "Timeout in seconds", "default": 30}
                    },
                    "required": ["code"]
                }
            }
        }
    
    @staticmethod
    async def execute(tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a code execution tool."""
        if tool_name == "execute_python":
            return await CodeExecutionTool._execute_python(
                arguments["code"],
                arguments.get("timeout", 30)
            )
        else:
            raise ValueError(f"Unknown code execution tool: {tool_name}")
    
    @staticmethod
    async def _execute_python(code: str, timeout: int) -> Dict[str, Any]:
        """Execute Python code safely."""
        try:
            # In a real implementation, this would use a sandboxed environment
            # For demo purposes, we'll simulate execution
            
            logger.warning("Code execution is simulated for safety")
            
            return {
                "code": code,
                "output": f"# Simulated execution of:\n{code}\n# Output: Code executed successfully (simulated)",
                "success": True,
                "execution_time": "0.1s",
                "note": "This is a simulated execution for safety"
            }
        except Exception as e:
            return {
                "code": code,
                "output": "",
                "success": False,
                "error": str(e),
                "execution_time": "0.0s"
            }


class GitTool:
    """MCP tool for Git operations."""
    
    @staticmethod
    def get_schema() -> Dict[str, Any]:
        """Get the MCP schema for Git tools."""
        return {
            "git_status": {
                "description": "Get Git repository status",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Repository path", "default": "."}
                    }
                }
            },
            "git_log": {
                "description": "Get Git commit history",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Repository path", "default": "."},
                        "limit": {"type": "integer", "description": "Number of commits", "default": 10}
                    }
                }
            }
        }
    
    @staticmethod
    async def execute(tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a Git tool."""
        if tool_name == "git_status":
            return await GitTool._git_status(arguments.get("path", "."))
        elif tool_name == "git_log":
            return await GitTool._git_log(
                arguments.get("path", "."),
                arguments.get("limit", 10)
            )
        else:
            raise ValueError(f"Unknown Git tool: {tool_name}")
    
    @staticmethod
    async def _git_status(path: str) -> Dict[str, Any]:
        """Get Git status."""
        try:
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                cwd=path,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                raise Exception(f"Git command failed: {result.stderr}")
            
            return {
                "path": path,
                "status": result.stdout,
                "clean": len(result.stdout.strip()) == 0,
                "command": "git status --porcelain"
            }
        except Exception as e:
            raise Exception(f"Failed to get Git status: {e}")
    
    @staticmethod
    async def _git_log(path: str, limit: int) -> Dict[str, Any]:
        """Get Git log."""
        try:
            result = subprocess.run(
                ["git", "log", f"--max-count={limit}", "--oneline"],
                cwd=path,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                raise Exception(f"Git command failed: {result.stderr}")
            
            commits = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    parts = line.split(' ', 1)
                    commits.append({
                        "hash": parts[0],
                        "message": parts[1] if len(parts) > 1 else ""
                    })
            
            return {
                "path": path,
                "commits": commits,
                "count": len(commits),
                "command": f"git log --max-count={limit} --oneline"
            }
        except Exception as e:
            raise Exception(f"Failed to get Git log: {e}")


class DatabaseTool:
    """MCP tool for database operations."""
    
    @staticmethod
    def get_schema() -> Dict[str, Any]:
        """Get the MCP schema for database tools."""
        return {
            "query_sqlite": {
                "description": "Execute SQLite query",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "database": {"type": "string", "description": "Database file path"},
                        "query": {"type": "string", "description": "SQL query to execute"}
                    },
                    "required": ["database", "query"]
                }
            }
        }
    
    @staticmethod
    async def execute(tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a database tool."""
        if tool_name == "query_sqlite":
            return await DatabaseTool._query_sqlite(
                arguments["database"],
                arguments["query"]
            )
        else:
            raise ValueError(f"Unknown database tool: {tool_name}")
    
    @staticmethod
    async def _query_sqlite(database: str, query: str) -> Dict[str, Any]:
        """Execute SQLite query."""
        try:
            import sqlite3
            
            with sqlite3.connect(database) as conn:
                cursor = conn.cursor()
                cursor.execute(query)
                
                if query.strip().upper().startswith('SELECT'):
                    results = cursor.fetchall()
                    columns = [desc[0] for desc in cursor.description]
                    
                    return {
                        "database": database,
                        "query": query,
                        "results": results,
                        "columns": columns,
                        "row_count": len(results)
                    }
                else:
                    conn.commit()
                    return {
                        "database": database,
                        "query": query,
                        "affected_rows": cursor.rowcount,
                        "success": True
                    }
        except Exception as e:
            raise Exception(f"Database query failed: {e}")
