"""
MCP integration with Agent Swarm agents and routers.
"""

from __future__ import annotations

import json
from typing import Any, Dict, List, Optional

from ..agents import MultiLLMAgent
from ..backends import <PERSON><PERSON><PERSON><PERSON>, LLMTier, Message, LLMResponse
from ..utils.logging import get_logger
from .client import <PERSON><PERSON><PERSON>, MCP<PERSON>oolCall, MCPToolRegistry

logger = get_logger("mcp.integration")


class MCPEnabledAgent(MultiLLMAgent):
    """Agent with MCP tool calling capabilities."""
    
    def __init__(
        self, 
        name: str, 
        role: str, 
        llm_router: LLMRouter,
        mcp_registry: Optional[MCPToolRegistry] = None
    ) -> None:
        super().__init__(name, role, llm_router)
        self.mcp_registry = mcp_registry or MCPToolRegistry()
        self.tool_calls_history: List[Dict[str, Any]] = []
    
    async def process_task_with_tools(
        self,
        task: str,
        task_type: str = "general",
        preferred_tier: Optional[LLMTier] = None,
        temperature: float = 0.3,
        max_tokens: Optional[int] = None,
        max_tool_calls: int = 5,
    ) -> str:
        """Process a task with MCP tool calling support."""
        
        self.logger.info(f"{self.name} processing {task_type} task with tools")
        
        # Get available tools
        available_tools = self.mcp_registry.get_tools_for_llm()
        
        # Add system message with role and tool instructions
        system_msg = Message(
            role="system",
            content=f"""You are {self.name}, a {self.role}. {self._get_role_instructions()}

You have access to the following tools:
{self._format_tools_for_prompt(available_tools)}

When you need to use a tool, respond with a JSON object containing:
{{"tool_calls": [{{"name": "tool_name", "arguments": {{"param": "value"}}}}]}}

Always explain what you're doing and why you're using each tool."""
        )
        
        # Add user task
        user_msg = Message(role="user", content=task)
        
        # Prepare messages with conversation history
        messages = [system_msg] + self.conversation_history[-4:] + [user_msg]
        
        tool_call_count = 0
        final_response = ""
        
        while tool_call_count < max_tool_calls:
            try:
                # Get LLM response
                response = await self.llm_router.route_request(
                    messages=messages,
                    task_type=task_type,
                    preferred_tier=preferred_tier,
                    temperature=temperature,
                    max_tokens=max_tokens
                )
                
                # Check if response contains tool calls
                tool_calls = self._extract_tool_calls(response.content)
                
                if not tool_calls:
                    # No tool calls, this is the final response
                    final_response = response.content
                    break
                
                # Execute tool calls
                tool_results = []
                for tool_call in tool_calls:
                    self.logger.info(f"{self.name} calling tool: {tool_call['name']}")
                    
                    result = await self.mcp_registry.call_tool(
                        tool_call["name"],
                        tool_call.get("arguments", {})
                    )
                    
                    tool_results.append({
                        "tool": tool_call["name"],
                        "arguments": tool_call.get("arguments", {}),
                        "result": result.result if result.success else result.error,
                        "success": result.success
                    })
                    
                    # Track tool usage
                    self.tool_calls_history.append({
                        "tool": tool_call["name"],
                        "arguments": tool_call.get("arguments", {}),
                        "success": result.success,
                        "task_type": task_type
                    })
                
                # Add tool results to conversation
                tool_results_msg = Message(
                    role="assistant",
                    content=f"Tool calls executed: {json.dumps(tool_results, indent=2)}"
                )
                messages.append(tool_results_msg)
                
                # Add follow-up prompt
                followup_msg = Message(
                    role="user",
                    content="Based on the tool results above, please provide your final response to the original task."
                )
                messages.append(followup_msg)
                
                tool_call_count += 1
                
            except Exception as e:
                self.logger.error(f"{self.name} tool execution failed: {e}")
                final_response = f"I encountered an error while using tools: {e}. Let me try to help without tools."
                break
        
        if not final_response:
            final_response = "I've completed the requested tool operations. Please see the results above."
        
        # Update conversation history
        assistant_msg = Message(role="assistant", content=final_response)
        self.conversation_history.extend([user_msg, assistant_msg])
        
        # Keep history manageable
        if len(self.conversation_history) > 20:
            self.conversation_history = self.conversation_history[-20:]
        
        self.logger.info(f"{self.name} completed task with {tool_call_count} tool calls")
        
        return final_response
    
    def _extract_tool_calls(self, response_content: str) -> List[Dict[str, Any]]:
        """Extract tool calls from LLM response."""
        try:
            # Look for JSON in the response
            if "tool_calls" in response_content:
                # Try to extract JSON
                start = response_content.find("{")
                end = response_content.rfind("}") + 1
                
                if start != -1 and end != 0:
                    json_str = response_content[start:end]
                    data = json.loads(json_str)
                    return data.get("tool_calls", [])
        except Exception as e:
            self.logger.debug(f"Failed to extract tool calls: {e}")
        
        return []
    
    def _format_tools_for_prompt(self, tools: List[Dict[str, Any]]) -> str:
        """Format tools for inclusion in prompt."""
        if not tools:
            return "No tools available."
        
        tool_descriptions = []
        for tool in tools:
            func = tool.get("function", {})
            name = func.get("name", "unknown")
            description = func.get("description", "No description")
            parameters = func.get("parameters", {})
            
            tool_descriptions.append(f"- {name}: {description}")
            if parameters.get("properties"):
                params = ", ".join(parameters["properties"].keys())
                tool_descriptions.append(f"  Parameters: {params}")
        
        return "\n".join(tool_descriptions)
    
    def get_tool_usage_stats(self) -> Dict[str, Any]:
        """Get statistics about tool usage."""
        if not self.tool_calls_history:
            return {"total_calls": 0, "tools_used": [], "success_rate": 0.0}
        
        total_calls = len(self.tool_calls_history)
        successful_calls = sum(1 for call in self.tool_calls_history if call["success"])
        tools_used = list(set(call["tool"] for call in self.tool_calls_history))
        
        return {
            "total_calls": total_calls,
            "successful_calls": successful_calls,
            "success_rate": successful_calls / total_calls if total_calls > 0 else 0.0,
            "tools_used": tools_used,
            "recent_calls": self.tool_calls_history[-5:]  # Last 5 calls
        }


class MCPRouter(LLMRouter):
    """LLM Router with MCP tool support."""
    
    def __init__(self, mcp_registry: Optional[MCPToolRegistry] = None) -> None:
        super().__init__()
        self.mcp_registry = mcp_registry or MCPToolRegistry()
    
    async def route_request_with_tools(
        self,
        messages: List[Message],
        task_type: str = "general",
        preferred_tier: Optional[LLMTier] = None,
        enable_tools: bool = True,
        **kwargs: Any,
    ) -> LLMResponse:
        """Route request with optional tool calling support."""
        
        if not enable_tools:
            return await self.route_request(messages, task_type, preferred_tier, **kwargs)
        
        # Add tools to the request if the LLM supports them
        available_tools = self.mcp_registry.get_tools_for_llm()
        
        # For now, we'll add tool information to the system message
        # In a full implementation, this would use the LLM's native tool calling
        if available_tools and messages:
            system_msg = None
            other_messages = []
            
            for msg in messages:
                if msg.role == "system":
                    system_msg = msg
                else:
                    other_messages.append(msg)
            
            if system_msg:
                # Enhance system message with tool information
                enhanced_content = system_msg.content + f"\n\nAvailable tools:\n{self._format_tools_for_system(available_tools)}"
                enhanced_system_msg = Message(
                    role="system",
                    content=enhanced_content,
                    metadata=system_msg.metadata
                )
                messages = [enhanced_system_msg] + other_messages
        
        return await self.route_request(messages, task_type, preferred_tier, **kwargs)
    
    def _format_tools_for_system(self, tools: List[Dict[str, Any]]) -> str:
        """Format tools for system message."""
        tool_descriptions = []
        for tool in tools:
            func = tool.get("function", {})
            name = func.get("name", "unknown")
            description = func.get("description", "No description")
            tool_descriptions.append(f"- {name}: {description}")
        
        return "\n".join(tool_descriptions)


# Factory functions for easy setup
async def create_mcp_enabled_agent(
    name: str,
    role: str,
    llm_router: LLMRouter,
    mcp_servers: Optional[List[Dict[str, Any]]] = None
) -> MCPEnabledAgent:
    """Create an MCP-enabled agent with default tool setup."""
    
    # Create MCP registry
    registry = MCPToolRegistry()
    
    # Set up default MCP servers if none provided
    if mcp_servers is None:
        mcp_servers = [
            {"id": "filesystem", "type": "filesystem"},
            {"id": "web_search", "type": "web_search"}
        ]
    
    # Create and register MCP clients
    for server_config in mcp_servers:
        client = MCPClient()
        await client.initialize()
        await client.connect_server(server_config["id"], server_config)
        await registry.register_client(server_config["id"], client)
    
    return MCPEnabledAgent(name, role, llm_router, registry)


async def setup_default_mcp_tools() -> MCPToolRegistry:
    """Set up default MCP tools for development."""
    registry = MCPToolRegistry()
    
    # Filesystem tools
    fs_client = MCPClient()
    await fs_client.initialize()
    await fs_client.connect_server("filesystem", {"type": "filesystem"})
    await registry.register_client("filesystem", fs_client)
    
    # Web search tools
    web_client = MCPClient()
    await web_client.initialize()
    await web_client.connect_server("web_search", {"type": "web_search"})
    await registry.register_client("web_search", web_client)
    
    return registry
