"""
Model Context Protocol (MCP) integration for Agent Swarm.

Provides standardized tool calling and resource access across all LLM providers.
"""

from .client import (
    MC<PERSON>lient,
    MCPToolRegistry,
    MCPTool,
    MCPToolCall,
    MCPToolResult,
    MCPResource
)
from .server import MCPServerManager
from .tools import (
    FileSystemTool,
    DatabaseTool,
    WebSearchTool,
    CodeExecutionTool,
    GitTool,
)
from .integration import MCP<PERSON>nabledAgent, MCPRouter, setup_default_mcp_tools

__all__ = [
    # Core MCP components
    "MCPClient",
    "MCPToolRegistry",
    "MCPServerManager",
    "MCPTool",
    "MCPToolCall",
    "MCPToolResult",
    "MCPResource",
    # Built-in tools
    "FileSystemTool",
    "DatabaseTool",
    "WebSearchTool",
    "CodeExecutionTool",
    "GitTool",
    # Agent integration
    "MCPEnabledAgent",
    "MCPRouter",
    "setup_default_mcp_tools",
]
