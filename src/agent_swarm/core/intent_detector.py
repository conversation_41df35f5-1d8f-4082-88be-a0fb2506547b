"""
Intent Detection System for Agent Swarm

Detects user intent from natural language and routes to appropriate agents/tools.
This is the core of the revolutionary agent-swarm concept.
"""

import re
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum


class IntentType(Enum):
    """Types of user intents."""
    SEARCH = "search"
    EDIT_FILE = "edit_file"
    CREATE_FILE = "create_file"
    EXPLAIN_CODE = "explain_code"
    FIX_BUG = "fix_bug"
    IMPLEMENT_FEATURE = "implement_feature"
    REVIEW_CODE = "review_code"
    RUN_COMMAND = "run_command"
    INSTALL_PACKAGE = "install_package"
    HELP = "help"
    CHAT = "chat"
    UNKNOWN = "unknown"


@dataclass
class Intent:
    """Represents a detected user intent."""
    intent_type: IntentType
    confidence: float
    entities: Dict[str, Any]
    suggested_agent: Optional[str] = None
    suggested_tools: List[str] = None
    reasoning: str = ""


class IntentDetector:
    """
    Detects user intent from natural language input.
    
    This is the brain of the agent-swarm system that determines:
    1. What the user wants to do
    2. Which agent should handle it
    3. Which tools are needed
    4. How confident we are
    """
    
    def __init__(self):
        """Initialize the intent detector with patterns and rules."""
        self.patterns = self._build_intent_patterns()
        self.agent_recommendations = self._build_agent_recommendations()
        self.tool_recommendations = self._build_tool_recommendations()
    
    def detect_intent(self, user_input: str) -> Intent:
        """
        Detect intent from user input.
        
        Args:
            user_input: Natural language input from user
            
        Returns:
            Intent object with detected information
        """
        user_input = user_input.strip().lower()
        
        # Check for explicit commands first
        if user_input.startswith('/'):
            return self._handle_explicit_command(user_input)
        
        # Detect natural language intent
        best_intent = None
        best_confidence = 0.0
        
        for intent_type, patterns in self.patterns.items():
            confidence, entities = self._match_patterns(user_input, patterns)
            
            if confidence > best_confidence:
                best_confidence = confidence
                best_intent = Intent(
                    intent_type=intent_type,
                    confidence=confidence,
                    entities=entities,
                    suggested_agent=self.agent_recommendations.get(intent_type),
                    suggested_tools=self.tool_recommendations.get(intent_type, []),
                    reasoning=f"Matched {intent_type.value} patterns with {confidence:.2f} confidence"
                )
        
        # Fallback to chat if no strong intent detected
        if best_confidence < 0.3:
            return Intent(
                intent_type=IntentType.CHAT,
                confidence=0.8,
                entities={"message": user_input},
                suggested_agent="general_agent",
                suggested_tools=["chat"],
                reasoning="No specific intent detected, defaulting to chat"
            )
        
        return best_intent
    
    def _handle_explicit_command(self, command: str) -> Intent:
        """Handle explicit commands like /search, /edit, etc."""
        parts = command[1:].split()
        cmd = parts[0] if parts else ""
        args = parts[1:] if len(parts) > 1 else []
        
        command_map = {
            "search": IntentType.SEARCH,
            "edit": IntentType.EDIT_FILE,
            "create": IntentType.CREATE_FILE,
            "explain": IntentType.EXPLAIN_CODE,
            "fix": IntentType.FIX_BUG,
            "implement": IntentType.IMPLEMENT_FEATURE,
            "review": IntentType.REVIEW_CODE,
            "run": IntentType.RUN_COMMAND,
            "install": IntentType.INSTALL_PACKAGE,
            "help": IntentType.HELP,
        }
        
        intent_type = command_map.get(cmd, IntentType.UNKNOWN)
        
        return Intent(
            intent_type=intent_type,
            confidence=1.0,
            entities={"command": cmd, "args": args},
            suggested_agent=self.agent_recommendations.get(intent_type),
            suggested_tools=self.tool_recommendations.get(intent_type, []),
            reasoning=f"Explicit command: {command}"
        )
    
    def _match_patterns(self, text: str, patterns: List[Dict]) -> Tuple[float, Dict[str, Any]]:
        """Match text against patterns and return confidence + entities."""
        best_confidence = 0.0
        best_entities = {}
        
        for pattern in patterns:
            confidence = 0.0
            entities = {}
            
            # Check keywords
            if "keywords" in pattern:
                keyword_matches = sum(1 for keyword in pattern["keywords"] if keyword in text)
                keyword_confidence = keyword_matches / len(pattern["keywords"])
                confidence += keyword_confidence * 0.6
            
            # Check regex patterns
            if "regex" in pattern:
                for regex_pattern in pattern["regex"]:
                    match = re.search(regex_pattern, text)
                    if match:
                        confidence += 0.4
                        entities.update(match.groupdict())
            
            # Check phrases
            if "phrases" in pattern:
                phrase_matches = sum(1 for phrase in pattern["phrases"] if phrase in text)
                if phrase_matches > 0:
                    confidence += 0.3
            
            # Apply pattern weight
            confidence *= pattern.get("weight", 1.0)
            
            if confidence > best_confidence:
                best_confidence = confidence
                best_entities = entities
        
        return best_confidence, best_entities
    
    def _build_intent_patterns(self) -> Dict[IntentType, List[Dict]]:
        """Build patterns for intent detection."""
        return {
            IntentType.SEARCH: [
                {
                    "keywords": ["search", "find", "look", "locate"],
                    "phrases": ["search for", "find me", "look for", "where is"],
                    "regex": [r"search (?:for )?(?P<query>.+)", r"find (?P<query>.+)"],
                    "weight": 1.0
                }
            ],
            
            IntentType.EDIT_FILE: [
                {
                    "keywords": ["edit", "modify", "change", "update"],
                    "phrases": ["edit file", "modify the", "change this", "update file"],
                    "regex": [r"edit (?P<file>\S+)", r"modify (?P<file>\S+)"],
                    "weight": 1.0
                }
            ],
            
            IntentType.CREATE_FILE: [
                {
                    "keywords": ["create", "make", "new", "generate"],
                    "phrases": ["create file", "make a", "new file", "generate code"],
                    "regex": [r"create (?P<file>\S+)", r"make (?:a )?(?P<type>\w+)"],
                    "weight": 1.0
                }
            ],
            
            IntentType.EXPLAIN_CODE: [
                {
                    "keywords": ["explain", "describe", "what", "how"],
                    "phrases": ["explain this", "what does", "how does", "describe the"],
                    "regex": [r"explain (?P<target>.+)", r"what (?:does|is) (?P<target>.+)"],
                    "weight": 1.0
                }
            ],
            
            IntentType.FIX_BUG: [
                {
                    "keywords": ["fix", "bug", "error", "issue", "problem"],
                    "phrases": ["fix the bug", "there's an error", "something wrong"],
                    "regex": [r"fix (?P<issue>.+)", r"bug in (?P<location>.+)"],
                    "weight": 1.0
                }
            ],
            
            IntentType.IMPLEMENT_FEATURE: [
                {
                    "keywords": ["implement", "add", "feature", "functionality"],
                    "phrases": ["add feature", "implement this", "new functionality"],
                    "regex": [r"implement (?P<feature>.+)", r"add (?P<feature>.+)"],
                    "weight": 1.0
                }
            ],
            
            IntentType.REVIEW_CODE: [
                {
                    "keywords": ["review", "check", "analyze", "audit"],
                    "phrases": ["review code", "check this", "analyze the"],
                    "regex": [r"review (?P<target>.+)", r"check (?P<target>.+)"],
                    "weight": 1.0
                }
            ],
            
            IntentType.RUN_COMMAND: [
                {
                    "keywords": ["run", "execute", "start", "launch"],
                    "phrases": ["run the", "execute this", "start the"],
                    "regex": [r"run (?P<command>.+)", r"execute (?P<command>.+)"],
                    "weight": 1.0
                }
            ],
            
            IntentType.INSTALL_PACKAGE: [
                {
                    "keywords": ["install", "add", "package", "dependency"],
                    "phrases": ["install package", "add dependency", "need to install"],
                    "regex": [r"install (?P<package>\S+)", r"add (?P<package>\S+)"],
                    "weight": 1.0
                }
            ],
            
            IntentType.HELP: [
                {
                    "keywords": ["help", "how", "what", "guide"],
                    "phrases": ["help me", "how do i", "what can", "show me"],
                    "regex": [r"help (?:me )?(?P<topic>.+)?", r"how (?:do i|to) (?P<action>.+)"],
                    "weight": 1.0
                }
            ]
        }
    
    def _build_agent_recommendations(self) -> Dict[IntentType, str]:
        """Build agent recommendations for each intent type."""
        return {
            IntentType.SEARCH: "search_agent",
            IntentType.EDIT_FILE: "coding_agent",
            IntentType.CREATE_FILE: "coding_agent",
            IntentType.EXPLAIN_CODE: "analysis_agent",
            IntentType.FIX_BUG: "debugging_agent",
            IntentType.IMPLEMENT_FEATURE: "development_agent",
            IntentType.REVIEW_CODE: "review_agent",
            IntentType.RUN_COMMAND: "system_agent",
            IntentType.INSTALL_PACKAGE: "package_agent",
            IntentType.HELP: "assistant_agent",
            IntentType.CHAT: "general_agent",
        }
    
    def _build_tool_recommendations(self) -> Dict[IntentType, List[str]]:
        """Build tool recommendations for each intent type."""
        return {
            IntentType.SEARCH: ["search_tool", "context_engine", "file_finder"],
            IntentType.EDIT_FILE: ["file_editor", "diff_tool", "backup_manager"],
            IntentType.CREATE_FILE: ["file_creator", "template_engine", "code_generator"],
            IntentType.EXPLAIN_CODE: ["code_analyzer", "documentation_generator"],
            IntentType.FIX_BUG: ["debugger", "error_analyzer", "test_runner"],
            IntentType.IMPLEMENT_FEATURE: ["code_generator", "file_editor", "test_creator"],
            IntentType.REVIEW_CODE: ["code_reviewer", "linter", "security_scanner"],
            IntentType.RUN_COMMAND: ["command_executor", "process_manager"],
            IntentType.INSTALL_PACKAGE: ["package_manager", "dependency_resolver"],
            IntentType.HELP: ["documentation", "tutorial_generator"],
            IntentType.CHAT: ["conversation_manager", "context_engine"],
        }
    
    def get_intent_explanation(self, intent: Intent) -> str:
        """Get human-readable explanation of detected intent."""
        explanation = f"🎯 **Intent Detected**: {intent.intent_type.value.replace('_', ' ').title()}\n"
        explanation += f"🔍 **Confidence**: {intent.confidence:.1%}\n"
        
        if intent.suggested_agent:
            explanation += f"🤖 **Recommended Agent**: {intent.suggested_agent}\n"
        
        if intent.suggested_tools:
            tools_str = ", ".join(intent.suggested_tools)
            explanation += f"🛠️  **Suggested Tools**: {tools_str}\n"
        
        if intent.entities:
            explanation += f"📋 **Extracted Info**: {intent.entities}\n"
        
        explanation += f"💭 **Reasoning**: {intent.reasoning}"
        
        return explanation
