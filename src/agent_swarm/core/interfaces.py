"""
Granular interfaces for Agent Swarm components.
Implements Interface Segregation Principle with focused, single-purpose interfaces.
"""

from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, AsyncGenerator
from pathlib import Path

from ..backends import Message, LLMResponse


class Initializable(ABC):
    """Interface for components that need initialization."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the component."""
        pass
    
    @abstractmethod
    async def is_ready(self) -> bool:
        """Check if component is ready for use."""
        pass


class Configurable(ABC):
    """Interface for components that can be configured."""
    
    @abstractmethod
    def get_config(self) -> Dict[str, Any]:
        """Get current configuration."""
        pass
    
    @abstractmethod
    def update_config(self, config: Dict[str, Any]) -> None:
        """Update configuration."""
        pass


class HealthCheckable(ABC):
    """Interface for components that support health checks."""
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check and return status."""
        pass


class Monitorable(ABC):
    """Interface for components that provide metrics."""
    
    @abstractmethod
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance and usage metrics."""
        pass
    
    @abstractmethod
    def reset_metrics(self) -> None:
        """Reset metrics counters."""
        pass


class Cacheable(ABC):
    """Interface for components that support caching."""
    
    @abstractmethod
    async def get_from_cache(self, key: str) -> Optional[Any]:
        """Get item from cache."""
        pass
    
    @abstractmethod
    async def put_in_cache(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Put item in cache with optional TTL."""
        pass
    
    @abstractmethod
    async def clear_cache(self) -> None:
        """Clear all cached items."""
        pass


class Streamable(ABC):
    """Interface for components that support streaming operations."""
    
    @abstractmethod
    async def stream_generate(self, *args, **kwargs) -> AsyncGenerator[str, None]:
        """Generate streaming response."""
        pass


class Searchable(ABC):
    """Interface for components that support search operations."""
    
    @abstractmethod
    async def search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for items matching query."""
        pass
    
    @abstractmethod
    async def search_similar(self, item: Any, limit: int = 10) -> List[Dict[str, Any]]:
        """Find items similar to the given item."""
        pass


class Indexable(ABC):
    """Interface for components that can be indexed."""
    
    @abstractmethod
    async def index_content(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Index content and return document ID."""
        pass
    
    @abstractmethod
    async def remove_from_index(self, doc_id: str) -> bool:
        """Remove document from index."""
        pass
    
    @abstractmethod
    async def reindex(self) -> None:
        """Rebuild the entire index."""
        pass


class Persistable(ABC):
    """Interface for components that can persist state."""
    
    @abstractmethod
    async def save_state(self, path: Path) -> None:
        """Save component state to file."""
        pass
    
    @abstractmethod
    async def load_state(self, path: Path) -> None:
        """Load component state from file."""
        pass


class EventEmitter(ABC):
    """Interface for components that emit events."""
    
    @abstractmethod
    def add_listener(self, event: str, callback: callable) -> None:
        """Add event listener."""
        pass
    
    @abstractmethod
    def remove_listener(self, event: str, callback: callable) -> None:
        """Remove event listener."""
        pass
    
    @abstractmethod
    async def emit(self, event: str, data: Any) -> None:
        """Emit event to all listeners."""
        pass


class Validatable(ABC):
    """Interface for components that can validate data."""
    
    @abstractmethod
    def validate(self, data: Any) -> Dict[str, Any]:
        """Validate data and return validation result."""
        pass
    
    @abstractmethod
    def get_schema(self) -> Dict[str, Any]:
        """Get validation schema."""
        pass


class Transformable(ABC):
    """Interface for components that can transform data."""
    
    @abstractmethod
    async def transform(self, input_data: Any) -> Any:
        """Transform input data to output format."""
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """Get list of supported input/output formats."""
        pass


# Composite interfaces for common combinations
class CacheableSearchable(Cacheable, Searchable):
    """Interface for searchable components with caching."""
    pass


class MonitorableHealthCheckable(Monitorable, HealthCheckable):
    """Interface for components with monitoring and health checks."""
    pass


class ConfigurableInitializable(Configurable, Initializable):
    """Interface for configurable components that need initialization."""
    pass


class FullyObservable(Monitorable, HealthCheckable, EventEmitter):
    """Interface for fully observable components."""
    pass