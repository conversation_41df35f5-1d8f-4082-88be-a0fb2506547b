"""
Enhanced Intent Processor - Revolutionary Intent Understanding System.

This module integrates the mathematical algorithm framework with Agent Swarm's
intent detection system, providing unprecedented intent understanding capabilities.

Features:
- AdaptiveIntentProcessor integration for 5-stage processing
- IntentFilteringTriangle for multi-dimensional intent analysis
- Seamless integration with existing IntentDetector
- Enhanced confidence scoring and processing recommendations
- Real-time intent analytics and optimization

This represents the next evolution in AI intent understanding.
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

from .intent_detector import IntentDetector, Intent, IntentType
from ..algorithms import (
    AlgorithmEngine,
    AlgorithmConfig,
    ExecuteAlgorithmAction,
    AdaptiveIntentProcessor,
    IntentFilteringTriangle,
    create_algorithm_engine,
)

logger = logging.getLogger(__name__)


@dataclass
class EnhancedIntent:
    """Enhanced intent with mathematical analysis."""
    # Original intent data
    original_intent: Intent
    
    # Mathematical analysis results
    adaptive_analysis: Optional[Dict[str, Any]] = None
    filtering_analysis: Optional[Dict[str, Any]] = None
    
    # Enhanced metrics
    processing_confidence: float = 0.0
    flow_state: str = "unknown"
    intent_coordinates: Optional[Dict[str, float]] = None
    processing_recommendations: Optional[Dict[str, Any]] = None
    
    # Performance metrics
    processing_time: float = 0.0
    algorithms_used: List[str] = None
    
    # Integration data
    enhanced_reasoning: str = ""
    optimization_suggestions: List[str] = None


class EnhancedIntentProcessor:
    """
    Enhanced Intent Processor with mathematical algorithm integration.
    
    This processor combines traditional pattern-based intent detection with
    revolutionary mathematical algorithms for unprecedented intent understanding.
    """
    
    def __init__(self, enable_adaptive=True, enable_filtering=True, verbose=False):
        """Initialize the enhanced intent processor."""
        self.enable_adaptive = enable_adaptive
        self.enable_filtering = enable_filtering
        self.verbose = verbose
        
        # Initialize traditional intent detector
        self.intent_detector = IntentDetector()
        
        # Initialize mathematical algorithm engines
        self.adaptive_engine = None
        self.filtering_engine = None
        
        # Processing statistics
        self.processing_stats = {
            'total_processed': 0,
            'adaptive_used': 0,
            'filtering_used': 0,
            'average_processing_time': 0.0,
            'confidence_improvements': 0,
        }
        
        # Intent processing history for analytics
        self.processing_history = []
        
    async def initialize(self):
        """Initialize the mathematical algorithm engines."""
        try:
            if self.enable_adaptive:
                # Initialize AdaptiveIntentProcessor
                self.adaptive_engine = create_algorithm_engine(
                    algorithm_name="adaptive_intent_processor",
                    parameters={
                        "enable_early_stopping": True,
                        "confidence_threshold": 0.8,
                        "processing_strategy": "adaptive",
                        "enable_learning": True,
                        "context_weight": 0.3,
                    }
                )
                self.adaptive_engine.registry.register_algorithm(AdaptiveIntentProcessor)
                
                if self.verbose:
                    logger.info("✅ AdaptiveIntentProcessor initialized")
            
            if self.enable_filtering:
                # Initialize IntentFilteringTriangle
                self.filtering_engine = create_algorithm_engine(
                    algorithm_name="intent_filtering_triangle",
                    parameters={
                        "filtering_strategy": "holistic",
                        "enable_symbiotic_intelligence": True,
                        "enable_flow_state_analysis": True,
                        "visualization_detail_level": "detailed",
                    }
                )
                self.filtering_engine.registry.register_algorithm(IntentFilteringTriangle)
                
                if self.verbose:
                    logger.info("✅ IntentFilteringTriangle initialized")
                    
        except Exception as e:
            logger.error(f"Failed to initialize mathematical algorithms: {e}")
            # Graceful degradation - continue with traditional intent detection
            self.enable_adaptive = False
            self.enable_filtering = False
    
    async def process_intent(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> EnhancedIntent:
        """
        Process user intent with enhanced mathematical analysis.
        
        Args:
            user_input: Raw user input text
            context: Optional context information
            
        Returns:
            EnhancedIntent with comprehensive analysis
        """
        start_time = time.time()
        
        try:
            # Step 1: Traditional intent detection
            original_intent = self.intent_detector.detect_intent(user_input)
            
            # Step 2: Enhanced mathematical analysis
            enhanced_intent = EnhancedIntent(
                original_intent=original_intent,
                algorithms_used=[],
                optimization_suggestions=[],
            )
            
            # Step 3: Apply AdaptiveIntentProcessor if enabled
            if self.enable_adaptive and self.adaptive_engine:
                adaptive_analysis = await self._apply_adaptive_processing(
                    user_input, context, enhanced_intent
                )
                enhanced_intent.adaptive_analysis = adaptive_analysis
                enhanced_intent.algorithms_used.append("AdaptiveIntentProcessor")
                self.processing_stats['adaptive_used'] += 1
            
            # Step 4: Apply IntentFilteringTriangle if enabled
            if self.enable_filtering and self.filtering_engine:
                filtering_analysis = await self._apply_filtering_analysis(
                    user_input, context, enhanced_intent
                )
                enhanced_intent.filtering_analysis = filtering_analysis
                enhanced_intent.algorithms_used.append("IntentFilteringTriangle")
                self.processing_stats['filtering_used'] += 1
            
            # Step 5: Synthesize results
            await self._synthesize_analysis(enhanced_intent)
            
            # Step 6: Update statistics
            processing_time = time.time() - start_time
            enhanced_intent.processing_time = processing_time
            self._update_processing_stats(enhanced_intent, processing_time)
            
            if self.verbose:
                logger.info(f"Intent processed in {processing_time:.4f}s with confidence {enhanced_intent.processing_confidence:.2f}")
            
            return enhanced_intent
            
        except Exception as e:
            logger.error(f"Enhanced intent processing failed: {e}")
            
            # Fallback to basic intent
            processing_time = time.time() - start_time
            return EnhancedIntent(
                original_intent=original_intent,
                processing_time=processing_time,
                enhanced_reasoning=f"Fallback to basic intent detection due to error: {e}",
                algorithms_used=["IntentDetector"],
            )
    
    async def _apply_adaptive_processing(
        self, 
        user_input: str, 
        context: Optional[Dict[str, Any]], 
        enhanced_intent: EnhancedIntent
    ) -> Dict[str, Any]:
        """Apply AdaptiveIntentProcessor analysis."""
        try:
            # Prepare input for adaptive processor
            adaptive_input = {
                'intent': user_input,
                'original_intent_type': enhanced_intent.original_intent.intent_type.value,
                'original_confidence': enhanced_intent.original_intent.confidence,
                'context': context or {},
            }
            
            # Execute adaptive processing
            action = ExecuteAlgorithmAction(
                algorithm_name="adaptive_intent_processor",
                input_data=adaptive_input,
            )
            
            observation = await self.adaptive_engine.execute_action(action)
            
            if observation.success:
                return observation.output_data
            else:
                logger.warning(f"AdaptiveIntentProcessor failed: {observation.error}")
                return None
                
        except Exception as e:
            logger.error(f"Adaptive processing error: {e}")
            return None
    
    async def _apply_filtering_analysis(
        self, 
        user_input: str, 
        context: Optional[Dict[str, Any]], 
        enhanced_intent: EnhancedIntent
    ) -> Dict[str, Any]:
        """Apply IntentFilteringTriangle analysis."""
        try:
            # Prepare input for filtering triangle
            filtering_input = {
                'intent': user_input,
                'original_intent_type': enhanced_intent.original_intent.intent_type.value,
                'original_confidence': enhanced_intent.original_intent.confidence,
                'context': context or {},
            }
            
            # Execute filtering analysis
            action = ExecuteAlgorithmAction(
                algorithm_name="intent_filtering_triangle",
                input_data=filtering_input,
            )
            
            observation = await self.filtering_engine.execute_action(action)
            
            if observation.success:
                return observation.output_data
            else:
                logger.warning(f"IntentFilteringTriangle failed: {observation.error}")
                return None
                
        except Exception as e:
            logger.error(f"Filtering analysis error: {e}")
            return None
    
    async def _synthesize_analysis(self, enhanced_intent: EnhancedIntent):
        """Synthesize results from all analysis methods."""
        # Start with original confidence
        confidence_scores = [enhanced_intent.original_intent.confidence]
        reasoning_parts = [enhanced_intent.original_intent.reasoning]
        
        # Integrate adaptive analysis
        if enhanced_intent.adaptive_analysis:
            adaptive_confidence = enhanced_intent.adaptive_analysis.get('final_confidence', 0.0)
            confidence_scores.append(adaptive_confidence)
            
            # Extract flow information
            if 'early_stopped' in enhanced_intent.adaptive_analysis:
                early_stopped = enhanced_intent.adaptive_analysis['early_stopped']
                stages_executed = enhanced_intent.adaptive_analysis.get('stages_executed', 0)
                reasoning_parts.append(
                    f"Adaptive processing: {stages_executed} stages, "
                    f"{'early stopped' if early_stopped else 'full pipeline'}"
                )
        
        # Integrate filtering analysis
        if enhanced_intent.filtering_analysis:
            filtering_confidence = enhanced_intent.filtering_analysis.get('holistic_score', {}).get('overall_confidence', 0.0)
            confidence_scores.append(filtering_confidence)
            
            # Extract 3D coordinates
            enhanced_intent.intent_coordinates = enhanced_intent.filtering_analysis.get('intent_coordinates')
            
            # Extract flow state
            flow_analysis = enhanced_intent.filtering_analysis.get('flow_state_analysis', {})
            enhanced_intent.flow_state = flow_analysis.get('flow_state', 'unknown')
            
            # Extract processing recommendations
            enhanced_intent.processing_recommendations = enhanced_intent.filtering_analysis.get('filtering_recommendations')
            
            # Extract optimization suggestions
            if flow_analysis.get('recommendations'):
                enhanced_intent.optimization_suggestions.extend(flow_analysis['recommendations'])
            
            reasoning_parts.append(
                f"3D filtering: {enhanced_intent.flow_state} flow state, "
                f"coordinates ({enhanced_intent.intent_coordinates.get('clarity', 0):.2f}, "
                f"{enhanced_intent.intent_coordinates.get('complexity', 0):.2f}, "
                f"{enhanced_intent.intent_coordinates.get('context', 0):.2f})"
            )
        
        # Calculate enhanced confidence (weighted average with bias toward higher scores)
        if len(confidence_scores) > 1:
            # Weight: original 30%, adaptive 40%, filtering 30%
            weights = [0.3, 0.4, 0.3][:len(confidence_scores)]
            enhanced_intent.processing_confidence = sum(
                score * weight for score, weight in zip(confidence_scores, weights)
            ) / sum(weights)
            
            # Check for confidence improvement
            if enhanced_intent.processing_confidence > enhanced_intent.original_intent.confidence:
                self.processing_stats['confidence_improvements'] += 1
        else:
            enhanced_intent.processing_confidence = confidence_scores[0]
        
        # Combine reasoning
        enhanced_intent.enhanced_reasoning = " | ".join(reasoning_parts)
    
    def _update_processing_stats(self, enhanced_intent: EnhancedIntent, processing_time: float):
        """Update processing statistics."""
        self.processing_stats['total_processed'] += 1
        
        # Update average processing time
        total = self.processing_stats['total_processed']
        current_avg = self.processing_stats['average_processing_time']
        self.processing_stats['average_processing_time'] = (
            (current_avg * (total - 1) + processing_time) / total
        )
        
        # Store in history (keep last 100)
        history_entry = {
            'timestamp': datetime.now().isoformat(),
            'user_input': enhanced_intent.original_intent.entities.get('message', ''),
            'original_confidence': enhanced_intent.original_intent.confidence,
            'enhanced_confidence': enhanced_intent.processing_confidence,
            'processing_time': processing_time,
            'algorithms_used': enhanced_intent.algorithms_used,
            'flow_state': enhanced_intent.flow_state,
        }
        
        self.processing_history.append(history_entry)
        if len(self.processing_history) > 100:
            self.processing_history.pop(0)
    
    def get_processing_analytics(self) -> Dict[str, Any]:
        """Get comprehensive processing analytics."""
        if not self.processing_history:
            return {'message': 'No processing history available'}
        
        # Calculate analytics
        total_processed = self.processing_stats['total_processed']
        confidence_improvements = self.processing_stats['confidence_improvements']
        
        # Recent performance
        recent_entries = self.processing_history[-10:] if len(self.processing_history) >= 10 else self.processing_history
        recent_avg_time = sum(entry['processing_time'] for entry in recent_entries) / len(recent_entries)
        recent_avg_confidence = sum(entry['enhanced_confidence'] for entry in recent_entries) / len(recent_entries)
        
        # Algorithm usage
        algorithm_usage = {}
        for entry in self.processing_history:
            for algorithm in entry['algorithms_used']:
                algorithm_usage[algorithm] = algorithm_usage.get(algorithm, 0) + 1
        
        # Flow state distribution
        flow_states = [entry['flow_state'] for entry in self.processing_history if entry['flow_state'] != 'unknown']
        flow_distribution = {}
        for state in flow_states:
            flow_distribution[state] = flow_distribution.get(state, 0) + 1
        
        return {
            'total_processed': total_processed,
            'confidence_improvement_rate': confidence_improvements / max(1, total_processed),
            'average_processing_time': self.processing_stats['average_processing_time'],
            'recent_average_time': recent_avg_time,
            'recent_average_confidence': recent_avg_confidence,
            'algorithm_usage': algorithm_usage,
            'flow_state_distribution': flow_distribution,
            'adaptive_usage_rate': self.processing_stats['adaptive_used'] / max(1, total_processed),
            'filtering_usage_rate': self.processing_stats['filtering_used'] / max(1, total_processed),
        }
    
    def get_intent_explanation(self, enhanced_intent: EnhancedIntent) -> str:
        """Get comprehensive explanation of enhanced intent analysis."""
        explanation = []
        
        # Original intent information
        original = enhanced_intent.original_intent
        explanation.append(f"🎯 **Intent**: {original.intent_type.value.replace('_', ' ').title()}")
        explanation.append(f"🔍 **Enhanced Confidence**: {enhanced_intent.processing_confidence:.1%}")
        
        # 3D coordinates if available
        if enhanced_intent.intent_coordinates:
            coords = enhanced_intent.intent_coordinates
            explanation.append(
                f"📍 **3D Coordinates**: Clarity {coords['clarity']:.2f}, "
                f"Complexity {coords['complexity']:.2f}, Context {coords['context']:.2f}"
            )
        
        # Flow state
        if enhanced_intent.flow_state != 'unknown':
            explanation.append(f"🌊 **Flow State**: {enhanced_intent.flow_state}")
        
        # Processing recommendations
        if enhanced_intent.processing_recommendations:
            rec = enhanced_intent.processing_recommendations
            explanation.append(f"⚡ **Processing**: {rec.get('primary_recommendation', 'standard')}")
            explanation.append(f"🎯 **Priority**: {rec.get('processing_priority', 'normal')}")
        
        # Algorithms used
        if enhanced_intent.algorithms_used:
            algorithms = ", ".join(enhanced_intent.algorithms_used)
            explanation.append(f"🧠 **Algorithms**: {algorithms}")
        
        # Performance
        explanation.append(f"⏱️ **Processing Time**: {enhanced_intent.processing_time:.4f}s")
        
        # Enhanced reasoning
        explanation.append(f"💭 **Analysis**: {enhanced_intent.enhanced_reasoning}")
        
        # Optimization suggestions
        if enhanced_intent.optimization_suggestions:
            suggestions = enhanced_intent.optimization_suggestions[:3]  # Show top 3
            explanation.append(f"💡 **Suggestions**: {'; '.join(suggestions)}")
        
        return "\n".join(explanation)
