"""
Centralized configuration management for Agent Swarm.
Provides hierarchical configuration with validation, environment variable support,
and hot reloading capabilities.
"""

import os
import json
import yaml
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Type, get_type_hints
from dataclasses import dataclass, field
from enum import Enum
import asyncio
from datetime import datetime

from pydantic import BaseModel, Field, validator
from ..utils.logging import get_logger
from .events import emit_event, EventPriority

logger = get_logger("core.config")


class ConfigFormat(Enum):
    """Supported configuration file formats."""
    JSON = "json"
    YAML = "yaml"
    ENV = "env"


class ConfigScope(Enum):
    """Configuration scope levels."""
    GLOBAL = "global"
    PROJECT = "project"
    USER = "user"
    RUNTIME = "runtime"


@dataclass
class ConfigSource:
    """Configuration source metadata."""
    path: Optional[Path] = None
    format: Optional[ConfigFormat] = None
    scope: ConfigScope = ConfigScope.RUNTIME
    priority: int = 0  # Higher priority overrides lower
    last_modified: Optional[datetime] = None
    is_watched: bool = False


class BaseConfig(BaseModel):
    """Base configuration model with common settings."""
    
    class Config:
        extra = "allow"  # Allow additional fields
        validate_assignment = True
    
    # Metadata
    config_version: str = Field(default="1.0", description="Configuration version")
    last_updated: datetime = Field(default_factory=datetime.now)
    
    # Core settings
    debug: bool = Field(default=False, description="Enable debug mode")
    log_level: str = Field(default="INFO", description="Logging level")
    
    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"log_level must be one of {valid_levels}")
        return v.upper()


class LLMConfig(BaseConfig):
    """LLM-specific configuration."""
    
    # Provider settings
    default_provider: str = Field(default="ollama", description="Default LLM provider")
    ollama_base_url: str = Field(default="http://localhost:11434", description="Ollama server URL")
    
    # API keys (will be loaded from environment)
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API key")
    anthropic_api_key: Optional[str] = Field(default=None, description="Anthropic API key")
    google_api_key: Optional[str] = Field(default=None, description="Google API key")
    
    # Model settings
    default_model: str = Field(default="deepseek-r1:7b", description="Default model")
    max_tokens: int = Field(default=4096, description="Maximum tokens per request")
    temperature: float = Field(default=0.7, description="Default temperature")
    
    # Performance settings
    request_timeout: int = Field(default=120, description="Request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    
    @validator('temperature')
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError("temperature must be between 0.0 and 2.0")
        return v


class RAGConfig(BaseConfig):
    """RAG system configuration."""
    
    # Vector store settings
    vector_store: str = Field(default="memory", description="Vector store backend")
    embedding_model: str = Field(default="sentence-transformers/all-MiniLM-L6-v2")
    
    # Chunking settings
    chunk_size: int = Field(default=1000, description="Text chunk size")
    chunk_overlap: int = Field(default=200, description="Chunk overlap size")
    
    # Search settings
    max_results: int = Field(default=10, description="Maximum search results")
    similarity_threshold: float = Field(default=0.7, description="Similarity threshold")
    
    # Indexing settings
    auto_index: bool = Field(default=True, description="Auto-index project files")
    index_patterns: List[str] = Field(
        default=["*.py", "*.md", "*.txt", "*.json"],
        description="File patterns to index"
    )
    exclude_patterns: List[str] = Field(
        default=["*.pyc", "__pycache__", ".git", "node_modules"],
        description="Patterns to exclude from indexing"
    )


class CLIConfig(BaseConfig):
    """CLI-specific configuration."""
    
    # Interface settings
    theme: str = Field(default="dark", description="CLI theme")
    auto_complete: bool = Field(default=True, description="Enable auto-completion")
    history_size: int = Field(default=1000, description="Command history size")
    
    # Safety settings
    safe_mode: bool = Field(default=True, description="Enable safe mode for commands")
    confirm_destructive: bool = Field(default=True, description="Confirm destructive operations")
    
    # Performance settings
    command_timeout: int = Field(default=30, description="Command timeout in seconds")
    max_output_lines: int = Field(default=1000, description="Maximum output lines to display")


class CacheConfig(BaseConfig):
    """Cache system configuration."""
    
    # Backend settings
    backend: str = Field(default="memory", description="Cache backend")
    memory_max_size: int = Field(default=1000, description="Memory cache max entries")
    disk_max_size_mb: int = Field(default=100, description="Disk cache max size in MB")
    
    # TTL settings
    default_ttl: Optional[int] = Field(default=3600, description="Default TTL in seconds")
    llm_response_ttl: int = Field(default=1800, description="LLM response cache TTL")
    context_ttl: int = Field(default=7200, description="Context cache TTL")
    
    # Cache directory
    cache_dir: str = Field(default=".cache", description="Cache directory path")


class AgentSwarmConfig(BaseConfig):
    """Main Agent Swarm configuration."""
    
    # Sub-configurations
    llm: LLMConfig = Field(default_factory=LLMConfig)
    rag: RAGConfig = Field(default_factory=RAGConfig)
    cli: CLIConfig = Field(default_factory=CLIConfig)
    cache: CacheConfig = Field(default_factory=CacheConfig)
    
    # Global settings
    project_path: Optional[str] = Field(default=None, description="Current project path")
    data_dir: str = Field(default="~/.agent-swarm", description="Data directory")
    
    # Feature flags
    enable_mcp: bool = Field(default=True, description="Enable MCP integration")
    enable_rag: bool = Field(default=True, description="Enable RAG system")
    enable_caching: bool = Field(default=True, description="Enable caching")
    enable_events: bool = Field(default=True, description="Enable event system")


class ConfigManager:
    """Centralized configuration manager with hot reloading."""
    
    def __init__(self):
        self._config: AgentSwarmConfig = AgentSwarmConfig()
        self._sources: List[ConfigSource] = []
        self._watchers: Dict[Path, asyncio.Task] = {}
        self._lock = asyncio.Lock()
        
        # Load default configuration
        self._load_defaults()
    
    def _load_defaults(self) -> None:
        """Load default configuration values."""
        # Load from environment variables
        self._load_from_env()
        
        # Load from standard config files
        config_paths = [
            Path.home() / ".agent-swarm" / "config.yaml",
            Path.cwd() / ".agent-swarm.yaml",
            Path.cwd() / "agent-swarm.json",
        ]
        
        for config_path in config_paths:
            if config_path.exists():
                self._load_from_file(config_path)
    
    def _load_from_env(self) -> None:
        """Load configuration from environment variables."""
        env_mapping = {
            "AGENT_SWARM_DEBUG": ("debug", bool),
            "AGENT_SWARM_LOG_LEVEL": ("log_level", str),
            "AGENT_SWARM_PROJECT_PATH": ("project_path", str),
            "AGENT_SWARM_DATA_DIR": ("data_dir", str),
            
            # LLM settings
            "AGENT_SWARM_DEFAULT_PROVIDER": ("llm.default_provider", str),
            "AGENT_SWARM_OLLAMA_URL": ("llm.ollama_base_url", str),
            "OPENAI_API_KEY": ("llm.openai_api_key", str),
            "ANTHROPIC_API_KEY": ("llm.anthropic_api_key", str),
            "GOOGLE_API_KEY": ("llm.google_api_key", str),
            
            # RAG settings
            "AGENT_SWARM_VECTOR_STORE": ("rag.vector_store", str),
            "AGENT_SWARM_CHUNK_SIZE": ("rag.chunk_size", int),
            "AGENT_SWARM_AUTO_INDEX": ("rag.auto_index", bool),
            
            # CLI settings
            "AGENT_SWARM_SAFE_MODE": ("cli.safe_mode", bool),
            "AGENT_SWARM_THEME": ("cli.theme", str),
            
            # Cache settings
            "AGENT_SWARM_CACHE_BACKEND": ("cache.backend", str),
            "AGENT_SWARM_CACHE_DIR": ("cache.cache_dir", str),
        }
        
        for env_var, (config_path, config_type) in env_mapping.items():
            value = os.getenv(env_var)
            if value is not None:
                # Convert value to appropriate type
                if config_type == bool:
                    value = value.lower() in ('true', '1', 'yes', 'on')
                elif config_type == int:
                    try:
                        value = int(value)
                    except ValueError:
                        logger.warning(f"Invalid integer value for {env_var}: {value}")
                        continue
                
                # Set nested configuration value
                self._set_nested_value(config_path, value)
        
        # Add environment source
        env_source = ConfigSource(
            format=ConfigFormat.ENV,
            scope=ConfigScope.GLOBAL,
            priority=100  # High priority for env vars
        )
        self._sources.append(env_source)
    
    def _load_from_file(self, file_path: Path) -> None:
        """Load configuration from file."""
        try:
            if file_path.suffix.lower() in ['.yaml', '.yml']:
                with open(file_path, 'r') as f:
                    data = yaml.safe_load(f)
                format_type = ConfigFormat.YAML
            elif file_path.suffix.lower() == '.json':
                with open(file_path, 'r') as f:
                    data = json.load(f)
                format_type = ConfigFormat.JSON
            else:
                logger.warning(f"Unsupported config file format: {file_path}")
                return
            
            # Merge configuration
            self._merge_config(data)
            
            # Add source
            source = ConfigSource(
                path=file_path,
                format=format_type,
                scope=self._determine_scope(file_path),
                priority=self._determine_priority(file_path),
                last_modified=datetime.fromtimestamp(file_path.stat().st_mtime)
            )
            self._sources.append(source)
            
            logger.info(f"Loaded configuration from {file_path}")
            
        except Exception as e:
            logger.error(f"Error loading config from {file_path}: {e}")
    
    def _determine_scope(self, file_path: Path) -> ConfigScope:
        """Determine configuration scope based on file location."""
        if file_path.is_relative_to(Path.home()):
            return ConfigScope.USER
        elif file_path.is_relative_to(Path.cwd()):
            return ConfigScope.PROJECT
        else:
            return ConfigScope.GLOBAL
    
    def _determine_priority(self, file_path: Path) -> int:
        """Determine configuration priority based on scope."""
        scope = self._determine_scope(file_path)
        priority_map = {
            ConfigScope.GLOBAL: 10,
            ConfigScope.USER: 20,
            ConfigScope.PROJECT: 30,
            ConfigScope.RUNTIME: 40
        }
        return priority_map.get(scope, 0)
    
    def _set_nested_value(self, path: str, value: Any) -> None:
        """Set nested configuration value using dot notation."""
        keys = path.split('.')
        current = self._config
        
        for key in keys[:-1]:
            if hasattr(current, key):
                current = getattr(current, key)
            else:
                logger.warning(f"Invalid config path: {path}")
                return
        
        final_key = keys[-1]
        if hasattr(current, final_key):
            setattr(current, final_key, value)
        else:
            logger.warning(f"Invalid config key: {final_key}")
    
    def _merge_config(self, data: Dict[str, Any]) -> None:
        """Merge configuration data into current config."""
        try:
            # Create new config instance with merged data
            current_dict = self._config.dict()
            merged_dict = self._deep_merge(current_dict, data)
            self._config = AgentSwarmConfig(**merged_dict)
        except Exception as e:
            logger.error(f"Error merging configuration: {e}")
    
    def _deep_merge(self, base: Dict[str, Any], update: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries."""
        result = base.copy()
        
        for key, value in update.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    async def watch_file(self, file_path: Path) -> None:
        """Watch configuration file for changes."""
        if file_path in self._watchers:
            return  # Already watching
        
        async def file_watcher():
            last_modified = file_path.stat().st_mtime if file_path.exists() else 0
            
            while True:
                try:
                    await asyncio.sleep(1)  # Check every second
                    
                    if not file_path.exists():
                        continue
                    
                    current_modified = file_path.stat().st_mtime
                    if current_modified > last_modified:
                        logger.info(f"Configuration file changed: {file_path}")
                        await self.reload_from_file(file_path)
                        last_modified = current_modified
                        
                        # Emit configuration change event
                        await emit_event(
                            "config.file_changed",
                            data={"file_path": str(file_path)},
                            source="config_manager",
                            priority=EventPriority.HIGH
                        )
                        
                except Exception as e:
                    logger.error(f"Error watching config file {file_path}: {e}")
                    await asyncio.sleep(5)  # Wait longer on error
        
        # Start watcher task
        task = asyncio.create_task(file_watcher())
        self._watchers[file_path] = task
        
        # Mark source as watched
        for source in self._sources:
            if source.path == file_path:
                source.is_watched = True
                break
    
    async def reload_from_file(self, file_path: Path) -> None:
        """Reload configuration from specific file."""
        async with self._lock:
            # Remove old source
            self._sources = [s for s in self._sources if s.path != file_path]
            
            # Reload from file
            self._load_from_file(file_path)
            
            # Emit reload event
            await emit_event(
                "config.reloaded",
                data={"file_path": str(file_path)},
                source="config_manager",
                priority=EventPriority.HIGH
            )
    
    async def save_to_file(self, file_path: Path, format_type: ConfigFormat = ConfigFormat.YAML) -> None:
        """Save current configuration to file."""
        try:
            config_dict = self._config.dict()
            
            if format_type == ConfigFormat.YAML:
                with open(file_path, 'w') as f:
                    yaml.dump(config_dict, f, default_flow_style=False, indent=2)
            elif format_type == ConfigFormat.JSON:
                with open(file_path, 'w') as f:
                    json.dump(config_dict, f, indent=2, default=str)
            else:
                raise ValueError(f"Unsupported format: {format_type}")
            
            logger.info(f"Saved configuration to {file_path}")
            
        except Exception as e:
            logger.error(f"Error saving config to {file_path}: {e}")
            raise
    
    def get_config(self) -> AgentSwarmConfig:
        """Get current configuration."""
        return self._config
    
    def get_llm_config(self) -> LLMConfig:
        """Get LLM configuration."""
        return self._config.llm
    
    def get_rag_config(self) -> RAGConfig:
        """Get RAG configuration."""
        return self._config.rag
    
    def get_cli_config(self) -> CLIConfig:
        """Get CLI configuration."""
        return self._config.cli
    
    def get_cache_config(self) -> CacheConfig:
        """Get cache configuration."""
        return self._config.cache
    
    def update_config(self, **kwargs) -> None:
        """Update configuration values."""
        try:
            current_dict = self._config.dict()
            updated_dict = self._deep_merge(current_dict, kwargs)
            self._config = AgentSwarmConfig(**updated_dict)
            
            # Emit update event
            asyncio.create_task(emit_event(
                "config.updated",
                data=kwargs,
                source="config_manager",
                priority=EventPriority.NORMAL
            ))
            
        except Exception as e:
            logger.error(f"Error updating configuration: {e}")
            raise
    
    def get_sources(self) -> List[ConfigSource]:
        """Get list of configuration sources."""
        return self._sources.copy()
    
    async def cleanup(self) -> None:
        """Cleanup watchers and resources."""
        for task in self._watchers.values():
            task.cancel()
        
        self._watchers.clear()


# Global configuration manager instance
_global_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """Get global configuration manager instance."""
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = ConfigManager()
    return _global_config_manager


def get_config() -> AgentSwarmConfig:
    """Get current global configuration."""
    return get_config_manager().get_config()


def get_llm_config() -> LLMConfig:
    """Get LLM configuration."""
    return get_config_manager().get_llm_config()


def get_rag_config() -> RAGConfig:
    """Get RAG configuration."""
    return get_config_manager().get_rag_config()


def get_cli_config() -> CLIConfig:
    """Get CLI configuration."""
    return get_config_manager().get_cli_config()


def get_cache_config() -> CacheConfig:
    """Get cache configuration."""
    return get_config_manager().get_cache_config()