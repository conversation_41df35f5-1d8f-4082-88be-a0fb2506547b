"""
Multi-Step Agent Orchestrator

Implements intelligent multi-step reasoning and chained tool calls.
Combines the best of ReAct and Plan-and-Execute patterns.
"""

import asyncio
import json
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime


class StepType(Enum):
    """Types of execution steps."""
    THINK = "think"          # Reasoning step
    SEARCH = "search"        # Search for information
    ANALYZE = "analyze"      # Analyze found information
    PLAN = "plan"           # Create execution plan
    EXECUTE = "execute"     # Execute action
    VALIDATE = "validate"   # Validate results
    REFLECT = "reflect"     # Reflect on progress


class StepStatus(Enum):
    """Status of execution steps."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class ExecutionStep:
    """A single step in multi-step execution."""
    step_id: str
    step_type: StepType
    description: str
    action: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    status: StepStatus = StepStatus.PENDING
    result: Optional[Any] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    reasoning: str = ""


@dataclass
class ExecutionPlan:
    """Complete execution plan for multi-step task."""
    plan_id: str
    goal: str
    steps: List[ExecutionStep]
    context: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    status: str = "created"


class MultiStepOrchestrator:
    """
    Orchestrates multi-step agent execution with intelligent reasoning.

    Features:
    - Automatic dependency discovery (@file imports)
    - Multi-step planning and execution
    - Context accumulation across steps
    - Intelligent tool chaining
    - Error recovery and replanning
    """

    def __init__(self, shell_instance=None):
        """Initialize the orchestrator."""
        self.shell = shell_instance
        self.active_plans: Dict[str, ExecutionPlan] = {}
        self.execution_history: List[ExecutionPlan] = []

        # Initialize query analyzer for intelligent chunking
        from .query_analyzer import QueryAnalyzer
        self.query_analyzer = QueryAnalyzer()

        # Available tools for execution
        self.tools = {
            "search": self._tool_search,
            "analyze_file": self._tool_analyze_file,
            "edit_file": self._tool_edit_file,
            "create_file": self._tool_create_file,
            "find_dependencies": self._tool_find_dependencies,
            "explain_code": self._tool_explain_code,
            "run_command": self._tool_run_command,
        }

    async def execute_multi_step(self, user_request: str) -> Dict[str, Any]:
        """
        Execute a multi-step request with intelligent planning.

        Args:
            user_request: Natural language request from user

        Returns:
            Execution result with all steps and context
        """
        # Step 1: Analyze request and determine if multi-step is needed
        analysis = await self._analyze_request(user_request)

        if not analysis["needs_multi_step"]:
            # Simple single-step execution
            return await self._execute_single_step(user_request, analysis)

        # Step 2: Create execution plan
        plan = await self._create_execution_plan(user_request, analysis)

        # Step 3: Execute plan with context accumulation
        result = await self._execute_plan(plan)

        return result

    async def _analyze_request(self, request: str) -> Dict[str, Any]:
        """Analyze request to determine execution strategy using intelligent chunking."""

        # Use query analyzer for intelligent analysis
        extracted = self.query_analyzer.analyze_query(request)

        # Determine if multi-step is needed based on extracted information
        needs_multi_step = (
            extracted.complexity_score > 0.3 or
            len(extracted.chunks) > 1 or
            len(extracted.dependencies) > 0 or
            len(extracted.target_files) > 1 or
            len(extracted.technologies) > 1 or
            extracted.primary_action in ['create', 'refactor', 'analyze']
        )

        # Determine request type from extracted information
        request_type = self._determine_request_type(extracted)

        # Get all mentioned files (target + context)
        mentioned_files = extracted.target_files + extracted.context_files

        return {
            "needs_multi_step": needs_multi_step,
            "request_type": request_type,
            "mentioned_files": mentioned_files,
            "complexity": self._get_complexity_level(extracted.complexity_score),
            "estimated_steps": extracted.estimated_steps,
            "extracted_info": extracted,  # Include full extracted information
            "chunks": [
                {
                    "id": chunk.chunk_id,
                    "text": chunk.text,
                    "type": chunk.query_type.value,
                    "priority": chunk.priority.value,
                    "actions": chunk.actions,
                    "files": chunk.files,
                    "technologies": chunk.technologies
                }
                for chunk in extracted.chunks
            ]
        }

    def _determine_request_type(self, extracted) -> str:
        """Determine request type from extracted information."""
        primary_action = extracted.primary_action
        technologies = extracted.technologies

        # Project creation patterns
        if primary_action == "create" and any(tech in technologies for tech in ['react', 'vue', 'angular']):
            return "project_creation"
        elif primary_action == "create" and "project" in extracted.original_query.lower():
            return "project_creation"

        # TypeScript editing patterns
        elif primary_action == "edit" and "typescript" in technologies:
            return "typescript_editing"
        elif "types" in extracted.original_query.lower() and primary_action == "edit":
            return "typescript_editing"

        # Analysis patterns
        elif primary_action in ["analyze", "explain"] or "how do" in extracted.original_query.lower():
            if "calculate" in extracted.original_query.lower():
                return "analysis"
            else:
                return "explanation"

        # Search patterns
        elif primary_action == "search":
            return "search"

        # Default based on primary action
        return primary_action if primary_action else "unknown"

    def _get_complexity_level(self, score: float) -> str:
        """Convert complexity score to level."""
        if score > 0.7:
            return "very_high"
        elif score > 0.5:
            return "high"
        elif score > 0.3:
            return "medium"
        elif score > 0.1:
            return "low"
        else:
            return "very_low"

    def _estimate_steps(self, request: str, request_type: str) -> int:
        """Estimate number of steps needed."""
        if request_type == "project_creation":
            return 5  # Plan, create structure, create files, configure, validate
        elif request_type == "typescript_editing":
            return 3  # Analyze types, edit file, update types
        elif request_type == "analysis":
            return 4  # Search, collect context, analyze, explain
        else:
            return 2  # Basic multi-step

    async def _create_execution_plan(self, request: str, analysis: Dict[str, Any]) -> ExecutionPlan:
        """Create detailed execution plan based on request analysis."""

        plan_id = f"plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        steps = []

        if analysis["request_type"] == "project_creation":
            steps = await self._plan_project_creation(request)
        elif analysis["request_type"] == "typescript_editing":
            steps = await self._plan_typescript_editing(request, analysis["mentioned_files"])
        elif analysis["request_type"] == "analysis":
            steps = await self._plan_analysis(request, analysis["mentioned_files"])
        elif analysis["request_type"] == "explanation":
            steps = await self._plan_explanation(request, analysis["mentioned_files"])
        else:
            # Generic multi-step plan
            steps = await self._plan_generic(request, analysis)

        plan = ExecutionPlan(
            plan_id=plan_id,
            goal=request,
            steps=steps,
            context={"analysis": analysis}
        )

        self.active_plans[plan_id] = plan
        return plan

    async def _plan_analysis(self, request: str, mentioned_files: List[str]) -> List[ExecutionStep]:
        """Plan for analysis requests like 'how do we calculate the best agent'."""
        steps = []

        # Step 1: Search for relevant code
        steps.append(ExecutionStep(
            step_id="search_1",
            step_type=StepType.SEARCH,
            description="Search for relevant code and files",
            action="search",
            parameters={
                "query": self._extract_search_terms(request),
                "file_types": [".py", ".js", ".ts"],
                "include_patterns": ["agent", "calculate", "best", "score"]
            }
        ))

        # Step 2: Find dependencies for mentioned files
        if mentioned_files:
            steps.append(ExecutionStep(
                step_id="deps_1",
                step_type=StepType.ANALYZE,
                description="Find dependencies and imports",
                action="find_dependencies",
                parameters={"files": mentioned_files},
                dependencies=["search_1"]
            ))

        # Step 3: Analyze collected files
        steps.append(ExecutionStep(
            step_id="analyze_1",
            step_type=StepType.ANALYZE,
            description="Analyze code and understand the calculation logic",
            action="analyze_file",
            parameters={"focus": "calculation logic, algorithms, scoring"},
            dependencies=["search_1"] + (["deps_1"] if mentioned_files else [])
        ))

        # Step 4: Provide comprehensive explanation
        steps.append(ExecutionStep(
            step_id="explain_1",
            step_type=StepType.EXECUTE,
            description="Provide detailed explanation of the calculation",
            action="explain_code",
            parameters={"format": "detailed", "include_examples": True},
            dependencies=["analyze_1"]
        ))

        return steps

    async def _plan_typescript_editing(self, request: str, mentioned_files: List[str]) -> List[ExecutionStep]:
        """Plan for TypeScript editing with type updates."""
        steps = []

        # Step 1: Analyze current TypeScript file and types
        steps.append(ExecutionStep(
            step_id="analyze_ts",
            step_type=StepType.ANALYZE,
            description="Analyze TypeScript file and type definitions",
            action="analyze_file",
            parameters={
                "files": mentioned_files,
                "focus": "types, interfaces, imports"
            }
        ))

        # Step 2: Find related type files
        steps.append(ExecutionStep(
            step_id="find_types",
            step_type=StepType.SEARCH,
            description="Find related type definition files",
            action="find_dependencies",
            parameters={
                "files": mentioned_files,
                "include_types": True,
                "extensions": [".d.ts", ".ts"]
            },
            dependencies=["analyze_ts"]
        ))

        # Step 3: Edit the main file
        steps.append(ExecutionStep(
            step_id="edit_main",
            step_type=StepType.EXECUTE,
            description="Edit the main TypeScript file",
            action="edit_file",
            parameters={"preserve_types": True},
            dependencies=["analyze_ts", "find_types"]
        ))

        # Step 4: Update type definitions
        steps.append(ExecutionStep(
            step_id="update_types",
            step_type=StepType.EXECUTE,
            description="Update related type definitions",
            action="edit_file",
            parameters={"update_types": True},
            dependencies=["edit_main"]
        ))

        return steps

    async def _plan_project_creation(self, request: str) -> List[ExecutionStep]:
        """Plan for complete project creation."""
        steps = []

        # Step 1: Create project plan
        steps.append(ExecutionStep(
            step_id="plan_project",
            step_type=StepType.PLAN,
            description="Create detailed project structure plan",
            action="create_plan",
            parameters={"request": request}
        ))

        # Step 2: Create directory structure
        steps.append(ExecutionStep(
            step_id="create_structure",
            step_type=StepType.EXECUTE,
            description="Create project directory structure",
            action="create_directories",
            dependencies=["plan_project"]
        ))

        # Step 3: Create core files
        steps.append(ExecutionStep(
            step_id="create_core",
            step_type=StepType.EXECUTE,
            description="Create core project files",
            action="create_file",
            parameters={"file_type": "core"},
            dependencies=["create_structure"]
        ))

        # Step 4: Create configuration files
        steps.append(ExecutionStep(
            step_id="create_config",
            step_type=StepType.EXECUTE,
            description="Create configuration files",
            action="create_file",
            parameters={"file_type": "config"},
            dependencies=["create_core"]
        ))

        # Step 5: Validate project
        steps.append(ExecutionStep(
            step_id="validate_project",
            step_type=StepType.VALIDATE,
            description="Validate project structure and files",
            action="validate_project",
            dependencies=["create_config"]
        ))

        return steps

    async def _plan_explanation(self, request: str, mentioned_files: List[str]) -> List[ExecutionStep]:
        """Plan for explanation requests with dependency discovery."""
        steps = []

        # Step 1: Analyze mentioned files
        if mentioned_files:
            steps.append(ExecutionStep(
                step_id="analyze_main",
                step_type=StepType.ANALYZE,
                description="Analyze main files mentioned in request",
                action="analyze_file",
                parameters={"files": mentioned_files}
            ))

            # Step 2: Find and add dependencies
            steps.append(ExecutionStep(
                step_id="find_deps",
                step_type=StepType.SEARCH,
                description="Find imported modules and dependencies",
                action="find_dependencies",
                parameters={"files": mentioned_files, "recursive": True},
                dependencies=["analyze_main"]
            ))

            # Step 3: Explain with full context
            steps.append(ExecutionStep(
                step_id="explain_full",
                step_type=StepType.EXECUTE,
                description="Provide comprehensive explanation with context",
                action="explain_code",
                parameters={"include_dependencies": True, "detailed": True},
                dependencies=["find_deps"]
            ))
        else:
            # Simple explanation without dependencies
            steps.append(ExecutionStep(
                step_id="explain_simple",
                step_type=StepType.EXECUTE,
                description="Provide explanation based on request",
                action="explain_code",
                parameters={"request": request}
            ))

        return steps

    async def _plan_generic(self, request: str, analysis: Dict[str, Any]) -> List[ExecutionStep]:
        """Generic multi-step plan."""
        steps = []

        # Step 1: Gather context
        steps.append(ExecutionStep(
            step_id="gather_context",
            step_type=StepType.SEARCH,
            description="Gather relevant context and files",
            action="search",
            parameters={"query": request}
        ))

        # Step 2: Execute main action
        steps.append(ExecutionStep(
            step_id="main_action",
            step_type=StepType.EXECUTE,
            description="Execute main requested action",
            action="execute_request",
            parameters={"request": request},
            dependencies=["gather_context"]
        ))

        return steps

    def _extract_search_terms(self, request: str) -> str:
        """Extract search terms from request."""
        # Remove common words and extract key terms
        import re

        # Remove common words
        common_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "how", "do", "we"}
        words = re.findall(r'\b\w+\b', request.lower())
        key_words = [w for w in words if w not in common_words and len(w) > 2]

        return " ".join(key_words[:5])  # Top 5 key words

    async def _execute_single_step(self, request: str, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Execute simple single-step request."""
        return {
            "type": "single_step",
            "request": request,
            "analysis": analysis,
            "result": "Single step execution - delegated to appropriate agent",
            "multi_step": False
        }

    async def _execute_plan(self, plan: ExecutionPlan) -> Dict[str, Any]:
        """Execute the complete plan with dependency management."""
        plan.status = "executing"
        context = {}

        # Execute steps in dependency order
        completed_steps = set()

        while len(completed_steps) < len(plan.steps):
            # Find steps ready to execute
            ready_steps = [
                step for step in plan.steps
                if (step.status == StepStatus.PENDING and
                    all(dep in completed_steps for dep in step.dependencies))
            ]

            if not ready_steps:
                # Check for circular dependencies or errors
                pending_steps = [s for s in plan.steps if s.status == StepStatus.PENDING]
                if pending_steps:
                    # Mark remaining as failed
                    for step in pending_steps:
                        step.status = StepStatus.FAILED
                        step.error = "Dependency deadlock or previous step failure"
                break

            # Execute ready steps
            for step in ready_steps:
                try:
                    step.status = StepStatus.RUNNING
                    step.start_time = datetime.now()

                    # Execute the step
                    result = await self._execute_step(step, context)

                    step.result = result
                    step.status = StepStatus.COMPLETED
                    step.end_time = datetime.now()

                    # Add result to context for next steps
                    context[step.step_id] = result
                    completed_steps.add(step.step_id)

                except Exception as e:
                    step.status = StepStatus.FAILED
                    step.error = str(e)
                    step.end_time = datetime.now()

                    # Decide whether to continue or abort
                    if step.step_type in [StepType.PLAN, StepType.EXECUTE]:
                        # Critical step failed, abort
                        break
                    else:
                        # Non-critical step, mark as completed to continue
                        completed_steps.add(step.step_id)

        plan.status = "completed"

        # Compile final result
        return {
            "type": "multi_step",
            "plan_id": plan.plan_id,
            "goal": plan.goal,
            "steps_completed": len(completed_steps),
            "total_steps": len(plan.steps),
            "success": len(completed_steps) == len(plan.steps),
            "context": context,
            "steps": [
                {
                    "step_id": step.step_id,
                    "description": step.description,
                    "status": step.status.value,
                    "result": step.result,
                    "error": step.error
                }
                for step in plan.steps
            ]
        }

    async def _execute_step(self, step: ExecutionStep, context: Dict[str, Any]) -> Any:
        """Execute a single step."""
        if step.action in self.tools:
            tool = self.tools[step.action]
            return await tool(step.parameters, context)
        else:
            raise ValueError(f"Unknown action: {step.action}")

    # Tool implementations
    async def _tool_search(self, params: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Search tool implementation."""
        # Integrate with existing search functionality
        if self.shell and hasattr(self.shell, 'context_engine'):
            query = params.get("query", "")
            results = await self.shell.context_engine.search_code(query)
            return {"search_results": results, "query": query}
        return {"search_results": [], "query": params.get("query", "")}

    async def _tool_analyze_file(self, params: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """File analysis tool."""
        files = params.get("files", [])
        focus = params.get("focus", "general analysis")

        # Analyze files with focus area
        analysis = {
            "files_analyzed": files,
            "focus_area": focus,
            "findings": f"Analysis focused on {focus} for files: {files}"
        }

        return analysis

    async def _tool_find_dependencies(self, params: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Find file dependencies and imports."""
        files = params.get("files", [])
        dependencies = []

        # Mock dependency discovery - in real implementation, parse imports
        for file in files:
            if file.endswith('.py'):
                dependencies.extend([f"{file}.dependency1", f"{file}.dependency2"])
            elif file.endswith('.ts'):
                dependencies.extend([f"{file}.types", f"{file}.interfaces"])

        return {"dependencies": dependencies, "source_files": files}

    async def _tool_edit_file(self, params: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """File editing tool."""
        return {"action": "edit_file", "params": params, "status": "completed"}

    async def _tool_create_file(self, params: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """File creation tool."""
        return {"action": "create_file", "params": params, "status": "completed"}

    async def _tool_explain_code(self, params: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Code explanation tool."""
        return {"explanation": "Detailed code explanation based on context", "params": params}

    async def _tool_run_command(self, params: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Command execution tool."""
        return {"command_result": "Command executed successfully", "params": params}
