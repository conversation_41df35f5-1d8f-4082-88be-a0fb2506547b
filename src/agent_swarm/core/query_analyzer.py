"""
Intelligent Query Chunking and Information Extraction

Breaks down complex queries into manageable chunks and extracts
key information for better multi-step processing.
"""

import re
import json
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum


class QueryType(Enum):
    """Types of query components."""
    ACTION = "action"           # What to do (create, edit, analyze, explain)
    TARGET = "target"           # What to work on (files, functions, projects)
    CONTEXT = "context"         # Additional context (@files, dependencies)
    CONSTRAINT = "constraint"   # Limitations or requirements
    GOAL = "goal"              # Desired outcome
    CONDITION = "condition"     # If/when conditions


class Priority(Enum):
    """Priority levels for query chunks."""
    CRITICAL = "critical"       # Must be handled
    HIGH = "high"              # Should be handled
    MEDIUM = "medium"          # Nice to handle
    LOW = "low"               # Optional


@dataclass
class QueryChunk:
    """A single chunk of a query with extracted information."""
    chunk_id: str
    text: str
    query_type: QueryType
    priority: Priority
    entities: List[str] = field(default_factory=list)
    actions: List[str] = field(default_factory=list)
    files: List[str] = field(default_factory=list)
    technologies: List[str] = field(default_factory=list)
    keywords: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ExtractedInformation:
    """Complete extracted information from a query."""
    original_query: str
    chunks: List[QueryChunk]
    primary_action: str
    target_files: List[str]
    context_files: List[str]
    technologies: List[str]
    complexity_score: float
    estimated_steps: int
    dependencies: List[str]
    goals: List[str]
    constraints: List[str]


class QueryAnalyzer:
    """
    Intelligent query analyzer that chunks queries and extracts information.
    
    Features:
    - Smart query chunking by semantic meaning
    - Entity extraction (files, technologies, actions)
    - Dependency discovery from text
    - Complexity assessment
    - Priority assignment
    """
    
    def __init__(self):
        """Initialize the query analyzer."""
        self.action_patterns = {
            'create': [
                r'\b(create|make|build|generate|add|new)\b',
                r'\b(setup|initialize|scaffold)\b'
            ],
            'edit': [
                r'\b(edit|modify|change|update|fix|refactor)\b',
                r'\b(improve|enhance|optimize)\b'
            ],
            'analyze': [
                r'\b(analyze|examine|review|check|inspect)\b',
                r'\b(understand|study|investigate)\b'
            ],
            'explain': [
                r'\b(explain|describe|show|tell|clarify)\b',
                r'\b(how does|what is|why does)\b'
            ],
            'search': [
                r'\b(find|search|look for|locate)\b',
                r'\b(where is|show me)\b'
            ],
            'delete': [
                r'\b(delete|remove|clean|clear)\b'
            ]
        }
        
        self.technology_patterns = {
            'python': [r'\b(python|\.py|pip|conda|venv)\b'],
            'javascript': [r'\b(javascript|js|node|npm|yarn)\b'],
            'typescript': [r'\b(typescript|ts|tsx|tsc)\b'],
            'react': [r'\b(react|jsx|component|hook)\b'],
            'vue': [r'\b(vue|vuejs|nuxt)\b'],
            'angular': [r'\b(angular|ng)\b'],
            'docker': [r'\b(docker|dockerfile|container)\b'],
            'kubernetes': [r'\b(kubernetes|k8s|kubectl)\b'],
            'database': [r'\b(database|db|sql|mysql|postgres|mongodb)\b'],
            'api': [r'\b(api|rest|graphql|endpoint)\b'],
            'auth': [r'\b(auth|authentication|login|jwt|oauth)\b'],
            'test': [r'\b(test|testing|spec|unit|integration)\b']
        }
        
        self.file_patterns = [
            r'@([^\s]+)',                    # @file.py
            r'\b([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z0-9]+)\b',  # file.ext
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)/([a-zA-Z_][a-zA-Z0-9_]*)\b',  # dir/file
            r'\.([a-zA-Z0-9]+)\b'           # .extension
        ]
        
        self.dependency_patterns = [
            r'import(?:ed)?\s+from\s+["\']([^"\']+)["\']',
            r'from\s+([a-zA-Z_][a-zA-Z0-9_.]*)\s+import',
            r'require\(["\']([^"\']+)["\']\)',
            r'uses?\s+([a-zA-Z_][a-zA-Z0-9_.]*)',
            r'depends?\s+on\s+([a-zA-Z_][a-zA-Z0-9_.]*)',
            r'references?\s+([a-zA-Z_][a-zA-Z0-9_.]*)'
        ]
    
    def analyze_query(self, query: str) -> ExtractedInformation:
        """
        Analyze a query and extract all relevant information.
        
        Args:
            query: The user query to analyze
            
        Returns:
            ExtractedInformation with all extracted data
        """
        # Step 1: Chunk the query
        chunks = self._chunk_query(query)
        
        # Step 2: Extract information from chunks
        extracted = self._extract_information(query, chunks)
        
        # Step 3: Assess complexity and estimate steps
        extracted.complexity_score = self._calculate_complexity(extracted)
        extracted.estimated_steps = self._estimate_steps(extracted)
        
        return extracted
    
    def _chunk_query(self, query: str) -> List[QueryChunk]:
        """Break query into semantic chunks."""
        chunks = []
        
        # Split by common separators
        separators = [
            r'\band\s+',           # "and"
            r'\bthen\s+',          # "then"
            r'\bafter\s+that\s+',  # "after that"
            r'\bnext\s+',          # "next"
            r'\balso\s+',          # "also"
            r'[,;]',               # commas and semicolons
            r'\.\s+',              # periods
        ]
        
        # Start with the full query
        current_chunks = [query.strip()]
        
        # Apply each separator
        for separator in separators:
            new_chunks = []
            for chunk in current_chunks:
                parts = re.split(separator, chunk, flags=re.IGNORECASE)
                new_chunks.extend([part.strip() for part in parts if part.strip()])
            current_chunks = new_chunks
        
        # Create QueryChunk objects
        for i, chunk_text in enumerate(current_chunks):
            if len(chunk_text) < 5:  # Skip very short chunks
                continue
                
            chunk = QueryChunk(
                chunk_id=f"chunk_{i+1}",
                text=chunk_text,
                query_type=self._classify_chunk_type(chunk_text),
                priority=self._assess_priority(chunk_text),
                entities=self._extract_entities(chunk_text),
                actions=self._extract_actions(chunk_text),
                files=self._extract_files(chunk_text),
                technologies=self._extract_technologies(chunk_text),
                keywords=self._extract_keywords(chunk_text),
                dependencies=self._extract_dependencies(chunk_text)
            )
            
            chunks.append(chunk)
        
        return chunks
    
    def _classify_chunk_type(self, chunk: str) -> QueryType:
        """Classify the type of a query chunk."""
        chunk_lower = chunk.lower()
        
        # Check for actions
        for action, patterns in self.action_patterns.items():
            for pattern in patterns:
                if re.search(pattern, chunk_lower):
                    return QueryType.ACTION
        
        # Check for file references
        if '@' in chunk or any(re.search(pattern, chunk) for pattern in self.file_patterns):
            return QueryType.CONTEXT
        
        # Check for conditions
        if any(word in chunk_lower for word in ['if', 'when', 'unless', 'provided']):
            return QueryType.CONDITION
        
        # Check for constraints
        if any(word in chunk_lower for word in ['must', 'should', 'need', 'require', 'ensure']):
            return QueryType.CONSTRAINT
        
        # Check for goals
        if any(word in chunk_lower for word in ['want', 'goal', 'aim', 'objective', 'result']):
            return QueryType.GOAL
        
        # Default to target
        return QueryType.TARGET
    
    def _assess_priority(self, chunk: str) -> Priority:
        """Assess the priority of a chunk."""
        chunk_lower = chunk.lower()
        
        # Critical indicators
        if any(word in chunk_lower for word in ['critical', 'urgent', 'must', 'required', 'essential']):
            return Priority.CRITICAL
        
        # High priority indicators
        if any(word in chunk_lower for word in ['important', 'should', 'need', 'primary']):
            return Priority.HIGH
        
        # Low priority indicators
        if any(word in chunk_lower for word in ['optional', 'nice', 'maybe', 'if possible']):
            return Priority.LOW
        
        # Default to medium
        return Priority.MEDIUM
    
    def _extract_entities(self, chunk: str) -> List[str]:
        """Extract named entities from chunk."""
        entities = []
        
        # Extract capitalized words (potential entities)
        capitalized = re.findall(r'\b[A-Z][a-zA-Z]+\b', chunk)
        entities.extend(capitalized)
        
        # Extract quoted strings
        quoted = re.findall(r'["\']([^"\']+)["\']', chunk)
        entities.extend(quoted)
        
        return list(set(entities))
    
    def _extract_actions(self, chunk: str) -> List[str]:
        """Extract action verbs from chunk."""
        actions = []
        chunk_lower = chunk.lower()
        
        for action, patterns in self.action_patterns.items():
            for pattern in patterns:
                if re.search(pattern, chunk_lower):
                    actions.append(action)
        
        return list(set(actions))
    
    def _extract_files(self, chunk: str) -> List[str]:
        """Extract file references from chunk."""
        files = []
        
        for pattern in self.file_patterns:
            matches = re.findall(pattern, chunk)
            if isinstance(matches[0], tuple) if matches else False:
                # Handle tuple matches (like dir/file pattern)
                files.extend(['/'.join(match) for match in matches])
            else:
                files.extend(matches)
        
        return list(set(files))
    
    def _extract_technologies(self, chunk: str) -> List[str]:
        """Extract technology references from chunk."""
        technologies = []
        chunk_lower = chunk.lower()
        
        for tech, patterns in self.technology_patterns.items():
            for pattern in patterns:
                if re.search(pattern, chunk_lower):
                    technologies.append(tech)
        
        return list(set(technologies))
    
    def _extract_keywords(self, chunk: str) -> List[str]:
        """Extract important keywords from chunk."""
        # Remove common words
        common_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
            'of', 'with', 'by', 'this', 'that', 'these', 'those', 'i', 'you', 'we',
            'they', 'it', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has',
            'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'can'
        }
        
        # Extract words
        words = re.findall(r'\b[a-zA-Z]+\b', chunk.lower())
        keywords = [word for word in words if word not in common_words and len(word) > 2]
        
        return list(set(keywords))
    
    def _extract_dependencies(self, chunk: str) -> List[str]:
        """Extract dependency references from chunk."""
        dependencies = []
        
        for pattern in self.dependency_patterns:
            matches = re.findall(pattern, chunk, re.IGNORECASE)
            dependencies.extend(matches)
        
        return list(set(dependencies))
    
    def _extract_information(self, query: str, chunks: List[QueryChunk]) -> ExtractedInformation:
        """Extract comprehensive information from chunks."""
        # Aggregate information from all chunks
        all_actions = []
        all_files = []
        all_context_files = []
        all_technologies = []
        all_dependencies = []
        all_goals = []
        all_constraints = []
        
        for chunk in chunks:
            all_actions.extend(chunk.actions)
            all_files.extend(chunk.files)
            all_technologies.extend(chunk.technologies)
            all_dependencies.extend(chunk.dependencies)
            
            if chunk.query_type == QueryType.CONTEXT:
                all_context_files.extend(chunk.files)
            elif chunk.query_type == QueryType.GOAL:
                all_goals.append(chunk.text)
            elif chunk.query_type == QueryType.CONSTRAINT:
                all_constraints.append(chunk.text)
        
        # Determine primary action
        primary_action = all_actions[0] if all_actions else "analyze"
        
        # Separate target files from context files
        target_files = [f for f in all_files if f not in all_context_files]
        
        return ExtractedInformation(
            original_query=query,
            chunks=chunks,
            primary_action=primary_action,
            target_files=list(set(target_files)),
            context_files=list(set(all_context_files)),
            technologies=list(set(all_technologies)),
            complexity_score=0.0,  # Will be calculated
            estimated_steps=0,     # Will be calculated
            dependencies=list(set(all_dependencies)),
            goals=all_goals,
            constraints=all_constraints
        )
    
    def _calculate_complexity(self, extracted: ExtractedInformation) -> float:
        """Calculate complexity score (0.0 to 1.0)."""
        score = 0.0
        
        # Base complexity from number of chunks
        score += min(len(extracted.chunks) * 0.1, 0.3)
        
        # File complexity
        total_files = len(extracted.target_files) + len(extracted.context_files)
        score += min(total_files * 0.05, 0.2)
        
        # Technology complexity
        score += min(len(extracted.technologies) * 0.03, 0.15)
        
        # Action complexity
        action_weights = {
            'create': 0.3,
            'edit': 0.2,
            'analyze': 0.15,
            'explain': 0.1,
            'search': 0.05,
            'delete': 0.1
        }
        score += action_weights.get(extracted.primary_action, 0.1)
        
        # Dependency complexity
        score += min(len(extracted.dependencies) * 0.02, 0.1)
        
        # Constraint complexity
        score += min(len(extracted.constraints) * 0.05, 0.1)
        
        return min(score, 1.0)
    
    def _estimate_steps(self, extracted: ExtractedInformation) -> int:
        """Estimate number of execution steps needed."""
        steps = 1  # Base step
        
        # Add steps based on complexity
        if extracted.complexity_score > 0.7:
            steps += 4
        elif extracted.complexity_score > 0.5:
            steps += 3
        elif extracted.complexity_score > 0.3:
            steps += 2
        elif extracted.complexity_score > 0.1:
            steps += 1
        
        # Add steps for dependencies
        if extracted.dependencies:
            steps += 1
        
        # Add steps for multiple files
        if len(extracted.target_files) > 1:
            steps += 1
        
        # Add steps for multiple technologies
        if len(extracted.technologies) > 1:
            steps += 1
        
        return min(steps, 8)  # Cap at 8 steps
    
    def get_chunk_summary(self, extracted: ExtractedInformation) -> str:
        """Get a human-readable summary of the extracted information."""
        summary = f"Query Analysis Summary:\n"
        summary += f"  Primary Action: {extracted.primary_action}\n"
        summary += f"  Complexity: {extracted.complexity_score:.2f}\n"
        summary += f"  Estimated Steps: {extracted.estimated_steps}\n"
        
        if extracted.target_files:
            summary += f"  Target Files: {', '.join(extracted.target_files[:3])}\n"
        
        if extracted.context_files:
            summary += f"  Context Files: {', '.join(extracted.context_files[:3])}\n"
        
        if extracted.technologies:
            summary += f"  Technologies: {', '.join(extracted.technologies)}\n"
        
        if extracted.dependencies:
            summary += f"  Dependencies: {', '.join(extracted.dependencies[:3])}\n"
        
        summary += f"\nChunks ({len(extracted.chunks)}):\n"
        for chunk in extracted.chunks:
            summary += f"  {chunk.chunk_id}: {chunk.text[:50]}... ({chunk.query_type.value}, {chunk.priority.value})\n"
        
        return summary
