"""
Agent Router System for Agent Swarm

Routes requests to appropriate agents based on intent detection.
Provides visibility into agent selection and tool usage.
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from datetime import datetime
import logging

from .intent_detector import Intent, IntentType, IntentDetector


@dataclass
class AgentCapability:
    """Represents an agent's capabilities."""
    name: str
    description: str
    specialties: List[str]
    tools: List[str]
    confidence_threshold: float = 0.5
    max_concurrent_tasks: int = 3
    current_load: int = 0


@dataclass
class RoutingDecision:
    """Represents a routing decision."""
    selected_agent: str
    selected_tools: List[str]
    confidence: float
    reasoning: str
    alternatives: List[str]
    estimated_duration: Optional[float] = None


@dataclass
class TaskExecution:
    """Represents a task execution."""
    task_id: str
    intent: Intent
    routing_decision: RoutingDecision
    agent: str
    tools: List[str]
    status: str  # "pending", "running", "completed", "failed"
    start_time: datetime
    end_time: Optional[datetime] = None
    result: Optional[Any] = None
    error: Optional[str] = None


class AgentRouter:
    """
    Routes user requests to appropriate agents based on intent and capabilities.
    
    This is the core orchestration system that makes Agent Swarm revolutionary:
    1. Analyzes user intent
    2. Selects best agent for the task
    3. Provides visibility into decision making
    4. Manages agent load balancing
    5. Tracks task execution
    """
    
    def __init__(self):
        """Initialize the agent router."""
        self.intent_detector = IntentDetector()
        self.agents: Dict[str, AgentCapability] = {}
        self.active_tasks: Dict[str, TaskExecution] = {}
        self.task_history: List[TaskExecution] = []
        self.routing_callbacks: List[Callable] = []
        self.logger = logging.getLogger(__name__)
        
        # Initialize default agents
        self._register_default_agents()
    
    def register_agent(self, capability: AgentCapability) -> None:
        """Register an agent with its capabilities."""
        self.agents[capability.name] = capability
        self.logger.info(f"Registered agent: {capability.name}")
    
    def register_routing_callback(self, callback: Callable) -> None:
        """Register a callback for routing decisions."""
        self.routing_callbacks.append(callback)
    
    async def route_request(self, user_input: str, context: Optional[Dict] = None) -> RoutingDecision:
        """
        Route a user request to the appropriate agent.
        
        Args:
            user_input: Natural language input from user
            context: Optional context information
            
        Returns:
            RoutingDecision with selected agent and reasoning
        """
        # Detect intent
        intent = self.intent_detector.detect_intent(user_input)
        
        # Find best agent
        routing_decision = self._select_agent(intent, context)
        
        # Notify callbacks
        for callback in self.routing_callbacks:
            try:
                await callback(intent, routing_decision)
            except Exception as e:
                self.logger.error(f"Routing callback failed: {e}")
        
        return routing_decision
    
    async def execute_task(self, user_input: str, context: Optional[Dict] = None) -> TaskExecution:
        """
        Execute a complete task including routing and execution.
        
        Args:
            user_input: Natural language input from user
            context: Optional context information
            
        Returns:
            TaskExecution with complete execution details
        """
        # Detect intent and route
        intent = self.intent_detector.detect_intent(user_input)
        routing_decision = await self.route_request(user_input, context)
        
        # Create task execution
        task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        task = TaskExecution(
            task_id=task_id,
            intent=intent,
            routing_decision=routing_decision,
            agent=routing_decision.selected_agent,
            tools=routing_decision.selected_tools,
            status="pending",
            start_time=datetime.now()
        )
        
        # Track task
        self.active_tasks[task_id] = task
        
        # Update agent load
        if routing_decision.selected_agent in self.agents:
            self.agents[routing_decision.selected_agent].current_load += 1
        
        try:
            # Execute task (this would integrate with actual agent execution)
            task.status = "running"
            result = await self._execute_with_agent(task)
            
            task.status = "completed"
            task.result = result
            task.end_time = datetime.now()
            
        except Exception as e:
            task.status = "failed"
            task.error = str(e)
            task.end_time = datetime.now()
            self.logger.error(f"Task {task_id} failed: {e}")
        
        finally:
            # Update agent load
            if routing_decision.selected_agent in self.agents:
                self.agents[routing_decision.selected_agent].current_load -= 1
            
            # Move to history
            self.task_history.append(task)
            del self.active_tasks[task_id]
        
        return task
    
    def get_agent_recommendations(self, user_input: str) -> List[Dict[str, Any]]:
        """
        Get agent recommendations for user input without executing.
        
        Args:
            user_input: Natural language input from user
            
        Returns:
            List of agent recommendations with reasoning
        """
        intent = self.intent_detector.detect_intent(user_input)
        
        recommendations = []
        
        # Score all agents for this intent
        for agent_name, agent in self.agents.items():
            score = self._calculate_agent_score(agent, intent)
            
            if score > 0.1:  # Only include viable agents
                recommendations.append({
                    "agent": agent_name,
                    "score": score,
                    "reasoning": self._get_scoring_reasoning(agent, intent, score),
                    "specialties": agent.specialties,
                    "tools": agent.tools,
                    "current_load": agent.current_load,
                    "max_load": agent.max_concurrent_tasks
                })
        
        # Sort by score
        recommendations.sort(key=lambda x: x["score"], reverse=True)
        
        return recommendations
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status."""
        return {
            "agents": {
                name: {
                    "description": agent.description,
                    "specialties": agent.specialties,
                    "tools": agent.tools,
                    "current_load": agent.current_load,
                    "max_load": agent.max_concurrent_tasks,
                    "utilization": agent.current_load / agent.max_concurrent_tasks
                }
                for name, agent in self.agents.items()
            },
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len([t for t in self.task_history if t.status == "completed"]),
            "failed_tasks": len([t for t in self.task_history if t.status == "failed"]),
            "total_tasks": len(self.task_history)
        }
    
    def _select_agent(self, intent: Intent, context: Optional[Dict] = None) -> RoutingDecision:
        """Select the best agent for the given intent."""
        best_agent = None
        best_score = 0.0
        alternatives = []
        
        for agent_name, agent in self.agents.items():
            score = self._calculate_agent_score(agent, intent)
            
            if score > best_score:
                if best_agent:
                    alternatives.append(best_agent)
                best_agent = agent_name
                best_score = score
            elif score > 0.3:  # Good alternative
                alternatives.append(agent_name)
        
        if not best_agent:
            best_agent = "general_agent"
            best_score = 0.5
        
        selected_agent = self.agents.get(best_agent)
        selected_tools = intent.suggested_tools or []
        
        if selected_agent:
            # Add agent's specialized tools
            selected_tools.extend([tool for tool in selected_agent.tools if tool not in selected_tools])
        
        reasoning = self._build_routing_reasoning(intent, best_agent, best_score, alternatives)
        
        return RoutingDecision(
            selected_agent=best_agent,
            selected_tools=selected_tools,
            confidence=best_score,
            reasoning=reasoning,
            alternatives=alternatives[:3]  # Top 3 alternatives
        )
    
    def _calculate_agent_score(self, agent: AgentCapability, intent: Intent) -> float:
        """Calculate how well an agent matches the intent."""
        score = 0.0
        
        # Base score from intent suggestion
        if intent.suggested_agent == agent.name:
            score += 0.5
        
        # Specialty matching
        intent_keywords = [intent.intent_type.value] + list(intent.entities.keys())
        specialty_matches = sum(1 for specialty in agent.specialties 
                              if any(keyword in specialty.lower() for keyword in intent_keywords))
        if agent.specialties:
            score += (specialty_matches / len(agent.specialties)) * 0.3
        
        # Tool matching
        if intent.suggested_tools:
            tool_matches = sum(1 for tool in intent.suggested_tools if tool in agent.tools)
            score += (tool_matches / len(intent.suggested_tools)) * 0.2
        
        # Load balancing penalty
        if agent.current_load >= agent.max_concurrent_tasks:
            score *= 0.1  # Heavy penalty for overloaded agents
        elif agent.current_load > agent.max_concurrent_tasks * 0.8:
            score *= 0.7  # Moderate penalty for high load
        
        return min(score, 1.0)
    
    def _get_scoring_reasoning(self, agent: AgentCapability, intent: Intent, score: float) -> str:
        """Get human-readable reasoning for agent scoring."""
        reasons = []
        
        if intent.suggested_agent == agent.name:
            reasons.append("Direct intent match")
        
        intent_keywords = [intent.intent_type.value] + list(intent.entities.keys())
        specialty_matches = [s for s in agent.specialties 
                           if any(keyword in s.lower() for keyword in intent_keywords)]
        if specialty_matches:
            reasons.append(f"Specialties: {', '.join(specialty_matches)}")
        
        if intent.suggested_tools:
            tool_matches = [t for t in intent.suggested_tools if t in agent.tools]
            if tool_matches:
                reasons.append(f"Tools: {', '.join(tool_matches)}")
        
        if agent.current_load > 0:
            reasons.append(f"Current load: {agent.current_load}/{agent.max_concurrent_tasks}")
        
        return "; ".join(reasons) if reasons else "General capability match"
    
    def _build_routing_reasoning(self, intent: Intent, selected_agent: str, score: float, alternatives: List[str]) -> str:
        """Build human-readable routing reasoning."""
        reasoning = f"Selected {selected_agent} (confidence: {score:.1%})"
        
        if alternatives:
            alt_str = ", ".join(alternatives[:2])
            reasoning += f". Alternatives: {alt_str}"
        
        reasoning += f". Intent: {intent.intent_type.value} ({intent.confidence:.1%})"
        
        return reasoning
    
    async def _execute_with_agent(self, task: TaskExecution) -> Any:
        """Execute task with the selected agent (placeholder for actual execution)."""
        # This would integrate with actual agent execution
        # For now, return a mock result
        await asyncio.sleep(0.1)  # Simulate work
        
        return {
            "status": "completed",
            "agent": task.agent,
            "tools_used": task.tools,
            "intent": task.intent.intent_type.value,
            "message": f"Task completed by {task.agent}"
        }
    
    def _register_default_agents(self) -> None:
        """Register default agents with their capabilities."""
        default_agents = [
            AgentCapability(
                name="search_agent",
                description="Specialized in searching and finding information",
                specialties=["search", "find", "locate", "query", "lookup"],
                tools=["search_tool", "context_engine", "file_finder", "grep_tool"]
            ),
            AgentCapability(
                name="coding_agent",
                description="Expert in code editing and file manipulation",
                specialties=["edit", "code", "file", "modify", "update"],
                tools=["file_editor", "diff_tool", "backup_manager", "syntax_checker"]
            ),
            AgentCapability(
                name="analysis_agent",
                description="Analyzes and explains code and systems",
                specialties=["explain", "analyze", "describe", "understand"],
                tools=["code_analyzer", "documentation_generator", "context_engine"]
            ),
            AgentCapability(
                name="debugging_agent",
                description="Specialized in finding and fixing bugs",
                specialties=["debug", "fix", "bug", "error", "issue"],
                tools=["debugger", "error_analyzer", "test_runner", "log_analyzer"]
            ),
            AgentCapability(
                name="development_agent",
                description="Implements new features and functionality",
                specialties=["implement", "develop", "create", "build", "feature"],
                tools=["code_generator", "template_engine", "test_creator", "file_editor"]
            ),
            AgentCapability(
                name="general_agent",
                description="General purpose conversational agent",
                specialties=["chat", "help", "general", "conversation"],
                tools=["conversation_manager", "help_system", "context_engine"]
            )
        ]
        
        for agent in default_agents:
            self.register_agent(agent)
