"""
Revolutionary Multi-Agent Communication System for Agent Swarm.

Based on cutting-edge research in:
- Emergent Communication (DeepMind, OpenAI)
- Graph Neural Networks for Multi-Agent Systems
- Consensus Algorithms (RAFT, Byzantine Fault Tolerance)
- Swarm Intelligence and Collective Decision Making
- Attention Mechanisms for Agent Coordination

Features:
- Dynamic communication topology
- Emergent communication protocols
- Consensus algorithms with Byzantine fault tolerance
- Attention-based message routing
- Expertise-aware agent selection
- Conflict resolution mechanisms
"""

import asyncio
import time
import math
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable
from enum import Enum
from uuid import uuid4
import numpy as np
from collections import defaultdict, deque

from ..utils.logging import get_logger
from .events import emit_event, EventPriority
from .interfaces import EventEmitter, Monitorable

logger = get_logger("core.multi_agent")


class CommunicationProtocol(Enum):
    """Communication protocols for multi-agent systems."""
    BROADCAST = "broadcast"           # One-to-all communication
    UNICAST = "unicast"              # One-to-one communication
    MULTICAST = "multicast"          # One-to-many communication
    GOSSIP = "gossip"                # Probabilistic spreading
    HIERARCHICAL = "hierarchical"     # Tree-based communication
    MESH = "mesh"                    # Full connectivity
    RING = "ring"                    # Ring topology
    STAR = "star"                    # Star topology
    EMERGENT = "emergent"            # Self-organizing protocol


class MessageType(Enum):
    """Types of messages in multi-agent communication."""
    PROPOSAL = "proposal"            # Agent proposes a solution
    VOTE = "vote"                   # Agent votes on proposals
    CONSENSUS = "consensus"         # Consensus reached notification
    QUERY = "query"                 # Request for information
    RESPONSE = "response"           # Response to query
    EXPERTISE = "expertise"         # Expertise advertisement
    COORDINATION = "coordination"   # Coordination message
    CONFLICT = "conflict"           # Conflict notification
    RESOLUTION = "resolution"       # Conflict resolution
    HEARTBEAT = "heartbeat"         # Agent alive signal


class AgentRole(Enum):
    """Specialized roles for agents in the swarm."""
    COORDINATOR = "coordinator"      # Orchestrates consensus
    SPECIALIST = "specialist"       # Domain expert
    VALIDATOR = "validator"         # Validates proposals
    MEDIATOR = "mediator"           # Resolves conflicts
    OBSERVER = "observer"           # Monitors and reports
    LEARNER = "learner"             # Learns from interactions
    CRITIC = "critic"               # Provides critical analysis
    SYNTHESIZER = "synthesizer"     # Combines multiple perspectives


@dataclass
class Message:
    """Multi-agent communication message."""
    id: str = field(default_factory=lambda: str(uuid4()))
    sender_id: str = ""
    receiver_id: Optional[str] = None  # None for broadcast
    message_type: MessageType = MessageType.QUERY
    content: Any = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    priority: int = 1  # 1-10 scale
    ttl: Optional[float] = None  # Time to live
    requires_response: bool = False
    conversation_id: Optional[str] = None
    
    @property
    def is_expired(self) -> bool:
        """Check if message has expired."""
        if self.ttl is None:
            return False
        return time.time() - self.timestamp > self.ttl
    
    @property
    def age(self) -> float:
        """Get message age in seconds."""
        return time.time() - self.timestamp


@dataclass
class AgentProfile:
    """Profile of an agent in the swarm."""
    id: str
    role: AgentRole
    expertise_domains: List[str]
    trust_score: float = 1.0  # 0.0 to 1.0
    performance_history: List[float] = field(default_factory=list)
    communication_preferences: Dict[str, Any] = field(default_factory=dict)
    last_seen: float = field(default_factory=time.time)
    is_active: bool = True
    
    @property
    def average_performance(self) -> float:
        """Calculate average performance score."""
        if not self.performance_history:
            return 0.5
        return sum(self.performance_history) / len(self.performance_history)
    
    def update_performance(self, score: float) -> None:
        """Update performance history."""
        self.performance_history.append(score)
        # Keep only last 100 scores
        if len(self.performance_history) > 100:
            self.performance_history = self.performance_history[-100:]


@dataclass
class ConsensusProposal:
    """A proposal for consensus among agents."""
    id: str = field(default_factory=lambda: str(uuid4()))
    proposer_id: str = ""
    content: Any = None
    confidence: float = 0.0
    evidence: List[str] = field(default_factory=list)
    votes: Dict[str, float] = field(default_factory=dict)  # agent_id -> vote_weight
    created_at: float = field(default_factory=time.time)
    deadline: Optional[float] = None
    
    @property
    def total_votes(self) -> float:
        """Calculate total vote weight."""
        return sum(self.votes.values())
    
    @property
    def vote_count(self) -> int:
        """Get number of votes."""
        return len(self.votes)
    
    @property
    def is_expired(self) -> bool:
        """Check if proposal has expired."""
        if self.deadline is None:
            return False
        return time.time() > self.deadline


class CommunicationTopology:
    """Manages communication topology between agents."""
    
    def __init__(self, protocol: CommunicationProtocol = CommunicationProtocol.MESH):
        self.protocol = protocol
        self.connections: Dict[str, Set[str]] = defaultdict(set)
        self.weights: Dict[Tuple[str, str], float] = {}
        self.message_history: Dict[str, List[Message]] = defaultdict(list)
        
    def add_agent(self, agent_id: str) -> None:
        """Add agent to topology."""
        if agent_id not in self.connections:
            self.connections[agent_id] = set()
            self._update_topology()
    
    def remove_agent(self, agent_id: str) -> None:
        """Remove agent from topology."""
        if agent_id in self.connections:
            # Remove all connections to this agent
            for other_agent in self.connections:
                self.connections[other_agent].discard(agent_id)
            del self.connections[agent_id]
            
            # Remove weights
            keys_to_remove = [k for k in self.weights.keys() if agent_id in k]
            for key in keys_to_remove:
                del self.weights[key]
    
    def connect_agents(self, agent1: str, agent2: str, weight: float = 1.0) -> None:
        """Create connection between two agents."""
        self.connections[agent1].add(agent2)
        self.connections[agent2].add(agent1)
        self.weights[(agent1, agent2)] = weight
        self.weights[(agent2, agent1)] = weight
    
    def get_neighbors(self, agent_id: str) -> Set[str]:
        """Get neighboring agents."""
        return self.connections.get(agent_id, set())
    
    def get_connection_weight(self, agent1: str, agent2: str) -> float:
        """Get connection weight between agents."""
        return self.weights.get((agent1, agent2), 0.0)
    
    def _update_topology(self) -> None:
        """Update topology based on protocol."""
        agents = list(self.connections.keys())
        
        if self.protocol == CommunicationProtocol.MESH:
            # Full connectivity
            for i, agent1 in enumerate(agents):
                for j, agent2 in enumerate(agents):
                    if i != j:
                        self.connect_agents(agent1, agent2)
        
        elif self.protocol == CommunicationProtocol.RING:
            # Ring topology
            for i in range(len(agents)):
                next_agent = agents[(i + 1) % len(agents)]
                self.connect_agents(agents[i], next_agent)
        
        elif self.protocol == CommunicationProtocol.STAR:
            # Star topology - first agent is center
            if agents:
                center = agents[0]
                for agent in agents[1:]:
                    self.connect_agents(center, agent)
    
    def find_path(self, source: str, target: str) -> List[str]:
        """Find shortest path between agents using BFS."""
        if source == target:
            return [source]
        
        queue = deque([(source, [source])])
        visited = {source}
        
        while queue:
            current, path = queue.popleft()
            
            for neighbor in self.get_neighbors(current):
                if neighbor == target:
                    return path + [neighbor]
                
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append((neighbor, path + [neighbor]))
        
        return []  # No path found


class AttentionMechanism:
    """Attention mechanism for agent communication routing."""
    
    def __init__(self, embedding_dim: int = 64):
        self.embedding_dim = embedding_dim
        self.agent_embeddings: Dict[str, np.ndarray] = {}
        self.message_embeddings: Dict[str, np.ndarray] = {}
        
    def update_agent_embedding(self, agent_id: str, features: Dict[str, float]) -> None:
        """Update agent embedding based on features."""
        # Simple feature encoding (in production, use learned embeddings)
        embedding = np.zeros(self.embedding_dim)
        
        # Encode basic features
        feature_keys = list(features.keys())[:self.embedding_dim]
        for i, key in enumerate(feature_keys):
            embedding[i] = features[key]
        
        self.agent_embeddings[agent_id] = embedding
    
    def compute_attention_scores(self, message: Message, candidate_agents: List[str]) -> Dict[str, float]:
        """Compute attention scores for message routing."""
        if message.id not in self.message_embeddings:
            # Create message embedding (simplified)
            msg_embedding = np.random.normal(0, 1, self.embedding_dim)
            self.message_embeddings[message.id] = msg_embedding
        
        msg_embedding = self.message_embeddings[message.id]
        scores = {}
        
        for agent_id in candidate_agents:
            if agent_id in self.agent_embeddings:
                agent_embedding = self.agent_embeddings[agent_id]
                # Compute attention score (dot product + softmax)
                score = np.dot(msg_embedding, agent_embedding)
                scores[agent_id] = float(score)
            else:
                scores[agent_id] = 0.0
        
        # Apply softmax
        if scores:
            max_score = max(scores.values())
            exp_scores = {k: math.exp(v - max_score) for k, v in scores.items()}
            sum_exp = sum(exp_scores.values())
            scores = {k: v / sum_exp for k, v in exp_scores.items()}
        
        return scores


class ConsensusAlgorithm(ABC):
    """Abstract base class for consensus algorithms."""
    
    @abstractmethod
    async def reach_consensus(
        self, 
        proposals: List[ConsensusProposal], 
        agents: Dict[str, AgentProfile],
        timeout: float = 30.0
    ) -> Optional[ConsensusProposal]:
        """Reach consensus among proposals."""
        pass


class WeightedVotingConsensus(ConsensusAlgorithm):
    """Weighted voting consensus algorithm."""
    
    def __init__(self, min_participation: float = 0.5):
        self.min_participation = min_participation
    
    async def reach_consensus(
        self, 
        proposals: List[ConsensusProposal], 
        agents: Dict[str, AgentProfile],
        timeout: float = 30.0
    ) -> Optional[ConsensusProposal]:
        """Reach consensus using weighted voting."""
        if not proposals:
            return None
        
        # Calculate agent weights based on trust and performance
        agent_weights = {}
        for agent_id, profile in agents.items():
            weight = profile.trust_score * profile.average_performance
            agent_weights[agent_id] = weight
        
        # Score each proposal
        proposal_scores = {}
        for proposal in proposals:
            total_weight = 0.0
            weighted_score = 0.0
            
            for agent_id, vote in proposal.votes.items():
                if agent_id in agent_weights:
                    weight = agent_weights[agent_id]
                    total_weight += weight
                    weighted_score += vote * weight
            
            if total_weight > 0:
                proposal_scores[proposal.id] = weighted_score / total_weight
            else:
                proposal_scores[proposal.id] = 0.0
        
        # Check participation threshold
        total_agents = len(agents)
        participating_agents = len(set().union(*[p.votes.keys() for p in proposals]))
        participation_rate = participating_agents / total_agents if total_agents > 0 else 0
        
        if participation_rate < self.min_participation:
            logger.warning(f"Insufficient participation: {participation_rate:.2f} < {self.min_participation}")
            return None
        
        # Select best proposal
        if proposal_scores:
            best_proposal_id = max(proposal_scores.keys(), key=lambda k: proposal_scores[k])
            best_proposal = next(p for p in proposals if p.id == best_proposal_id)
            return best_proposal
        
        return None


class ByzantineFaultTolerantConsensus(ConsensusAlgorithm):
    """Byzantine Fault Tolerant consensus algorithm (simplified PBFT)."""
    
    def __init__(self, fault_tolerance: float = 0.33):
        self.fault_tolerance = fault_tolerance  # Fraction of faulty nodes tolerated
    
    async def reach_consensus(
        self, 
        proposals: List[ConsensusProposal], 
        agents: Dict[str, AgentProfile],
        timeout: float = 30.0
    ) -> Optional[ConsensusProposal]:
        """Reach consensus with Byzantine fault tolerance."""
        if not proposals or not agents:
            return None
        
        n_agents = len(agents)
        max_faulty = int(n_agents * self.fault_tolerance)
        min_honest = n_agents - max_faulty
        
        # Require 2/3 majority for Byzantine fault tolerance
        required_votes = max(min_honest, int(2 * n_agents / 3) + 1)
        
        # Score proposals based on agreement
        proposal_agreements = {}
        for proposal in proposals:
            # Count strong agreements (votes > 0.7)
            strong_votes = sum(1 for vote in proposal.votes.values() if vote > 0.7)
            proposal_agreements[proposal.id] = strong_votes
        
        # Select proposal with sufficient agreement
        for proposal in proposals:
            if proposal_agreements[proposal.id] >= required_votes:
                return proposal
        
        return None


class MultiAgentCommunicationHub(EventEmitter, Monitorable):
    """Central hub for multi-agent communication and consensus."""
    
    def __init__(self):
        self.agents: Dict[str, AgentProfile] = {}
        self.topology = CommunicationTopology()
        self.attention = AttentionMechanism()
        self.consensus_algorithms = {
            'weighted_voting': WeightedVotingConsensus(),
            'byzantine_ft': ByzantineFaultTolerantConsensus()
        }
        
        # Message handling
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.active_conversations: Dict[str, List[Message]] = {}
        self.pending_proposals: Dict[str, ConsensusProposal] = {}
        
        # Event listeners
        self.event_listeners: Dict[str, List[Callable]] = defaultdict(list)
        
        # Statistics
        self.stats = {
            'messages_sent': 0,
            'messages_received': 0,
            'consensus_reached': 0,
            'consensus_failed': 0,
            'average_consensus_time': 0.0
        }
        
        # Start message processing
        self._processing_task = None
    
    async def initialize(self) -> None:
        """Initialize the communication hub."""
        self._processing_task = asyncio.create_task(self._process_messages())
        logger.info("Multi-agent communication hub initialized")
    
    async def shutdown(self) -> None:
        """Shutdown the communication hub."""
        if self._processing_task:
            self._processing_task.cancel()
            try:
                await self._processing_task
            except asyncio.CancelledError:
                pass
        logger.info("Multi-agent communication hub shutdown")
    
    def register_agent(
        self, 
        agent_id: str, 
        role: AgentRole, 
        expertise_domains: List[str]
    ) -> None:
        """Register a new agent in the swarm."""
        profile = AgentProfile(
            id=agent_id,
            role=role,
            expertise_domains=expertise_domains
        )
        self.agents[agent_id] = profile
        self.topology.add_agent(agent_id)
        
        # Update attention mechanism
        features = {
            'trust_score': profile.trust_score,
            'performance': profile.average_performance,
            'expertise_count': len(expertise_domains),
            'role_weight': self._get_role_weight(role)
        }
        self.attention.update_agent_embedding(agent_id, features)
        
        logger.info(f"Registered agent {agent_id} with role {role.value}")
        asyncio.create_task(self.emit('agent.registered', {
            'agent_id': agent_id,
            'role': role.value,
            'expertise': expertise_domains
        }))
    
    def unregister_agent(self, agent_id: str) -> None:
        """Unregister an agent from the swarm."""
        if agent_id in self.agents:
            del self.agents[agent_id]
            self.topology.remove_agent(agent_id)
            logger.info(f"Unregistered agent {agent_id}")
            asyncio.create_task(self.emit('agent.unregistered', {'agent_id': agent_id}))
    
    async def send_message(self, message: Message) -> bool:
        """Send a message through the communication system."""
        if message.is_expired:
            logger.warning(f"Message {message.id} expired, not sending")
            return False
        
        await self.message_queue.put(message)
        self.stats['messages_sent'] += 1
        
        logger.debug(f"Queued message {message.id} from {message.sender_id}")
        return True
    
    async def broadcast_message(
        self, 
        sender_id: str, 
        content: Any, 
        message_type: MessageType = MessageType.QUERY
    ) -> str:
        """Broadcast a message to all agents."""
        message = Message(
            sender_id=sender_id,
            receiver_id=None,  # Broadcast
            message_type=message_type,
            content=content
        )
        
        await self.send_message(message)
        return message.id
    
    async def send_to_experts(
        self, 
        sender_id: str, 
        content: Any, 
        expertise_domain: str,
        message_type: MessageType = MessageType.QUERY
    ) -> List[str]:
        """Send message to agents with specific expertise."""
        expert_agents = [
            agent_id for agent_id, profile in self.agents.items()
            if expertise_domain in profile.expertise_domains and agent_id != sender_id
        ]
        
        message_ids = []
        for agent_id in expert_agents:
            message = Message(
                sender_id=sender_id,
                receiver_id=agent_id,
                message_type=message_type,
                content=content
            )
            await self.send_message(message)
            message_ids.append(message.id)
        
        return message_ids
    
    async def initiate_consensus(
        self, 
        proposer_id: str, 
        content: Any, 
        algorithm: str = 'weighted_voting',
        timeout: float = 30.0
    ) -> Optional[str]:
        """Initiate consensus process among agents."""
        proposal = ConsensusProposal(
            proposer_id=proposer_id,
            content=content,
            deadline=time.time() + timeout
        )
        
        self.pending_proposals[proposal.id] = proposal
        
        # Broadcast proposal to all agents
        await self.broadcast_message(
            proposer_id,
            {
                'proposal_id': proposal.id,
                'content': content,
                'deadline': proposal.deadline
            },
            MessageType.PROPOSAL
        )
        
        logger.info(f"Initiated consensus {proposal.id} by {proposer_id}")
        return proposal.id
    
    async def vote_on_proposal(
        self, 
        voter_id: str, 
        proposal_id: str, 
        vote: float
    ) -> bool:
        """Vote on a consensus proposal."""
        if proposal_id not in self.pending_proposals:
            logger.warning(f"Proposal {proposal_id} not found")
            return False
        
        proposal = self.pending_proposals[proposal_id]
        if proposal.is_expired:
            logger.warning(f"Proposal {proposal_id} expired")
            return False
        
        # Record vote
        proposal.votes[voter_id] = max(0.0, min(1.0, vote))  # Clamp to [0,1]
        
        logger.debug(f"Agent {voter_id} voted {vote} on proposal {proposal_id}")
        
        # Check if we can reach consensus early
        if len(proposal.votes) >= len(self.agents) * 0.8:  # 80% participation
            await self._try_reach_consensus(proposal_id)
        
        return True
    
    async def _try_reach_consensus(self, proposal_id: str) -> None:
        """Try to reach consensus on a proposal."""
        if proposal_id not in self.pending_proposals:
            return
        
        proposal = self.pending_proposals[proposal_id]
        
        # Try different consensus algorithms
        for algo_name, algorithm in self.consensus_algorithms.items():
            try:
                result = await algorithm.reach_consensus([proposal], self.agents)
                if result:
                    # Consensus reached
                    del self.pending_proposals[proposal_id]
                    self.stats['consensus_reached'] += 1
                    
                    await self.broadcast_message(
                        "system",
                        {
                            'proposal_id': proposal_id,
                            'consensus_result': result.content,
                            'algorithm': algo_name,
                            'votes': result.votes
                        },
                        MessageType.CONSENSUS
                    )
                    
                    logger.info(f"Consensus reached on {proposal_id} using {algo_name}")
                    await self.emit('consensus.reached', {
                        'proposal_id': proposal_id,
                        'result': result.content,
                        'algorithm': algo_name
                    })
                    return
                    
            except Exception as e:
                logger.error(f"Consensus algorithm {algo_name} failed: {e}")
        
        # No consensus reached
        logger.warning(f"Failed to reach consensus on {proposal_id}")
    
    async def _process_messages(self) -> None:
        """Process messages from the queue."""
        while True:
            try:
                message = await self.message_queue.get()
                await self._handle_message(message)
                self.stats['messages_received'] += 1
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error processing message: {e}")
    
    async def _handle_message(self, message: Message) -> None:
        """Handle a single message."""
        if message.is_expired:
            logger.warning(f"Dropping expired message {message.id}")
            return
        
        # Route message based on type
        if message.message_type == MessageType.PROPOSAL:
            await self._handle_proposal_message(message)
        elif message.message_type == MessageType.VOTE:
            await self._handle_vote_message(message)
        elif message.message_type == MessageType.QUERY:
            await self._handle_query_message(message)
        elif message.message_type == MessageType.RESPONSE:
            await self._handle_response_message(message)
        
        # Emit message event
        await self.emit('message.processed', {
            'message_id': message.id,
            'type': message.message_type.value,
            'sender': message.sender_id,
            'receiver': message.receiver_id
        })
    
    async def _handle_proposal_message(self, message: Message) -> None:
        """Handle proposal message."""
        # Notify relevant agents about the proposal
        content = message.content
        if isinstance(content, dict) and 'proposal_id' in content:
            proposal_id = content['proposal_id']
            logger.debug(f"Broadcasting proposal {proposal_id}")
    
    async def _handle_vote_message(self, message: Message) -> None:
        """Handle vote message."""
        content = message.content
        if isinstance(content, dict):
            proposal_id = content.get('proposal_id')
            vote = content.get('vote', 0.0)
            if proposal_id:
                await self.vote_on_proposal(message.sender_id, proposal_id, vote)
    
    async def _handle_query_message(self, message: Message) -> None:
        """Handle query message."""
        # Use attention mechanism to route to best agents
        if message.receiver_id is None:  # Broadcast query
            candidates = list(self.agents.keys())
            if message.sender_id in candidates:
                candidates.remove(message.sender_id)
            
            attention_scores = self.attention.compute_attention_scores(message, candidates)
            
            # Route to top agents based on attention
            top_agents = sorted(attention_scores.items(), key=lambda x: x[1], reverse=True)[:3]
            
            for agent_id, score in top_agents:
                if score > 0.1:  # Threshold for relevance
                    # Forward query to relevant agent
                    logger.debug(f"Routing query {message.id} to {agent_id} (score: {score:.3f})")
    
    async def _handle_response_message(self, message: Message) -> None:
        """Handle response message."""
        # Track conversation
        if message.conversation_id:
            if message.conversation_id not in self.active_conversations:
                self.active_conversations[message.conversation_id] = []
            self.active_conversations[message.conversation_id].append(message)
    
    def _get_role_weight(self, role: AgentRole) -> float:
        """Get weight for agent role."""
        weights = {
            AgentRole.COORDINATOR: 1.0,
            AgentRole.SPECIALIST: 0.9,
            AgentRole.VALIDATOR: 0.8,
            AgentRole.MEDIATOR: 0.8,
            AgentRole.OBSERVER: 0.6,
            AgentRole.LEARNER: 0.5,
            AgentRole.CRITIC: 0.7,
            AgentRole.SYNTHESIZER: 0.9
        }
        return weights.get(role, 0.5)
    
    # EventEmitter implementation
    def add_listener(self, event: str, callback: callable) -> None:
        """Add event listener."""
        self.event_listeners[event].append(callback)
    
    def remove_listener(self, event: str, callback: callable) -> None:
        """Remove event listener."""
        if event in self.event_listeners:
            self.event_listeners[event] = [
                cb for cb in self.event_listeners[event] if cb != callback
            ]
    
    async def emit(self, event: str, data: Any) -> None:
        """Emit event to all listeners."""
        if event in self.event_listeners:
            for callback in self.event_listeners[event]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    logger.error(f"Error in event listener for {event}: {e}")
    
    # Monitorable implementation
    def get_metrics(self) -> Dict[str, Any]:
        """Get communication hub metrics."""
        return {
            'agents_registered': len(self.agents),
            'active_conversations': len(self.active_conversations),
            'pending_proposals': len(self.pending_proposals),
            'messages_sent': self.stats['messages_sent'],
            'messages_received': self.stats['messages_received'],
            'consensus_reached': self.stats['consensus_reached'],
            'consensus_failed': self.stats['consensus_failed'],
            'success_rate': (
                self.stats['consensus_reached'] / 
                max(1, self.stats['consensus_reached'] + self.stats['consensus_failed'])
            ),
            'topology_protocol': self.topology.protocol.value,
            'agent_roles': {role.value: 0 for role in AgentRole},
        }
    
    def reset_metrics(self) -> None:
        """Reset metrics counters."""
        self.stats = {
            'messages_sent': 0,
            'messages_received': 0,
            'consensus_reached': 0,
            'consensus_failed': 0,
            'average_consensus_time': 0.0
        }


# Global communication hub instance
_global_communication_hub: Optional[MultiAgentCommunicationHub] = None


def get_communication_hub() -> MultiAgentCommunicationHub:
    """Get global communication hub instance."""
    global _global_communication_hub
    if _global_communication_hub is None:
        _global_communication_hub = MultiAgentCommunicationHub()
    return _global_communication_hub


async def initialize_communication_hub() -> MultiAgentCommunicationHub:
    """Initialize and return the global communication hub."""
    hub = get_communication_hub()
    await hub.initialize()
    return hub