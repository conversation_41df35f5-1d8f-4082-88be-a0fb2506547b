"""
Enhanced event system for Agent Swarm.
Implements observer pattern with async support, event filtering, and middleware.
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Callable, Union, Set
from dataclasses import dataclass, field
from enum import Enum
from uuid import uuid4

from ..utils.logging import get_logger

logger = get_logger("core.events")


class EventPriority(Enum):
    """Event priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Event:
    """Event data structure."""
    name: str
    data: Any = None
    source: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    event_id: str = field(default_factory=lambda: str(uuid4()))
    priority: EventPriority = EventPriority.NORMAL
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary."""
        return {
            "name": self.name,
            "data": self.data,
            "source": self.source,
            "timestamp": self.timestamp,
            "event_id": self.event_id,
            "priority": self.priority.value,
            "metadata": self.metadata
        }


class EventFilter(ABC):
    """Abstract base class for event filters."""
    
    @abstractmethod
    def should_process(self, event: Event) -> bool:
        """Determine if event should be processed."""
        pass


class NameFilter(EventFilter):
    """Filter events by name pattern."""
    
    def __init__(self, patterns: Union[str, List[str]]):
        self.patterns = patterns if isinstance(patterns, list) else [patterns]
    
    def should_process(self, event: Event) -> bool:
        """Check if event name matches any pattern."""
        return any(pattern in event.name for pattern in self.patterns)


class SourceFilter(EventFilter):
    """Filter events by source."""
    
    def __init__(self, sources: Union[str, List[str]]):
        self.sources = sources if isinstance(sources, list) else [sources]
    
    def should_process(self, event: Event) -> bool:
        """Check if event source matches."""
        return event.source in self.sources


class PriorityFilter(EventFilter):
    """Filter events by minimum priority."""
    
    def __init__(self, min_priority: EventPriority):
        self.min_priority = min_priority
    
    def should_process(self, event: Event) -> bool:
        """Check if event priority meets minimum."""
        return event.priority.value >= self.min_priority.value


class CompositeFilter(EventFilter):
    """Combine multiple filters with AND/OR logic."""
    
    def __init__(self, filters: List[EventFilter], use_and: bool = True):
        self.filters = filters
        self.use_and = use_and
    
    def should_process(self, event: Event) -> bool:
        """Apply composite filter logic."""
        if not self.filters:
            return True
        
        if self.use_and:
            return all(f.should_process(event) for f in self.filters)
        else:
            return any(f.should_process(event) for f in self.filters)


@dataclass
class EventListener:
    """Event listener with metadata."""
    callback: Callable[[Event], Any]
    filter: Optional[EventFilter] = None
    listener_id: str = field(default_factory=lambda: str(uuid4()))
    created_at: float = field(default_factory=time.time)
    call_count: int = 0
    last_called: Optional[float] = None
    is_async: bool = field(init=False)
    
    def __post_init__(self):
        """Initialize async flag."""
        self.is_async = asyncio.iscoroutinefunction(self.callback)
    
    def should_process(self, event: Event) -> bool:
        """Check if listener should process event."""
        if self.filter is None:
            return True
        return self.filter.should_process(event)
    
    async def call(self, event: Event) -> Any:
        """Call the listener callback."""
        self.call_count += 1
        self.last_called = time.time()
        
        try:
            if self.is_async:
                return await self.callback(event)
            else:
                return self.callback(event)
        except Exception as e:
            logger.error(f"Error in event listener {self.listener_id}: {e}")
            raise


class EventMiddleware(ABC):
    """Abstract base class for event middleware."""
    
    @abstractmethod
    async def process(self, event: Event, next_handler: Callable) -> Any:
        """Process event and call next handler."""
        pass


class LoggingMiddleware(EventMiddleware):
    """Middleware that logs all events."""
    
    def __init__(self, log_level: str = "DEBUG"):
        self.log_level = log_level
    
    async def process(self, event: Event, next_handler: Callable) -> Any:
        """Log event and continue processing."""
        logger.log(
            getattr(logger, self.log_level.lower(), logger.debug),
            f"Event: {event.name} from {event.source} (ID: {event.event_id})"
        )
        return await next_handler(event)


class MetricsMiddleware(EventMiddleware):
    """Middleware that collects event metrics."""
    
    def __init__(self):
        self.event_counts: Dict[str, int] = {}
        self.processing_times: Dict[str, List[float]] = {}
    
    async def process(self, event: Event, next_handler: Callable) -> Any:
        """Collect metrics and continue processing."""
        start_time = time.time()
        
        # Update count
        self.event_counts[event.name] = self.event_counts.get(event.name, 0) + 1
        
        try:
            result = await next_handler(event)
            
            # Record processing time
            processing_time = time.time() - start_time
            if event.name not in self.processing_times:
                self.processing_times[event.name] = []
            self.processing_times[event.name].append(processing_time)
            
            return result
        except Exception as e:
            # Still record the time even if processing failed
            processing_time = time.time() - start_time
            if event.name not in self.processing_times:
                self.processing_times[event.name] = []
            self.processing_times[event.name].append(processing_time)
            raise
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get collected metrics."""
        metrics = {
            "event_counts": self.event_counts.copy(),
            "avg_processing_times": {}
        }
        
        for event_name, times in self.processing_times.items():
            if times:
                metrics["avg_processing_times"][event_name] = sum(times) / len(times)
        
        return metrics


class EventBus:
    """Central event bus with async support and middleware."""
    
    def __init__(self):
        self._listeners: Dict[str, List[EventListener]] = {}
        self._global_listeners: List[EventListener] = []
        self._middleware: List[EventMiddleware] = []
        self._lock = asyncio.Lock()
        
        # Statistics
        self._events_emitted = 0
        self._events_processed = 0
        self._errors = 0
    
    def add_middleware(self, middleware: EventMiddleware) -> None:
        """Add middleware to the event processing pipeline."""
        self._middleware.append(middleware)
    
    def remove_middleware(self, middleware: EventMiddleware) -> None:
        """Remove middleware from the pipeline."""
        if middleware in self._middleware:
            self._middleware.remove(middleware)
    
    async def subscribe(
        self,
        event_name: str,
        callback: Callable[[Event], Any],
        event_filter: Optional[EventFilter] = None
    ) -> str:
        """Subscribe to specific event."""
        listener = EventListener(callback=callback, filter=event_filter)
        
        async with self._lock:
            if event_name not in self._listeners:
                self._listeners[event_name] = []
            self._listeners[event_name].append(listener)
        
        logger.debug(f"Subscribed listener {listener.listener_id} to event {event_name}")
        return listener.listener_id
    
    async def subscribe_global(
        self,
        callback: Callable[[Event], Any],
        event_filter: Optional[EventFilter] = None
    ) -> str:
        """Subscribe to all events."""
        listener = EventListener(callback=callback, filter=event_filter)
        
        async with self._lock:
            self._global_listeners.append(listener)
        
        logger.debug(f"Subscribed global listener {listener.listener_id}")
        return listener.listener_id
    
    async def unsubscribe(self, listener_id: str) -> bool:
        """Unsubscribe listener by ID."""
        async with self._lock:
            # Check global listeners
            for listener in self._global_listeners[:]:
                if listener.listener_id == listener_id:
                    self._global_listeners.remove(listener)
                    logger.debug(f"Unsubscribed global listener {listener_id}")
                    return True
            
            # Check event-specific listeners
            for event_name, listeners in self._listeners.items():
                for listener in listeners[:]:
                    if listener.listener_id == listener_id:
                        listeners.remove(listener)
                        logger.debug(f"Unsubscribed listener {listener_id} from {event_name}")
                        return True
        
        return False
    
    async def emit(
        self,
        event_name: str,
        data: Any = None,
        source: Optional[str] = None,
        priority: EventPriority = EventPriority.NORMAL,
        **metadata
    ) -> None:
        """Emit an event."""
        event = Event(
            name=event_name,
            data=data,
            source=source,
            priority=priority,
            metadata=metadata
        )
        
        self._events_emitted += 1
        logger.debug(f"Emitting event: {event_name} (ID: {event.event_id})")
        
        # Process through middleware and listeners
        await self._process_event(event)
    
    async def _process_event(self, event: Event) -> None:
        """Process event through middleware and listeners."""
        try:
            # Create middleware chain
            async def process_listeners(evt: Event) -> None:
                await self._notify_listeners(evt)
            
            # Build middleware chain (reverse order)
            handler = process_listeners
            for middleware in reversed(self._middleware):
                current_handler = handler
                handler = lambda evt, h=current_handler, m=middleware: m.process(evt, h)
            
            # Execute the chain
            await handler(event)
            
        except Exception as e:
            self._errors += 1
            logger.error(f"Error processing event {event.event_id}: {e}")
    
    async def _notify_listeners(self, event: Event) -> None:
        """Notify all relevant listeners."""
        listeners_to_notify = []
        
        async with self._lock:
            # Get event-specific listeners
            if event.name in self._listeners:
                listeners_to_notify.extend(self._listeners[event.name])
            
            # Add global listeners
            listeners_to_notify.extend(self._global_listeners)
        
        # Filter and notify listeners
        tasks = []
        for listener in listeners_to_notify:
            if listener.should_process(event):
                tasks.append(self._safe_call_listener(listener, event))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
            self._events_processed += len(tasks)
    
    async def _safe_call_listener(self, listener: EventListener, event: Event) -> None:
        """Safely call a listener, handling exceptions."""
        try:
            await listener.call(event)
        except Exception as e:
            self._errors += 1
            logger.error(f"Error in listener {listener.listener_id}: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get event bus statistics."""
        total_listeners = len(self._global_listeners)
        for listeners in self._listeners.values():
            total_listeners += len(listeners)
        
        return {
            "events_emitted": self._events_emitted,
            "events_processed": self._events_processed,
            "errors": self._errors,
            "total_listeners": total_listeners,
            "event_types": list(self._listeners.keys()),
            "middleware_count": len(self._middleware)
        }
    
    async def clear(self) -> None:
        """Clear all listeners and reset stats."""
        async with self._lock:
            self._listeners.clear()
            self._global_listeners.clear()
            self._middleware.clear()
            self._events_emitted = 0
            self._events_processed = 0
            self._errors = 0


# Global event bus instance
_global_event_bus: Optional[EventBus] = None


def get_event_bus() -> EventBus:
    """Get global event bus instance."""
    global _global_event_bus
    if _global_event_bus is None:
        _global_event_bus = EventBus()
    return _global_event_bus


# Convenience functions
async def emit_event(
    event_name: str,
    data: Any = None,
    source: Optional[str] = None,
    priority: EventPriority = EventPriority.NORMAL,
    **metadata
) -> None:
    """Emit an event using the global event bus."""
    bus = get_event_bus()
    await bus.emit(event_name, data, source, priority, **metadata)


async def subscribe_to_event(
    event_name: str,
    callback: Callable[[Event], Any],
    event_filter: Optional[EventFilter] = None
) -> str:
    """Subscribe to an event using the global event bus."""
    bus = get_event_bus()
    return await bus.subscribe(event_name, callback, event_filter)


# Decorator for event emission
def emits_event(event_name: str, source: Optional[str] = None):
    """Decorator that emits an event when function completes."""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            result = await func(*args, **kwargs)
            await emit_event(
                event_name,
                data={"result": result, "args": args, "kwargs": kwargs},
                source=source or func.__name__
            )
            return result
        
        def sync_wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            # For sync functions, we'd need to handle this differently
            # or require async context
            return result
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator