"""
Professional Coding Agent using the full Agent Swarm framework.
Demonstrates multi-LLM routing, MCP tools, RAG context, and CLI integration.
"""

from __future__ import annotations

import asyncio
from pathlib import Path
from typing import Any, Dict, List, Optional

from ..backends import <PERSON><PERSON><PERSON>er, LLMTier, Message
from ..context.dev_rag import DevelopmentRAG, setup_project_rag
from ..mcp import MCPEnabledAgent, MCPToolRegistry, setup_default_mcp_tools
from ..tools import DevelopmentTool, FileSystemTool, LinuxCLITool
from ..utils.logging import get_logger

logger = get_logger("agents.coding")


class CodingAgent(MCPEnabledAgent):
    """
    Professional coding agent with full framework integration.

    Features:
    - Multi-LLM routing (local for simple tasks, cloud for complex)
    - RAG-powered context from codebase
    - MCP tool calling for file operations
    - CLI tools for development workflows
    - Intelligent task decomposition
    """

    def __init__(
        self,
        name: str,
        llm_router: LLMRouter,
        project_path: Optional[str] = None,
        mcp_registry: Optional[MCPToolRegistry] = None,
        dev_rag: Optional[DevelopmentRAG] = None
    ):
        super().__init__(
            name,
            "Professional Software Developer",
            llm_router,
            mcp_registry
        )

        self.project_path = Path(project_path) if project_path else Path.cwd()
        self.dev_rag = dev_rag

        # Initialize development tools
        self.cli_tool = LinuxCLITool(safe_mode=True)
        self.fs_tool = FileSystemTool(safe_mode=True)
        self.dev_tool = DevelopmentTool()

        # Track coding sessions
        self.coding_sessions: List[Dict[str, Any]] = []
        self.current_session: Optional[Dict[str, Any]] = None

    async def start_coding_session(self, task_description: str) -> Dict[str, Any]:
        """Start a new coding session with context gathering."""

        logger.info(f"{self.name} starting coding session: {task_description}")

        session = {
            "task": task_description,
            "start_time": asyncio.get_event_loop().time(),
            "steps": [],
            "files_created": [],
            "files_modified": [],
            "commands_executed": [],
            "context_used": []
        }

        self.current_session = session
        self.coding_sessions.append(session)

        # Gather initial context
        await self._gather_initial_context(task_description)

        return session

    async def _gather_initial_context(self, task: str) -> None:
        """Gather relevant context for the coding task."""

        if not self.dev_rag:
            return

        try:
            # Search for relevant code patterns
            context_result = await self.dev_rag.get_context_for_task(task, "coding")

            if context_result.chunks:
                self.current_session["context_used"] = [
                    {
                        "file": chunk.metadata.get("file_path"),
                        "content_preview": chunk.content[:200],
                        "score": chunk.score
                    }
                    for chunk in context_result.chunks[:5]
                ]

                logger.info(f"Gathered context from {len(context_result.chunks)} code examples")

        except Exception as e:
            logger.warning(f"Failed to gather RAG context: {e}")

    async def implement_feature(
        self,
        feature_description: str,
        file_path: Optional[str] = None,
        test_required: bool = True
    ) -> Dict[str, Any]:
        """Implement a complete feature with tests."""

        session = await self.start_coding_session(f"Implement feature: {feature_description}")

        try:
            # Step 1: Analyze requirements and plan
            plan = await self._analyze_and_plan(feature_description, file_path)
            session["steps"].append({"step": "planning", "result": plan})

            # Step 2: Gather relevant context
            context = await self._gather_implementation_context(feature_description, file_path)
            session["steps"].append({"step": "context_gathering", "result": context})

            # Step 3: Implement the feature
            implementation = await self._implement_code(feature_description, file_path, plan, context)
            session["steps"].append({"step": "implementation", "result": implementation})

            # Step 4: Create tests if required
            if test_required:
                tests = await self._create_tests(feature_description, file_path, implementation)
                session["steps"].append({"step": "testing", "result": tests})

            # Step 5: Validate implementation
            validation = await self._validate_implementation(file_path, test_required)
            session["steps"].append({"step": "validation", "result": validation})

            session["success"] = validation.get("success", False)
            session["end_time"] = asyncio.get_event_loop().time()

            return session

        except Exception as e:
            session["error"] = str(e)
            session["success"] = False
            session["end_time"] = asyncio.get_event_loop().time()
            logger.error(f"Feature implementation failed: {e}")
            return session

    async def _analyze_and_plan(
        self,
        feature_description: str,
        file_path: Optional[str]
    ) -> Dict[str, Any]:
        """Analyze requirements and create implementation plan."""

        # Use cloud LLM for complex planning
        planning_prompt = f"""
        Analyze this feature request and create a detailed implementation plan:

        Feature: {feature_description}
        Target file: {file_path or "To be determined"}
        Project path: {self.project_path}

        Please provide:
        1. Requirements analysis
        2. Implementation approach
        3. File structure needed
        4. Key functions/classes to implement
        5. Potential challenges
        6. Testing strategy

        Be specific and actionable.
        """

        messages = [Message(role="user", content=planning_prompt)]

        response = await self.llm_router.route_request(
            messages=messages,
            task_type="architecture",
            preferred_tier=LLMTier.CLOUD_PREMIUM,
            temperature=0.3
        )

        return {
            "analysis": response.content,
            "llm_used": response.metadata.get("llm_used"),
            "cost": response.metadata.get("cost", 0)
        }

    async def _gather_implementation_context(
        self,
        feature_description: str,
        file_path: Optional[str]
    ) -> Dict[str, Any]:
        """Gather relevant code context for implementation."""

        context = {
            "similar_implementations": [],
            "related_files": [],
            "relevant_patterns": []
        }

        if not self.dev_rag:
            return context

        try:
            # Search for similar implementations
            similar = await self.dev_rag.search_code(feature_description)
            context["similar_implementations"] = [
                {
                    "file": chunk.metadata.get("file_path"),
                    "content": chunk.content[:500],
                    "score": chunk.score
                }
                for chunk in similar[:3]
            ]

            # Find related files if target file is specified
            if file_path:
                related = await self.dev_rag.get_related_files(file_path)
                context["related_files"] = [
                    {
                        "file": chunk.metadata.get("file_path"),
                        "relevance": chunk.score
                    }
                    for chunk in related[:5]
                ]

            # Search for relevant patterns
            patterns = await self.dev_rag.search_code(
                f"class function implementation {feature_description}",
                language="python"
            )
            context["relevant_patterns"] = [
                {
                    "file": chunk.metadata.get("file_path"),
                    "pattern": chunk.content[:300]
                }
                for chunk in patterns[:3]
            ]

        except Exception as e:
            logger.warning(f"Failed to gather implementation context: {e}")

        return context

    async def _implement_code(
        self,
        feature_description: str,
        file_path: Optional[str],
        plan: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Implement the actual code."""

        # Prepare implementation prompt with context
        context_info = ""
        if context.get("similar_implementations"):
            context_info += "\nSimilar implementations in codebase:\n"
            for impl in context["similar_implementations"]:
                context_info += f"- {impl['file']}: {impl['content'][:200]}...\n"

        if context.get("relevant_patterns"):
            context_info += "\nRelevant patterns:\n"
            for pattern in context["relevant_patterns"]:
                context_info += f"- {pattern['file']}: {pattern['pattern'][:200]}...\n"

        implementation_prompt = f"""
        Implement this feature based on the analysis and context:

        Feature: {feature_description}
        Target file: {file_path or "Create new file"}

        Implementation Plan:
        {plan.get('analysis', 'No plan available')}

        Codebase Context:
        {context_info}

        Please provide:
        1. Complete, working code implementation
        2. Proper error handling
        3. Clear documentation/comments
        4. Follow existing code patterns from context

        Make the code production-ready and well-structured.
        """

        messages = [Message(role="user", content=implementation_prompt)]

        # Use local LLM for straightforward implementation
        response = await self.llm_router.route_request(
            messages=messages,
            task_type="coding",
            preferred_tier=LLMTier.LOCAL_FAST,
            temperature=0.2
        )

        # Extract code from response and save to file
        code_content = self._extract_code_from_response(response.content)

        if file_path and code_content:
            # Use MCP tools to write the file
            write_result = await self.mcp_registry.call_tool("write_file", {
                "path": file_path,
                "content": code_content
            })

            if write_result.success:
                self.current_session["files_created"].append(file_path)

        return {
            "code": code_content,
            "file_path": file_path,
            "llm_used": response.metadata.get("llm_used"),
            "file_written": write_result.success if file_path else False
        }

    async def _create_tests(
        self,
        feature_description: str,
        file_path: Optional[str],
        implementation: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create comprehensive tests for the implementation."""

        test_prompt = f"""
        Create comprehensive tests for this implementation:

        Feature: {feature_description}
        Implementation file: {file_path}
        Code:
        {implementation.get('code', 'No code available')}

        Please provide:
        1. Unit tests covering all functions/methods
        2. Edge case testing
        3. Error condition testing
        4. Integration tests if applicable
        5. Use pytest framework

        Make tests thorough and maintainable.
        """

        messages = [Message(role="user", content=test_prompt)]

        response = await self.llm_router.route_request(
            messages=messages,
            task_type="testing",
            preferred_tier=LLMTier.LOCAL_FAST,
            temperature=0.2
        )

        # Extract test code and save to test file
        test_code = self._extract_code_from_response(response.content)

        if file_path and test_code:
            # Generate test file path
            test_file_path = self._generate_test_file_path(file_path)

            write_result = await self.mcp_registry.call_tool("write_file", {
                "path": test_file_path,
                "content": test_code
            })

            if write_result.success:
                self.current_session["files_created"].append(test_file_path)

        return {
            "test_code": test_code,
            "test_file": test_file_path if file_path else None,
            "file_written": write_result.success if file_path else False
        }

    async def _validate_implementation(
        self,
        file_path: Optional[str],
        test_required: bool
    ) -> Dict[str, Any]:
        """Validate the implementation by running tests and checks."""

        validation = {
            "syntax_check": False,
            "test_results": None,
            "lint_results": None,
            "success": False
        }

        if not file_path:
            return validation

        try:
            # Syntax check
            syntax_result = await self.cli_tool.execute_command(
                f"python -m py_compile {file_path}"
            )
            validation["syntax_check"] = syntax_result["success"]

            # Run tests if they exist
            if test_required:
                test_file = self._generate_test_file_path(file_path)
                if Path(test_file).exists():
                    test_result = await self.cli_tool.execute_command(
                        f"python -m pytest {test_file} -v"
                    )
                    validation["test_results"] = {
                        "success": test_result["success"],
                        "output": test_result["stdout"]
                    }

            # Basic lint check
            lint_result = await self.cli_tool.execute_command(
                f"python -m flake8 {file_path} --max-line-length=88"
            )
            validation["lint_results"] = {
                "success": lint_result["success"],
                "output": lint_result["stdout"]
            }

            # Overall success
            validation["success"] = (
                validation["syntax_check"] and
                (not test_required or validation.get("test_results", {}).get("success", True))
            )

        except Exception as e:
            validation["error"] = str(e)

        return validation

    def _extract_code_from_response(self, response: str) -> str:
        """Extract code blocks from LLM response."""

        # Look for code blocks
        lines = response.split('\n')
        code_lines = []
        in_code_block = False

        for line in lines:
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                continue

            if in_code_block:
                code_lines.append(line)

        return '\n'.join(code_lines) if code_lines else response

    def _generate_test_file_path(self, file_path: str) -> str:
        """Generate test file path from implementation file path."""

        path_obj = Path(file_path)

        # If in src/ directory, put tests in tests/
        if "src" in path_obj.parts:
            # src/module/file.py -> tests/test_file.py
            relative_path = path_obj.relative_to("src")
            test_path = Path("tests") / f"test_{relative_path.name}"
        else:
            # file.py -> test_file.py
            test_path = path_obj.parent / f"test_{path_obj.name}"

        return str(test_path)

    async def fix_bug(
        self,
        bug_description: str,
        file_path: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """Fix a bug using context and debugging tools."""

        session = await self.start_coding_session(f"Fix bug: {bug_description}")

        try:
            # Gather debugging context
            debug_context = await self._gather_debug_context(
                bug_description, file_path, error_message
            )
            session["steps"].append({"step": "debug_context", "result": debug_context})

            # Analyze the bug
            analysis = await self._analyze_bug(bug_description, file_path, error_message, debug_context)
            session["steps"].append({"step": "bug_analysis", "result": analysis})

            # Implement fix
            fix = await self._implement_bug_fix(bug_description, file_path, analysis)
            session["steps"].append({"step": "bug_fix", "result": fix})

            # Validate fix
            validation = await self._validate_bug_fix(file_path, bug_description)
            session["steps"].append({"step": "fix_validation", "result": validation})

            session["success"] = validation.get("success", False)
            session["end_time"] = asyncio.get_event_loop().time()

            return session

        except Exception as e:
            session["error"] = str(e)
            session["success"] = False
            session["end_time"] = asyncio.get_event_loop().time()
            return session

    async def _gather_debug_context(
        self,
        bug_description: str,
        file_path: Optional[str],
        error_message: Optional[str]
    ) -> Dict[str, Any]:
        """Gather context for debugging."""

        context = {
            "similar_bugs": [],
            "error_patterns": [],
            "related_code": []
        }

        if not self.dev_rag:
            return context

        try:
            # Search for similar bugs
            if error_message:
                similar_bugs = await self.dev_rag.search_code(error_message)
                context["similar_bugs"] = [
                    {
                        "file": chunk.metadata.get("file_path"),
                        "content": chunk.content[:300]
                    }
                    for chunk in similar_bugs[:3]
                ]

            # Search for error handling patterns
            error_patterns = await self.dev_rag.search_code("try except error handling")
            context["error_patterns"] = [
                {
                    "file": chunk.metadata.get("file_path"),
                    "pattern": chunk.content[:200]
                }
                for chunk in error_patterns[:3]
            ]

            # Get related code if file is specified
            if file_path:
                related = await self.dev_rag.get_related_files(file_path)
                context["related_code"] = [
                    {
                        "file": chunk.metadata.get("file_path"),
                        "relevance": chunk.score
                    }
                    for chunk in related[:3]
                ]

        except Exception as e:
            logger.warning(f"Failed to gather debug context: {e}")

        return context

    async def _analyze_bug(
        self,
        bug_description: str,
        file_path: Optional[str],
        error_message: Optional[str],
        debug_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze the bug and determine root cause."""

        context_info = ""
        if debug_context.get("similar_bugs"):
            context_info += "\nSimilar bugs found:\n"
            for bug in debug_context["similar_bugs"]:
                context_info += f"- {bug['file']}: {bug['content'][:150]}...\n"

        analysis_prompt = f"""
        Analyze this bug and determine the root cause:

        Bug Description: {bug_description}
        File: {file_path or "Unknown"}
        Error Message: {error_message or "Not provided"}

        Context from codebase:
        {context_info}

        Please provide:
        1. Root cause analysis
        2. Specific lines/functions that need fixing
        3. Recommended fix approach
        4. Potential side effects to consider
        5. Testing strategy for the fix

        Be specific and actionable.
        """

        messages = [Message(role="user", content=analysis_prompt)]

        response = await self.llm_router.route_request(
            messages=messages,
            task_type="debugging",
            preferred_tier=LLMTier.CLOUD_PREMIUM,
            temperature=0.2
        )

        return {
            "analysis": response.content,
            "llm_used": response.metadata.get("llm_used")
        }

    async def _implement_bug_fix(
        self,
        bug_description: str,
        file_path: Optional[str],
        analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Implement the bug fix."""

        if not file_path:
            return {"error": "No file path specified for fix"}

        # Read current file content
        read_result = await self.mcp_registry.call_tool("read_file", {"path": file_path})

        if not read_result.success:
            return {"error": f"Could not read file: {file_path}"}

        current_content = read_result.result["content"]

        fix_prompt = f"""
        Fix this bug based on the analysis:

        Bug: {bug_description}
        Analysis: {analysis.get('analysis', 'No analysis available')}

        Current file content:
        {current_content}

        Please provide the complete fixed file content.
        Ensure the fix is minimal and doesn't break existing functionality.
        """

        messages = [Message(role="user", content=fix_prompt)]

        response = await self.llm_router.route_request(
            messages=messages,
            task_type="coding",
            preferred_tier=LLMTier.LOCAL_FAST,
            temperature=0.1
        )

        # Extract fixed code
        fixed_code = self._extract_code_from_response(response.content)

        # Write fixed code back to file
        write_result = await self.mcp_registry.call_tool("write_file", {
            "path": file_path,
            "content": fixed_code
        })

        if write_result.success:
            self.current_session["files_modified"].append(file_path)

        return {
            "fixed_code": fixed_code,
            "file_written": write_result.success,
            "llm_used": response.metadata.get("llm_used")
        }

    async def _validate_bug_fix(self, file_path: Optional[str], bug_description: str) -> Dict[str, Any]:
        """Validate that the bug fix works."""

        if not file_path:
            return {"success": False, "error": "No file to validate"}

        validation = {
            "syntax_check": False,
            "test_results": None,
            "success": False
        }

        try:
            # Syntax check
            syntax_result = await self.cli_tool.execute_command(
                f"python -m py_compile {file_path}"
            )
            validation["syntax_check"] = syntax_result["success"]

            # Run tests if they exist
            test_file = self._generate_test_file_path(file_path)
            if Path(test_file).exists():
                test_result = await self.cli_tool.execute_command(
                    f"python -m pytest {test_file} -v"
                )
                validation["test_results"] = {
                    "success": test_result["success"],
                    "output": test_result["stdout"]
                }

            validation["success"] = validation["syntax_check"]

        except Exception as e:
            validation["error"] = str(e)

        return validation

    async def get_coding_session_summary(self) -> Dict[str, Any]:
        """Get summary of all coding sessions."""

        if not self.coding_sessions:
            return {"total_sessions": 0}

        total_sessions = len(self.coding_sessions)
        successful_sessions = sum(1 for s in self.coding_sessions if s.get("success", False))

        files_created = []
        files_modified = []

        for session in self.coding_sessions:
            files_created.extend(session.get("files_created", []))
            files_modified.extend(session.get("files_modified", []))

        return {
            "total_sessions": total_sessions,
            "successful_sessions": successful_sessions,
            "success_rate": successful_sessions / total_sessions if total_sessions > 0 else 0.0,
            "files_created": len(set(files_created)),
            "files_modified": len(set(files_modified)),
            "recent_sessions": self.coding_sessions[-3:] if self.coding_sessions else []
        }


# Factory function for easy setup
async def create_coding_agent(
    name: str = "CodingBot",
    project_path: Optional[str] = None,
    enable_rag: bool = True,
    llm_router: Optional[LLMRouter] = None
) -> CodingAgent:
    """Create a fully configured coding agent."""

    if llm_router is None:
        llm_router = LLMRouter()

    # Set up MCP tools
    mcp_registry = await setup_default_mcp_tools()

    # Set up RAG if enabled
    dev_rag = None
    if enable_rag and project_path:
        dev_rag = await setup_project_rag(project_path, auto_index=True)

    return CodingAgent(
        name=name,
        llm_router=llm_router,
        project_path=project_path,
        mcp_registry=mcp_registry,
        dev_rag=dev_rag
    )
