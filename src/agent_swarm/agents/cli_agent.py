"""
CLI Agent for Linux development workflows.
Combines MCP tools, RAG context, and development utilities.
"""

from __future__ import annotations

import asyncio
from typing import Any, Dict, List, Optional

from ..backends import LLMRouter, Message
from ..context import RAGSystem, create_rag_system
from ..mcp import <PERSON><PERSON><PERSON><PERSON>bledA<PERSON>, MCPToolRegistry
from ..tools import (
    DevelopmentTool,
    FileSystemTool,
    LinuxCLITool,
    NetworkTool,
    ProcessTool,
    SystemInfoTool,
)
from ..utils.logging import get_logger

logger = get_logger("agents.cli")


class CLIAgent(MCPEnabledAgent):
    """Advanced CLI agent with development tools and RAG context."""
    
    def __init__(
        self,
        name: str,
        llm_router: LLMRouter,
        mcp_registry: Optional[MCPToolRegistry] = None,
        rag_system: Optional[RAGSystem] = None,
        enable_dangerous_commands: bool = False
    ):
        super().__init__(name, "CLI Development Assistant", llm_router, mcp_registry)
        
        self.rag_system = rag_system
        self.enable_dangerous_commands = enable_dangerous_commands
        
        # Initialize CLI tools
        self.cli_tool = LinuxCLITool(safe_mode=not enable_dangerous_commands)
        self.fs_tool = FileSystemTool(safe_mode=not enable_dangerous_commands)
        self.process_tool = ProcessTool()
        self.network_tool = NetworkTool()
        self.system_tool = SystemInfoTool()
        self.dev_tool = DevelopmentTool()
        
        # Track command history
        self.command_history: List[Dict[str, Any]] = []
    
    async def execute_command(
        self,
        command: str,
        cwd: Optional[str] = None,
        timeout: int = 30
    ) -> Dict[str, Any]:
        """Execute a shell command with logging and safety checks."""
        
        logger.info(f"{self.name} executing command: {command}")
        
        # Get context from RAG if available
        context = ""
        if self.rag_system:
            try:
                rag_result = await self.rag_system.retrieve(
                    f"command usage examples: {command}",
                    top_k=3
                )
                if rag_result.chunks:
                    context = "\n".join([chunk.content for chunk in rag_result.chunks[:2]])
            except Exception as e:
                logger.debug(f"RAG context retrieval failed: {e}")
        
        # Execute command
        result = await self.cli_tool.execute_command(command, cwd=cwd, timeout=timeout)
        
        # Log to history
        self.command_history.append({
            "command": command,
            "cwd": cwd,
            "result": result,
            "timestamp": asyncio.get_event_loop().time(),
            "context_used": bool(context)
        })
        
        # Keep history manageable
        if len(self.command_history) > 100:
            self.command_history = self.command_history[-100:]
        
        return result
    
    async def analyze_system(self) -> Dict[str, Any]:
        """Perform comprehensive system analysis."""
        
        logger.info(f"{self.name} performing system analysis")
        
        analysis = {}
        
        try:
            # System information
            analysis["system_info"] = await self.system_tool.get_system_info()
            
            # Resource usage
            analysis["resources"] = await self.process_tool.get_system_resources()
            
            # Network interfaces
            analysis["network"] = await self.network_tool.get_network_interfaces()
            
            # Check common development tools
            dev_tools = [
                "python3", "pip", "git", "docker", "node", "npm", 
                "curl", "wget", "vim", "code", "make", "gcc"
            ]
            analysis["dev_tools"] = await self.system_tool.check_dependencies(dev_tools)
            
            # Disk usage for important directories
            important_dirs = ["/", "/home", "/tmp", "/var"]
            disk_usage = {}
            for directory in important_dirs:
                result = await self.execute_command(f"df -h {directory}")
                if result["success"]:
                    disk_usage[directory] = result["stdout"]
            analysis["disk_usage"] = disk_usage
            
            analysis["success"] = True
            
        except Exception as e:
            analysis["success"] = False
            analysis["error"] = str(e)
        
        return analysis
    
    async def setup_development_environment(
        self,
        project_type: str = "python",
        project_name: str = "new_project"
    ) -> Dict[str, Any]:
        """Set up a complete development environment."""
        
        logger.info(f"{self.name} setting up {project_type} development environment")
        
        setup_results = {}
        
        try:
            # Create project structure
            setup_results["project_creation"] = await self.dev_tool.create_project_structure(
                project_name, project_type
            )
            
            if not setup_results["project_creation"]["success"]:
                return setup_results
            
            # Install dependencies based on project type
            if project_type == "python":
                # Create virtual environment
                venv_result = await self.execute_command(
                    f"python3 -m venv {project_name}/venv",
                    cwd="."
                )
                setup_results["venv_creation"] = venv_result
                
                # Install basic Python tools
                if venv_result["success"]:
                    pip_install = await self.execute_command(
                        f"source {project_name}/venv/bin/activate && pip install pytest black flake8 mypy",
                        cwd="."
                    )
                    setup_results["tool_installation"] = pip_install
            
            elif project_type == "node":
                # Initialize npm
                npm_init = await self.execute_command(
                    "npm init -y",
                    cwd=project_name
                )
                setup_results["npm_init"] = npm_init
                
                # Install basic Node.js tools
                if npm_init["success"]:
                    npm_install = await self.execute_command(
                        "npm install --save-dev jest eslint prettier",
                        cwd=project_name
                    )
                    setup_results["tool_installation"] = npm_install
            
            # Initialize git repository
            git_init = await self.execute_command(
                "git init",
                cwd=project_name
            )
            setup_results["git_init"] = git_init
            
            setup_results["success"] = True
            
        except Exception as e:
            setup_results["success"] = False
            setup_results["error"] = str(e)
        
        return setup_results
    
    async def debug_issue(self, issue_description: str) -> Dict[str, Any]:
        """Help debug a development issue using RAG context and system analysis."""
        
        logger.info(f"{self.name} debugging issue: {issue_description}")
        
        debug_info = {
            "issue": issue_description,
            "suggestions": [],
            "system_checks": {},
            "relevant_context": []
        }
        
        try:
            # Get relevant context from RAG
            if self.rag_system:
                rag_result = await self.rag_system.get_context_for_task(
                    issue_description, 
                    "debugging"
                )
                debug_info["relevant_context"] = [
                    {
                        "content": chunk.content,
                        "score": chunk.score,
                        "source": chunk.metadata.get("source", "unknown")
                    }
                    for chunk in rag_result.chunks
                ]
            
            # Perform system checks based on issue type
            if "network" in issue_description.lower():
                debug_info["system_checks"]["network"] = await self.network_tool.get_network_interfaces()
                
                # Test connectivity
                ping_result = await self.network_tool.ping_host("*******", 2)
                debug_info["system_checks"]["connectivity"] = ping_result
            
            if "memory" in issue_description.lower() or "performance" in issue_description.lower():
                debug_info["system_checks"]["resources"] = await self.process_tool.get_system_resources()
                
                # Get top processes
                processes = await self.process_tool.list_processes()
                if processes["success"]:
                    debug_info["system_checks"]["top_processes"] = processes["processes"][:10]
            
            if "disk" in issue_description.lower() or "space" in issue_description.lower():
                disk_result = await self.execute_command("df -h")
                debug_info["system_checks"]["disk_usage"] = disk_result
            
            # Generate suggestions based on context and checks
            debug_info["suggestions"] = await self._generate_debug_suggestions(
                issue_description, 
                debug_info
            )
            
            debug_info["success"] = True
            
        except Exception as e:
            debug_info["success"] = False
            debug_info["error"] = str(e)
        
        return debug_info
    
    async def _generate_debug_suggestions(
        self, 
        issue: str, 
        debug_info: Dict[str, Any]
    ) -> List[str]:
        """Generate debugging suggestions based on issue and system state."""
        
        suggestions = []
        
        # Basic suggestions based on keywords
        issue_lower = issue.lower()
        
        if "permission" in issue_lower or "access denied" in issue_lower:
            suggestions.extend([
                "Check file permissions with 'ls -la'",
                "Verify user ownership with 'whoami'",
                "Consider using 'sudo' if administrative access is needed"
            ])
        
        if "port" in issue_lower or "connection refused" in issue_lower:
            suggestions.extend([
                "Check if the service is running with 'ps aux | grep <service>'",
                "Verify port is listening with 'netstat -tlnp'",
                "Check firewall rules with 'iptables -L'"
            ])
        
        if "python" in issue_lower:
            suggestions.extend([
                "Check Python version with 'python3 --version'",
                "Verify virtual environment is activated",
                "Check installed packages with 'pip list'"
            ])
        
        if "git" in issue_lower:
            suggestions.extend([
                "Check git status with 'git status'",
                "Verify remote configuration with 'git remote -v'",
                "Check git configuration with 'git config --list'"
            ])
        
        # Add context-based suggestions from RAG
        if debug_info.get("relevant_context"):
            suggestions.append("Check the relevant documentation and examples found in context")
        
        return suggestions
    
    async def get_command_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent command history."""
        return self.command_history[-limit:] if self.command_history else []
    
    async def get_agent_stats(self) -> Dict[str, Any]:
        """Get comprehensive agent statistics."""
        stats = await super().get_tool_usage_stats()
        
        stats.update({
            "commands_executed": len(self.command_history),
            "dangerous_commands_enabled": self.enable_dangerous_commands,
            "rag_enabled": self.rag_system is not None,
            "recent_commands": [cmd["command"] for cmd in self.command_history[-5:]]
        })
        
        if self.rag_system:
            rag_stats = await self.rag_system.get_stats()
            stats["rag_stats"] = rag_stats
        
        return stats


# Factory functions for easy setup
async def create_cli_agent(
    name: str = "DevBot",
    llm_router: Optional[LLMRouter] = None,
    enable_rag: bool = True,
    codebase_path: Optional[str] = None,
    enable_dangerous_commands: bool = False
) -> CLIAgent:
    """Create a CLI agent with optional RAG system."""
    
    if llm_router is None:
        from ..backends import LLMRouter
        llm_router = LLMRouter()
    
    # Set up RAG system if enabled
    rag_system = None
    if enable_rag:
        rag_system = await create_rag_system(
            vector_store="chroma",
            strategy="hybrid",
            chunk_size=800,
            top_k=5
        )
        
        # Add codebase if provided
        if codebase_path:
            await rag_system.add_codebase(codebase_path)
    
    # Set up MCP registry with development tools
    from ..mcp import setup_default_mcp_tools
    mcp_registry = await setup_default_mcp_tools()
    
    return CLIAgent(
        name=name,
        llm_router=llm_router,
        mcp_registry=mcp_registry,
        rag_system=rag_system,
        enable_dangerous_commands=enable_dangerous_commands
    )


async def create_project_agent(
    project_path: str,
    project_type: str = "python"
) -> CLIAgent:
    """Create a CLI agent specialized for a specific project."""
    
    agent = await create_cli_agent(
        name=f"{project_type.title()}ProjectBot",
        enable_rag=True,
        codebase_path=project_path
    )
    
    return agent
