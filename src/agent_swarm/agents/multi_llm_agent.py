"""
Multi-LLM Agent implementation.
Agent that can use different LLMs based on task complexity.
"""

from __future__ import annotations

from typing import List, Optional

from ..backends import <PERSON><PERSON><PERSON><PERSON>, LLMTier, Message
from ..utils.logging import agents_logger


class MultiLLMAgent:
    """Agent that can use different LLMs based on task complexity."""

    def __init__(self, name: str, role: str, llm_router: LLMRouter) -> None:
        self.name = name
        self.role = role
        self.llm_router = llm_router
        self.conversation_history: List[Message] = []
        self.logger = agents_logger

    async def process_task(
        self,
        task: str,
        task_type: str = "general",
        preferred_tier: Optional[LLMTier] = None,
        temperature: float = 0.3,
        max_tokens: Optional[int] = None,
    ) -> str:
        """Process a task using appropriate LLM."""

        self.logger.info(f"{self.name} processing {task_type} task")

        # Add system message with role
        system_msg = Message(
            role="system",
            content=f"You are {self.name}, a {self.role}. {self._get_role_instructions()}",
        )

        # Add user task
        user_msg = Message(role="user", content=task)

        # Prepare messages with conversation history
        messages = (
            [system_msg] + self.conversation_history[-4:] + [user_msg]
        )  # Keep last 4 for context

        try:
            # Route to appropriate LLM
            response = await self.llm_router.route_request(
                messages=messages,
                task_type=task_type,
                preferred_tier=preferred_tier,
                temperature=temperature,
                max_tokens=max_tokens,
            )

            # Update conversation history
            assistant_msg = Message(role="assistant", content=response.content)
            self.conversation_history.extend([user_msg, assistant_msg])

            # Keep history manageable
            if len(self.conversation_history) > 20:
                self.conversation_history = self.conversation_history[-20:]

            # Log which LLM was used
            cost = self.llm_router.estimate_cost(
                task_type,
                response.usage["prompt_tokens"],
                response.usage["completion_tokens"],
            )

            self.logger.info(
                f"{self.name} used {response.model} for {task_type} task "
                f"(cost: ${cost:.4f}, tokens: {response.usage['total_tokens']})"
            )

            return response.content

        except Exception as e:
            self.logger.error(f"{self.name} failed to process task: {e}")
            raise

    def _get_role_instructions(self) -> str:
        """Get role-specific instructions."""
        instructions = {
            "Junior Developer": (
                "You handle simple coding tasks, bug fixes, and code reviews. "
                "Keep responses concise and practical. Focus on working code."
            ),
            "Senior Developer": (
                "You handle standard coding tasks, code reviews, and debugging. "
                "Provide clear explanations and consider best practices."
            ),
            "Senior Architect": (
                "You design system architecture, solve complex problems, and make "
                "high-level technical decisions. Provide detailed analysis and "
                "consider scalability, maintainability, and performance."
            ),
            "Code Reviewer": (
                "You review code for quality, security, and best practices. "
                "Be thorough and constructive in your feedback."
            ),
            "Documentation Specialist": (
                "You create clear, comprehensive documentation and explanations. "
                "Focus on clarity and completeness for different audiences."
            ),
            "Architect": (
                "You design system architecture and solve complex technical problems. "
                "Consider scalability, performance, security, and maintainability."
            ),
        }
        return instructions.get(self.role, "You are a helpful assistant.")

    def clear_history(self) -> None:
        """Clear conversation history."""
        self.conversation_history.clear()
        self.logger.info(f"{self.name} conversation history cleared")

    def get_history_summary(self) -> dict:
        """Get summary of conversation history."""
        return {
            "agent": self.name,
            "role": self.role,
            "history_length": len(self.conversation_history),
            "last_messages": [
                {
                    "role": msg.role,
                    "content": (
                        msg.content[:100] + "..."
                        if len(msg.content) > 100
                        else msg.content
                    ),
                }
                for msg in self.conversation_history[-4:]
            ],
        }

    def __repr__(self) -> str:
        return f"MultiLLMAgent(name='{self.name}', role='{self.role}')"
