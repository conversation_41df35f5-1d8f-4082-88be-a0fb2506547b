"""
Configuration system for mathematical algorithms.

This module provides comprehensive configuration management with:
- Type-safe parameter definitions
- Validation and constraints
- Environment variable support
- Configuration inheritance and overrides
- Serialization and persistence

"""

from __future__ import annotations

import os
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Type, get_type_hints
from pydantic import field_validator, BaseModel, Field, validator, root_validator


class ParameterType(str, Enum):
    """Types of algorithm parameters."""
    FLOAT = "float"
    INT = "int"
    BOOL = "bool"
    STRING = "string"
    LIST = "list"
    DICT = "dict"
    ENUM = "enum"


class ParameterConstraint(BaseModel):
    """Constraints for algorithm parameters."""

    min_value: Optional[Union[int, float]] = Field(default=None, description="Minimum value")
    max_value: Optional[Union[int, float]] = Field(default=None, description="Maximum value")
    allowed_values: Optional[List[Any]] = Field(default=None, description="List of allowed values")
    pattern: Optional[str] = Field(default=None, description="Regex pattern for string validation")
    min_length: Optional[int] = Field(default=None, description="Minimum length for lists/strings")
    max_length: Optional[int] = Field(default=None, description="Maximum length for lists/strings")

    def validate_value(self, value: Any, param_type: ParameterType) -> bool:
        """Validate a value against these constraints."""
        try:
            # Type-specific validation
            if param_type in [ParameterType.FLOAT, ParameterType.INT]:
                if self.min_value is not None and value < self.min_value:
                    return False
                if self.max_value is not None and value > self.max_value:
                    return False

            if param_type in [ParameterType.STRING, ParameterType.LIST]:
                length = len(value)
                if self.min_length is not None and length < self.min_length:
                    return False
                if self.max_length is not None and length > self.max_length:
                    return False

            if self.allowed_values is not None and value not in self.allowed_values:
                return False

            if self.pattern is not None and param_type == ParameterType.STRING:
                import re
                if not re.match(self.pattern, str(value)):
                    return False

            return True

        except Exception:
            return False


class ParameterDefinition(BaseModel):
    """Definition of an algorithm parameter."""

    name: str = Field(..., description="Parameter name")
    param_type: ParameterType = Field(..., description="Parameter type")
    default_value: Any = Field(..., description="Default value")
    description: str = Field(..., description="Parameter description")
    required: bool = Field(default=False, description="Whether parameter is required")
    constraints: Optional[ParameterConstraint] = Field(default=None, description="Parameter constraints")
    env_var: Optional[str] = Field(default=None, description="Environment variable name")
    category: str = Field(default="general", description="Parameter category")

    def validate_value(self, value: Any) -> bool:
        """Validate a value for this parameter."""
        # Check type
        if not self._check_type(value):
            return False

        # Check constraints
        if self.constraints:
            return self.constraints.validate_value(value, self.param_type)

        return True

    def _check_type(self, value: Any) -> bool:
        """Check if value matches the expected type."""
        type_map = {
            ParameterType.FLOAT: (int, float),
            ParameterType.INT: int,
            ParameterType.BOOL: bool,
            ParameterType.STRING: str,
            ParameterType.LIST: list,
            ParameterType.DICT: dict,
        }

        expected_type = type_map.get(self.param_type)
        if expected_type is None:
            return True  # Unknown type, assume valid

        return isinstance(value, expected_type)

    def get_value_from_env(self) -> Optional[Any]:
        """Get parameter value from environment variable."""
        if not self.env_var:
            return None

        env_value = os.getenv(self.env_var)
        if env_value is None:
            return None

        # Convert string to appropriate type
        try:
            if self.param_type == ParameterType.FLOAT:
                return float(env_value)
            elif self.param_type == ParameterType.INT:
                return int(env_value)
            elif self.param_type == ParameterType.BOOL:
                return env_value.lower() in ("true", "1", "yes", "on")
            elif self.param_type == ParameterType.LIST:
                import json
                return json.loads(env_value)
            elif self.param_type == ParameterType.DICT:
                import json
                return json.loads(env_value)
            else:
                return env_value
        except (ValueError, TypeError, json.JSONDecodeError):
            return None


class AlgorithmParameters(BaseModel):
    """Container for algorithm parameters with validation."""

    parameters: Dict[str, Any] = Field(default_factory=dict, description="Parameter values")
    definitions: Dict[str, ParameterDefinition] = Field(default_factory=dict, description="Parameter definitions")

    def add_parameter(self, definition: ParameterDefinition) -> None:
        """Add a parameter definition."""
        self.definitions[definition.name] = definition

        # Set default value if not already set
        if definition.name not in self.parameters:
            # Try environment variable first
            env_value = definition.get_value_from_env()
            if env_value is not None:
                self.parameters[definition.name] = env_value
            else:
                self.parameters[definition.name] = definition.default_value

    def set_parameter(self, name: str, value: Any) -> bool:
        """Set a parameter value with validation."""
        if name not in self.definitions:
            return False

        definition = self.definitions[name]
        if not definition.validate_value(value):
            return False

        self.parameters[name] = value
        return True

    def get_parameter(self, name: str, default: Any = None) -> Any:
        """Get a parameter value."""
        return self.parameters.get(name, default)

    def validate_all(self) -> List[str]:
        """Validate all parameters and return list of errors."""
        errors = []

        for name, definition in self.definitions.items():
            # Check required parameters
            if definition.required and name not in self.parameters:
                errors.append(f"Required parameter '{name}' is missing")
                continue

            # Validate value if present
            if name in self.parameters:
                value = self.parameters[name]
                if not definition.validate_value(value):
                    errors.append(f"Invalid value for parameter '{name}': {value}")

        return errors

    def get_by_category(self, category: str) -> Dict[str, Any]:
        """Get all parameters in a specific category."""
        result = {}
        for name, definition in self.definitions.items():
            if definition.category == category and name in self.parameters:
                result[name] = self.parameters[name]
        return result


class OptimizationConfig(BaseModel):
    """Configuration for algorithm optimization."""

    strategy: str = Field(default="bayesian", description="Optimization strategy")
    max_iterations: int = Field(default=100, description="Maximum optimization iterations")
    target_metric: str = Field(default="accuracy", description="Metric to optimize")
    minimize: bool = Field(default=False, description="Whether to minimize the metric")
    early_stopping: bool = Field(default=True, description="Enable early stopping")
    patience: int = Field(default=10, description="Early stopping patience")

    # Strategy-specific parameters
    strategy_params: Dict[str, Any] = Field(default_factory=dict, description="Strategy-specific parameters")

    # Parameter space definition
    parameter_space: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Parameter search space")

    @field_validator('strategy')
    def validate_strategy(cls, v):
        """Validate optimization strategy."""
        allowed_strategies = ["grid_search", "random_search", "bayesian", "genetic", "gradient"]
        if v not in allowed_strategies:
            raise ValueError(f"Strategy must be one of {allowed_strategies}")
        return v


class ValidationConfig(BaseModel):
    """Configuration for algorithm validation."""

    level: str = Field(default="basic", description="Validation level")
    check_parameters: bool = Field(default=True, description="Validate parameters")
    check_dependencies: bool = Field(default=True, description="Check dependencies")
    check_performance: bool = Field(default=False, description="Run performance checks")
    performance_threshold: float = Field(default=1.0, description="Performance threshold in seconds")

    @field_validator('level')
    def validate_level(cls, v):
        """Validate validation level."""
        allowed_levels = ["basic", "thorough", "comprehensive"]
        if v not in allowed_levels:
            raise ValueError(f"Level must be one of {allowed_levels}")
        return v


class AlgorithmConfig(BaseModel):
    """Main configuration for algorithms."""

    # Algorithm identification
    algorithm_name: str = Field(..., description="Algorithm name")
    algorithm_version: str = Field(default="1.0.0", description="Algorithm version")

    # Core configuration
    parameters: AlgorithmParameters = Field(default_factory=AlgorithmParameters, description="Algorithm parameters")
    optimization: OptimizationConfig = Field(default_factory=OptimizationConfig, description="Optimization configuration")
    validation: ValidationConfig = Field(default_factory=ValidationConfig, description="Validation configuration")

    # Performance settings
    max_execution_time: float = Field(default=300.0, description="Maximum execution time in seconds")
    max_memory_usage: float = Field(default=1024.0, description="Maximum memory usage in MB")
    enable_caching: bool = Field(default=True, description="Enable result caching")
    cache_ttl: int = Field(default=3600, description="Cache TTL in seconds")

    # Monitoring and logging
    enable_metrics: bool = Field(default=True, description="Enable metrics collection")
    log_level: str = Field(default="INFO", description="Logging level")
    metrics_interval: float = Field(default=1.0, description="Metrics collection interval")

    # Environment and context
    environment: str = Field(default="development", description="Environment name")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional context")

    @classmethod
    def from_file(cls, config_path: Union[str, Path]) -> AlgorithmConfig:
        """Load configuration from file."""
        import json
        import yaml

        config_path = Path(config_path)

        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")

        with open(config_path, 'r') as f:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                data = yaml.safe_load(f)
            else:
                data = json.load(f)

        return cls(**data)

    def save_to_file(self, config_path: Union[str, Path], format: str = "yaml") -> None:
        """Save configuration to file."""
        import json
        import yaml

        config_path = Path(config_path)
        config_path.parent.mkdir(parents=True, exist_ok=True)

        data = self.dict()

        with open(config_path, 'w') as f:
            if format.lower() in ['yaml', 'yml']:
                yaml.dump(data, f, default_flow_style=False, indent=2)
            else:
                json.dump(data, f, indent=2)

    def validate_configuration(self) -> List[str]:
        """Validate the entire configuration."""
        errors = []

        # Validate parameters
        param_errors = self.parameters.validate_all()
        errors.extend(param_errors)

        # Validate performance settings
        if self.max_execution_time <= 0:
            errors.append("max_execution_time must be positive")

        if self.max_memory_usage <= 0:
            errors.append("max_memory_usage must be positive")

        # Validate log level
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log_level not in valid_log_levels:
            errors.append(f"log_level must be one of {valid_log_levels}")

        return errors

    def merge_with(self, other: AlgorithmConfig) -> AlgorithmConfig:
        """Merge this configuration with another, with other taking precedence."""
        merged_data = self.dict()
        other_data = other.dict()

        # Deep merge dictionaries
        def deep_merge(base: dict, override: dict) -> dict:
            result = base.copy()
            for key, value in override.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
            return result

        merged_data = deep_merge(merged_data, other_data)
        return AlgorithmConfig(**merged_data)
