"""
Event system for mathematical algorithms following OpenHands Action/Observation pattern.

This module implements the core event-driven architecture where:
- Actions represent what the algorithm wants to do
- Observations represent what actually happened
- Results contain the final output and metadata

Architecture:
    Algorithm Request → Action → Engine → Observation → Result
    
    This follows OpenHands patterns for clean separation of intent vs execution.
"""

from __future__ import annotations

import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

from pydantic import BaseModel, Field


class AlgorithmAction(BaseModel, ABC):
    """
    Base class for all algorithm actions.
    
    Actions represent what an algorithm wants to do, following the OpenHands pattern
    where actions are intent declarations that get executed by the runtime.
    """
    
    action_id: str = Field(default_factory=lambda: str(uuid4()), description="Unique action identifier")
    action_type: str = Field(..., description="Type of action to perform")
    timestamp: datetime = Field(default_factory=datetime.now, description="When action was created")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Action-specific parameters")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    @abstractmethod
    def validate_parameters(self) -> bool:
        """Validate that action parameters are correct."""
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert action to dictionary representation."""
        return {
            "action_id": self.action_id,
            "action_type": self.action_type,
            "timestamp": self.timestamp.isoformat(),
            "parameters": self.parameters,
            "metadata": self.metadata,
        }


class ExecuteAlgorithmAction(AlgorithmAction):
    """Action to execute a specific algorithm with given input data."""
    
    action_type: str = Field(default="execute_algorithm", description="Action type")
    algorithm_name: str = Field(..., description="Name of algorithm to execute")
    input_data: Any = Field(..., description="Input data for the algorithm")
    config_overrides: Dict[str, Any] = Field(default_factory=dict, description="Configuration overrides")
    
    def validate_parameters(self) -> bool:
        """Validate algorithm execution parameters."""
        return (
            bool(self.algorithm_name) and
            self.input_data is not None and
            isinstance(self.config_overrides, dict)
        )


class OptimizeParametersAction(AlgorithmAction):
    """Action to optimize algorithm parameters using training data."""
    
    action_type: str = Field(default="optimize_parameters", description="Action type")
    algorithm_name: str = Field(..., description="Algorithm to optimize")
    training_data: List[Any] = Field(..., description="Training data for optimization")
    optimization_strategy: str = Field(default="bayesian", description="Optimization strategy")
    target_metric: str = Field(default="accuracy", description="Metric to optimize")
    
    def validate_parameters(self) -> bool:
        """Validate optimization parameters."""
        return (
            bool(self.algorithm_name) and
            len(self.training_data) > 0 and
            bool(self.optimization_strategy) and
            bool(self.target_metric)
        )


class BenchmarkAction(AlgorithmAction):
    """Action to benchmark algorithm performance."""
    
    action_type: str = Field(default="benchmark", description="Action type")
    algorithm_name: str = Field(..., description="Algorithm to benchmark")
    benchmark_suite: str = Field(..., description="Benchmark suite to use")
    test_data: List[Any] = Field(..., description="Test data for benchmarking")
    metrics: List[str] = Field(default_factory=lambda: ["accuracy", "speed", "memory"], description="Metrics to collect")
    
    def validate_parameters(self) -> bool:
        """Validate benchmark parameters."""
        return (
            bool(self.algorithm_name) and
            bool(self.benchmark_suite) and
            len(self.test_data) > 0 and
            len(self.metrics) > 0
        )


class ValidateAction(AlgorithmAction):
    """Action to validate algorithm configuration and setup."""
    
    action_type: str = Field(default="validate", description="Action type")
    algorithm_name: str = Field(..., description="Algorithm to validate")
    validation_level: str = Field(default="basic", description="Validation level (basic, thorough, comprehensive)")
    
    def validate_parameters(self) -> bool:
        """Validate validation parameters."""
        return (
            bool(self.algorithm_name) and
            self.validation_level in ["basic", "thorough", "comprehensive"]
        )


class AlgorithmObservation(BaseModel, ABC):
    """
    Base class for all algorithm observations.
    
    Observations represent what actually happened when an action was executed,
    following the OpenHands pattern for clean separation of intent vs results.
    """
    
    observation_id: str = Field(default_factory=lambda: str(uuid4()), description="Unique observation identifier")
    action_id: str = Field(..., description="ID of the action that generated this observation")
    observation_type: str = Field(..., description="Type of observation")
    timestamp: datetime = Field(default_factory=datetime.now, description="When observation was created")
    success: bool = Field(..., description="Whether the action succeeded")
    execution_time: float = Field(..., description="Execution time in seconds")
    data: Dict[str, Any] = Field(default_factory=dict, description="Observation data")
    error: Optional[str] = Field(default=None, description="Error message if action failed")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert observation to dictionary representation."""
        return {
            "observation_id": self.observation_id,
            "action_id": self.action_id,
            "observation_type": self.observation_type,
            "timestamp": self.timestamp.isoformat(),
            "success": self.success,
            "execution_time": self.execution_time,
            "data": self.data,
            "error": self.error,
        }


class AlgorithmExecutionObservation(AlgorithmObservation):
    """Observation from algorithm execution."""
    
    observation_type: str = Field(default="algorithm_execution", description="Observation type")
    algorithm_name: str = Field(..., description="Name of executed algorithm")
    output_data: Any = Field(..., description="Algorithm output")
    confidence: float = Field(..., description="Confidence score of the result")
    metrics: Dict[str, float] = Field(default_factory=dict, description="Performance metrics")
    
    
class OptimizationObservation(AlgorithmObservation):
    """Observation from parameter optimization."""
    
    observation_type: str = Field(default="optimization", description="Observation type")
    algorithm_name: str = Field(..., description="Optimized algorithm name")
    best_parameters: Dict[str, Any] = Field(..., description="Best parameters found")
    best_score: float = Field(..., description="Best score achieved")
    optimization_history: List[Dict[str, Any]] = Field(default_factory=list, description="Optimization history")
    

class BenchmarkObservation(AlgorithmObservation):
    """Observation from benchmarking."""
    
    observation_type: str = Field(default="benchmark", description="Observation type")
    algorithm_name: str = Field(..., description="Benchmarked algorithm name")
    benchmark_results: Dict[str, float] = Field(..., description="Benchmark results")
    comparison_data: Dict[str, Any] = Field(default_factory=dict, description="Comparison with other algorithms")
    

class ValidationObservation(AlgorithmObservation):
    """Observation from validation."""
    
    observation_type: str = Field(default="validation", description="Observation type")
    algorithm_name: str = Field(..., description="Validated algorithm name")
    validation_results: Dict[str, bool] = Field(..., description="Validation results")
    issues_found: List[str] = Field(default_factory=list, description="Issues found during validation")
    

class ErrorObservation(AlgorithmObservation):
    """Observation for errors and exceptions."""
    
    observation_type: str = Field(default="error", description="Observation type")
    error_type: str = Field(..., description="Type of error")
    error_message: str = Field(..., description="Error message")
    stack_trace: Optional[str] = Field(default=None, description="Stack trace if available")
    recovery_suggestions: List[str] = Field(default_factory=list, description="Suggestions for recovery")


class AlgorithmResult(BaseModel):
    """
    Final result of algorithm execution containing all relevant information.
    
    This aggregates observations and provides a clean interface for algorithm consumers.
    """
    
    result_id: str = Field(default_factory=lambda: str(uuid4()), description="Unique result identifier")
    action_id: str = Field(..., description="ID of the original action")
    algorithm_name: str = Field(..., description="Name of the algorithm")
    timestamp: datetime = Field(default_factory=datetime.now, description="When result was created")
    success: bool = Field(..., description="Whether execution was successful")
    
    # Core result data
    output: Any = Field(..., description="Algorithm output")
    confidence: float = Field(..., description="Confidence in the result")
    
    # Performance metrics
    execution_time: float = Field(..., description="Total execution time")
    memory_usage: Optional[float] = Field(default=None, description="Memory usage in MB")
    cpu_usage: Optional[float] = Field(default=None, description="CPU usage percentage")
    
    # Metadata and context
    parameters_used: Dict[str, Any] = Field(default_factory=dict, description="Parameters used")
    observations: List[AlgorithmObservation] = Field(default_factory=list, description="All observations")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    # Error information
    error: Optional[str] = Field(default=None, description="Error message if failed")
    warnings: List[str] = Field(default_factory=list, description="Warning messages")
    
    def add_observation(self, observation: AlgorithmObservation) -> None:
        """Add an observation to this result."""
        self.observations.append(observation)
    
    def get_observations_by_type(self, observation_type: str) -> List[AlgorithmObservation]:
        """Get all observations of a specific type."""
        return [obs for obs in self.observations if obs.observation_type == observation_type]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary representation."""
        return {
            "result_id": self.result_id,
            "action_id": self.action_id,
            "algorithm_name": self.algorithm_name,
            "timestamp": self.timestamp.isoformat(),
            "success": self.success,
            "output": self.output,
            "confidence": self.confidence,
            "execution_time": self.execution_time,
            "memory_usage": self.memory_usage,
            "cpu_usage": self.cpu_usage,
            "parameters_used": self.parameters_used,
            "observations": [obs.to_dict() for obs in self.observations],
            "metadata": self.metadata,
            "error": self.error,
            "warnings": self.warnings,
        }
