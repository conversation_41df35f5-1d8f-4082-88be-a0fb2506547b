"""
Base classes for mathematical algorithms and algorithm pipelines.

This module provides:
- Abstract base classes for algorithms
- Algorithm stage composition
- Pipeline execution framework
- Result aggregation and chaining
- Error handling and recovery

Following OpenHands patterns for modular, composable architecture.
"""

from __future__ import annotations

import asyncio
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime
from uuid import uuid4

from pydantic import BaseModel, Field

from .config import AlgorithmConfig
from .events import AlgorithmResult
from .registry import AlgorithmContext, AlgorithmMetadata, BaseAlgorithm


class AlgorithmStageResult(BaseModel):
    """Result from a single algorithm stage."""

    stage_name: str = Field(..., description="Name of the stage")
    success: bool = Field(..., description="Whether stage succeeded")
    output: Any = Field(..., description="Stage output")
    confidence: float = Field(..., description="Confidence in the result")
    execution_time: float = Field(..., description="Stage execution time")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Stage metadata")
    error: Optional[str] = Field(default=None, description="Error message if failed")


class Algorithm(BaseAlgorithm):
    """
    Enhanced base class for mathematical algorithms with additional capabilities.

    Extends BaseAlgorithm with pipeline support, caching, and advanced features.
    """

    def __init__(self):
        super().__init__()
        self._cache: Dict[str, Any] = {}
        self._execution_history: List[Dict[str, Any]] = []

    @property
    @abstractmethod
    def metadata(self) -> AlgorithmMetadata:
        """Get algorithm metadata."""
        pass

    @abstractmethod
    async def execute(self, input_data: Any, context: AlgorithmContext) -> AlgorithmResult:
        """Execute the algorithm."""
        pass

    @abstractmethod
    def validate_input(self, input_data: Any) -> bool:
        """Validate input data."""
        pass

    def supports_caching(self) -> bool:
        """Whether this algorithm supports result caching."""
        return True

    def get_cache_key(self, input_data: Any, context: AlgorithmContext) -> str:
        """Generate cache key for input data and context."""
        import hashlib
        import json

        # Create a deterministic representation of input and relevant context
        cache_data = {
            "input": str(input_data),  # Simplified - should be more sophisticated
            "parameters": context.config.parameters.parameters,
            "algorithm": self.metadata.name,
            "version": self.metadata.version,
        }

        cache_str = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()

    async def execute_with_cache(self, input_data: Any, context: AlgorithmContext) -> AlgorithmResult:
        """Execute algorithm with caching support."""
        # Check if caching is enabled and supported
        if not context.config.enable_caching or not self.supports_caching():
            return await self.execute(input_data, context)

        # Generate cache key
        cache_key = self.get_cache_key(input_data, context)

        # Check cache
        if cache_key in self._cache:
            cached_result = self._cache[cache_key]
            # TODO: Check cache TTL
            return cached_result

        # Execute algorithm
        result = await self.execute(input_data, context)

        # Cache result if successful
        if result.success:
            self._cache[cache_key] = result

        return result

    def clear_cache(self) -> None:
        """Clear algorithm cache."""
        self._cache.clear()

    def get_execution_history(self) -> List[Dict[str, Any]]:
        """Get algorithm execution history."""
        return self._execution_history.copy()

    def record_execution(self, result: AlgorithmResult) -> None:
        """Record execution in history."""
        execution_record = {
            "timestamp": datetime.now().isoformat(),
            "success": result.success,
            "execution_time": result.execution_time,
            "confidence": result.confidence,
            "error": result.error,
        }

        self._execution_history.append(execution_record)

        # Keep only last 100 executions
        if len(self._execution_history) > 100:
            self._execution_history.pop(0)


class AlgorithmStage(Algorithm):
    """
    Base class for algorithm stages that can be composed into pipelines.

    Stages are lightweight algorithms designed to be chained together
    in processing pipelines.
    """

    def __init__(self, stage_name: str):
        super().__init__()
        self.stage_name = stage_name
        self.dependencies: List[str] = []
        self.optional_dependencies: List[str] = []

    @abstractmethod
    async def process(self, input_data: Any, context: AlgorithmContext) -> AlgorithmStageResult:
        """
        Process input data and return stage result.

        This is the main method that stages implement for their specific processing.
        """
        pass

    async def execute(self, input_data: Any, context: AlgorithmContext) -> AlgorithmResult:
        """Execute stage and convert result to AlgorithmResult."""
        stage_result = await self.process(input_data, context)

        return AlgorithmResult(
            action_id=str(uuid4()),
            algorithm_name=self.stage_name,
            success=stage_result.success,
            output=stage_result.output,
            confidence=stage_result.confidence,
            execution_time=stage_result.execution_time,
            metadata=stage_result.metadata,
            error=stage_result.error,
        )

    def add_dependency(self, stage_name: str, optional: bool = False) -> None:
        """Add a dependency on another stage."""
        if optional:
            if stage_name not in self.optional_dependencies:
                self.optional_dependencies.append(stage_name)
        else:
            if stage_name not in self.dependencies:
                self.dependencies.append(stage_name)

    def get_all_dependencies(self) -> List[str]:
        """Get all dependencies (required and optional)."""
        return self.dependencies + self.optional_dependencies

    def can_execute(self, completed_stages: List[str]) -> bool:
        """Check if stage can execute given completed stages."""
        # All required dependencies must be completed
        for dep in self.dependencies:
            if dep not in completed_stages:
                return False
        return True


class AlgorithmPipeline(Algorithm):
    """
    Pipeline for executing multiple algorithm stages in sequence or parallel.

    Provides sophisticated execution control with dependency management,
    error handling, and result aggregation.
    """

    def __init__(self, pipeline_name: str):
        super().__init__()
        self.pipeline_name = pipeline_name
        self.stages: Dict[str, AlgorithmStage] = {}
        self.execution_order: List[str] = []
        self.parallel_groups: List[List[str]] = []
        self.error_handling: str = "stop"  # "stop", "continue", "retry"
        self.max_retries: int = 3

    @property
    def metadata(self) -> AlgorithmMetadata:
        """Get pipeline metadata."""
        return AlgorithmMetadata(
            name=self.pipeline_name,
            version="1.0.0",
            description=f"Algorithm pipeline with {len(self.stages)} stages",
            author="Agent Swarm",
            category="pipeline",
            tags=["pipeline", "composite"],
            created_date=datetime.now(),
            updated_date=datetime.now(),
            dependencies=[],
            input_types=["any"],
            output_types=["any"],
            complexity="O(sum of stages)",
        )

    def add_stage(self, stage: AlgorithmStage) -> None:
        """Add a stage to the pipeline."""
        self.stages[stage.stage_name] = stage
        self._update_execution_order()

    def remove_stage(self, stage_name: str) -> bool:
        """Remove a stage from the pipeline."""
        if stage_name not in self.stages:
            return False

        del self.stages[stage_name]
        self._update_execution_order()
        return True

    def _update_execution_order(self) -> None:
        """Update execution order based on dependencies."""
        # Topological sort to determine execution order
        visited = set()
        temp_visited = set()
        order = []

        def visit(stage_name: str) -> None:
            if stage_name in temp_visited:
                raise ValueError(f"Circular dependency detected involving {stage_name}")
            if stage_name in visited:
                return

            temp_visited.add(stage_name)

            # Visit dependencies first
            stage = self.stages[stage_name]
            for dep in stage.dependencies:
                if dep in self.stages:
                    visit(dep)

            temp_visited.remove(stage_name)
            visited.add(stage_name)
            order.append(stage_name)

        # Visit all stages
        for stage_name in self.stages:
            if stage_name not in visited:
                visit(stage_name)

        self.execution_order = order

    def validate_input(self, input_data: Any) -> bool:
        """Validate input for the pipeline."""
        # Check if we have any stages
        if not self.stages:
            return False

        # Validate input for first stage(s)
        first_stages = self._get_first_stages()
        for stage_name in first_stages:
            stage = self.stages[stage_name]
            if not stage.validate_input(input_data):
                return False

        return True

    def _get_first_stages(self) -> List[str]:
        """Get stages that have no dependencies."""
        first_stages = []
        for stage_name, stage in self.stages.items():
            if not stage.dependencies:
                first_stages.append(stage_name)
        return first_stages

    async def execute(self, input_data: Any, context: AlgorithmContext) -> AlgorithmResult:
        """Execute the pipeline."""
        start_time = datetime.now()
        stage_results: Dict[str, AlgorithmStageResult] = {}
        completed_stages: List[str] = []
        pipeline_output = input_data
        total_confidence = 0.0
        successful_stages = 0

        try:
            # Execute stages in order
            for stage_name in self.execution_order:
                stage = self.stages[stage_name]

                # Check if stage can execute
                if not stage.can_execute(completed_stages):
                    # Skip stage if dependencies not met
                    continue

                # Update progress
                progress = len(completed_stages) / len(self.stages)
                context.update_progress(progress, f"Executing {stage_name}")

                # Execute stage with retry logic
                stage_result = await self._execute_stage_with_retry(
                    stage, pipeline_output, context
                )

                stage_results[stage_name] = stage_result

                if stage_result.success:
                    completed_stages.append(stage_name)
                    pipeline_output = stage_result.output
                    total_confidence += stage_result.confidence
                    successful_stages += 1
                else:
                    # Handle stage failure
                    if self.error_handling == "stop":
                        break
                    elif self.error_handling == "continue":
                        # Continue with original input for next stage
                        pass

            # Calculate final results
            execution_time = (datetime.now() - start_time).total_seconds()
            success = len(completed_stages) > 0
            avg_confidence = total_confidence / successful_stages if successful_stages > 0 else 0.0

            # Update final progress
            context.update_progress(1.0, "Pipeline completed")

            return AlgorithmResult(
                action_id=str(uuid4()),
                algorithm_name=self.pipeline_name,
                success=success,
                output=pipeline_output,
                confidence=avg_confidence,
                execution_time=execution_time,
                metadata={
                    "stages_executed": len(completed_stages),
                    "total_stages": len(self.stages),
                    "stage_results": {name: result.dict() for name, result in stage_results.items()},
                    "execution_order": self.execution_order,
                },
                error=None if success else "Pipeline execution failed",
            )

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()

            return AlgorithmResult(
                action_id=str(uuid4()),
                algorithm_name=self.pipeline_name,
                success=False,
                output=None,
                confidence=0.0,
                execution_time=execution_time,
                metadata={
                    "stages_executed": len(completed_stages),
                    "total_stages": len(self.stages),
                    "stage_results": {name: result.dict() for name, result in stage_results.items()},
                },
                error=str(e),
            )

    async def _execute_stage_with_retry(
        self,
        stage: AlgorithmStage,
        input_data: Any,
        context: AlgorithmContext,
    ) -> AlgorithmStageResult:
        """Execute a stage with retry logic."""
        last_error = None

        for attempt in range(self.max_retries + 1):
            try:
                result = await stage.process(input_data, context)
                if result.success:
                    return result
                else:
                    last_error = result.error

            except Exception as e:
                last_error = str(e)

            # Wait before retry (exponential backoff)
            if attempt < self.max_retries:
                await asyncio.sleep(2 ** attempt)

        # All retries failed
        return AlgorithmStageResult(
            stage_name=stage.stage_name,
            success=False,
            output=None,
            confidence=0.0,
            execution_time=0.0,
            error=f"Stage failed after {self.max_retries} retries: {last_error}",
        )

    def get_stage_dependencies(self) -> Dict[str, List[str]]:
        """Get dependency graph for all stages."""
        return {
            stage_name: stage.get_all_dependencies()
            for stage_name, stage in self.stages.items()
        }

    def validate_pipeline(self) -> List[str]:
        """Validate pipeline configuration."""
        errors = []

        # Check for circular dependencies
        try:
            self._update_execution_order()
        except ValueError as e:
            errors.append(str(e))

        # Check for missing dependencies
        for stage_name, stage in self.stages.items():
            for dep in stage.dependencies:
                if dep not in self.stages:
                    errors.append(f"Stage '{stage_name}' depends on missing stage '{dep}'")

        # Check if pipeline has at least one stage
        if not self.stages:
            errors.append("Pipeline has no stages")

        return errors
