"""
Algorithm registry and plugin system for mathematical algorithms.

This module provides:
- Dynamic algorithm discovery and registration
- Plugin architecture for extensible algorithms
- Version management and compatibility checking
- Algorithm metadata and documentation
- Dependency resolution and validation

Following OpenHands patterns for modular, extensible architecture.
"""

from __future__ import annotations

import importlib
import inspect
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, Union, Callable
from dataclasses import dataclass
from datetime import datetime

from pydantic import BaseModel, Field

from .config import AlgorithmConfig
from .events import AlgorithmResult


@dataclass
class AlgorithmMetadata:
    """Metadata for an algorithm."""
    
    name: str
    version: str
    description: str
    author: str
    category: str
    tags: List[str]
    created_date: datetime
    updated_date: datetime
    dependencies: List[str]
    input_types: List[str]
    output_types: List[str]
    complexity: str  # O(1), O(log n), O(n), O(n²), etc.
    documentation_url: Optional[str] = None
    source_url: Optional[str] = None


class AlgorithmContext:
    """Context passed to algorithms during execution."""
    
    def __init__(self, config: AlgorithmConfig):
        self.config = config
        self.metadata: Dict[str, Any] = {}
        self.cache: Dict[str, Any] = {}
        self.progress_callback: Optional[Callable[[float, str], None]] = None
    
    def get_parameter(self, name: str, default: Any = None) -> Any:
        """Get algorithm parameter."""
        return self.config.parameters.get_parameter(name, default)
    
    def set_metadata(self, key: str, value: Any) -> None:
        """Set execution metadata."""
        self.metadata[key] = value
    
    def update_progress(self, progress: float, message: str = "") -> None:
        """Update execution progress."""
        if self.progress_callback:
            self.progress_callback(progress, message)


class BaseAlgorithm(ABC):
    """
    Base class for all mathematical algorithms.
    
    Provides the interface that all algorithms must implement,
    following OpenHands patterns for consistent behavior.
    """
    
    def __init__(self):
        self._metadata: Optional[AlgorithmMetadata] = None
        self._config: Optional[AlgorithmConfig] = None
    
    @property
    @abstractmethod
    def metadata(self) -> AlgorithmMetadata:
        """Get algorithm metadata."""
        pass
    
    @abstractmethod
    async def execute(self, input_data: Any, context: AlgorithmContext) -> AlgorithmResult:
        """
        Execute the algorithm with given input data and context.
        
        Args:
            input_data: Input data for the algorithm
            context: Execution context with configuration and utilities
            
        Returns:
            AlgorithmResult containing output and metadata
        """
        pass
    
    @abstractmethod
    def validate_input(self, input_data: Any) -> bool:
        """
        Validate input data for the algorithm.
        
        Args:
            input_data: Input data to validate
            
        Returns:
            True if input is valid, False otherwise
        """
        pass
    
    def get_parameter_definitions(self) -> List[Dict[str, Any]]:
        """
        Get parameter definitions for this algorithm.
        
        Returns:
            List of parameter definitions
        """
        return []
    
    def estimate_complexity(self, input_size: int) -> float:
        """
        Estimate computational complexity for given input size.
        
        Args:
            input_size: Size of input data
            
        Returns:
            Estimated computational cost
        """
        return 1.0  # Default: O(1)
    
    def get_dependencies(self) -> List[str]:
        """Get list of algorithm dependencies."""
        return []
    
    def setup(self, config: AlgorithmConfig) -> None:
        """Setup algorithm with configuration."""
        self._config = config
    
    def cleanup(self) -> None:
        """Cleanup resources after execution."""
        pass


class AlgorithmPlugin(BaseModel):
    """
    Plugin wrapper for algorithms with additional metadata and capabilities.
    """
    
    algorithm_class: Type[BaseAlgorithm] = Field(..., description="Algorithm class")
    metadata: AlgorithmMetadata = Field(..., description="Algorithm metadata")
    enabled: bool = Field(default=True, description="Whether plugin is enabled")
    priority: int = Field(default=0, description="Plugin priority (higher = more priority)")
    
    class Config:
        arbitrary_types_allowed = True
    
    def create_instance(self) -> BaseAlgorithm:
        """Create an instance of the algorithm."""
        return self.algorithm_class()
    
    def is_compatible(self, requirements: Dict[str, Any]) -> bool:
        """Check if plugin is compatible with requirements."""
        # Check version compatibility
        if "min_version" in requirements:
            # TODO: Implement version comparison
            pass
        
        # Check dependencies
        if "required_dependencies" in requirements:
            required = set(requirements["required_dependencies"])
            available = set(self.metadata.dependencies)
            if not required.issubset(available):
                return False
        
        return True


class AlgorithmRegistry:
    """
    Registry for managing mathematical algorithms and plugins.
    
    Provides discovery, registration, and management of algorithms
    following OpenHands patterns for extensible architecture.
    """
    
    def __init__(self):
        self.algorithms: Dict[str, AlgorithmPlugin] = {}
        self.categories: Dict[str, List[str]] = {}
        self.search_paths: List[Path] = []
        self._discovery_cache: Dict[str, datetime] = {}
    
    def register_algorithm(
        self,
        algorithm_class: Type[BaseAlgorithm],
        metadata: Optional[AlgorithmMetadata] = None,
        enabled: bool = True,
        priority: int = 0,
    ) -> None:
        """
        Register an algorithm in the registry.
        
        Args:
            algorithm_class: Algorithm class to register
            metadata: Optional metadata (will be extracted from class if not provided)
            enabled: Whether algorithm is enabled
            priority: Algorithm priority
        """
        # Get metadata from class if not provided
        if metadata is None:
            instance = algorithm_class()
            metadata = instance.metadata
        
        # Create plugin
        plugin = AlgorithmPlugin(
            algorithm_class=algorithm_class,
            metadata=metadata,
            enabled=enabled,
            priority=priority,
        )
        
        # Register plugin
        self.algorithms[metadata.name] = plugin
        
        # Update categories
        category = metadata.category
        if category not in self.categories:
            self.categories[category] = []
        if metadata.name not in self.categories[category]:
            self.categories[category].append(metadata.name)
    
    def unregister_algorithm(self, name: str) -> bool:
        """
        Unregister an algorithm from the registry.
        
        Args:
            name: Algorithm name to unregister
            
        Returns:
            True if algorithm was unregistered, False if not found
        """
        if name not in self.algorithms:
            return False
        
        plugin = self.algorithms[name]
        category = plugin.metadata.category
        
        # Remove from algorithms
        del self.algorithms[name]
        
        # Remove from categories
        if category in self.categories and name in self.categories[category]:
            self.categories[category].remove(name)
            if not self.categories[category]:
                del self.categories[category]
        
        return True
    
    def get_algorithm(self, name: str) -> Optional[BaseAlgorithm]:
        """
        Get an algorithm instance by name.
        
        Args:
            name: Algorithm name
            
        Returns:
            Algorithm instance or None if not found
        """
        plugin = self.algorithms.get(name)
        if plugin is None or not plugin.enabled:
            return None
        
        return plugin.create_instance()
    
    def get_algorithm_metadata(self, name: str) -> Optional[AlgorithmMetadata]:
        """
        Get algorithm metadata by name.
        
        Args:
            name: Algorithm name
            
        Returns:
            Algorithm metadata or None if not found
        """
        plugin = self.algorithms.get(name)
        return plugin.metadata if plugin else None
    
    def list_algorithms(
        self,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        enabled_only: bool = True,
    ) -> List[str]:
        """
        List available algorithms with optional filtering.
        
        Args:
            category: Filter by category
            tags: Filter by tags (algorithm must have all tags)
            enabled_only: Only return enabled algorithms
            
        Returns:
            List of algorithm names
        """
        algorithms = []
        
        for name, plugin in self.algorithms.items():
            # Check enabled status
            if enabled_only and not plugin.enabled:
                continue
            
            # Check category
            if category and plugin.metadata.category != category:
                continue
            
            # Check tags
            if tags:
                algorithm_tags = set(plugin.metadata.tags)
                required_tags = set(tags)
                if not required_tags.issubset(algorithm_tags):
                    continue
            
            algorithms.append(name)
        
        # Sort by priority (higher first) then by name
        algorithms.sort(key=lambda name: (-self.algorithms[name].priority, name))
        
        return algorithms
    
    def search_algorithms(self, query: str) -> List[str]:
        """
        Search algorithms by name, description, or tags.
        
        Args:
            query: Search query
            
        Returns:
            List of matching algorithm names
        """
        query_lower = query.lower()
        matches = []
        
        for name, plugin in self.algorithms.items():
            if not plugin.enabled:
                continue
            
            metadata = plugin.metadata
            
            # Check name
            if query_lower in name.lower():
                matches.append((name, 3))  # High priority for name match
                continue
            
            # Check description
            if query_lower in metadata.description.lower():
                matches.append((name, 2))  # Medium priority for description match
                continue
            
            # Check tags
            for tag in metadata.tags:
                if query_lower in tag.lower():
                    matches.append((name, 1))  # Low priority for tag match
                    break
        
        # Sort by match priority then by algorithm priority
        matches.sort(key=lambda x: (-x[1], -self.algorithms[x[0]].priority, x[0]))
        
        return [name for name, _ in matches]
    
    def get_categories(self) -> List[str]:
        """Get list of all algorithm categories."""
        return list(self.categories.keys())
    
    def get_algorithms_by_category(self, category: str) -> List[str]:
        """Get algorithms in a specific category."""
        return self.categories.get(category, [])
    
    def add_search_path(self, path: Union[str, Path]) -> None:
        """Add a path to search for algorithm plugins."""
        path = Path(path)
        if path not in self.search_paths:
            self.search_paths.append(path)
    
    def discover_algorithms(self, force_refresh: bool = False) -> int:
        """
        Discover algorithms from search paths.
        
        Args:
            force_refresh: Force rediscovery even if cache is valid
            
        Returns:
            Number of algorithms discovered
        """
        discovered_count = 0
        
        for search_path in self.search_paths:
            if not search_path.exists():
                continue
            
            # Check cache
            cache_key = str(search_path)
            if not force_refresh and cache_key in self._discovery_cache:
                # TODO: Check if path has been modified since last discovery
                continue
            
            # Discover algorithms in path
            count = self._discover_in_path(search_path)
            discovered_count += count
            
            # Update cache
            self._discovery_cache[cache_key] = datetime.now()
        
        return discovered_count
    
    def _discover_in_path(self, path: Path) -> int:
        """Discover algorithms in a specific path."""
        discovered_count = 0
        
        # Look for Python files
        for py_file in path.rglob("*.py"):
            if py_file.name.startswith("_"):
                continue  # Skip private modules
            
            try:
                # Import module
                module_name = py_file.stem
                spec = importlib.util.spec_from_file_location(module_name, py_file)
                if spec is None or spec.loader is None:
                    continue
                
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # Find algorithm classes
                for name, obj in inspect.getmembers(module, inspect.isclass):
                    if (issubclass(obj, BaseAlgorithm) and 
                        obj != BaseAlgorithm and 
                        not inspect.isabstract(obj)):
                        
                        # Check if already registered
                        instance = obj()
                        algorithm_name = instance.metadata.name
                        
                        if algorithm_name not in self.algorithms:
                            self.register_algorithm(obj)
                            discovered_count += 1
                
            except Exception as e:
                # Log error but continue discovery
                print(f"Error discovering algorithms in {py_file}: {e}")
                continue
        
        return discovered_count
    
    def validate_registry(self) -> List[str]:
        """
        Validate all registered algorithms.
        
        Returns:
            List of validation errors
        """
        errors = []
        
        for name, plugin in self.algorithms.items():
            try:
                # Try to create instance
                algorithm = plugin.create_instance()
                
                # Validate metadata
                metadata = algorithm.metadata
                if not metadata.name:
                    errors.append(f"Algorithm '{name}' has empty name")
                
                if not metadata.version:
                    errors.append(f"Algorithm '{name}' has empty version")
                
                # Check dependencies
                dependencies = algorithm.get_dependencies()
                for dep in dependencies:
                    if dep not in self.algorithms:
                        errors.append(f"Algorithm '{name}' depends on missing algorithm '{dep}'")
                
            except Exception as e:
                errors.append(f"Error validating algorithm '{name}': {e}")
        
        return errors
    
    def get_registry_info(self) -> Dict[str, Any]:
        """Get comprehensive registry information."""
        return {
            "total_algorithms": len(self.algorithms),
            "enabled_algorithms": len([p for p in self.algorithms.values() if p.enabled]),
            "categories": {cat: len(algs) for cat, algs in self.categories.items()},
            "search_paths": [str(p) for p in self.search_paths],
            "last_discovery": max(self._discovery_cache.values()) if self._discovery_cache else None,
        }
