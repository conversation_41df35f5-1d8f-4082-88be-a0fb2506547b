"""
Core algorithm execution engine following OpenHands architecture patterns.

This module implements the main algorithm execution engine that:
- Processes actions and generates observations (OpenHands pattern)
- Manages algorithm lifecycle and state
- Provides sandboxed execution environment
- Handles performance monitoring and metrics
- Supports plugin architecture for extensibility

Architecture:
    Action → Engine → Runtime → Algorithm → Observation → Result

    This follows OpenHands patterns for clean separation of concerns and
    event-driven execution.
"""

from __future__ import annotations

import asyncio
import time
import traceback
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional, Type, Union
from uuid import uuid4

from ...utils.logging import get_logger
from .config import AlgorithmConfig
from .events import (
    AlgorithmAction,
    AlgorithmObservation,
    AlgorithmResult,
    ExecuteAlgorithmAction,
    OptimizeParametersAction,
    BenchmarkAction,
    ValidateAction,
    AlgorithmExecutionObservation,
    OptimizationObservation,
    BenchmarkObservation,
    ValidationObservation,
    ErrorObservation,
)
from .metrics import AlgorithmMetrics, PerformanceTracker
from .registry import AlgorithmRegistry

logger = get_logger("algorithms.engine")


class AlgorithmContext:
    """
    Context for algorithm execution containing state and resources.

    Similar to OpenHands runtime context, this provides algorithms with
    access to shared resources and state management.
    """

    def __init__(self, config: AlgorithmConfig):
        self.config = config
        self.state = AlgorithmState()
        self.metrics = AlgorithmMetrics(algorithm_name=config.algorithm_name)
        self.performance_tracker = PerformanceTracker()
        self.cache: Dict[str, Any] = {}
        self.metadata: Dict[str, Any] = {}

    def get_parameter(self, name: str, default: Any = None) -> Any:
        """Get algorithm parameter value."""
        return self.config.parameters.get_parameter(name, default)

    def set_metadata(self, key: str, value: Any) -> None:
        """Set context metadata."""
        self.metadata[key] = value

    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get context metadata."""
        return self.metadata.get(key, default)


class AlgorithmState:
    """
    State management for algorithm execution.

    Tracks execution history, current status, and provides state persistence.
    """

    def __init__(self):
        self.execution_id: str = str(uuid4())
        self.status: str = "initialized"
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.actions: List[AlgorithmAction] = []
        self.observations: List[AlgorithmObservation] = []
        self.current_stage: Optional[str] = None
        self.progress: float = 0.0
        self.error: Optional[str] = None

    def start_execution(self) -> None:
        """Mark execution as started."""
        self.status = "running"
        self.start_time = time.time()
        self.progress = 0.0

    def complete_execution(self, success: bool = True) -> None:
        """Mark execution as completed."""
        self.status = "completed" if success else "failed"
        self.end_time = time.time()
        self.progress = 1.0

    def add_action(self, action: AlgorithmAction) -> None:
        """Add an action to the execution history."""
        self.actions.append(action)

    def add_observation(self, observation: AlgorithmObservation) -> None:
        """Add an observation to the execution history."""
        self.observations.append(observation)

    def get_execution_time(self) -> Optional[float]:
        """Get total execution time."""
        if self.start_time is None:
            return None
        end_time = self.end_time or time.time()
        return end_time - self.start_time

    def update_progress(self, progress: float, stage: Optional[str] = None) -> None:
        """Update execution progress."""
        self.progress = max(0.0, min(1.0, progress))
        if stage:
            self.current_stage = stage


class AlgorithmEngine:
    """
    Main algorithm execution engine following OpenHands architecture.

    This engine processes actions, executes algorithms, and generates observations
    in a clean, event-driven manner similar to OpenHands runtime.
    """

    def __init__(self, config: AlgorithmConfig, registry: Optional[AlgorithmRegistry] = None):
        self.config = config
        self.registry = registry or AlgorithmRegistry()
        self.context = AlgorithmContext(config)
        self.logger = get_logger(f"algorithms.engine.{config.algorithm_name}")

        # Performance monitoring
        self.enable_monitoring = config.enable_metrics
        self.max_execution_time = config.max_execution_time
        self.max_memory_usage = config.max_memory_usage

    async def execute_action(self, action: AlgorithmAction) -> AlgorithmObservation:
        """
        Execute an action and return an observation.

        This is the core method that implements the OpenHands Action → Observation pattern.
        """
        start_time = time.time()

        try:
            # Validate action
            if not action.validate_parameters():
                return ErrorObservation(
                    action_id=action.action_id,
                    success=False,
                    execution_time=time.time() - start_time,
                    error_type="ValidationError",
                    error_message="Invalid action parameters",
                    data={"action": action.to_dict()}
                )

            # Add action to state
            self.context.state.add_action(action)

            # Route to appropriate handler
            if isinstance(action, ExecuteAlgorithmAction):
                observation = await self._execute_algorithm(action)
            elif isinstance(action, OptimizeParametersAction):
                observation = await self._optimize_parameters(action)
            elif isinstance(action, BenchmarkAction):
                observation = await self._benchmark_algorithm(action)
            elif isinstance(action, ValidateAction):
                observation = await self._validate_algorithm(action)
            else:
                observation = ErrorObservation(
                    action_id=action.action_id,
                    success=False,
                    execution_time=time.time() - start_time,
                    error_type="UnsupportedAction",
                    error_message=f"Unsupported action type: {action.action_type}",
                    data={"action_type": action.action_type}
                )

            # Add observation to state
            self.context.state.add_observation(observation)

            return observation

        except Exception as e:
            error_observation = ErrorObservation(
                action_id=action.action_id,
                success=False,
                execution_time=time.time() - start_time,
                error_type=type(e).__name__,
                error_message=str(e),
                stack_trace=traceback.format_exc(),
                data={"action": action.to_dict()}
            )

            self.context.state.add_observation(error_observation)
            self.logger.error(f"Error executing action {action.action_id}: {e}")

            return error_observation

    async def _execute_algorithm(self, action: ExecuteAlgorithmAction) -> AlgorithmObservation:
        """Execute a specific algorithm."""
        start_time = time.time()

        try:
            # Get algorithm from registry
            algorithm = self.registry.get_algorithm(action.algorithm_name)
            if algorithm is None:
                return ErrorObservation(
                    action_id=action.action_id,
                    success=False,
                    execution_time=time.time() - start_time,
                    error_type="AlgorithmNotFound",
                    error_message=f"Algorithm '{action.algorithm_name}' not found in registry",
                    data={"algorithm_name": action.algorithm_name}
                )

            # Apply configuration overrides
            if action.config_overrides:
                for key, value in action.config_overrides.items():
                    self.context.config.parameters.set_parameter(key, value)

            # Execute algorithm with timeout
            async def _execute_with_monitoring():
                async with self._execution_timeout():
                    self.context.state.start_execution()

                    # Start performance tracking
                    if self.enable_monitoring:
                        self.context.performance_tracker.start_tracking()

                    # Execute the algorithm
                    result = await algorithm.execute(action.input_data, self.context)

                    # Stop performance tracking
                    if self.enable_monitoring:
                        metrics = self.context.performance_tracker.stop_tracking()
                    else:
                        metrics = {}

                    self.context.state.complete_execution(success=True)
                    return result, metrics

            # Execute with timeout
            result, metrics = await asyncio.wait_for(
                _execute_with_monitoring(),
                timeout=self.max_execution_time
            )

            return AlgorithmExecutionObservation(
                action_id=action.action_id,
                success=True,
                execution_time=time.time() - start_time,
                algorithm_name=action.algorithm_name,
                output_data=result.output,
                confidence=result.confidence,
                metrics=metrics,
                data={
                    "parameters_used": self.context.config.parameters.parameters,
                    "execution_id": self.context.state.execution_id,
                }
            )

        except asyncio.TimeoutError:
            self.context.state.complete_execution(success=False)
            return ErrorObservation(
                action_id=action.action_id,
                success=False,
                execution_time=time.time() - start_time,
                error_type="TimeoutError",
                error_message=f"Algorithm execution exceeded timeout of {self.max_execution_time}s",
                data={"timeout": self.max_execution_time}
            )
        except Exception as e:
            self.context.state.complete_execution(success=False)
            return ErrorObservation(
                action_id=action.action_id,
                success=False,
                execution_time=time.time() - start_time,
                error_type=type(e).__name__,
                error_message=str(e),
                stack_trace=traceback.format_exc(),
                data={"algorithm_name": action.algorithm_name}
            )

    async def _optimize_parameters(self, action: OptimizeParametersAction) -> AlgorithmObservation:
        """Optimize algorithm parameters."""
        start_time = time.time()

        # TODO: Implement parameter optimization
        # This will be implemented in the optimization module

        return OptimizationObservation(
            action_id=action.action_id,
            success=True,
            execution_time=time.time() - start_time,
            algorithm_name=action.algorithm_name,
            best_parameters={},
            best_score=0.0,
            optimization_history=[],
            data={"message": "Parameter optimization not yet implemented"}
        )

    async def _benchmark_algorithm(self, action: BenchmarkAction) -> AlgorithmObservation:
        """Benchmark algorithm performance."""
        start_time = time.time()

        # TODO: Implement benchmarking
        # This will be implemented in the benchmarking module

        return BenchmarkObservation(
            action_id=action.action_id,
            success=True,
            execution_time=time.time() - start_time,
            algorithm_name=action.algorithm_name,
            benchmark_results={},
            comparison_data={},
            data={"message": "Benchmarking not yet implemented"}
        )

    async def _validate_algorithm(self, action: ValidateAction) -> AlgorithmObservation:
        """Validate algorithm configuration and setup."""
        start_time = time.time()

        try:
            validation_results = {}
            issues_found = []

            # Validate configuration
            config_errors = self.context.config.validate_configuration()
            validation_results["configuration"] = len(config_errors) == 0
            issues_found.extend(config_errors)

            # Check if algorithm exists
            algorithm = self.registry.get_algorithm(action.algorithm_name)
            validation_results["algorithm_exists"] = algorithm is not None
            if algorithm is None:
                issues_found.append(f"Algorithm '{action.algorithm_name}' not found")

            # Additional validation based on level
            if action.validation_level in ["thorough", "comprehensive"]:
                # TODO: Add more comprehensive validation
                pass

            success = len(issues_found) == 0

            return ValidationObservation(
                action_id=action.action_id,
                success=success,
                execution_time=time.time() - start_time,
                algorithm_name=action.algorithm_name,
                validation_results=validation_results,
                issues_found=issues_found,
                data={"validation_level": action.validation_level}
            )

        except Exception as e:
            return ErrorObservation(
                action_id=action.action_id,
                success=False,
                execution_time=time.time() - start_time,
                error_type=type(e).__name__,
                error_message=str(e),
                data={"algorithm_name": action.algorithm_name}
            )

    @asynccontextmanager
    async def _execution_timeout(self):
        """Context manager for execution timeout."""
        # Simply yield - timeout will be handled by asyncio.wait_for in the caller
        yield

    async def execute_pipeline(self, actions: List[AlgorithmAction]) -> List[AlgorithmObservation]:
        """Execute a pipeline of actions."""
        observations = []

        for action in actions:
            observation = await self.execute_action(action)
            observations.append(observation)

            # Stop on error if configured
            if not observation.success:
                self.logger.warning(f"Pipeline stopped due to error in action {action.action_id}")
                break

        return observations

    def create_result(self, action: AlgorithmAction, observations: List[AlgorithmObservation]) -> AlgorithmResult:
        """Create a final result from action and observations."""
        # Find the main execution observation
        execution_obs = None
        for obs in observations:
            if isinstance(obs, AlgorithmExecutionObservation):
                execution_obs = obs
                break

        if execution_obs:
            return AlgorithmResult(
                action_id=action.action_id,
                algorithm_name=execution_obs.algorithm_name,
                success=execution_obs.success,
                output=execution_obs.output_data,
                confidence=execution_obs.confidence,
                execution_time=execution_obs.execution_time,
                parameters_used=self.context.config.parameters.parameters,
                observations=observations,
                metadata=self.context.metadata,
            )
        else:
            # Handle error case
            error_obs = observations[-1] if observations else None
            return AlgorithmResult(
                action_id=action.action_id,
                algorithm_name=getattr(action, 'algorithm_name', 'unknown'),
                success=False,
                output=None,
                confidence=0.0,
                execution_time=error_obs.execution_time if error_obs else 0.0,
                parameters_used=self.context.config.parameters.parameters,
                observations=observations,
                error=error_obs.error if error_obs else "Unknown error",
                metadata=self.context.metadata,
            )
