"""
Core algorithm framework components.

This module provides the foundational classes for the mathematical algorithm framework,
following OpenHands-inspired event-driven architecture patterns.
"""

from .engine import (
    AlgorithmEngine,
    AlgorithmContext,
    AlgorithmState,
)

from .events import (
    AlgorithmAction,
    AlgorithmObservation,
    AlgorithmResult,

    # Specific action types
    ExecuteAlgorithmAction,
    OptimizeParametersAction,
    BenchmarkAction,
    ValidateAction,

    # Specific observation types
    AlgorithmExecutionObservation,
    OptimizationObservation,
    BenchmarkObservation,
    ValidationObservation,
    ErrorObservation,
)

from .config import (
    AlgorithmConfig,
    AlgorithmParameters,
    OptimizationConfig,
    ValidationConfig,
    ParameterDefinition,
    ParameterType,
    ParameterConstraint,
)

from .metrics import (
    AlgorithmMetrics,
    PerformanceTracker,
    BenchmarkSuite,
    MetricsCollector,
)

from .registry import (
    AlgorithmRegistry,
    AlgorithmPlugin,
    BaseAlgorithm,
    AlgorithmMetadata,
    AlgorithmContext,
)

from .base import (
    Algorithm,
    AlgorithmStage,
    AlgorithmPipeline,
    AlgorithmStageResult,
)

__all__ = [
    # Core engine
    "AlgorithmEngine",
    "AlgorithmContext",
    "AlgorithmState",

    # Event system
    "AlgorithmAction",
    "AlgorithmObservation",
    "AlgorithmResult",
    "ExecuteAlgorithmAction",
    "OptimizeParametersAction",
    "BenchmarkAction",
    "ValidateAction",
    "AlgorithmExecutionObservation",
    "OptimizationObservation",
    "BenchmarkObservation",
    "ValidationObservation",
    "ErrorObservation",

    # Configuration
    "AlgorithmConfig",
    "AlgorithmParameters",
    "OptimizationConfig",
    "ValidationConfig",
    "ParameterDefinition",
    "ParameterType",
    "ParameterConstraint",

    # Performance monitoring
    "AlgorithmMetrics",
    "PerformanceTracker",
    "BenchmarkSuite",
    "MetricsCollector",

    # Plugin system
    "AlgorithmRegistry",
    "AlgorithmPlugin",
    "BaseAlgorithm",
    "AlgorithmMetadata",
    "AlgorithmContext",

    # Base classes
    "Algorithm",
    "AlgorithmStage",
    "AlgorithmPipeline",
    "AlgorithmStageResult",
]
