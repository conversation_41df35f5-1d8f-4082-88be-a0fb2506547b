"""
Performance monitoring and metrics collection for mathematical algorithms.

This module provides comprehensive performance tracking with:
- Real-time metrics collection
- Memory and CPU usage monitoring
- Algorithm-specific performance indicators
- Benchmarking and comparison tools
- Historical performance analysis

Following OpenHands patterns for observability and monitoring.
"""

from __future__ import annotations

import psutil
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta

from pydantic import BaseModel, Field


@dataclass
class PerformanceSnapshot:
    """Snapshot of performance metrics at a specific point in time."""
    
    timestamp: float
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    execution_time: float
    custom_metrics: Dict[str, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "timestamp": self.timestamp,
            "cpu_percent": self.cpu_percent,
            "memory_mb": self.memory_mb,
            "memory_percent": self.memory_percent,
            "execution_time": self.execution_time,
            "custom_metrics": self.custom_metrics,
        }


class PerformanceTracker:
    """
    Real-time performance tracking for algorithm execution.
    
    Monitors system resources and algorithm-specific metrics during execution.
    """
    
    def __init__(self, collection_interval: float = 0.1):
        self.collection_interval = collection_interval
        self.is_tracking = False
        self.start_time: Optional[float] = None
        self.snapshots: List[PerformanceSnapshot] = []
        self.process = psutil.Process()
        self.custom_metrics: Dict[str, float] = {}
        
    def start_tracking(self) -> None:
        """Start performance tracking."""
        self.is_tracking = True
        self.start_time = time.time()
        self.snapshots.clear()
        self.custom_metrics.clear()
        
        # Take initial snapshot
        self._take_snapshot()
    
    def stop_tracking(self) -> Dict[str, Any]:
        """Stop tracking and return performance summary."""
        if not self.is_tracking:
            return {}
        
        self.is_tracking = False
        
        # Take final snapshot
        self._take_snapshot()
        
        return self._generate_summary()
    
    def add_custom_metric(self, name: str, value: float) -> None:
        """Add a custom metric value."""
        self.custom_metrics[name] = value
    
    def _take_snapshot(self) -> None:
        """Take a performance snapshot."""
        if not self.is_tracking or self.start_time is None:
            return
        
        try:
            # Get system metrics
            cpu_percent = self.process.cpu_percent()
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024  # Convert to MB
            memory_percent = self.process.memory_percent()
            
            # Calculate execution time
            execution_time = time.time() - self.start_time
            
            snapshot = PerformanceSnapshot(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_mb=memory_mb,
                memory_percent=memory_percent,
                execution_time=execution_time,
                custom_metrics=self.custom_metrics.copy(),
            )
            
            self.snapshots.append(snapshot)
            
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            # Handle cases where process monitoring fails
            pass
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate performance summary from snapshots."""
        if not self.snapshots:
            return {}
        
        # Calculate statistics
        cpu_values = [s.cpu_percent for s in self.snapshots]
        memory_values = [s.memory_mb for s in self.snapshots]
        
        summary = {
            "total_execution_time": self.snapshots[-1].execution_time,
            "cpu_usage": {
                "avg": sum(cpu_values) / len(cpu_values),
                "max": max(cpu_values),
                "min": min(cpu_values),
            },
            "memory_usage": {
                "avg_mb": sum(memory_values) / len(memory_values),
                "max_mb": max(memory_values),
                "min_mb": min(memory_values),
                "peak_percent": max(s.memory_percent for s in self.snapshots),
            },
            "snapshots_count": len(self.snapshots),
            "custom_metrics": self.custom_metrics,
        }
        
        return summary


class AlgorithmMetrics(BaseModel):
    """
    Comprehensive metrics collection for algorithm performance.
    
    Tracks various performance indicators and provides analysis capabilities.
    """
    
    algorithm_name: str = Field(..., description="Name of the algorithm")
    execution_count: int = Field(default=0, description="Number of executions")
    
    # Timing metrics
    total_execution_time: float = Field(default=0.0, description="Total execution time")
    avg_execution_time: float = Field(default=0.0, description="Average execution time")
    min_execution_time: float = Field(default=float('inf'), description="Minimum execution time")
    max_execution_time: float = Field(default=0.0, description="Maximum execution time")
    
    # Accuracy metrics
    total_confidence: float = Field(default=0.0, description="Total confidence scores")
    avg_confidence: float = Field(default=0.0, description="Average confidence")
    min_confidence: float = Field(default=float('inf'), description="Minimum confidence")
    max_confidence: float = Field(default=0.0, description="Maximum confidence")
    
    # Resource usage
    avg_memory_usage: float = Field(default=0.0, description="Average memory usage in MB")
    peak_memory_usage: float = Field(default=0.0, description="Peak memory usage in MB")
    avg_cpu_usage: float = Field(default=0.0, description="Average CPU usage percentage")
    
    # Error tracking
    error_count: int = Field(default=0, description="Number of errors")
    error_rate: float = Field(default=0.0, description="Error rate percentage")
    
    # Custom metrics
    custom_metrics: Dict[str, List[float]] = Field(default_factory=dict, description="Custom metric values")
    
    # Historical data
    recent_executions: List[Dict[str, Any]] = Field(default_factory=list, description="Recent execution data")
    
    def record_execution(
        self,
        execution_time: float,
        confidence: float,
        memory_usage: float = 0.0,
        cpu_usage: float = 0.0,
        success: bool = True,
        custom_metrics: Optional[Dict[str, float]] = None,
    ) -> None:
        """Record metrics from an algorithm execution."""
        self.execution_count += 1
        
        # Update timing metrics
        self.total_execution_time += execution_time
        self.avg_execution_time = self.total_execution_time / self.execution_count
        self.min_execution_time = min(self.min_execution_time, execution_time)
        self.max_execution_time = max(self.max_execution_time, execution_time)
        
        # Update confidence metrics
        if confidence > 0:
            self.total_confidence += confidence
            self.avg_confidence = self.total_confidence / self.execution_count
            self.min_confidence = min(self.min_confidence, confidence)
            self.max_confidence = max(self.max_confidence, confidence)
        
        # Update resource usage
        if memory_usage > 0:
            self.avg_memory_usage = (
                (self.avg_memory_usage * (self.execution_count - 1) + memory_usage) / 
                self.execution_count
            )
            self.peak_memory_usage = max(self.peak_memory_usage, memory_usage)
        
        if cpu_usage > 0:
            self.avg_cpu_usage = (
                (self.avg_cpu_usage * (self.execution_count - 1) + cpu_usage) / 
                self.execution_count
            )
        
        # Update error tracking
        if not success:
            self.error_count += 1
        self.error_rate = (self.error_count / self.execution_count) * 100
        
        # Record custom metrics
        if custom_metrics:
            for name, value in custom_metrics.items():
                if name not in self.custom_metrics:
                    self.custom_metrics[name] = []
                self.custom_metrics[name].append(value)
        
        # Add to recent executions (keep last 100)
        execution_data = {
            "timestamp": datetime.now().isoformat(),
            "execution_time": execution_time,
            "confidence": confidence,
            "memory_usage": memory_usage,
            "cpu_usage": cpu_usage,
            "success": success,
            "custom_metrics": custom_metrics or {},
        }
        
        self.recent_executions.append(execution_data)
        if len(self.recent_executions) > 100:
            self.recent_executions.pop(0)
    
    def get_performance_trend(self, metric: str, window_size: int = 10) -> List[float]:
        """Get performance trend for a specific metric."""
        if metric == "execution_time":
            values = [e["execution_time"] for e in self.recent_executions[-window_size:]]
        elif metric == "confidence":
            values = [e["confidence"] for e in self.recent_executions[-window_size:]]
        elif metric == "memory_usage":
            values = [e["memory_usage"] for e in self.recent_executions[-window_size:]]
        elif metric == "cpu_usage":
            values = [e["cpu_usage"] for e in self.recent_executions[-window_size:]]
        elif metric in self.custom_metrics:
            values = self.custom_metrics[metric][-window_size:]
        else:
            return []
        
        return values
    
    def get_summary(self) -> Dict[str, Any]:
        """Get comprehensive metrics summary."""
        return {
            "algorithm_name": self.algorithm_name,
            "execution_count": self.execution_count,
            "performance": {
                "avg_execution_time": self.avg_execution_time,
                "min_execution_time": self.min_execution_time if self.min_execution_time != float('inf') else 0,
                "max_execution_time": self.max_execution_time,
                "avg_confidence": self.avg_confidence,
                "error_rate": self.error_rate,
            },
            "resources": {
                "avg_memory_usage": self.avg_memory_usage,
                "peak_memory_usage": self.peak_memory_usage,
                "avg_cpu_usage": self.avg_cpu_usage,
            },
            "custom_metrics": {
                name: {
                    "avg": sum(values) / len(values) if values else 0,
                    "count": len(values),
                }
                for name, values in self.custom_metrics.items()
            },
        }


class MetricsCollector:
    """
    Centralized metrics collection and management.
    
    Aggregates metrics from multiple algorithms and provides analysis capabilities.
    """
    
    def __init__(self):
        self.algorithm_metrics: Dict[str, AlgorithmMetrics] = {}
        self.global_metrics = {
            "total_executions": 0,
            "total_errors": 0,
            "total_execution_time": 0.0,
        }
    
    def get_or_create_metrics(self, algorithm_name: str) -> AlgorithmMetrics:
        """Get or create metrics for an algorithm."""
        if algorithm_name not in self.algorithm_metrics:
            self.algorithm_metrics[algorithm_name] = AlgorithmMetrics(algorithm_name=algorithm_name)
        return self.algorithm_metrics[algorithm_name]
    
    def record_execution(self, algorithm_name: str, **kwargs) -> None:
        """Record execution metrics for an algorithm."""
        metrics = self.get_or_create_metrics(algorithm_name)
        metrics.record_execution(**kwargs)
        
        # Update global metrics
        self.global_metrics["total_executions"] += 1
        if not kwargs.get("success", True):
            self.global_metrics["total_errors"] += 1
        self.global_metrics["total_execution_time"] += kwargs.get("execution_time", 0)
    
    def get_algorithm_metrics(self, algorithm_name: str) -> Optional[AlgorithmMetrics]:
        """Get metrics for a specific algorithm."""
        return self.algorithm_metrics.get(algorithm_name)
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """Get metrics for all algorithms."""
        return {
            "global": self.global_metrics,
            "algorithms": {
                name: metrics.get_summary()
                for name, metrics in self.algorithm_metrics.items()
            },
        }
    
    def compare_algorithms(self, metric: str) -> Dict[str, float]:
        """Compare algorithms by a specific metric."""
        comparison = {}
        
        for name, metrics in self.algorithm_metrics.items():
            if metric == "avg_execution_time":
                comparison[name] = metrics.avg_execution_time
            elif metric == "avg_confidence":
                comparison[name] = metrics.avg_confidence
            elif metric == "error_rate":
                comparison[name] = metrics.error_rate
            elif metric == "avg_memory_usage":
                comparison[name] = metrics.avg_memory_usage
        
        return comparison


class BenchmarkSuite:
    """
    Comprehensive benchmarking suite for algorithm performance evaluation.
    """
    
    def __init__(self, name: str):
        self.name = name
        self.benchmarks: List[Dict[str, Any]] = []
        self.results: Dict[str, Dict[str, Any]] = {}
    
    def add_benchmark(
        self,
        name: str,
        test_data: List[Any],
        expected_results: Optional[List[Any]] = None,
        metrics: Optional[List[str]] = None,
    ) -> None:
        """Add a benchmark to the suite."""
        self.benchmarks.append({
            "name": name,
            "test_data": test_data,
            "expected_results": expected_results,
            "metrics": metrics or ["accuracy", "speed", "memory"],
        })
    
    async def run_benchmark(self, algorithm_name: str, engine) -> Dict[str, Any]:
        """Run benchmark suite for an algorithm."""
        # TODO: Implement benchmark execution
        # This will be implemented when we have the full algorithm framework
        
        results = {
            "algorithm_name": algorithm_name,
            "benchmark_suite": self.name,
            "timestamp": datetime.now().isoformat(),
            "results": {},
        }
        
        return results
    
    def compare_results(self, algorithm_names: List[str]) -> Dict[str, Any]:
        """Compare benchmark results across algorithms."""
        # TODO: Implement result comparison
        return {}
