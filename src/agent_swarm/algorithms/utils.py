"""
Utility functions for mathematical algorithm framework.

This module provides convenient functions for:
- Creating and configuring algorithm engines
- Loading and saving algorithm configurations
- Benchmarking and optimization utilities
- Mathematical helper functions
- Common algorithm patterns

Designed for ease of use while maintaining the power of the full framework.
"""

from __future__ import annotations

import math
import statistics
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable

from .core import (
    AlgorithmEngine,
    AlgorithmConfig,
    AlgorithmParameters,
    ParameterDefinition,
    ParameterType,
    ParameterConstraint,
    AlgorithmRegistry,
    ExecuteAlgorithmAction,
    BenchmarkAction,
    OptimizeParametersAction,
)


def create_algorithm_engine(
    algorithm_name: str,
    config_file: Optional[Union[str, Path]] = None,
    parameters: Optional[Dict[str, Any]] = None,
    **kwargs
) -> AlgorithmEngine:
    """
    Create an algorithm engine with simplified configuration.
    
    Args:
        algorithm_name: Name of the algorithm
        config_file: Optional path to configuration file
        parameters: Optional parameter overrides
        **kwargs: Additional configuration options
        
    Returns:
        Configured AlgorithmEngine instance
    """
    # Load configuration
    if config_file:
        config = AlgorithmConfig.from_file(config_file)
    else:
        config = AlgorithmConfig(algorithm_name=algorithm_name)
    
    # Apply parameter overrides
    if parameters:
        for name, value in parameters.items():
            config.parameters.set_parameter(name, value)
    
    # Apply additional configuration
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
    
    # Create and return engine
    return AlgorithmEngine(config)


def load_algorithm_config(
    config_path: Union[str, Path],
    parameter_overrides: Optional[Dict[str, Any]] = None,
) -> AlgorithmConfig:
    """
    Load algorithm configuration from file with optional overrides.
    
    Args:
        config_path: Path to configuration file
        parameter_overrides: Optional parameter overrides
        
    Returns:
        Loaded AlgorithmConfig
    """
    config = AlgorithmConfig.from_file(config_path)
    
    if parameter_overrides:
        for name, value in parameter_overrides.items():
            config.parameters.set_parameter(name, value)
    
    return config


async def benchmark_algorithm(
    algorithm_name: str,
    test_data: List[Any],
    config: Optional[AlgorithmConfig] = None,
    metrics: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """
    Benchmark an algorithm with test data.
    
    Args:
        algorithm_name: Name of algorithm to benchmark
        test_data: Test data for benchmarking
        config: Optional algorithm configuration
        metrics: Optional list of metrics to collect
        
    Returns:
        Benchmark results
    """
    if config is None:
        config = AlgorithmConfig(algorithm_name=algorithm_name)
    
    engine = AlgorithmEngine(config)
    
    action = BenchmarkAction(
        algorithm_name=algorithm_name,
        benchmark_suite="default",
        test_data=test_data,
        metrics=metrics or ["accuracy", "speed", "memory"],
    )
    
    observation = await engine.execute_action(action)
    
    return {
        "success": observation.success,
        "results": observation.data if observation.success else {},
        "error": observation.error,
        "execution_time": observation.execution_time,
    }


async def optimize_algorithm(
    algorithm_name: str,
    training_data: List[Any],
    optimization_strategy: str = "bayesian",
    target_metric: str = "accuracy",
    max_iterations: int = 100,
) -> Dict[str, Any]:
    """
    Optimize algorithm parameters using training data.
    
    Args:
        algorithm_name: Name of algorithm to optimize
        training_data: Training data for optimization
        optimization_strategy: Optimization strategy to use
        target_metric: Metric to optimize
        max_iterations: Maximum optimization iterations
        
    Returns:
        Optimization results
    """
    config = AlgorithmConfig(algorithm_name=algorithm_name)
    config.optimization.strategy = optimization_strategy
    config.optimization.target_metric = target_metric
    config.optimization.max_iterations = max_iterations
    
    engine = AlgorithmEngine(config)
    
    action = OptimizeParametersAction(
        algorithm_name=algorithm_name,
        training_data=training_data,
        optimization_strategy=optimization_strategy,
        target_metric=target_metric,
    )
    
    observation = await engine.execute_action(action)
    
    return {
        "success": observation.success,
        "best_parameters": getattr(observation, 'best_parameters', {}),
        "best_score": getattr(observation, 'best_score', 0.0),
        "optimization_history": getattr(observation, 'optimization_history', []),
        "error": observation.error,
        "execution_time": observation.execution_time,
    }


def create_parameter_definition(
    name: str,
    param_type: str,
    default_value: Any,
    description: str,
    min_value: Optional[Union[int, float]] = None,
    max_value: Optional[Union[int, float]] = None,
    allowed_values: Optional[List[Any]] = None,
    required: bool = False,
    env_var: Optional[str] = None,
    category: str = "general",
) -> ParameterDefinition:
    """
    Create a parameter definition with validation constraints.
    
    Args:
        name: Parameter name
        param_type: Parameter type (float, int, bool, string, list, dict)
        default_value: Default value
        description: Parameter description
        min_value: Minimum value (for numeric types)
        max_value: Maximum value (for numeric types)
        allowed_values: List of allowed values
        required: Whether parameter is required
        env_var: Environment variable name
        category: Parameter category
        
    Returns:
        ParameterDefinition instance
    """
    # Create constraints
    constraints = None
    if any([min_value is not None, max_value is not None, allowed_values is not None]):
        constraints = ParameterConstraint(
            min_value=min_value,
            max_value=max_value,
            allowed_values=allowed_values,
        )
    
    return ParameterDefinition(
        name=name,
        param_type=ParameterType(param_type),
        default_value=default_value,
        description=description,
        required=required,
        constraints=constraints,
        env_var=env_var,
        category=category,
    )


# Mathematical utility functions

def calculate_confidence(
    score: float,
    threshold: float = 0.5,
    scaling_factor: float = 1.0,
) -> float:
    """
    Calculate confidence score from a raw score.
    
    Args:
        score: Raw score value
        threshold: Threshold for confidence calculation
        scaling_factor: Scaling factor for confidence
        
    Returns:
        Confidence value between 0 and 1
    """
    if score < threshold:
        return 0.0
    
    # Sigmoid-like transformation
    confidence = 1.0 / (1.0 + math.exp(-scaling_factor * (score - threshold)))
    return min(1.0, max(0.0, confidence))


def normalize_scores(scores: List[float], method: str = "min_max") -> List[float]:
    """
    Normalize a list of scores.
    
    Args:
        scores: List of scores to normalize
        method: Normalization method (min_max, z_score, robust)
        
    Returns:
        Normalized scores
    """
    if not scores:
        return []
    
    if method == "min_max":
        min_score = min(scores)
        max_score = max(scores)
        if max_score == min_score:
            return [1.0] * len(scores)
        return [(s - min_score) / (max_score - min_score) for s in scores]
    
    elif method == "z_score":
        mean_score = statistics.mean(scores)
        std_score = statistics.stdev(scores) if len(scores) > 1 else 1.0
        if std_score == 0:
            return [0.0] * len(scores)
        return [(s - mean_score) / std_score for s in scores]
    
    elif method == "robust":
        median_score = statistics.median(scores)
        mad = statistics.median([abs(s - median_score) for s in scores])
        if mad == 0:
            return [0.0] * len(scores)
        return [(s - median_score) / mad for s in scores]
    
    else:
        raise ValueError(f"Unknown normalization method: {method}")


def compute_similarity(
    vector1: List[float],
    vector2: List[float],
    method: str = "cosine",
) -> float:
    """
    Compute similarity between two vectors.
    
    Args:
        vector1: First vector
        vector2: Second vector
        method: Similarity method (cosine, euclidean, manhattan)
        
    Returns:
        Similarity score
    """
    if len(vector1) != len(vector2):
        raise ValueError("Vectors must have the same length")
    
    if not vector1:
        return 0.0
    
    if method == "cosine":
        dot_product = sum(a * b for a, b in zip(vector1, vector2))
        norm1 = math.sqrt(sum(a * a for a in vector1))
        norm2 = math.sqrt(sum(b * b for b in vector2))
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    elif method == "euclidean":
        distance = math.sqrt(sum((a - b) ** 2 for a, b in zip(vector1, vector2)))
        # Convert distance to similarity (0 = identical, higher = more different)
        return 1.0 / (1.0 + distance)
    
    elif method == "manhattan":
        distance = sum(abs(a - b) for a, b in zip(vector1, vector2))
        return 1.0 / (1.0 + distance)
    
    else:
        raise ValueError(f"Unknown similarity method: {method}")


def apply_threshold(
    values: List[float],
    threshold: float,
    mode: str = "binary",
) -> List[Union[float, bool]]:
    """
    Apply threshold to a list of values.
    
    Args:
        values: List of values
        threshold: Threshold value
        mode: Threshold mode (binary, soft, clamp)
        
    Returns:
        Thresholded values
    """
    if mode == "binary":
        return [v >= threshold for v in values]
    
    elif mode == "soft":
        # Soft thresholding (values below threshold become 0)
        return [v if v >= threshold else 0.0 for v in values]
    
    elif mode == "clamp":
        # Clamp values to threshold minimum
        return [max(v, threshold) for v in values]
    
    else:
        raise ValueError(f"Unknown threshold mode: {mode}")


def create_default_registry() -> AlgorithmRegistry:
    """
    Create a default algorithm registry with built-in algorithms.
    
    Returns:
        AlgorithmRegistry with built-in algorithms registered
    """
    registry = AlgorithmRegistry()
    
    # TODO: Register built-in algorithms when they are implemented
    # registry.register_algorithm(AdaptiveIntentProcessor)
    # registry.register_algorithm(IntentFilteringTriangle)
    # etc.
    
    return registry


def validate_algorithm_input(
    input_data: Any,
    expected_type: type,
    min_size: Optional[int] = None,
    max_size: Optional[int] = None,
) -> bool:
    """
    Validate algorithm input data.
    
    Args:
        input_data: Input data to validate
        expected_type: Expected data type
        min_size: Minimum size (for collections)
        max_size: Maximum size (for collections)
        
    Returns:
        True if input is valid, False otherwise
    """
    # Check type
    if not isinstance(input_data, expected_type):
        return False
    
    # Check size constraints for collections
    if hasattr(input_data, '__len__'):
        size = len(input_data)
        if min_size is not None and size < min_size:
            return False
        if max_size is not None and size > max_size:
            return False
    
    return True


def create_algorithm_template(
    algorithm_name: str,
    output_path: Union[str, Path],
    category: str = "custom",
    complexity: str = "O(n)",
) -> None:
    """
    Create a template file for a new algorithm.
    
    Args:
        algorithm_name: Name of the new algorithm
        output_path: Path where to create the template file
        category: Algorithm category
        complexity: Algorithm complexity
    """
    template = f'''"""
{algorithm_name} algorithm implementation.

This algorithm provides [DESCRIPTION].
"""

from datetime import datetime
from typing import Any

from agent_swarm.algorithms.core import (
    Algorithm,
    AlgorithmContext,
    AlgorithmResult,
    AlgorithmMetadata,
)


class {algorithm_name}(Algorithm):
    """
    {algorithm_name} algorithm.
    
    [DETAILED DESCRIPTION]
    """
    
    @property
    def metadata(self) -> AlgorithmMetadata:
        """Get algorithm metadata."""
        return AlgorithmMetadata(
            name="{algorithm_name.lower()}",
            version="1.0.0",
            description="{algorithm_name} algorithm",
            author="Your Name",
            category="{category}",
            tags=["{category}", "custom"],
            created_date=datetime.now(),
            updated_date=datetime.now(),
            dependencies=[],
            input_types=["any"],
            output_types=["any"],
            complexity="{complexity}",
        )
    
    def validate_input(self, input_data: Any) -> bool:
        """Validate input data."""
        # TODO: Implement input validation
        return True
    
    async def execute(self, input_data: Any, context: AlgorithmContext) -> AlgorithmResult:
        """Execute the algorithm."""
        import time
        from uuid import uuid4
        
        start_time = time.time()
        
        try:
            # TODO: Implement algorithm logic
            output = input_data  # Placeholder
            confidence = 1.0  # Placeholder
            
            execution_time = time.time() - start_time
            
            return AlgorithmResult(
                action_id=str(uuid4()),
                algorithm_name=self.metadata.name,
                success=True,
                output=output,
                confidence=confidence,
                execution_time=execution_time,
                metadata={{"algorithm_version": self.metadata.version}},
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            return AlgorithmResult(
                action_id=str(uuid4()),
                algorithm_name=self.metadata.name,
                success=False,
                output=None,
                confidence=0.0,
                execution_time=execution_time,
                error=str(e),
            )
'''
    
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w') as f:
        f.write(template)
    
    print(f"Algorithm template created at: {output_path}")
