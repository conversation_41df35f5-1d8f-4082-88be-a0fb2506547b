"""
Mathematical Algorithm Framework for Agent Swarm.

This module provides a comprehensive, reusable framework for mathematical algorithms
with OpenHands-inspired architecture patterns:

- Event-driven algorithm execution (Action → Observation)
- Configurable algorithm parameters and optimization
- Performance monitoring and metrics collection
- Extensible plugin architecture for custom algorithms
- Production-ready error handling and validation

Key Components:
- AlgorithmEngine: Core execution engine with event system
- AlgorithmConfig: Comprehensive configuration management
- AlgorithmMetrics: Performance monitoring and analytics
- AlgorithmRegistry: Plugin system for algorithm discovery
- AlgorithmOptimizer: Automatic parameter optimization

Usage:
    # Basic algorithm execution
    from agent_swarm.algorithms import AlgorithmEngine, AlgorithmConfig

    config = AlgorithmConfig(
        algorithm_type="adaptive_intent_processing",
        parameters={"confidence_threshold": 0.7}
    )

    engine = AlgorithmEngine(config)
    result = await engine.execute(input_data)

    # Advanced optimization
    from agent_swarm.algorithms import AlgorithmOptimizer

    optimizer = AlgorithmOptimizer(engine)
    optimized_params = await optimizer.optimize(training_data)

Architecture:
    This framework follows OpenHands patterns:

    1. Event-Driven: Algorithm execution uses Action/Observation pattern
    2. Configurable: Comprehensive configuration with validation
    3. Observable: Built-in metrics and performance monitoring
    4. Extensible: Plugin system for custom algorithms
    5. Testable: Comprehensive testing and benchmarking support
"""

from .core import (
    # Core algorithm framework
    AlgorithmEngine,
    AlgorithmAction,
    AlgorithmObservation,
    AlgorithmResult,
    AlgorithmState,
    AlgorithmContext,

    # Event system
    ExecuteAlgorithmAction,
    OptimizeParametersAction,
    BenchmarkAction,
    ValidateAction,
    AlgorithmExecutionObservation,
    OptimizationObservation,
    BenchmarkObservation,
    ValidationObservation,
    ErrorObservation,

    # Configuration system
    AlgorithmConfig,
    AlgorithmParameters,
    OptimizationConfig,
    ValidationConfig,
    ParameterDefinition,
    ParameterType,
    ParameterConstraint,

    # Performance monitoring
    AlgorithmMetrics,
    PerformanceTracker,
    BenchmarkSuite,
    MetricsCollector,

    # Plugin system
    AlgorithmRegistry,
    AlgorithmPlugin,
    BaseAlgorithm,
    AlgorithmMetadata,
)

# Optimization imports commented out until module is created
# from .optimization import (
#     AlgorithmOptimizer,
#     OptimizationStrategy,
#     ParameterSpace,
#     OptimizationResult,
#     GridSearchOptimizer,
#     BayesianOptimizer,
#     GeneticOptimizer,
#     GradientOptimizer,
# )

from .algorithms import (
    # Built-in algorithms
    AdaptiveIntentProcessor,
    IntentFilteringTriangle,
    ConfidenceThresholder,
    PatternMatcher,
    # VectorSpaceClassifier,  # Not implemented yet

    # Algorithm stages
    ExplicitCommandStage,
    PatternMatchingStage,
    ContextualAnalysisStage,
    DeepReasoningStage,
    MultiAgentConsensusStage,
)

from .utils import (
    # Utility functions
    create_algorithm_engine,
    load_algorithm_config,
    benchmark_algorithm,
    optimize_algorithm,

    # Mathematical utilities
    calculate_confidence,
    normalize_scores,
    compute_similarity,
    apply_threshold,
)

# Version and metadata
__version__ = "1.0.0"
__author__ = "Agent Swarm Contributors"

__all__ = [
    # Core framework
    "AlgorithmEngine",
    "AlgorithmAction",
    "AlgorithmObservation",
    "AlgorithmResult",
    "AlgorithmState",
    "AlgorithmContext",

    # Event system
    "ExecuteAlgorithmAction",
    "OptimizeParametersAction",
    "BenchmarkAction",
    "ValidateAction",
    "AlgorithmExecutionObservation",
    "OptimizationObservation",
    "BenchmarkObservation",
    "ValidationObservation",
    "ErrorObservation",

    # Configuration
    "AlgorithmConfig",
    "AlgorithmParameters",
    "OptimizationConfig",
    "ValidationConfig",
    "ParameterDefinition",
    "ParameterType",
    "ParameterConstraint",

    # Performance monitoring
    "AlgorithmMetrics",
    "PerformanceTracker",
    "BenchmarkSuite",
    "MetricsCollector",

    # Plugin system
    "AlgorithmRegistry",
    "AlgorithmPlugin",
    "BaseAlgorithm",
    "AlgorithmMetadata",

    # Optimization (commented out until module is created)
    # "AlgorithmOptimizer",
    # "OptimizationStrategy",
    # "ParameterSpace",
    # "OptimizationResult",
    # "GridSearchOptimizer",
    # "BayesianOptimizer",
    # "GeneticOptimizer",
    # "GradientOptimizer",

    # Built-in algorithms
    "AdaptiveIntentProcessor",
    "IntentFilteringTriangle",
    "ConfidenceThresholder",
    "PatternMatcher",
    # "VectorSpaceClassifier",  # Not implemented yet

    # Algorithm stages
    "ExplicitCommandStage",
    "PatternMatchingStage",
    "ContextualAnalysisStage",
    "DeepReasoningStage",
    "MultiAgentConsensusStage",

    # Utilities
    "create_algorithm_engine",
    "load_algorithm_config",
    "benchmark_algorithm",
    "optimize_algorithm",
    "calculate_confidence",
    "normalize_scores",
    "compute_similarity",
    "apply_threshold",

    # Metadata
    "__version__",
    "__author__",
]
