"""
Pattern Matcher Algorithm.

This algorithm performs pattern matching on text input using various
matching strategies including regex, fuzzy matching, and semantic similarity.

Features:
- Multiple matching strategies (exact, regex, fuzzy, semantic)
- Configurable similarity thresholds
- Pattern compilation and caching
- Performance optimization for large pattern sets

This demonstrates advanced algorithm capabilities within the framework.
"""

import re
import time
from datetime import datetime
from typing import Any, Dict, List, Tuple, Optional
from uuid import uuid4

from ..core import (
    Algorithm,
    AlgorithmContext,
    AlgorithmResult,
)
from ..core.registry import AlgorithmMetadata
from ..core.config import (
    ParameterDefinition,
    ParameterType,
    ParameterConstraint,
)


class PatternMatcher(Algorithm):
    """
    Pattern matching algorithm with multiple strategies.

    This algorithm matches input text against a set of patterns using
    various strategies including exact matching, regex, fuzzy matching,
    and semantic similarity.

    Input: Text string or dict with 'text' and 'patterns'
    Output: List of matches with scores and metadata
    """

    def __init__(self):
        super().__init__()
        self._compiled_patterns: Dict[str, re.Pattern] = {}
        self._pattern_cache: Dict[str, List[Tuple[str, float]]] = {}

    @property
    def metadata(self) -> AlgorithmMetadata:
        """Get algorithm metadata."""
        return AlgorithmMetadata(
            name="pattern_matcher",
            version="1.0.0",
            description="Pattern matching with multiple strategies and optimization",
            author="Agent Swarm Framework",
            category="pattern_matching",
            tags=["pattern", "matching", "regex", "fuzzy", "text"],
            created_date=datetime(2025, 1, 6),
            updated_date=datetime(2025, 1, 6),
            dependencies=[],
            input_types=["string", "dict"],
            output_types=["list", "dict"],
            complexity="O(n*m)",  # n = text length, m = number of patterns
            documentation_url="https://github.com/agent-swarm/docs/pattern-matcher",
        )

    def get_parameter_definitions(self) -> List[ParameterDefinition]:
        """Get parameter definitions for this algorithm."""
        return [
            ParameterDefinition(
                name="strategy",
                param_type=ParameterType.STRING,
                default_value="fuzzy",
                description="Pattern matching strategy",
                required=True,
                constraints=ParameterConstraint(
                    allowed_values=["exact", "regex", "fuzzy", "semantic", "hybrid"]
                ),
                category="matching",
            ),
            ParameterDefinition(
                name="similarity_threshold",
                param_type=ParameterType.FLOAT,
                default_value=0.7,
                description="Minimum similarity threshold for matches",
                required=False,
                constraints=ParameterConstraint(min_value=0.0, max_value=1.0),
                category="matching",
            ),
            ParameterDefinition(
                name="case_sensitive",
                param_type=ParameterType.BOOL,
                default_value=False,
                description="Whether matching should be case sensitive",
                required=False,
                category="matching",
            ),
            ParameterDefinition(
                name="max_matches",
                param_type=ParameterType.INT,
                default_value=10,
                description="Maximum number of matches to return",
                required=False,
                constraints=ParameterConstraint(min_value=1, max_value=1000),
                category="output",
            ),
            ParameterDefinition(
                name="enable_caching",
                param_type=ParameterType.BOOL,
                default_value=True,
                description="Enable pattern compilation and result caching",
                required=False,
                category="performance",
            ),
            ParameterDefinition(
                name="fuzzy_ratio_threshold",
                param_type=ParameterType.FLOAT,
                default_value=80.0,
                description="Threshold for fuzzy string matching (0-100)",
                required=False,
                constraints=ParameterConstraint(min_value=0.0, max_value=100.0),
                category="fuzzy",
            ),
        ]

    def validate_input(self, input_data: Any) -> bool:
        """Validate input data."""
        if isinstance(input_data, str):
            return True

        if isinstance(input_data, dict):
            # Must have 'text' field and optionally 'patterns'
            return 'text' in input_data and isinstance(input_data['text'], str)

        return False

    def estimate_complexity(self, input_size: int) -> float:
        """Estimate computational complexity."""
        # Complexity depends on strategy and number of patterns
        # Simplified estimate: O(n*m) where n=text length, m=patterns
        return float(input_size * 10)  # Assume 10 patterns on average

    async def execute(self, input_data: Any, context: AlgorithmContext) -> AlgorithmResult:
        """Execute the pattern matching algorithm."""
        start_time = time.time()

        try:
            # Extract parameters
            strategy = context.get_parameter("strategy", "fuzzy")
            similarity_threshold = context.get_parameter("similarity_threshold", 0.7)
            case_sensitive = context.get_parameter("case_sensitive", False)
            max_matches = context.get_parameter("max_matches", 10)
            enable_caching = context.get_parameter("enable_caching", True)
            fuzzy_ratio_threshold = context.get_parameter("fuzzy_ratio_threshold", 80.0)

            # Extract text and patterns
            if isinstance(input_data, str):
                text = input_data
                patterns = self._get_default_patterns()
            else:
                text = input_data['text']
                patterns = input_data.get('patterns', self._get_default_patterns())

            context.update_progress(0.1, "Preprocessing input")

            # Preprocess text
            processed_text = text if case_sensitive else text.lower()

            # Preprocess patterns
            processed_patterns = []
            for pattern in patterns:
                if isinstance(pattern, str):
                    processed_patterns.append({
                        'pattern': pattern if case_sensitive else pattern.lower(),
                        'original': pattern,
                        'weight': 1.0,
                    })
                elif isinstance(pattern, dict):
                    processed_patterns.append({
                        'pattern': pattern['pattern'] if case_sensitive else pattern['pattern'].lower(),
                        'original': pattern['pattern'],
                        'weight': pattern.get('weight', 1.0),
                    })

            context.update_progress(0.3, f"Applying {strategy} matching")

            # Apply matching strategy
            if strategy == "exact":
                matches = self._exact_match(processed_text, processed_patterns)
            elif strategy == "regex":
                matches = self._regex_match(processed_text, processed_patterns, enable_caching)
            elif strategy == "fuzzy":
                matches = self._fuzzy_match(processed_text, processed_patterns, fuzzy_ratio_threshold)
            elif strategy == "semantic":
                matches = self._semantic_match(processed_text, processed_patterns)
            elif strategy == "hybrid":
                matches = self._hybrid_match(processed_text, processed_patterns, fuzzy_ratio_threshold)
            else:
                raise ValueError(f"Unknown matching strategy: {strategy}")

            context.update_progress(0.7, "Filtering and ranking matches")

            # Filter by similarity threshold and limit results
            filtered_matches = [
                match for match in matches
                if match['similarity'] >= similarity_threshold
            ]

            # Sort by similarity (descending) and weight
            filtered_matches.sort(
                key=lambda x: (x['similarity'] * x.get('weight', 1.0)),
                reverse=True
            )

            # Limit results
            final_matches = filtered_matches[:max_matches]

            # Calculate result confidence
            context.update_progress(0.9, "Calculating confidence")
            result_confidence = self._calculate_confidence(final_matches, similarity_threshold)

            # Prepare output
            output = {
                'matches': final_matches,
                'total_matches': len(filtered_matches),
                'strategy_used': strategy,
                'text_length': len(text),
                'patterns_tested': len(processed_patterns),
            }

            execution_time = time.time() - start_time
            context.update_progress(1.0, "Pattern matching completed")

            return AlgorithmResult(
                action_id=str(uuid4()),
                algorithm_name=self.metadata.name,
                success=True,
                output=output,
                confidence=result_confidence,
                execution_time=execution_time,
                parameters_used={
                    "strategy": strategy,
                    "similarity_threshold": similarity_threshold,
                    "case_sensitive": case_sensitive,
                    "max_matches": max_matches,
                    "fuzzy_ratio_threshold": fuzzy_ratio_threshold,
                },
                metadata={
                    "input_text_length": len(text),
                    "patterns_count": len(patterns),
                    "matches_found": len(final_matches),
                    "avg_similarity": sum(m['similarity'] for m in final_matches) / len(final_matches) if final_matches else 0,
                    "strategy_used": strategy,
                    "algorithm_version": self.metadata.version,
                },
            )

        except Exception as e:
            execution_time = time.time() - start_time

            return AlgorithmResult(
                action_id=str(uuid4()),
                algorithm_name=self.metadata.name,
                success=False,
                output=None,
                confidence=0.0,
                execution_time=execution_time,
                error=f"Pattern matching failed: {str(e)}",
                metadata={
                    "error_type": type(e).__name__,
                    "input_type": type(input_data).__name__,
                },
            )

    def _get_default_patterns(self) -> List[str]:
        """Get default patterns for demonstration."""
        return [
            "hello",
            "world",
            "pattern",
            "match",
            "algorithm",
            "test",
            "example",
            "demo",
        ]

    def _exact_match(self, text: str, patterns: List[Dict]) -> List[Dict]:
        """Perform exact string matching."""
        matches = []

        for pattern_info in patterns:
            pattern = pattern_info['pattern']
            if pattern in text:
                # Find all occurrences
                start = 0
                while True:
                    pos = text.find(pattern, start)
                    if pos == -1:
                        break

                    matches.append({
                        'pattern': pattern_info['original'],
                        'similarity': 1.0,
                        'position': pos,
                        'length': len(pattern),
                        'weight': pattern_info['weight'],
                        'match_type': 'exact',
                    })

                    start = pos + 1

        return matches

    def _regex_match(self, text: str, patterns: List[Dict], enable_caching: bool) -> List[Dict]:
        """Perform regex pattern matching."""
        matches = []

        for pattern_info in patterns:
            pattern = pattern_info['pattern']

            try:
                # Compile and cache regex patterns
                if enable_caching:
                    if pattern not in self._compiled_patterns:
                        self._compiled_patterns[pattern] = re.compile(pattern)
                    regex = self._compiled_patterns[pattern]
                else:
                    regex = re.compile(pattern)

                # Find all matches
                for match in regex.finditer(text):
                    matches.append({
                        'pattern': pattern_info['original'],
                        'similarity': 1.0,  # Regex matches are binary
                        'position': match.start(),
                        'length': match.end() - match.start(),
                        'matched_text': match.group(),
                        'weight': pattern_info['weight'],
                        'match_type': 'regex',
                    })

            except re.error:
                # Invalid regex pattern, skip
                continue

        return matches

    def _fuzzy_match(self, text: str, patterns: List[Dict], threshold: float) -> List[Dict]:
        """Perform fuzzy string matching."""
        matches = []

        # Simple fuzzy matching using character-based similarity
        for pattern_info in patterns:
            pattern = pattern_info['pattern']

            # Sliding window approach for fuzzy matching
            pattern_len = len(pattern)
            best_similarity = 0.0
            best_position = -1

            for i in range(len(text) - pattern_len + 1):
                window = text[i:i + pattern_len]
                similarity = self._calculate_string_similarity(pattern, window)

                if similarity > best_similarity and similarity * 100 >= threshold:
                    best_similarity = similarity
                    best_position = i

            if best_position >= 0:
                matches.append({
                    'pattern': pattern_info['original'],
                    'similarity': best_similarity,
                    'position': best_position,
                    'length': pattern_len,
                    'matched_text': text[best_position:best_position + pattern_len],
                    'weight': pattern_info['weight'],
                    'match_type': 'fuzzy',
                })

        return matches

    def _semantic_match(self, text: str, patterns: List[Dict]) -> List[Dict]:
        """Perform semantic similarity matching."""
        # Placeholder for semantic matching
        # In a real implementation, this would use embeddings or NLP models
        matches = []

        for pattern_info in patterns:
            pattern = pattern_info['pattern']

            # Simple semantic similarity based on word overlap
            text_words = set(text.lower().split())
            pattern_words = set(pattern.lower().split())

            if pattern_words and text_words:
                overlap = len(text_words.intersection(pattern_words))
                similarity = overlap / len(pattern_words.union(text_words))

                if similarity > 0:
                    matches.append({
                        'pattern': pattern_info['original'],
                        'similarity': similarity,
                        'position': -1,  # No specific position for semantic matches
                        'length': -1,
                        'weight': pattern_info['weight'],
                        'match_type': 'semantic',
                        'word_overlap': overlap,
                    })

        return matches

    def _hybrid_match(self, text: str, patterns: List[Dict], fuzzy_threshold: float) -> List[Dict]:
        """Perform hybrid matching combining multiple strategies."""
        # Combine exact, fuzzy, and semantic matching
        exact_matches = self._exact_match(text, patterns)
        fuzzy_matches = self._fuzzy_match(text, patterns, fuzzy_threshold)
        semantic_matches = self._semantic_match(text, patterns)

        # Merge and deduplicate matches
        all_matches = exact_matches + fuzzy_matches + semantic_matches

        # Remove duplicates based on pattern and position
        unique_matches = []
        seen = set()

        for match in all_matches:
            key = (match['pattern'], match.get('position', -1))
            if key not in seen:
                seen.add(key)
                unique_matches.append(match)

        return unique_matches

    def _calculate_string_similarity(self, str1: str, str2: str) -> float:
        """Calculate similarity between two strings."""
        if not str1 or not str2:
            return 0.0

        if str1 == str2:
            return 1.0

        # Simple character-based similarity (Jaccard-like)
        set1 = set(str1)
        set2 = set(str2)

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def _calculate_confidence(self, matches: List[Dict], threshold: float) -> float:
        """Calculate confidence in the matching results."""
        if not matches:
            return 0.0

        # Base confidence on match quality and quantity
        avg_similarity = sum(match['similarity'] for match in matches) / len(matches)

        # Adjust for number of matches (more matches = higher confidence, up to a point)
        match_count_factor = min(1.0, len(matches) / 5.0)

        # Adjust for how much above threshold the matches are
        threshold_factor = max(0.0, (avg_similarity - threshold) / (1.0 - threshold))

        confidence = avg_similarity * 0.6 + match_count_factor * 0.2 + threshold_factor * 0.2

        return max(0.0, min(1.0, confidence))
