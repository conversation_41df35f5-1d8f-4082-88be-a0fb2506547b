"""
Algorithm stages for building processing pipelines.

This module contains stage implementations that can be composed into
algorithm pipelines for complex processing workflows.

Stages follow the OpenHands-inspired architecture with:
- Clear input/output contracts
- Dependency management
- Performance monitoring
- Error handling and recovery

Available stages:
- ExplicitCommandStage: O(1) - Direct command processing
- PatternMatchingStage: O(log n) - Efficient pattern matching
- ContextualAnalysisStage: O(n) - Context-aware analysis
- DeepReasoningStage: O(n²) - Complex reasoning algorithms
- MultiAgentConsensusStage: O(n³) - Multi-agent decision making
"""

from .explicit_command_stage import ExplicitCommandStage
from .pattern_matching_stage import PatternMatchingStage
from .contextual_analysis_stage import ContextualAnalysisStage
from .deep_reasoning_stage import DeepReasoningStage
from .multi_agent_consensus_stage import MultiAgentConsensusStage

__all__ = [
    "ExplicitCommandStage",
    "PatternMatchingStage", 
    "ContextualAnalysisStage",
    "DeepReasoningStage",
    "MultiAgentConsensusStage",
]
