"""
Contextual Analysis Stage - O(n) complexity.

This stage performs contextual analysis of user input considering
conversation history, project context, and environmental factors.

Features:
- O(n) linear complexity
- Context-aware processing
- History integration
- Environmental awareness
- Semantic understanding

This stage is typically third in the adaptive intent processing pipeline.
"""

import time
from datetime import datetime
from typing import Any, Dict, List
from uuid import uuid4

from ...core import (
    AlgorithmStage,
    AlgorithmContext,
    AlgorithmStageResult,
    AlgorithmMetadata,
)


class ContextualAnalysisStage(AlgorithmStage):
    """
    Stage for contextual analysis with O(n) complexity.

    This stage analyzes input in the context of conversation history,
    project state, and environmental factors.

    Input: Text with context information
    Output: Contextual analysis results
    """

    def __init__(self):
        super().__init__("contextual_analysis")

    @property
    def metadata(self) -> AlgorithmMetadata:
        """Get stage metadata."""
        return AlgorithmMetadata(
            name="contextual_analysis_stage",
            version="1.0.0",
            description="O(n) contextual analysis stage",
            author="Agent Swarm Framework",
            category="contextual_analysis",
            tags=["context", "analysis", "O(n)", "semantic", "history"],
            created_date=datetime(2025, 1, 6),
            updated_date=datetime(2025, 1, 6),
            dependencies=[],
            input_types=["string", "dict"],
            output_types=["dict"],
            complexity="O(n)",
        )

    def validate_input(self, input_data: Any) -> bool:
        """Validate input data."""
        return isinstance(input_data, (str, dict))

    def estimate_complexity(self, input_size: int) -> float:
        """Estimate computational complexity - O(n)."""
        return float(input_size)

    async def process(self, input_data: Any, context: AlgorithmContext) -> AlgorithmStageResult:
        """Process contextual analysis with O(n) complexity."""
        start_time = time.time()

        try:
            # Extract input and previous stage results
            if isinstance(input_data, str):
                text = input_data
                previous_results = {}
            else:
                text = input_data.get('text') or input_data.get('intent', str(input_data))
                previous_results = input_data.get('previous_stage_results', {})

            context.update_progress(0.2, "Analyzing contextual factors")

            # Analyze various contextual factors
            context_factors = []

            # 1. Temporal context
            temporal_context = self._analyze_temporal_context(text)
            if temporal_context['relevance'] > 0.3:
                context_factors.append(temporal_context)

            # 2. Semantic context
            semantic_context = self._analyze_semantic_context(text, previous_results)
            if semantic_context['relevance'] > 0.3:
                context_factors.append(semantic_context)

            # 3. Intent progression context
            progression_context = self._analyze_intent_progression(text, previous_results)
            if progression_context['relevance'] > 0.3:
                context_factors.append(progression_context)

            # 4. Domain context
            domain_context = self._analyze_domain_context(text)
            if domain_context['relevance'] > 0.3:
                context_factors.append(domain_context)

            context.update_progress(0.6, "Integrating contextual information")

            # 5. Environmental context
            env_context = self._analyze_environmental_context(context)
            if env_context['relevance'] > 0.3:
                context_factors.append(env_context)

            # Calculate contextual confidence
            contextual_confidence = self._calculate_contextual_confidence(
                context_factors, text, previous_results
            )

            # Generate contextual insights
            insights = self._generate_contextual_insights(context_factors, text)

            context.update_progress(0.9, "Finalizing contextual analysis")

            # Prepare output
            output = {
                'stage': 'contextual_analysis',
                'text_analyzed': text,
                'context_factors': context_factors,
                'contextual_insights': insights,
                'confidence': contextual_confidence,
                'processing_method': 'multi_factor_analysis',
                'complexity': 'O(n)',
                'factors_analyzed': len(context_factors),
                'previous_stage_integration': bool(previous_results),
            }

            execution_time = time.time() - start_time
            context.update_progress(1.0, "Contextual analysis completed")

            return AlgorithmStageResult(
                stage_name=self.stage_name,
                success=True,
                output=output,
                confidence=contextual_confidence,
                execution_time=execution_time,
                metadata={
                    'stage_type': 'contextual_analysis',
                    'factors_found': len(context_factors),
                    'text_length': len(text),
                    'processing_efficiency': 'O(n)',
                    'implemented': True,
                },
            )

        except Exception as e:
            execution_time = time.time() - start_time

            return AlgorithmStageResult(
                stage_name=self.stage_name,
                success=False,
                output=None,
                confidence=0.0,
                execution_time=execution_time,
                error=f"Contextual analysis failed: {str(e)}",
                metadata={'stage_type': 'contextual_analysis', 'error_type': type(e).__name__},
            )

    def _analyze_temporal_context(self, text: str) -> Dict[str, Any]:
        """Analyze temporal context indicators in the text."""
        import re

        temporal_indicators = {
            'immediate': r'\b(now|immediately|urgent|asap|right away)\b',
            'short_term': r'\b(today|tonight|this (morning|afternoon|evening))\b',
            'medium_term': r'\b(tomorrow|this week|next week|soon)\b',
            'long_term': r'\b(next month|next year|eventually|someday)\b',
            'past': r'\b(yesterday|last (week|month|year)|previously|before)\b',
            'ongoing': r'\b(currently|ongoing|in progress|continuing)\b',
        }

        temporal_matches = {}
        total_relevance = 0.0

        text_lower = text.lower()
        for category, pattern in temporal_indicators.items():
            matches = re.findall(pattern, text_lower)
            if matches:
                relevance = min(0.8, len(matches) * 0.3)
                temporal_matches[category] = {
                    'matches': matches,
                    'relevance': relevance,
                }
                total_relevance += relevance

        return {
            'type': 'temporal',
            'relevance': min(0.9, total_relevance),
            'indicators': temporal_matches,
            'primary_timeframe': max(temporal_matches.keys(), key=lambda k: temporal_matches[k]['relevance']) if temporal_matches else 'unspecified',
        }

    def _analyze_semantic_context(self, text: str, previous_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze semantic context and relationships."""
        # Extract semantic indicators
        semantic_indicators = self._extract_semantic_indicators(text)

        # Analyze relationship to previous stages
        stage_coherence = 0.5  # Default coherence
        if previous_results:
            stage_coherence = self._calculate_stage_coherence(text, previous_results)

        # Calculate semantic density
        semantic_density = len(semantic_indicators) / max(1, len(text.split()))

        relevance = min(0.9, (semantic_density * 2 + stage_coherence) / 2)

        return {
            'type': 'semantic',
            'relevance': relevance,
            'indicators': semantic_indicators,
            'stage_coherence': stage_coherence,
            'semantic_density': semantic_density,
        }

    def _analyze_intent_progression(self, text: str, previous_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze intent progression and evolution."""
        progression_indicators = {
            'clarification': ['clarify', 'explain', 'what do you mean', 'more details'],
            'refinement': ['actually', 'instead', 'rather', 'modify', 'change'],
            'expansion': ['also', 'additionally', 'furthermore', 'and', 'plus'],
            'completion': ['done', 'finished', 'complete', 'that\'s all', 'end'],
            'continuation': ['continue', 'next', 'then', 'after that', 'proceed'],
        }

        text_lower = text.lower()
        progression_matches = {}

        for category, indicators in progression_indicators.items():
            matches = [ind for ind in indicators if ind in text_lower]
            if matches:
                progression_matches[category] = {
                    'matches': matches,
                    'strength': len(matches) * 0.2,
                }

        # Analyze progression coherence with previous stages
        progression_coherence = 0.5
        if previous_results and progression_matches:
            progression_coherence = 0.7  # Higher coherence if progression detected

        relevance = min(0.8, len(progression_matches) * 0.3 + progression_coherence * 0.4)

        return {
            'type': 'intent_progression',
            'relevance': relevance,
            'progression_indicators': progression_matches,
            'coherence': progression_coherence,
            'primary_progression': max(progression_matches.keys(), key=lambda k: progression_matches[k]['strength']) if progression_matches else 'none',
        }

    def _analyze_domain_context(self, text: str) -> Dict[str, Any]:
        """Analyze domain-specific context."""
        domain_indicators = {
            'technical': ['code', 'function', 'algorithm', 'database', 'API', 'framework', 'library'],
            'business': ['project', 'deadline', 'meeting', 'client', 'requirements', 'budget'],
            'creative': ['design', 'creative', 'artistic', 'visual', 'aesthetic', 'style'],
            'analytical': ['analyze', 'data', 'statistics', 'metrics', 'performance', 'optimization'],
            'educational': ['learn', 'teach', 'explain', 'understand', 'tutorial', 'guide'],
            'operational': ['deploy', 'configure', 'setup', 'install', 'manage', 'maintain'],
        }

        text_lower = text.lower()
        domain_matches = {}

        for domain, indicators in domain_indicators.items():
            matches = [ind for ind in indicators if ind in text_lower]
            if matches:
                domain_matches[domain] = {
                    'matches': matches,
                    'strength': len(matches) * 0.15,
                }

        # Calculate domain focus
        if domain_matches:
            primary_domain = max(domain_matches.keys(), key=lambda k: domain_matches[k]['strength'])
            domain_focus = domain_matches[primary_domain]['strength']
        else:
            primary_domain = 'general'
            domain_focus = 0.3

        relevance = min(0.8, domain_focus + 0.2)

        return {
            'type': 'domain',
            'relevance': relevance,
            'domain_indicators': domain_matches,
            'primary_domain': primary_domain,
            'domain_focus': domain_focus,
        }

    def _analyze_environmental_context(self, context: AlgorithmContext) -> Dict[str, Any]:
        """Analyze environmental context from algorithm context."""
        env_factors = {}

        # Extract environment information from context
        if hasattr(context, 'config'):
            env_factors['environment'] = getattr(context.config, 'environment', 'unknown')
            env_factors['algorithm_name'] = getattr(context.config, 'algorithm_name', 'unknown')

        # Check for metadata
        if hasattr(context, 'metadata') and context.metadata:
            env_factors['metadata_available'] = True
            env_factors['metadata_keys'] = list(context.metadata.keys())
        else:
            env_factors['metadata_available'] = False

        # Calculate relevance based on available environmental information
        relevance = 0.4  # Base relevance
        if env_factors.get('environment') != 'unknown':
            relevance += 0.2
        if env_factors.get('metadata_available'):
            relevance += 0.2

        return {
            'type': 'environmental',
            'relevance': min(0.8, relevance),
            'factors': env_factors,
        }

    def _extract_semantic_indicators(self, text: str) -> List[Dict[str, Any]]:
        """Extract semantic indicators from text."""
        indicators = []

        # Simple semantic analysis based on word patterns
        words = text.lower().split()

        # Noun phrases (simplified)
        nouns = ['system', 'process', 'method', 'approach', 'solution', 'problem', 'issue', 'task']
        for noun in nouns:
            if noun in words:
                indicators.append({
                    'type': 'noun_concept',
                    'value': noun,
                    'strength': 0.6,
                })

        # Action verbs
        verbs = ['create', 'build', 'develop', 'implement', 'design', 'analyze', 'optimize']
        for verb in verbs:
            if verb in words:
                indicators.append({
                    'type': 'action_verb',
                    'value': verb,
                    'strength': 0.7,
                })

        return indicators

    def _calculate_stage_coherence(self, text: str, previous_results: Dict[str, Any]) -> float:
        """Calculate coherence with previous stage results."""
        # Simplified coherence calculation
        coherence = 0.5  # Base coherence

        # Check if text relates to previous stage outputs
        if 'explicit_command' in previous_results:
            explicit_result = previous_results['explicit_command']
            if explicit_result.get('is_explicit_command', False):
                coherence += 0.2  # Higher coherence if following explicit command

        if 'pattern_matching' in previous_results:
            pattern_result = previous_results['pattern_matching']
            patterns_found = pattern_result.get('patterns_found', [])
            if patterns_found:
                coherence += 0.1  # Slight boost if patterns were found

        return min(1.0, coherence)

    def _calculate_contextual_confidence(
        self,
        context_factors: List[Dict[str, Any]],
        text: str,
        previous_results: Dict[str, Any]
    ) -> float:
        """Calculate overall contextual confidence."""
        if not context_factors:
            return 0.3  # Low confidence without context factors

        # Weight different types of context
        type_weights = {
            'temporal': 0.8,
            'semantic': 0.9,
            'intent_progression': 0.85,
            'domain': 0.7,
            'environmental': 0.6,
        }

        total_weighted_relevance = 0.0
        total_weight = 0.0

        for factor in context_factors:
            factor_type = factor['type']
            relevance = factor['relevance']
            weight = type_weights.get(factor_type, 0.5)

            total_weighted_relevance += relevance * weight
            total_weight += weight

        if total_weight > 0:
            base_confidence = total_weighted_relevance / total_weight
        else:
            base_confidence = 0.3

        # Bonus for multiple context types
        context_diversity_bonus = min(0.1, len(context_factors) * 0.02)

        # Previous stage integration bonus
        integration_bonus = 0.05 if previous_results else 0.0

        final_confidence = base_confidence + context_diversity_bonus + integration_bonus

        return min(0.85, final_confidence)  # Cap at 0.85 for contextual analysis

    def _generate_contextual_insights(self, context_factors: List[Dict[str, Any]], text: str) -> List[str]:
        """Generate insights based on contextual analysis."""
        insights = []

        for factor in context_factors:
            factor_type = factor['type']
            relevance = factor['relevance']

            if factor_type == 'temporal' and relevance > 0.5:
                primary_timeframe = factor.get('primary_timeframe', 'unspecified')
                insights.append(f"Intent has {primary_timeframe} temporal context")

            elif factor_type == 'domain' and relevance > 0.5:
                primary_domain = factor.get('primary_domain', 'general')
                insights.append(f"Intent is focused on {primary_domain} domain")

            elif factor_type == 'intent_progression' and relevance > 0.5:
                primary_progression = factor.get('primary_progression', 'none')
                if primary_progression != 'none':
                    insights.append(f"Intent shows {primary_progression} progression pattern")

            elif factor_type == 'semantic' and relevance > 0.7:
                semantic_density = factor.get('semantic_density', 0)
                if semantic_density > 0.1:
                    insights.append("Intent has rich semantic content")

        if not insights:
            insights.append("Intent requires additional context for full understanding")

        return insights
