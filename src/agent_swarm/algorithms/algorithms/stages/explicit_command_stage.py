"""
Explicit Command Stage - O(1) complexity.

This stage handles explicit commands that can be processed immediately
without complex analysis. It provides the fastest processing path for
clear, unambiguous user inputs.

Features:
- O(1) constant time complexity
- Direct command mapping
- High confidence results
- Minimal resource usage
- Comprehensive command validation

This stage is typically the first in an adaptive intent processing pipeline.
"""

import time
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import uuid4

from ...core import (
    AlgorithmStage,
    AlgorithmContext,
    AlgorithmStageResult,
    AlgorithmMetadata,
    ParameterDefinition,
    ParameterType,
    ParameterConstraint,
)


class ExplicitCommandStage(AlgorithmStage):
    """
    Stage for processing explicit commands with O(1) complexity.
    
    This stage handles direct, unambiguous commands that can be processed
    immediately without complex analysis or reasoning.
    
    Input: String command or dict with 'command' field
    Output: Command classification and parameters
    """
    
    def __init__(self):
        super().__init__("explicit_command")
        self._command_map = self._build_command_map()
    
    @property
    def metadata(self) -> AlgorithmMetadata:
        """Get stage metadata."""
        return AlgorithmMetadata(
            name="explicit_command_stage",
            version="1.0.0",
            description="O(1) explicit command processing stage",
            author="Agent Swarm Framework",
            category="command_processing",
            tags=["explicit", "command", "O(1)", "fast", "direct"],
            created_date=datetime(2025, 1, 6),
            updated_date=datetime(2025, 1, 6),
            dependencies=[],
            input_types=["string", "dict"],
            output_types=["dict"],
            complexity="O(1)",
        )
    
    def get_parameter_definitions(self) -> List[ParameterDefinition]:
        """Get parameter definitions for this stage."""
        return [
            ParameterDefinition(
                name="command_prefix",
                param_type=ParameterType.STRING,
                default_value="/",
                description="Prefix that identifies explicit commands",
                required=False,
                category="command",
            ),
            ParameterDefinition(
                name="case_sensitive",
                param_type=ParameterType.BOOL,
                default_value=False,
                description="Whether command matching is case sensitive",
                required=False,
                category="command",
            ),
            ParameterDefinition(
                name="strict_mode",
                param_type=ParameterType.BOOL,
                default_value=True,
                description="Whether to require exact command matches",
                required=False,
                category="validation",
            ),
            ParameterDefinition(
                name="confidence_boost",
                param_type=ParameterType.FLOAT,
                default_value=0.95,
                description="Confidence level for explicit command matches",
                required=False,
                constraints=ParameterConstraint(min_value=0.0, max_value=1.0),
                category="confidence",
            ),
        ]
    
    def validate_input(self, input_data: Any) -> bool:
        """Validate input data."""
        if isinstance(input_data, str):
            return True
        
        if isinstance(input_data, dict):
            return 'command' in input_data or 'text' in input_data
        
        return False
    
    def estimate_complexity(self, input_size: int) -> float:
        """Estimate computational complexity - O(1)."""
        return 1.0  # Constant time regardless of input size
    
    async def process(self, input_data: Any, context: AlgorithmContext) -> AlgorithmStageResult:
        """Process explicit commands."""
        start_time = time.time()
        
        try:
            # Extract parameters
            command_prefix = context.get_parameter("command_prefix", "/")
            case_sensitive = context.get_parameter("case_sensitive", False)
            strict_mode = context.get_parameter("strict_mode", True)
            confidence_boost = context.get_parameter("confidence_boost", 0.95)
            
            # Extract command text
            if isinstance(input_data, str):
                command_text = input_data
                metadata_input = {}
            else:
                command_text = input_data.get('command') or input_data.get('text', '')
                metadata_input = {k: v for k, v in input_data.items() 
                               if k not in ['command', 'text']}
            
            # Update progress
            context.update_progress(0.2, "Checking for explicit command")
            
            # Check if this is an explicit command
            if not command_text.startswith(command_prefix):
                return AlgorithmStageResult(
                    stage_name=self.stage_name,
                    success=True,
                    output={
                        'is_explicit_command': False,
                        'reason': f'Does not start with command prefix "{command_prefix}"',
                        'original_input': command_text,
                        **metadata_input,
                    },
                    confidence=0.0,
                    execution_time=time.time() - start_time,
                    metadata={'stage_type': 'explicit_command', 'prefix_check': False},
                )
            
            # Extract command and arguments
            context.update_progress(0.5, "Parsing command")
            command_part = command_text[len(command_prefix):].strip()
            
            if not command_part:
                return AlgorithmStageResult(
                    stage_name=self.stage_name,
                    success=True,
                    output={
                        'is_explicit_command': False,
                        'reason': 'Empty command after prefix',
                        'original_input': command_text,
                        **metadata_input,
                    },
                    confidence=0.0,
                    execution_time=time.time() - start_time,
                    metadata={'stage_type': 'explicit_command', 'empty_command': True},
                )
            
            # Parse command and arguments
            parts = command_part.split()
            command_name = parts[0]
            command_args = parts[1:] if len(parts) > 1 else []
            
            # Normalize case if needed
            lookup_command = command_name if case_sensitive else command_name.lower()
            
            context.update_progress(0.8, "Looking up command")
            
            # Look up command in command map
            if lookup_command in self._command_map:
                command_info = self._command_map[lookup_command]
                
                # Validate arguments if in strict mode
                if strict_mode and command_info.get('required_args', 0) > len(command_args):
                    return AlgorithmStageResult(
                        stage_name=self.stage_name,
                        success=True,
                        output={
                            'is_explicit_command': True,
                            'command_recognized': True,
                            'command_valid': False,
                            'command_name': command_name,
                            'command_args': command_args,
                            'error': f'Command requires {command_info["required_args"]} arguments, got {len(command_args)}',
                            'original_input': command_text,
                            **metadata_input,
                        },
                        confidence=0.3,  # Recognized but invalid
                        execution_time=time.time() - start_time,
                        metadata={
                            'stage_type': 'explicit_command',
                            'command_recognized': True,
                            'validation_failed': True,
                        },
                    )
                
                # Command is valid
                output = {
                    'is_explicit_command': True,
                    'command_recognized': True,
                    'command_valid': True,
                    'command_name': command_name,
                    'command_args': command_args,
                    'command_info': command_info,
                    'original_input': command_text,
                    **metadata_input,
                }
                
                confidence = confidence_boost
                
            else:
                # Command not recognized
                output = {
                    'is_explicit_command': True,
                    'command_recognized': False,
                    'command_valid': False,
                    'command_name': command_name,
                    'command_args': command_args,
                    'error': f'Unknown command: {command_name}',
                    'original_input': command_text,
                    'suggestions': self._get_command_suggestions(lookup_command),
                    **metadata_input,
                }
                
                confidence = 0.1  # Low confidence for unrecognized commands
            
            execution_time = time.time() - start_time
            context.update_progress(1.0, "Command processing completed")
            
            return AlgorithmStageResult(
                stage_name=self.stage_name,
                success=True,
                output=output,
                confidence=confidence,
                execution_time=execution_time,
                metadata={
                    'stage_type': 'explicit_command',
                    'command_prefix': command_prefix,
                    'case_sensitive': case_sensitive,
                    'strict_mode': strict_mode,
                    'command_recognized': output.get('command_recognized', False),
                },
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            return AlgorithmStageResult(
                stage_name=self.stage_name,
                success=False,
                output=None,
                confidence=0.0,
                execution_time=execution_time,
                error=f"Explicit command processing failed: {str(e)}",
                metadata={
                    'stage_type': 'explicit_command',
                    'error_type': type(e).__name__,
                },
            )
    
    def _build_command_map(self) -> Dict[str, Dict[str, Any]]:
        """Build the command mapping dictionary."""
        return {
            # System commands
            'help': {
                'description': 'Show help information',
                'category': 'system',
                'required_args': 0,
                'optional_args': ['command'],
                'examples': ['/help', '/help config'],
            },
            'version': {
                'description': 'Show version information',
                'category': 'system',
                'required_args': 0,
                'optional_args': [],
                'examples': ['/version'],
            },
            'status': {
                'description': 'Show system status',
                'category': 'system',
                'required_args': 0,
                'optional_args': [],
                'examples': ['/status'],
            },
            
            # Configuration commands
            'config': {
                'description': 'Configuration management',
                'category': 'config',
                'required_args': 0,
                'optional_args': ['action', 'key', 'value'],
                'examples': ['/config', '/config show', '/config set key value'],
            },
            'set': {
                'description': 'Set configuration value',
                'category': 'config',
                'required_args': 2,
                'optional_args': [],
                'examples': ['/set model gpt-4', '/set temperature 0.7'],
            },
            'get': {
                'description': 'Get configuration value',
                'category': 'config',
                'required_args': 1,
                'optional_args': [],
                'examples': ['/get model', '/get temperature'],
            },
            
            # Agent commands
            'agent': {
                'description': 'Agent management',
                'category': 'agent',
                'required_args': 1,
                'optional_args': ['params'],
                'examples': ['/agent list', '/agent create coding', '/agent switch main'],
            },
            'switch': {
                'description': 'Switch to different agent',
                'category': 'agent',
                'required_args': 1,
                'optional_args': [],
                'examples': ['/switch coding', '/switch main'],
            },
            
            # Context commands
            'context': {
                'description': 'Context management',
                'category': 'context',
                'required_args': 0,
                'optional_args': ['action', 'path'],
                'examples': ['/context', '/context add /path/to/file', '/context clear'],
            },
            'index': {
                'description': 'Index files or directories',
                'category': 'context',
                'required_args': 1,
                'optional_args': [],
                'examples': ['/index .', '/index /path/to/project'],
            },
            
            # Utility commands
            'clear': {
                'description': 'Clear screen or context',
                'category': 'utility',
                'required_args': 0,
                'optional_args': ['target'],
                'examples': ['/clear', '/clear screen', '/clear context'],
            },
            'exit': {
                'description': 'Exit the application',
                'category': 'utility',
                'required_args': 0,
                'optional_args': [],
                'examples': ['/exit', '/quit'],
            },
            'quit': {
                'description': 'Exit the application',
                'category': 'utility',
                'required_args': 0,
                'optional_args': [],
                'examples': ['/quit', '/exit'],
            },
        }
    
    def _get_command_suggestions(self, command: str) -> List[str]:
        """Get command suggestions for unrecognized commands."""
        suggestions = []
        
        # Simple similarity-based suggestions
        for known_command in self._command_map.keys():
            if command in known_command or known_command in command:
                suggestions.append(known_command)
        
        # Limit to top 3 suggestions
        return suggestions[:3]
