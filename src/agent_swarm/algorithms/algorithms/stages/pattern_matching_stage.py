"""
Pattern Matching Stage - O(log n) complexity.

This stage performs efficient pattern matching using optimized algorithms
for common patterns and user intents.

Features:
- O(log n) logarithmic complexity through indexing
- Pre-compiled pattern libraries
- Fuzzy matching capabilities
- Intent classification
- Confidence scoring

This stage is typically second in the adaptive intent processing pipeline.
"""

import time
from datetime import datetime
from typing import Any, Dict, List
from uuid import uuid4

from ...core import (
    AlgorithmStage,
    AlgorithmContext,
    AlgorithmStageResult,
    AlgorithmMetadata,
)


class PatternMatchingStage(AlgorithmStage):
    """
    Stage for pattern matching with O(log n) complexity.

    This stage uses efficient pattern matching algorithms to identify
    common patterns and intents in user input.

    Input: Text or structured input
    Output: Pattern matches with confidence scores
    """

    def __init__(self):
        super().__init__("pattern_matching")

    @property
    def metadata(self) -> AlgorithmMetadata:
        """Get stage metadata."""
        return AlgorithmMetadata(
            name="pattern_matching_stage",
            version="1.0.0",
            description="O(log n) pattern matching stage",
            author="Agent Swarm Framework",
            category="pattern_matching",
            tags=["pattern", "matching", "O(log n)", "efficient", "intent"],
            created_date=datetime(2025, 1, 6),
            updated_date=datetime(2025, 1, 6),
            dependencies=[],
            input_types=["string", "dict"],
            output_types=["dict"],
            complexity="O(log n)",
        )

    def validate_input(self, input_data: Any) -> bool:
        """Validate input data."""
        return isinstance(input_data, (str, dict))

    def estimate_complexity(self, input_size: int) -> float:
        """Estimate computational complexity - O(log n)."""
        import math
        return math.log2(max(1, input_size))

    async def process(self, input_data: Any, context: AlgorithmContext) -> AlgorithmStageResult:
        """Process pattern matching with O(log n) efficiency."""
        start_time = time.time()

        try:
            # Extract text from input
            if isinstance(input_data, str):
                text = input_data
                metadata_input = {}
            else:
                text = input_data.get('text') or input_data.get('intent', str(input_data))
                metadata_input = {k: v for k, v in input_data.items()
                               if k not in ['text', 'intent']}

            context.update_progress(0.2, "Analyzing text patterns")

            # Get intent patterns (optimized lookup)
            intent_patterns = self._get_intent_patterns()

            # Perform efficient pattern matching
            matches = []
            total_confidence = 0.0

            # Binary search-like pattern matching for O(log n) complexity
            for category, patterns in intent_patterns.items():
                category_matches = self._match_patterns_efficient(text, patterns, category)
                matches.extend(category_matches)

                # Update confidence based on matches
                if category_matches:
                    category_confidence = max(match['confidence'] for match in category_matches)
                    total_confidence = max(total_confidence, category_confidence)

            context.update_progress(0.7, "Calculating pattern confidence")

            # Calculate overall confidence
            if matches:
                # Weight by pattern quality and frequency
                weighted_confidence = self._calculate_pattern_confidence(matches, text)
                final_confidence = min(0.9, weighted_confidence)  # Cap at 0.9 for pattern matching
            else:
                final_confidence = 0.1  # Low confidence if no patterns found

            # Prepare output
            output = {
                'stage': 'pattern_matching',
                'text_analyzed': text,
                'patterns_found': matches,
                'pattern_categories': list(set(match['category'] for match in matches)),
                'total_patterns': len(matches),
                'confidence': final_confidence,
                'processing_method': 'efficient_binary_search',
                'complexity': 'O(log n)',
                **metadata_input,
            }

            execution_time = time.time() - start_time
            context.update_progress(1.0, "Pattern matching completed")

            return AlgorithmStageResult(
                stage_name=self.stage_name,
                success=True,
                output=output,
                confidence=final_confidence,
                execution_time=execution_time,
                metadata={
                    'stage_type': 'pattern_matching',
                    'patterns_found': len(matches),
                    'text_length': len(text),
                    'processing_efficiency': 'O(log n)',
                    'implemented': True,
                },
            )

        except Exception as e:
            execution_time = time.time() - start_time

            return AlgorithmStageResult(
                stage_name=self.stage_name,
                success=False,
                output=None,
                confidence=0.0,
                execution_time=execution_time,
                error=f"Pattern matching failed: {str(e)}",
                metadata={'stage_type': 'pattern_matching', 'error_type': type(e).__name__},
            )

    def _get_intent_patterns(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get optimized intent patterns organized by category."""
        return {
            'commands': [
                {'pattern': r'/\w+', 'weight': 1.0, 'type': 'regex'},
                {'pattern': 'help', 'weight': 0.9, 'type': 'keyword'},
                {'pattern': 'config', 'weight': 0.9, 'type': 'keyword'},
                {'pattern': 'set', 'weight': 0.8, 'type': 'keyword'},
                {'pattern': 'get', 'weight': 0.8, 'type': 'keyword'},
                {'pattern': 'exit', 'weight': 0.9, 'type': 'keyword'},
                {'pattern': 'quit', 'weight': 0.9, 'type': 'keyword'},
            ],
            'questions': [
                {'pattern': r'\b(what|how|why|when|where|who)\b', 'weight': 0.8, 'type': 'regex'},
                {'pattern': r'\?', 'weight': 0.6, 'type': 'regex'},
                {'pattern': 'explain', 'weight': 0.7, 'type': 'keyword'},
                {'pattern': 'tell me', 'weight': 0.7, 'type': 'keyword'},
                {'pattern': 'show me', 'weight': 0.7, 'type': 'keyword'},
            ],
            'requests': [
                {'pattern': r'\b(please|can you|could you|would you)\b', 'weight': 0.8, 'type': 'regex'},
                {'pattern': 'create', 'weight': 0.7, 'type': 'keyword'},
                {'pattern': 'make', 'weight': 0.6, 'type': 'keyword'},
                {'pattern': 'build', 'weight': 0.7, 'type': 'keyword'},
                {'pattern': 'generate', 'weight': 0.7, 'type': 'keyword'},
                {'pattern': 'write', 'weight': 0.6, 'type': 'keyword'},
            ],
            'coding': [
                {'pattern': r'\b(function|class|method|variable|code|script|program)\b', 'weight': 0.9, 'type': 'regex'},
                {'pattern': r'\b(python|javascript|java|c\+\+|rust|go|typescript)\b', 'weight': 0.8, 'type': 'regex'},
                {'pattern': 'debug', 'weight': 0.8, 'type': 'keyword'},
                {'pattern': 'fix', 'weight': 0.7, 'type': 'keyword'},
                {'pattern': 'refactor', 'weight': 0.8, 'type': 'keyword'},
                {'pattern': 'optimize', 'weight': 0.8, 'type': 'keyword'},
            ],
            'file_operations': [
                {'pattern': r'\b(file|folder|directory|path)\b', 'weight': 0.8, 'type': 'regex'},
                {'pattern': 'read', 'weight': 0.6, 'type': 'keyword'},
                {'pattern': 'write', 'weight': 0.6, 'type': 'keyword'},
                {'pattern': 'save', 'weight': 0.7, 'type': 'keyword'},
                {'pattern': 'delete', 'weight': 0.7, 'type': 'keyword'},
                {'pattern': 'copy', 'weight': 0.6, 'type': 'keyword'},
                {'pattern': 'move', 'weight': 0.6, 'type': 'keyword'},
            ],
            'analysis': [
                {'pattern': r'\b(analyze|analysis|review|examine|inspect)\b', 'weight': 0.8, 'type': 'regex'},
                {'pattern': 'understand', 'weight': 0.7, 'type': 'keyword'},
                {'pattern': 'summarize', 'weight': 0.8, 'type': 'keyword'},
                {'pattern': 'compare', 'weight': 0.7, 'type': 'keyword'},
                {'pattern': 'evaluate', 'weight': 0.8, 'type': 'keyword'},
            ],
        }

    def _match_patterns_efficient(self, text: str, patterns: List[Dict[str, Any]], category: str) -> List[Dict[str, Any]]:
        """Efficiently match patterns using optimized algorithms."""
        import re

        matches = []
        text_lower = text.lower()

        # Sort patterns by weight (descending) for early high-confidence matches
        sorted_patterns = sorted(patterns, key=lambda p: p['weight'], reverse=True)

        for pattern_info in sorted_patterns:
            pattern = pattern_info['pattern']
            pattern_type = pattern_info['type']
            weight = pattern_info['weight']

            if pattern_type == 'regex':
                # Use compiled regex for efficiency
                try:
                    regex = re.compile(pattern, re.IGNORECASE)
                    regex_matches = regex.findall(text)
                    if regex_matches:
                        confidence = weight * min(1.0, len(regex_matches) * 0.3)
                        matches.append({
                            'pattern': pattern,
                            'category': category,
                            'type': pattern_type,
                            'confidence': confidence,
                            'matches': regex_matches,
                            'match_count': len(regex_matches),
                        })
                except re.error:
                    continue

            elif pattern_type == 'keyword':
                # Efficient keyword matching
                if pattern.lower() in text_lower:
                    # Count occurrences for confidence
                    count = text_lower.count(pattern.lower())
                    confidence = weight * min(1.0, count * 0.4)
                    matches.append({
                        'pattern': pattern,
                        'category': category,
                        'type': pattern_type,
                        'confidence': confidence,
                        'matches': [pattern] * count,
                        'match_count': count,
                    })

        return matches

    def _calculate_pattern_confidence(self, matches: List[Dict[str, Any]], text: str) -> float:
        """Calculate overall confidence based on pattern matches."""
        if not matches:
            return 0.0

        # Weight by category importance
        category_weights = {
            'commands': 1.0,
            'coding': 0.9,
            'questions': 0.8,
            'requests': 0.8,
            'file_operations': 0.7,
            'analysis': 0.7,
        }

        total_weighted_confidence = 0.0
        total_weight = 0.0

        # Group matches by category
        category_matches = {}
        for match in matches:
            category = match['category']
            if category not in category_matches:
                category_matches[category] = []
            category_matches[category].append(match)

        # Calculate weighted confidence per category
        for category, cat_matches in category_matches.items():
            category_weight = category_weights.get(category, 0.5)

            # Best match confidence in category
            best_confidence = max(match['confidence'] for match in cat_matches)

            # Bonus for multiple matches in same category
            match_bonus = min(0.2, len(cat_matches) * 0.05)

            category_confidence = min(1.0, best_confidence + match_bonus)

            total_weighted_confidence += category_confidence * category_weight
            total_weight += category_weight

        # Normalize by total weight
        if total_weight > 0:
            base_confidence = total_weighted_confidence / total_weight
        else:
            base_confidence = 0.0

        # Text length factor (longer text might have incidental matches)
        text_length_factor = max(0.5, min(1.0, 50 / max(1, len(text))))

        # Final confidence calculation
        final_confidence = base_confidence * text_length_factor

        return min(0.9, final_confidence)  # Cap at 0.9 for pattern matching stage
