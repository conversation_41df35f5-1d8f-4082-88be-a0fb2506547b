"""
Intent Filtering Triangle Algorithm - Revolutionary Multi-Dimensional Intent Filtering.

This algorithm implements the Intent Filtering Triangle, a breakthrough approach to
intent understanding that processes user intents through three fundamental dimensions:

1. **Clarity Dimension** - How clear and explicit the intent is
2. **Complexity Dimension** - How complex the intent processing requirements are
3. **Context Dimension** - How much contextual information is needed

The algorithm uses mathematical optimization to create a holistic understanding
of user intents by mapping them into a 3D intent space and applying sophisticated
filtering algorithms to determine the optimal processing approach.

Architecture:
    Input Intent → Triangle Mapping → Multi-Dimensional Analysis → Filtering → Output

Mathematical Foundation:
    - 3D Intent Space: (Clarity, Complexity, Context)
    - Holistic Scoring: Weighted geometric mean of dimensions
    - Symbiotic Intelligence: Dimensions influence each other
    - Flow State Algorithms: Optimal processing path determination

Features:
- Multi-dimensional intent analysis with mathematical rigor
- Holistic intelligence patterns with symbiotic dimension interaction
- Flow state algorithms for optimal processing paths
- Adaptive filtering with machine learning optimization
- Real-time intent space visualization and analytics
- Production-ready performance with comprehensive monitoring

This algorithm represents the next evolution in AI intent understanding.
"""

import math
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union
from uuid import uuid4

from ..core import (
    Algorithm,
    AlgorithmContext,
    AlgorithmResult,
)
from ..core.registry import AlgorithmMetadata
from ..core.config import (
    ParameterDefinition,
    ParameterType,
    ParameterConstraint,
)


class IntentFilteringTriangle(Algorithm):
    """
    Intent Filtering Triangle Algorithm with multi-dimensional analysis.

    This algorithm maps user intents into a 3D space defined by Clarity, Complexity,
    and Context dimensions, then applies sophisticated filtering to determine the
    optimal processing approach.

    Input: User intent string or structured intent data
    Output: Multi-dimensional intent analysis with filtering recommendations
    """

    def __init__(self):
        super().__init__()
        self._intent_space_cache = {}
        self._filtering_history = []
        self._dimension_weights = {
            'clarity': 0.4,
            'complexity': 0.35,
            'context': 0.25,
        }
        self._symbiotic_factors = {
            'clarity_complexity': 0.15,
            'clarity_context': 0.12,
            'complexity_context': 0.18,
        }
        self._flow_state_thresholds = {
            'optimal': 0.8,
            'good': 0.6,
            'acceptable': 0.4,
            'challenging': 0.2,
        }

    @property
    def metadata(self) -> AlgorithmMetadata:
        """Get algorithm metadata."""
        return AlgorithmMetadata(
            name="intent_filtering_triangle",
            version="1.0.0",
            description="Revolutionary multi-dimensional intent filtering with holistic intelligence",
            author="Agent Swarm Framework",
            category="intent_filtering",
            tags=["intent", "filtering", "triangle", "multi-dimensional", "holistic", "revolutionary"],
            created_date=datetime(2025, 1, 6),
            updated_date=datetime(2025, 1, 6),
            dependencies=[],
            input_types=["string", "dict"],
            output_types=["dict"],
            complexity="O(n log n)",
            documentation_url="https://github.com/agent-swarm/docs/intent-filtering-triangle",
        )

    def get_parameter_definitions(self) -> List[ParameterDefinition]:
        """Get parameter definitions for this algorithm."""
        return [
            ParameterDefinition(
                name="clarity_weight",
                param_type=ParameterType.FLOAT,
                default_value=0.4,
                description="Weight for clarity dimension in holistic scoring",
                required=False,
                constraints=ParameterConstraint(min_value=0.0, max_value=1.0),
                category="weighting",
            ),
            ParameterDefinition(
                name="complexity_weight",
                param_type=ParameterType.FLOAT,
                default_value=0.35,
                description="Weight for complexity dimension in holistic scoring",
                required=False,
                constraints=ParameterConstraint(min_value=0.0, max_value=1.0),
                category="weighting",
            ),
            ParameterDefinition(
                name="context_weight",
                param_type=ParameterType.FLOAT,
                default_value=0.25,
                description="Weight for context dimension in holistic scoring",
                required=False,
                constraints=ParameterConstraint(min_value=0.0, max_value=1.0),
                category="weighting",
            ),
            ParameterDefinition(
                name="enable_symbiotic_intelligence",
                param_type=ParameterType.BOOL,
                default_value=True,
                description="Enable symbiotic intelligence between dimensions",
                required=False,
                category="intelligence",
            ),
            ParameterDefinition(
                name="symbiotic_strength",
                param_type=ParameterType.FLOAT,
                default_value=0.15,
                description="Strength of symbiotic interactions between dimensions",
                required=False,
                constraints=ParameterConstraint(min_value=0.0, max_value=0.5),
                category="intelligence",
            ),
            ParameterDefinition(
                name="enable_flow_state_analysis",
                param_type=ParameterType.BOOL,
                default_value=True,
                description="Enable flow state analysis for optimal processing",
                required=False,
                category="optimization",
            ),
            ParameterDefinition(
                name="filtering_strategy",
                param_type=ParameterType.STRING,
                default_value="holistic",
                description="Intent filtering strategy",
                required=False,
                constraints=ParameterConstraint(
                    allowed_values=["holistic", "dimensional", "adaptive", "geometric", "harmonic"]
                ),
                category="strategy",
            ),
            ParameterDefinition(
                name="enable_intent_space_caching",
                param_type=ParameterType.BOOL,
                default_value=True,
                description="Enable caching of intent space calculations",
                required=False,
                category="performance",
            ),
            ParameterDefinition(
                name="visualization_detail_level",
                param_type=ParameterType.STRING,
                default_value="detailed",
                description="Level of detail for intent space visualization",
                required=False,
                constraints=ParameterConstraint(
                    allowed_values=["minimal", "standard", "detailed", "comprehensive"]
                ),
                category="visualization",
            ),
        ]

    def validate_input(self, input_data: Any) -> bool:
        """Validate input data."""
        if isinstance(input_data, str):
            return len(input_data.strip()) > 0

        if isinstance(input_data, dict):
            # Must have 'intent' or 'text' field
            return 'intent' in input_data or 'text' in input_data

        return False

    def estimate_complexity(self, input_size: int) -> float:
        """Estimate computational complexity - O(n log n)."""
        if input_size <= 1:
            return 1.0
        return float(input_size * math.log2(input_size))

    async def execute(self, input_data: Any, context: AlgorithmContext) -> AlgorithmResult:
        """Execute the intent filtering triangle algorithm."""
        start_time = time.time()

        try:
            # Extract parameters
            clarity_weight = context.get_parameter("clarity_weight", 0.4)
            complexity_weight = context.get_parameter("complexity_weight", 0.35)
            context_weight = context.get_parameter("context_weight", 0.25)
            enable_symbiotic = context.get_parameter("enable_symbiotic_intelligence", True)
            symbiotic_strength = context.get_parameter("symbiotic_strength", 0.15)
            enable_flow_state = context.get_parameter("enable_flow_state_analysis", True)
            filtering_strategy = context.get_parameter("filtering_strategy", "holistic")
            enable_caching = context.get_parameter("enable_intent_space_caching", True)
            visualization_level = context.get_parameter("visualization_detail_level", "detailed")

            # Update dimension weights
            self._dimension_weights = {
                'clarity': clarity_weight,
                'complexity': complexity_weight,
                'context': context_weight,
            }

            # Extract intent data
            if isinstance(input_data, str):
                intent_text = input_data
                intent_metadata = {}
            else:
                intent_text = input_data.get('intent') or input_data.get('text', '')
                intent_metadata = {k: v for k, v in input_data.items()
                                 if k not in ['intent', 'text']}

            context.update_progress(0.1, "Initializing intent filtering triangle")

            # Step 1: Map intent to 3D space
            context.update_progress(0.2, "Mapping intent to 3D space")
            intent_coordinates = await self._map_intent_to_3d_space(
                intent_text, intent_metadata, enable_caching
            )

            # Step 2: Analyze dimensions
            context.update_progress(0.4, "Analyzing dimensional characteristics")
            dimensional_analysis = await self._analyze_dimensions(
                intent_text, intent_coordinates, intent_metadata
            )

            # Step 3: Apply symbiotic intelligence
            if enable_symbiotic:
                context.update_progress(0.6, "Applying symbiotic intelligence")
                symbiotic_analysis = await self._apply_symbiotic_intelligence(
                    dimensional_analysis, symbiotic_strength
                )
            else:
                symbiotic_analysis = dimensional_analysis

            # Step 4: Calculate holistic score
            context.update_progress(0.7, "Calculating holistic intent score")
            holistic_score = await self._calculate_holistic_score(
                symbiotic_analysis, filtering_strategy
            )

            # Step 5: Flow state analysis
            if enable_flow_state:
                context.update_progress(0.8, "Performing flow state analysis")
                flow_state_analysis = await self._analyze_flow_state(
                    holistic_score, symbiotic_analysis
                )
            else:
                flow_state_analysis = {'flow_state': 'unknown', 'recommendations': []}

            # Step 6: Generate filtering recommendations
            context.update_progress(0.9, "Generating filtering recommendations")
            filtering_recommendations = await self._generate_filtering_recommendations(
                holistic_score, flow_state_analysis, symbiotic_analysis
            )

            # Step 7: Create visualization data
            visualization_data = await self._create_visualization_data(
                intent_coordinates, dimensional_analysis, holistic_score, visualization_level
            )

            # Update filtering history
            self._update_filtering_history(intent_text, holistic_score, filtering_recommendations)

            # Prepare comprehensive output
            output = {
                'intent_text': intent_text,
                'intent_metadata': intent_metadata,
                'intent_coordinates': intent_coordinates,
                'dimensional_analysis': dimensional_analysis,
                'symbiotic_analysis': symbiotic_analysis if enable_symbiotic else None,
                'holistic_score': holistic_score,
                'flow_state_analysis': flow_state_analysis if enable_flow_state else None,
                'filtering_recommendations': filtering_recommendations,
                'visualization_data': visualization_data,
                'processing_strategy': filtering_strategy,
                'algorithm_version': self.metadata.version,
                'processing_timestamp': datetime.now().isoformat(),
            }

            execution_time = time.time() - start_time
            context.update_progress(1.0, "Intent filtering triangle completed")

            return AlgorithmResult(
                action_id=str(uuid4()),
                algorithm_name=self.metadata.name,
                success=True,
                output=output,
                confidence=holistic_score.get('overall_confidence', 0.0),
                execution_time=execution_time,
                parameters_used={
                    "clarity_weight": clarity_weight,
                    "complexity_weight": complexity_weight,
                    "context_weight": context_weight,
                    "filtering_strategy": filtering_strategy,
                    "enable_symbiotic_intelligence": enable_symbiotic,
                    "enable_flow_state_analysis": enable_flow_state,
                },
                metadata={
                    "input_length": len(intent_text),
                    "intent_coordinates": intent_coordinates,
                    "holistic_score": holistic_score.get('overall_score', 0.0),
                    "flow_state": flow_state_analysis.get('flow_state', 'unknown'),
                    "filtering_strategy": filtering_strategy,
                    "symbiotic_enabled": enable_symbiotic,
                    "algorithm_version": self.metadata.version,
                },
            )

        except Exception as e:
            execution_time = time.time() - start_time

            return AlgorithmResult(
                action_id=str(uuid4()),
                algorithm_name=self.metadata.name,
                success=False,
                output=None,
                confidence=0.0,
                execution_time=execution_time,
                error=f"Intent filtering triangle failed: {str(e)}",
                metadata={
                    "error_type": type(e).__name__,
                    "input_type": type(input_data).__name__,
                },
            )

    async def _map_intent_to_3d_space(
        self,
        intent_text: str,
        intent_metadata: Dict[str, Any],
        enable_caching: bool
    ) -> Dict[str, float]:
        """Map intent to 3D coordinate space (Clarity, Complexity, Context)."""

        # Check cache first
        cache_key = f"{intent_text}_{hash(str(intent_metadata))}"
        if enable_caching and cache_key in self._intent_space_cache:
            return self._intent_space_cache[cache_key]

        # Calculate Clarity dimension (0.0 to 1.0)
        clarity_score = self._calculate_clarity_dimension(intent_text)

        # Calculate Complexity dimension (0.0 to 1.0)
        complexity_score = self._calculate_complexity_dimension(intent_text, intent_metadata)

        # Calculate Context dimension (0.0 to 1.0)
        context_score = self._calculate_context_dimension(intent_text, intent_metadata)

        coordinates = {
            'clarity': clarity_score,
            'complexity': complexity_score,
            'context': context_score,
        }

        # Cache the result
        if enable_caching:
            self._intent_space_cache[cache_key] = coordinates

        return coordinates

    def _calculate_clarity_dimension(self, intent_text: str) -> float:
        """Calculate clarity dimension score."""
        clarity_indicators = {
            'explicit_commands': {
                'patterns': [r'^/', r'^\w+:', r'please\s+\w+', r'can\s+you\s+\w+'],
                'weight': 0.9,
            },
            'clear_actions': {
                'patterns': [r'\b(create|build|make|generate|write|delete|remove)\b'],
                'weight': 0.8,
            },
            'specific_objects': {
                'patterns': [r'\b(function|class|file|database|API|script)\b'],
                'weight': 0.7,
            },
            'question_words': {
                'patterns': [r'\b(what|how|why|when|where|who)\b'],
                'weight': 0.6,
            },
            'vague_terms': {
                'patterns': [r'\b(something|anything|stuff|things|maybe|perhaps)\b'],
                'weight': -0.4,  # Negative weight for vague terms
            },
        }

        import re
        text_lower = intent_text.lower()
        clarity_score = 0.5  # Base clarity

        for category, info in clarity_indicators.items():
            patterns = info['patterns']
            weight = info['weight']

            matches = 0
            for pattern in patterns:
                matches += len(re.findall(pattern, text_lower))

            if matches > 0:
                clarity_score += weight * min(0.3, matches * 0.1)

        # Length factor (very short or very long intents are less clear)
        length = len(intent_text.split())
        if length < 3:
            clarity_score *= 0.7
        elif length > 30:
            clarity_score *= 0.8

        return max(0.0, min(1.0, clarity_score))

    def _calculate_complexity_dimension(self, intent_text: str, intent_metadata: Dict[str, Any]) -> float:
        """Calculate complexity dimension score."""
        complexity_indicators = {
            'technical_terms': {
                'patterns': [r'\b(algorithm|optimization|machine learning|neural network|database|API)\b'],
                'weight': 0.8,
            },
            'multiple_actions': {
                'patterns': [r'\band\b', r'\bthen\b', r'\bafter\b', r'\bnext\b'],
                'weight': 0.6,
            },
            'conditional_logic': {
                'patterns': [r'\bif\b', r'\bunless\b', r'\bwhen\b', r'\bdepending\b'],
                'weight': 0.7,
            },
            'integration_terms': {
                'patterns': [r'\bintegrate\b', r'\bconnect\b', r'\bcombine\b', r'\bmerge\b'],
                'weight': 0.6,
            },
            'analysis_terms': {
                'patterns': [r'\banalyze\b', r'\bevaluate\b', r'\bcompare\b', r'\boptimize\b'],
                'weight': 0.7,
            },
        }

        import re
        text_lower = intent_text.lower()
        complexity_score = 0.3  # Base complexity

        for category, info in complexity_indicators.items():
            patterns = info['patterns']
            weight = info['weight']

            matches = 0
            for pattern in patterns:
                matches += len(re.findall(pattern, text_lower))

            if matches > 0:
                complexity_score += weight * min(0.4, matches * 0.15)

        # Metadata complexity
        if intent_metadata:
            complexity_score += len(intent_metadata) * 0.05

        # Sentence structure complexity
        sentences = intent_text.split('.')
        if len(sentences) > 2:
            complexity_score += 0.1

        return max(0.0, min(1.0, complexity_score))

    def _calculate_context_dimension(self, intent_text: str, intent_metadata: Dict[str, Any]) -> float:
        """Calculate context dimension score."""
        context_indicators = {
            'temporal_references': {
                'patterns': [r'\b(now|today|yesterday|tomorrow|recently|soon)\b'],
                'weight': 0.6,
            },
            'referential_terms': {
                'patterns': [r'\bthis\b', r'\bthat\b', r'\bthese\b', r'\bthose\b', r'\bit\b'],
                'weight': 0.7,
            },
            'project_references': {
                'patterns': [r'\bproject\b', r'\bapplication\b', r'\bsystem\b', r'\bcodebase\b'],
                'weight': 0.5,
            },
            'user_state': {
                'patterns': [r'\bI\s+(am|was|have|need|want)\b', r'\bmy\b', r'\bme\b'],
                'weight': 0.4,
            },
            'environmental': {
                'patterns': [r'\bhere\b', r'\bthere\b', r'\bcurrent\b', r'\bexisting\b'],
                'weight': 0.5,
            },
        }

        import re
        text_lower = intent_text.lower()
        context_score = 0.2  # Base context need

        for category, info in context_indicators.items():
            patterns = info['patterns']
            weight = info['weight']

            matches = 0
            for pattern in patterns:
                matches += len(re.findall(pattern, text_lower))

            if matches > 0:
                context_score += weight * min(0.3, matches * 0.1)

        # Metadata provides context
        if intent_metadata:
            context_score += min(0.3, len(intent_metadata) * 0.1)

        # Incomplete sentences need more context
        if not intent_text.strip().endswith(('.', '!', '?')):
            context_score += 0.1

        return max(0.0, min(1.0, context_score))

    async def _analyze_dimensions(
        self,
        intent_text: str,
        coordinates: Dict[str, float],
        intent_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze dimensional characteristics and relationships."""

        clarity = coordinates['clarity']
        complexity = coordinates['complexity']
        context = coordinates['context']

        # Dimensional analysis
        analysis = {
            'clarity_analysis': {
                'score': clarity,
                'level': self._get_dimension_level(clarity),
                'characteristics': self._analyze_clarity_characteristics(intent_text, clarity),
            },
            'complexity_analysis': {
                'score': complexity,
                'level': self._get_dimension_level(complexity),
                'characteristics': self._analyze_complexity_characteristics(intent_text, complexity),
            },
            'context_analysis': {
                'score': context,
                'level': self._get_dimension_level(context),
                'characteristics': self._analyze_context_characteristics(intent_text, context),
            },
            'dimensional_relationships': self._analyze_dimensional_relationships(coordinates),
            'intent_classification': self._classify_intent_by_dimensions(coordinates),
        }

        return analysis

    def _get_dimension_level(self, score: float) -> str:
        """Get descriptive level for dimension score."""
        if score >= 0.8:
            return "very_high"
        elif score >= 0.6:
            return "high"
        elif score >= 0.4:
            return "medium"
        elif score >= 0.2:
            return "low"
        else:
            return "very_low"

    def _analyze_clarity_characteristics(self, intent_text: str, clarity_score: float) -> List[str]:
        """Analyze clarity characteristics."""
        characteristics = []

        if clarity_score >= 0.8:
            characteristics.append("explicit_and_unambiguous")
        elif clarity_score >= 0.6:
            characteristics.append("mostly_clear")
        elif clarity_score >= 0.4:
            characteristics.append("somewhat_ambiguous")
        else:
            characteristics.append("highly_ambiguous")

        # Additional characteristics based on text analysis
        if intent_text.startswith('/'):
            characteristics.append("command_syntax")
        if '?' in intent_text:
            characteristics.append("interrogative")
        if any(word in intent_text.lower() for word in ['please', 'can you', 'could you']):
            characteristics.append("polite_request")

        return characteristics

    def _analyze_complexity_characteristics(self, intent_text: str, complexity_score: float) -> List[str]:
        """Analyze complexity characteristics."""
        characteristics = []

        if complexity_score >= 0.8:
            characteristics.append("highly_complex")
        elif complexity_score >= 0.6:
            characteristics.append("moderately_complex")
        elif complexity_score >= 0.4:
            characteristics.append("simple_with_nuances")
        else:
            characteristics.append("straightforward")

        # Technical complexity indicators
        if any(term in intent_text.lower() for term in ['algorithm', 'optimization', 'machine learning']):
            characteristics.append("technical_domain")
        if any(word in intent_text.lower() for word in ['and', 'then', 'after']):
            characteristics.append("multi_step")

        return characteristics

    def _analyze_context_characteristics(self, intent_text: str, context_score: float) -> List[str]:
        """Analyze context characteristics."""
        characteristics = []

        if context_score >= 0.8:
            characteristics.append("highly_context_dependent")
        elif context_score >= 0.6:
            characteristics.append("context_aware")
        elif context_score >= 0.4:
            characteristics.append("some_context_needed")
        else:
            characteristics.append("context_independent")

        # Context type indicators
        if any(word in intent_text.lower() for word in ['this', 'that', 'it']):
            characteristics.append("referential")
        if any(word in intent_text.lower() for word in ['current', 'existing', 'my']):
            characteristics.append("state_dependent")

        return characteristics

    def _analyze_dimensional_relationships(self, coordinates: Dict[str, float]) -> Dict[str, Any]:
        """Analyze relationships between dimensions."""
        clarity = coordinates['clarity']
        complexity = coordinates['complexity']
        context = coordinates['context']

        relationships = {
            'clarity_complexity_correlation': self._calculate_correlation(clarity, complexity),
            'clarity_context_correlation': self._calculate_correlation(clarity, context),
            'complexity_context_correlation': self._calculate_correlation(complexity, context),
            'dimensional_balance': self._calculate_dimensional_balance(coordinates),
            'dominant_dimension': max(coordinates.keys(), key=lambda k: coordinates[k]),
            'weakest_dimension': min(coordinates.keys(), key=lambda k: coordinates[k]),
        }

        return relationships

    def _calculate_correlation(self, dim1: float, dim2: float) -> str:
        """Calculate correlation between two dimensions."""
        diff = abs(dim1 - dim2)
        if diff < 0.1:
            return "highly_correlated"
        elif diff < 0.3:
            return "moderately_correlated"
        elif diff < 0.5:
            return "weakly_correlated"
        else:
            return "uncorrelated"

    def _calculate_dimensional_balance(self, coordinates: Dict[str, float]) -> Dict[str, Any]:
        """Calculate dimensional balance metrics."""
        values = list(coordinates.values())
        mean_val = sum(values) / len(values)
        variance = sum((v - mean_val) ** 2 for v in values) / len(values)
        std_dev = math.sqrt(variance)

        return {
            'mean': mean_val,
            'variance': variance,
            'standard_deviation': std_dev,
            'balance_level': 'balanced' if std_dev < 0.2 else 'imbalanced',
        }

    def _classify_intent_by_dimensions(self, coordinates: Dict[str, float]) -> Dict[str, Any]:
        """Classify intent based on dimensional coordinates."""
        clarity = coordinates['clarity']
        complexity = coordinates['complexity']
        context = coordinates['context']

        # Intent type classification
        if clarity >= 0.7 and complexity <= 0.3:
            intent_type = "simple_explicit"
        elif clarity >= 0.7 and complexity >= 0.7:
            intent_type = "complex_explicit"
        elif clarity <= 0.3 and complexity <= 0.3:
            intent_type = "simple_ambiguous"
        elif clarity <= 0.3 and complexity >= 0.7:
            intent_type = "complex_ambiguous"
        else:
            intent_type = "moderate_mixed"

        # Processing recommendation
        if clarity >= 0.8:
            processing_rec = "direct_processing"
        elif complexity >= 0.8:
            processing_rec = "complex_analysis_required"
        elif context >= 0.8:
            processing_rec = "context_gathering_required"
        else:
            processing_rec = "standard_processing"

        return {
            'intent_type': intent_type,
            'processing_recommendation': processing_rec,
            'confidence_level': min(clarity, 1.0 - complexity, 1.0 - context),
        }

    async def _apply_symbiotic_intelligence(
        self,
        dimensional_analysis: Dict[str, Any],
        symbiotic_strength: float
    ) -> Dict[str, Any]:
        """Apply symbiotic intelligence between dimensions."""

        # Extract dimension scores
        clarity = dimensional_analysis['clarity_analysis']['score']
        complexity = dimensional_analysis['complexity_analysis']['score']
        context = dimensional_analysis['context_analysis']['score']

        # Calculate symbiotic adjustments
        symbiotic_adjustments = {
            'clarity_boost_from_low_complexity': 0.0,
            'complexity_boost_from_high_clarity': 0.0,
            'context_boost_from_ambiguity': 0.0,
        }

        # Symbiotic rule 1: Low complexity boosts clarity perception
        if complexity < 0.4 and clarity > 0.3:
            boost = symbiotic_strength * (0.4 - complexity)
            symbiotic_adjustments['clarity_boost_from_low_complexity'] = boost
            clarity = min(1.0, clarity + boost)

        # Symbiotic rule 2: High clarity can reduce perceived complexity
        if clarity > 0.7 and complexity > 0.3:
            reduction = symbiotic_strength * (clarity - 0.7)
            symbiotic_adjustments['complexity_boost_from_high_clarity'] = -reduction
            complexity = max(0.0, complexity - reduction)

        # Symbiotic rule 3: Ambiguity increases context need
        if clarity < 0.5 or complexity > 0.7:
            boost = symbiotic_strength * max(0.5 - clarity, complexity - 0.7)
            symbiotic_adjustments['context_boost_from_ambiguity'] = boost
            context = min(1.0, context + boost)

        # Create symbiotic analysis
        symbiotic_analysis = dimensional_analysis.copy()
        symbiotic_analysis['symbiotic_adjustments'] = symbiotic_adjustments
        symbiotic_analysis['adjusted_scores'] = {
            'clarity': clarity,
            'complexity': complexity,
            'context': context,
        }

        # Update analysis with adjusted scores
        symbiotic_analysis['clarity_analysis']['adjusted_score'] = clarity
        symbiotic_analysis['complexity_analysis']['adjusted_score'] = complexity
        symbiotic_analysis['context_analysis']['adjusted_score'] = context

        return symbiotic_analysis

    async def _calculate_holistic_score(
        self,
        analysis: Dict[str, Any],
        strategy: str
    ) -> Dict[str, Any]:
        """Calculate holistic intent score using specified strategy."""

        # Get scores (use adjusted scores if available)
        if 'adjusted_scores' in analysis:
            scores = analysis['adjusted_scores']
        else:
            scores = {
                'clarity': analysis['clarity_analysis']['score'],
                'complexity': analysis['complexity_analysis']['score'],
                'context': analysis['context_analysis']['score'],
            }

        clarity = scores['clarity']
        complexity = scores['complexity']
        context = scores['context']

        if strategy == "holistic":
            # Weighted geometric mean for holistic understanding
            weighted_product = (
                (clarity ** self._dimension_weights['clarity']) *
                (complexity ** self._dimension_weights['complexity']) *
                (context ** self._dimension_weights['context'])
            )
            overall_score = weighted_product

        elif strategy == "dimensional":
            # Simple weighted average
            overall_score = (
                clarity * self._dimension_weights['clarity'] +
                complexity * self._dimension_weights['complexity'] +
                context * self._dimension_weights['context']
            )

        elif strategy == "geometric":
            # Pure geometric mean
            overall_score = (clarity * complexity * context) ** (1/3)

        elif strategy == "harmonic":
            # Harmonic mean (emphasizes lower scores)
            harmonic_sum = (
                self._dimension_weights['clarity'] / max(0.01, clarity) +
                self._dimension_weights['complexity'] / max(0.01, complexity) +
                self._dimension_weights['context'] / max(0.01, context)
            )
            overall_score = sum(self._dimension_weights.values()) / harmonic_sum

        elif strategy == "adaptive":
            # Adaptive strategy based on dimensional characteristics
            if clarity > 0.8:  # High clarity - use weighted average
                overall_score = (
                    clarity * 0.6 + complexity * 0.25 + context * 0.15
                )
            elif complexity > 0.8:  # High complexity - emphasize complexity
                overall_score = (
                    clarity * 0.2 + complexity * 0.6 + context * 0.2
                )
            else:  # Default to geometric mean
                overall_score = (clarity * complexity * context) ** (1/3)

        else:
            # Default to holistic
            overall_score = (clarity * complexity * context) ** (1/3)

        # Calculate confidence based on dimensional consistency
        dimensional_variance = self._calculate_dimensional_variance(scores)
        confidence = max(0.0, overall_score * (1.0 - dimensional_variance))

        return {
            'overall_score': overall_score,
            'overall_confidence': confidence,
            'strategy_used': strategy,
            'dimensional_scores': scores,
            'dimensional_variance': dimensional_variance,
            'score_components': {
                'clarity_contribution': clarity * self._dimension_weights['clarity'],
                'complexity_contribution': complexity * self._dimension_weights['complexity'],
                'context_contribution': context * self._dimension_weights['context'],
            },
        }

    def _calculate_dimensional_variance(self, scores: Dict[str, float]) -> float:
        """Calculate variance in dimensional scores."""
        values = list(scores.values())
        mean_val = sum(values) / len(values)
        variance = sum((v - mean_val) ** 2 for v in values) / len(values)
        return variance

    async def _analyze_flow_state(
        self,
        holistic_score: Dict[str, Any],
        analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze flow state for optimal processing."""

        overall_score = holistic_score['overall_score']
        confidence = holistic_score['overall_confidence']

        # Determine flow state based on score and confidence
        if overall_score >= self._flow_state_thresholds['optimal'] and confidence >= 0.8:
            flow_state = "optimal"
            flow_description = "Intent is in optimal flow state - clear, manageable, and well-contextualized"
        elif overall_score >= self._flow_state_thresholds['good'] and confidence >= 0.6:
            flow_state = "good"
            flow_description = "Intent is in good flow state - mostly clear with manageable complexity"
        elif overall_score >= self._flow_state_thresholds['acceptable'] and confidence >= 0.4:
            flow_state = "acceptable"
            flow_description = "Intent is in acceptable flow state - some ambiguity but processable"
        elif overall_score >= self._flow_state_thresholds['challenging']:
            flow_state = "challenging"
            flow_description = "Intent is in challenging flow state - requires careful processing"
        else:
            flow_state = "difficult"
            flow_description = "Intent is in difficult flow state - significant processing challenges"

        # Generate flow state recommendations
        recommendations = self._generate_flow_state_recommendations(
            flow_state, holistic_score, analysis
        )

        # Calculate flow metrics
        flow_metrics = self._calculate_flow_metrics(holistic_score, analysis)

        return {
            'flow_state': flow_state,
            'flow_description': flow_description,
            'flow_score': overall_score,
            'flow_confidence': confidence,
            'recommendations': recommendations,
            'flow_metrics': flow_metrics,
        }

    def _generate_flow_state_recommendations(
        self,
        flow_state: str,
        holistic_score: Dict[str, Any],
        analysis: Dict[str, Any]
    ) -> List[str]:
        """Generate recommendations based on flow state."""
        recommendations = []

        scores = holistic_score['dimensional_scores']
        clarity = scores['clarity']
        complexity = scores['complexity']
        context = scores['context']

        if flow_state == "optimal":
            recommendations.append("Proceed with direct processing")
            recommendations.append("Intent is ready for immediate action")

        elif flow_state == "good":
            if clarity < 0.7:
                recommendations.append("Consider clarification for better results")
            if complexity > 0.6:
                recommendations.append("Break down into smaller steps if possible")
            recommendations.append("Standard processing approach recommended")

        elif flow_state == "acceptable":
            if clarity < 0.5:
                recommendations.append("Request clarification from user")
            if complexity > 0.7:
                recommendations.append("Use complex analysis processing")
            if context > 0.6:
                recommendations.append("Gather additional context before processing")

        elif flow_state == "challenging":
            recommendations.append("Use adaptive processing with multiple stages")
            if clarity < 0.4:
                recommendations.append("Prioritize intent clarification")
            if complexity > 0.8:
                recommendations.append("Apply deep reasoning algorithms")
            if context > 0.7:
                recommendations.append("Extensive context gathering required")

        else:  # difficult
            recommendations.append("Use full multi-agent consensus processing")
            recommendations.append("Consider asking user to rephrase intent")
            recommendations.append("Apply all available processing techniques")

        return recommendations

    def _calculate_flow_metrics(
        self,
        holistic_score: Dict[str, Any],
        analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate detailed flow metrics."""

        scores = holistic_score['dimensional_scores']
        variance = holistic_score['dimensional_variance']

        # Flow efficiency (how well-balanced the dimensions are)
        flow_efficiency = 1.0 - variance

        # Processing readiness (how ready the intent is for processing)
        processing_readiness = (
            scores['clarity'] * 0.5 +
            (1.0 - scores['complexity']) * 0.3 +
            (1.0 - scores['context']) * 0.2
        )

        # Intent stability (how consistent the dimensional analysis is)
        intent_stability = 1.0 - abs(scores['clarity'] - scores['complexity']) * 0.5

        # Optimization potential (how much the intent could be improved)
        optimization_potential = max(
            1.0 - scores['clarity'],
            scores['complexity'],
            scores['context']
        )

        return {
            'flow_efficiency': flow_efficiency,
            'processing_readiness': processing_readiness,
            'intent_stability': intent_stability,
            'optimization_potential': optimization_potential,
            'dimensional_harmony': 1.0 - variance,
        }

    async def _generate_filtering_recommendations(
        self,
        holistic_score: Dict[str, Any],
        flow_state_analysis: Dict[str, Any],
        dimensional_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate comprehensive filtering recommendations."""

        overall_score = holistic_score['overall_score']
        flow_state = flow_state_analysis['flow_state']
        intent_classification = dimensional_analysis['intent_classification']

        # Primary processing recommendation
        if flow_state == "optimal":
            primary_recommendation = "direct_execution"
        elif flow_state in ["good", "acceptable"]:
            primary_recommendation = "standard_processing"
        else:
            primary_recommendation = "complex_processing"

        # Processing pipeline recommendation
        if intent_classification['intent_type'] == "simple_explicit":
            pipeline_recommendation = "single_stage"
        elif intent_classification['intent_type'] in ["complex_explicit", "simple_ambiguous"]:
            pipeline_recommendation = "multi_stage"
        else:
            pipeline_recommendation = "full_pipeline"

        # Confidence threshold recommendation
        if overall_score >= 0.8:
            confidence_threshold = 0.9
        elif overall_score >= 0.6:
            confidence_threshold = 0.8
        elif overall_score >= 0.4:
            confidence_threshold = 0.7
        else:
            confidence_threshold = 0.6

        # Resource allocation recommendation
        if flow_state == "optimal":
            resource_allocation = "minimal"
        elif flow_state in ["good", "acceptable"]:
            resource_allocation = "standard"
        else:
            resource_allocation = "intensive"

        return {
            'primary_recommendation': primary_recommendation,
            'pipeline_recommendation': pipeline_recommendation,
            'confidence_threshold': confidence_threshold,
            'resource_allocation': resource_allocation,
            'processing_priority': self._calculate_processing_priority(overall_score, flow_state),
            'optimization_suggestions': flow_state_analysis['recommendations'],
            'estimated_processing_time': self._estimate_processing_time(
                intent_classification, flow_state
            ),
        }

    def _calculate_processing_priority(self, overall_score: float, flow_state: str) -> str:
        """Calculate processing priority based on score and flow state."""
        if flow_state == "optimal" and overall_score >= 0.9:
            return "immediate"
        elif flow_state in ["optimal", "good"] and overall_score >= 0.7:
            return "high"
        elif flow_state in ["good", "acceptable"] and overall_score >= 0.5:
            return "normal"
        elif flow_state == "acceptable" and overall_score >= 0.3:
            return "low"
        else:
            return "deferred"

    def _estimate_processing_time(self, intent_classification: Dict[str, Any], flow_state: str) -> str:
        """Estimate processing time based on classification and flow state."""
        intent_type = intent_classification['intent_type']

        base_times = {
            "simple_explicit": "fast",
            "complex_explicit": "medium",
            "simple_ambiguous": "medium",
            "moderate_mixed": "medium",
            "complex_ambiguous": "slow",
        }

        base_time = base_times.get(intent_type, "medium")

        # Adjust based on flow state
        if flow_state == "optimal":
            return base_time
        elif flow_state == "good":
            time_map = {"fast": "fast", "medium": "medium", "slow": "medium"}
            return time_map.get(base_time, "medium")
        elif flow_state == "acceptable":
            time_map = {"fast": "medium", "medium": "slow", "slow": "slow"}
            return time_map.get(base_time, "slow")
        else:  # challenging or difficult
            return "very_slow"

    async def _create_visualization_data(
        self,
        coordinates: Dict[str, float],
        dimensional_analysis: Dict[str, Any],
        holistic_score: Dict[str, Any],
        detail_level: str
    ) -> Dict[str, Any]:
        """Create visualization data for intent space."""

        visualization = {
            'coordinates_3d': coordinates,
            'dimensional_levels': {
                dim: dimensional_analysis[f'{dim}_analysis']['level']
                for dim in ['clarity', 'complexity', 'context']
            },
            'holistic_score_visual': {
                'score': holistic_score['overall_score'],
                'confidence': holistic_score['overall_confidence'],
                'color_intensity': holistic_score['overall_score'],
            },
        }

        if detail_level in ["detailed", "comprehensive"]:
            visualization.update({
                'dimensional_characteristics': {
                    dim: dimensional_analysis[f'{dim}_analysis']['characteristics']
                    for dim in ['clarity', 'complexity', 'context']
                },
                'relationships_graph': dimensional_analysis['dimensional_relationships'],
                'score_components': holistic_score['score_components'],
            })

        if detail_level == "comprehensive":
            visualization.update({
                'intent_space_region': self._determine_intent_space_region(coordinates),
                'processing_path_visualization': self._create_processing_path_visualization(
                    dimensional_analysis, holistic_score
                ),
                'optimization_vectors': self._calculate_optimization_vectors(coordinates),
            })

        return visualization

    def _determine_intent_space_region(self, coordinates: Dict[str, float]) -> str:
        """Determine which region of intent space the coordinates fall into."""
        clarity = coordinates['clarity']
        complexity = coordinates['complexity']
        context = coordinates['context']

        # Define 8 regions of the intent space cube
        if clarity >= 0.5 and complexity < 0.5 and context < 0.5:
            return "clear_simple_independent"
        elif clarity >= 0.5 and complexity >= 0.5 and context < 0.5:
            return "clear_complex_independent"
        elif clarity >= 0.5 and complexity < 0.5 and context >= 0.5:
            return "clear_simple_contextual"
        elif clarity >= 0.5 and complexity >= 0.5 and context >= 0.5:
            return "clear_complex_contextual"
        elif clarity < 0.5 and complexity < 0.5 and context < 0.5:
            return "ambiguous_simple_independent"
        elif clarity < 0.5 and complexity >= 0.5 and context < 0.5:
            return "ambiguous_complex_independent"
        elif clarity < 0.5 and complexity < 0.5 and context >= 0.5:
            return "ambiguous_simple_contextual"
        else:
            return "ambiguous_complex_contextual"

    def _create_processing_path_visualization(
        self,
        dimensional_analysis: Dict[str, Any],
        holistic_score: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create visualization of optimal processing path."""

        intent_type = dimensional_analysis['intent_classification']['intent_type']
        processing_rec = dimensional_analysis['intent_classification']['processing_recommendation']

        # Define processing stages based on intent characteristics
        if intent_type == "simple_explicit":
            stages = ["explicit_command", "direct_execution"]
        elif intent_type == "complex_explicit":
            stages = ["pattern_matching", "complexity_analysis", "execution"]
        elif intent_type == "simple_ambiguous":
            stages = ["clarification", "pattern_matching", "execution"]
        elif intent_type == "complex_ambiguous":
            stages = ["clarification", "contextual_analysis", "deep_reasoning", "execution"]
        else:
            stages = ["pattern_matching", "contextual_analysis", "execution"]

        return {
            'recommended_stages': stages,
            'processing_complexity': len(stages),
            'estimated_confidence_progression': self._estimate_confidence_progression(stages),
        }

    def _estimate_confidence_progression(self, stages: List[str]) -> List[Dict[str, Any]]:
        """Estimate confidence progression through processing stages."""
        progression = []
        base_confidence = 0.3

        confidence_gains = {
            "explicit_command": 0.6,
            "pattern_matching": 0.3,
            "clarification": 0.4,
            "contextual_analysis": 0.2,
            "complexity_analysis": 0.25,
            "deep_reasoning": 0.15,
            "direct_execution": 0.1,
            "execution": 0.1,
        }

        current_confidence = base_confidence
        for stage in stages:
            gain = confidence_gains.get(stage, 0.1)
            current_confidence = min(1.0, current_confidence + gain)
            progression.append({
                'stage': stage,
                'confidence': current_confidence,
                'gain': gain,
            })

        return progression

    def _calculate_optimization_vectors(self, coordinates: Dict[str, float]) -> Dict[str, Any]:
        """Calculate vectors for optimizing intent processing."""

        clarity = coordinates['clarity']
        complexity = coordinates['complexity']
        context = coordinates['context']

        # Calculate optimization directions
        optimization_vectors = {
            'clarity_improvement': max(0.0, 1.0 - clarity),
            'complexity_reduction': max(0.0, complexity - 0.3),
            'context_minimization': max(0.0, context - 0.2),
        }

        # Determine primary optimization target
        primary_target = max(optimization_vectors.keys(), key=lambda k: optimization_vectors[k])

        return {
            'optimization_vectors': optimization_vectors,
            'primary_optimization_target': primary_target,
            'optimization_potential': max(optimization_vectors.values()),
        }

    def _update_filtering_history(
        self,
        intent_text: str,
        holistic_score: Dict[str, Any],
        recommendations: Dict[str, Any]
    ) -> None:
        """Update filtering history for learning and analytics."""

        history_entry = {
            'timestamp': datetime.now().isoformat(),
            'intent_text': intent_text,
            'overall_score': holistic_score['overall_score'],
            'overall_confidence': holistic_score['overall_confidence'],
            'primary_recommendation': recommendations['primary_recommendation'],
            'processing_priority': recommendations['processing_priority'],
        }

        self._filtering_history.append(history_entry)

        # Keep only last 1000 entries
        if len(self._filtering_history) > 1000:
            self._filtering_history.pop(0)

    def get_filtering_analytics(self) -> Dict[str, Any]:
        """Get analytics from filtering history."""
        if not self._filtering_history:
            return {'message': 'No filtering history available'}

        # Calculate analytics
        total_processed = len(self._filtering_history)
        avg_score = sum(entry['overall_score'] for entry in self._filtering_history) / total_processed
        avg_confidence = sum(entry['overall_confidence'] for entry in self._filtering_history) / total_processed

        # Recommendation distribution
        recommendations = [entry['primary_recommendation'] for entry in self._filtering_history]
        recommendation_dist = {rec: recommendations.count(rec) for rec in set(recommendations)}

        # Priority distribution
        priorities = [entry['processing_priority'] for entry in self._filtering_history]
        priority_dist = {pri: priorities.count(pri) for pri in set(priorities)}

        return {
            'total_processed': total_processed,
            'average_score': avg_score,
            'average_confidence': avg_confidence,
            'recommendation_distribution': recommendation_dist,
            'priority_distribution': priority_dist,
            'recent_trends': self._calculate_recent_trends(),
        }

    def _calculate_recent_trends(self) -> Dict[str, Any]:
        """Calculate trends from recent filtering history."""
        if len(self._filtering_history) < 10:
            return {'message': 'Insufficient data for trend analysis'}

        recent_entries = self._filtering_history[-10:]
        older_entries = self._filtering_history[-20:-10] if len(self._filtering_history) >= 20 else []

        recent_avg_score = sum(entry['overall_score'] for entry in recent_entries) / len(recent_entries)
        recent_avg_confidence = sum(entry['overall_confidence'] for entry in recent_entries) / len(recent_entries)

        if older_entries:
            older_avg_score = sum(entry['overall_score'] for entry in older_entries) / len(older_entries)
            older_avg_confidence = sum(entry['overall_confidence'] for entry in older_entries) / len(older_entries)

            score_trend = "improving" if recent_avg_score > older_avg_score else "declining"
            confidence_trend = "improving" if recent_avg_confidence > older_avg_confidence else "declining"
        else:
            score_trend = "stable"
            confidence_trend = "stable"

        return {
            'recent_average_score': recent_avg_score,
            'recent_average_confidence': recent_avg_confidence,
            'score_trend': score_trend,
            'confidence_trend': confidence_trend,
        }
