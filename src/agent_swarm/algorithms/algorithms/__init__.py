"""
Built-in mathematical algorithms for Agent Swarm.

This module contains implementations of various mathematical algorithms
following the framework patterns:

- Adaptive Intent Processing algorithms
- Intent Filtering Triangle algorithms
- Confidence and threshold algorithms
- Pattern matching algorithms
- Vector space classification algorithms

All algorithms follow the OpenHands-inspired event-driven architecture
and provide comprehensive configuration, monitoring, and optimization.
"""

from .confidence_thresholder import ConfidenceThresholder
from .pattern_matcher import PatternMatcher
from .adaptive_intent_processor import AdaptiveIntentProcessor
from .intent_filtering_triangle import IntentFilteringTriangle

# Import algorithm stages
from .stages import (
    ExplicitCommandStage,
    PatternMatchingStage,
    ContextualAnalysisStage,
    DeepReasoningStage,
    MultiAgentConsensusStage,
)

# Placeholder imports for algorithms to be implemented
# from .vector_space_classifier import VectorSpaceClassifier

__all__ = [
    # Implemented algorithms
    "ConfidenceThresholder",
    "PatternMatcher",
    "AdaptiveIntentProcessor",
    "IntentFilteringTriangle",

    # Algorithm stages
    "ExplicitCommandStage",
    "PatternMatchingStage",
    "ContextualAnalysisStage",
    "DeepReasoningStage",
    "MultiAgentConsensusStage",

    # Placeholder for future algorithms
    # "VectorSpaceClassifier",
]
