"""
Confidence Thresholder Algorithm.

This algorithm applies confidence thresholding to input data with various
thresholding strategies and optimization capabilities.

Features:
- Multiple thresholding strategies (binary, soft, adaptive)
- Configurable threshold values and parameters
- Performance monitoring and optimization
- Comprehensive validation and error handling

This serves as an example of the mathematical algorithm framework in action.
"""

import math
import time
from datetime import datetime
from typing import Any, Dict, List, Union
from uuid import uuid4

from ..core import (
    Algorithm,
    AlgorithmContext,
    AlgorithmResult,
)
from ..core.registry import AlgorithmMetadata
from ..core.config import (
    ParameterDefinition,
    ParameterType,
    ParameterConstraint,
)


class ConfidenceThresholder(Algorithm):
    """
    Confidence thresholding algorithm with multiple strategies.

    This algorithm applies confidence thresholding to input data using
    various strategies like binary, soft, and adaptive thresholding.

    Input: List of confidence scores (floats between 0 and 1)
    Output: Thresholded values based on selected strategy
    """

    @property
    def metadata(self) -> AlgorithmMetadata:
        """Get algorithm metadata."""
        return AlgorithmMetadata(
            name="confidence_thresholder",
            version="1.0.0",
            description="Applies confidence thresholding with multiple strategies",
            author="Agent Swarm Framework",
            category="thresholding",
            tags=["confidence", "thresholding", "filtering", "optimization"],
            created_date=datetime(2025, 1, 6),
            updated_date=datetime(2025, 1, 6),
            dependencies=[],
            input_types=["list[float]", "dict"],
            output_types=["list", "dict"],
            complexity="O(n)",
            documentation_url="https://github.com/agent-swarm/docs/confidence-thresholder",
        )

    def get_parameter_definitions(self) -> List[ParameterDefinition]:
        """Get parameter definitions for this algorithm."""
        return [
            ParameterDefinition(
                name="threshold",
                param_type=ParameterType.FLOAT,
                default_value=0.5,
                description="Confidence threshold value",
                required=True,
                constraints=ParameterConstraint(min_value=0.0, max_value=1.0),
                category="thresholding",
            ),
            ParameterDefinition(
                name="strategy",
                param_type=ParameterType.STRING,
                default_value="binary",
                description="Thresholding strategy",
                required=True,
                constraints=ParameterConstraint(
                    allowed_values=["binary", "soft", "adaptive", "sigmoid"]
                ),
                category="thresholding",
            ),
            ParameterDefinition(
                name="adaptive_factor",
                param_type=ParameterType.FLOAT,
                default_value=0.1,
                description="Factor for adaptive thresholding",
                required=False,
                constraints=ParameterConstraint(min_value=0.01, max_value=1.0),
                category="adaptive",
            ),
            ParameterDefinition(
                name="sigmoid_steepness",
                param_type=ParameterType.FLOAT,
                default_value=10.0,
                description="Steepness parameter for sigmoid thresholding",
                required=False,
                constraints=ParameterConstraint(min_value=0.1, max_value=100.0),
                category="sigmoid",
            ),
            ParameterDefinition(
                name="min_output_value",
                param_type=ParameterType.FLOAT,
                default_value=0.0,
                description="Minimum output value for soft thresholding",
                required=False,
                constraints=ParameterConstraint(min_value=0.0, max_value=1.0),
                category="output",
            ),
        ]

    def validate_input(self, input_data: Any) -> bool:
        """Validate input data."""
        # Accept list of floats or dict with confidence scores
        if isinstance(input_data, list):
            # Check if all elements are numeric
            return all(isinstance(x, (int, float)) for x in input_data)

        elif isinstance(input_data, dict):
            # Check if dict has 'confidences' key with list of floats
            if 'confidences' not in input_data:
                return False
            confidences = input_data['confidences']
            return isinstance(confidences, list) and all(
                isinstance(x, (int, float)) for x in confidences
            )

        return False

    def estimate_complexity(self, input_size: int) -> float:
        """Estimate computational complexity."""
        # O(n) complexity - linear in input size
        return float(input_size)

    async def execute(self, input_data: Any, context: AlgorithmContext) -> AlgorithmResult:
        """Execute the confidence thresholding algorithm."""
        start_time = time.time()

        try:
            # Extract parameters
            threshold = context.get_parameter("threshold", 0.5)
            strategy = context.get_parameter("strategy", "binary")
            adaptive_factor = context.get_parameter("adaptive_factor", 0.1)
            sigmoid_steepness = context.get_parameter("sigmoid_steepness", 10.0)
            min_output_value = context.get_parameter("min_output_value", 0.0)

            # Extract confidence scores
            if isinstance(input_data, list):
                confidences = input_data
                metadata_input = {}
            else:
                confidences = input_data['confidences']
                metadata_input = {k: v for k, v in input_data.items() if k != 'confidences'}

            # Update progress
            context.update_progress(0.1, "Extracting confidence scores")

            # Apply thresholding strategy
            context.update_progress(0.3, f"Applying {strategy} thresholding")

            if strategy == "binary":
                output_values = self._binary_threshold(confidences, threshold)
            elif strategy == "soft":
                output_values = self._soft_threshold(confidences, threshold, min_output_value)
            elif strategy == "adaptive":
                output_values = self._adaptive_threshold(confidences, threshold, adaptive_factor)
            elif strategy == "sigmoid":
                output_values = self._sigmoid_threshold(confidences, threshold, sigmoid_steepness)
            else:
                raise ValueError(f"Unknown thresholding strategy: {strategy}")

            # Calculate confidence in the result
            context.update_progress(0.8, "Calculating result confidence")
            result_confidence = self._calculate_result_confidence(
                confidences, output_values, strategy
            )

            # Prepare output
            if isinstance(input_data, list):
                output = output_values
            else:
                output = {
                    **metadata_input,
                    'thresholded_values': output_values,
                    'original_confidences': confidences,
                }

            # Calculate execution metrics
            execution_time = time.time() - start_time

            context.update_progress(1.0, "Thresholding completed")

            return AlgorithmResult(
                action_id=str(uuid4()),
                algorithm_name=self.metadata.name,
                success=True,
                output=output,
                confidence=result_confidence,
                execution_time=execution_time,
                parameters_used={
                    "threshold": threshold,
                    "strategy": strategy,
                    "adaptive_factor": adaptive_factor,
                    "sigmoid_steepness": sigmoid_steepness,
                    "min_output_value": min_output_value,
                },
                metadata={
                    "input_size": len(confidences),
                    "strategy_used": strategy,
                    "threshold_applied": threshold,
                    "values_above_threshold": sum(1 for c in confidences if c >= threshold),
                    "values_below_threshold": sum(1 for c in confidences if c < threshold),
                    "avg_input_confidence": sum(confidences) / len(confidences) if confidences else 0,
                    "algorithm_version": self.metadata.version,
                },
            )

        except Exception as e:
            execution_time = time.time() - start_time

            return AlgorithmResult(
                action_id=str(uuid4()),
                algorithm_name=self.metadata.name,
                success=False,
                output=None,
                confidence=0.0,
                execution_time=execution_time,
                error=f"Confidence thresholding failed: {str(e)}",
                metadata={
                    "error_type": type(e).__name__,
                    "input_type": type(input_data).__name__,
                },
            )

    def _binary_threshold(self, confidences: List[float], threshold: float) -> List[bool]:
        """Apply binary thresholding."""
        return [c >= threshold for c in confidences]

    def _soft_threshold(
        self,
        confidences: List[float],
        threshold: float,
        min_value: float
    ) -> List[float]:
        """Apply soft thresholding."""
        return [c if c >= threshold else min_value for c in confidences]

    def _adaptive_threshold(
        self,
        confidences: List[float],
        base_threshold: float,
        adaptive_factor: float
    ) -> List[float]:
        """Apply adaptive thresholding based on local statistics."""
        if not confidences:
            return []

        # Calculate adaptive threshold based on mean and std
        mean_confidence = sum(confidences) / len(confidences)
        variance = sum((c - mean_confidence) ** 2 for c in confidences) / len(confidences)
        std_confidence = math.sqrt(variance)

        # Adjust threshold based on distribution
        adaptive_threshold = base_threshold + adaptive_factor * std_confidence
        adaptive_threshold = max(0.0, min(1.0, adaptive_threshold))  # Clamp to [0, 1]

        return [c if c >= adaptive_threshold else 0.0 for c in confidences]

    def _sigmoid_threshold(
        self,
        confidences: List[float],
        threshold: float,
        steepness: float
    ) -> List[float]:
        """Apply sigmoid thresholding for smooth transitions."""
        return [
            1.0 / (1.0 + math.exp(-steepness * (c - threshold)))
            for c in confidences
        ]

    def _calculate_result_confidence(
        self,
        input_confidences: List[float],
        output_values: List[Union[bool, float]],
        strategy: str
    ) -> float:
        """Calculate confidence in the thresholding result."""
        if not input_confidences:
            return 0.0

        # Base confidence on input quality and strategy appropriateness
        avg_input_confidence = sum(input_confidences) / len(input_confidences)

        # Calculate distribution metrics
        variance = sum((c - avg_input_confidence) ** 2 for c in input_confidences) / len(input_confidences)
        std_dev = math.sqrt(variance)

        # Strategy-specific confidence adjustments
        if strategy == "binary":
            # Higher confidence for clear separations
            confidence_factor = 1.0 - std_dev  # Lower std = higher confidence
        elif strategy == "soft":
            # Moderate confidence for soft thresholding
            confidence_factor = 0.8
        elif strategy == "adaptive":
            # Confidence based on adaptation effectiveness
            confidence_factor = 0.9 - std_dev * 0.5
        elif strategy == "sigmoid":
            # High confidence for smooth transitions
            confidence_factor = 0.95
        else:
            confidence_factor = 0.5

        # Combine factors
        result_confidence = avg_input_confidence * confidence_factor

        # Ensure confidence is in [0, 1] range
        return max(0.0, min(1.0, result_confidence))
