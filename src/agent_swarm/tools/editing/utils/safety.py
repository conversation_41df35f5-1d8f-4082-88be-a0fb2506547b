"""
Safety utilities for file editing operations.

Provides validation and safety checks to prevent dangerous operations.
"""

import os
import stat
from pathlib import Path
from typing import List, Set


# Dangerous paths that should never be edited
DANGEROUS_PATHS = {
    '/etc/passwd',
    '/etc/shadow',
    '/etc/sudoers',
    '/boot',
    '/sys',
    '/proc',
    '/dev',
    '/etc/fstab',
    '/etc/hosts',
    '/etc/resolv.conf',
}

# Dangerous file patterns
DANGEROUS_PATTERNS = {
    '*.key',
    '*.pem',
    '*.p12',
    '*.pfx',
    '*.crt',
    '*.cer',
    'id_rsa',
    'id_dsa',
    'id_ecdsa',
    'id_ed25519',
    '.env',
    '.env.*',
    'secrets.*',
    'password*',
    'passwd*',
}

# Binary file extensions
BINARY_EXTENSIONS = {
    '.exe', '.dll', '.so', '.dylib', '.bin', '.obj', '.o',
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.ico',
    '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv',
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.zip', '.tar', '.gz', '.bz2', '.xz', '.7z', '.rar',
    '.db', '.sqlite', '.sqlite3', '.mdb',
    '.class', '.jar', '.war', '.ear',
    '.pyc', '.pyo', '.pyd',
}

# Maximum safe file size (100MB)
MAX_SAFE_FILE_SIZE = 100 * 1024 * 1024


def validate_file_path(path: Path) -> bool:
    """
    Validate if a file path is safe to edit.
    
    Args:
        path: Path to validate
        
    Returns:
        True if path is safe, False otherwise
    """
    try:
        # Convert to absolute path for consistent checking
        abs_path = path.resolve()
        
        # Check against dangerous paths
        if _is_dangerous_path(abs_path):
            return False
        
        # Check for path traversal attempts
        if _has_path_traversal(path):
            return False
        
        # Check if path is within allowed directories
        if not _is_within_allowed_directories(abs_path):
            return False
        
        # Check file permissions
        if abs_path.exists() and not _has_safe_permissions(abs_path):
            return False
        
        return True
        
    except (OSError, ValueError):
        return False


def _is_dangerous_path(path: Path) -> bool:
    """Check if path is in the dangerous paths list."""
    path_str = str(path)
    
    # Check exact matches
    if path_str in DANGEROUS_PATHS:
        return True
    
    # Check if path starts with dangerous directories
    dangerous_dirs = {'/etc/', '/boot/', '/sys/', '/proc/', '/dev/'}
    for dangerous_dir in dangerous_dirs:
        if path_str.startswith(dangerous_dir):
            return True
    
    # Check dangerous patterns
    import fnmatch
    filename = path.name.lower()
    for pattern in DANGEROUS_PATTERNS:
        if fnmatch.fnmatch(filename, pattern):
            return True
    
    return False


def _has_path_traversal(path: Path) -> bool:
    """Check for path traversal attempts."""
    path_str = str(path)
    
    # Check for common path traversal patterns
    traversal_patterns = ['../', '..\\', '/../', '\\..\\']
    for pattern in traversal_patterns:
        if pattern in path_str:
            return True
    
    # Check for absolute paths outside current working directory
    # (when using relative paths)
    if not path.is_absolute():
        try:
            resolved = path.resolve()
            cwd = Path.cwd()
            # Check if resolved path is outside current directory tree
            try:
                resolved.relative_to(cwd)
            except ValueError:
                # Path is outside current directory
                return True
        except (OSError, ValueError):
            return True
    
    return False


def _is_within_allowed_directories(path: Path) -> bool:
    """Check if path is within allowed directories."""
    # For now, allow any path that's not explicitly dangerous
    # This can be configured based on specific requirements
    
    # Don't allow editing system directories
    system_dirs = {'/bin', '/sbin', '/usr/bin', '/usr/sbin', '/lib', '/usr/lib'}
    path_str = str(path)
    
    for sys_dir in system_dirs:
        if path_str.startswith(sys_dir + '/'):
            return False
    
    return True


def _has_safe_permissions(path: Path) -> bool:
    """Check if file has safe permissions for editing."""
    try:
        file_stat = path.stat()
        
        # Check if file is owned by current user or is world-writable
        current_uid = os.getuid() if hasattr(os, 'getuid') else None
        
        if current_uid is not None:
            # On Unix-like systems, check ownership
            if file_stat.st_uid != current_uid:
                # File not owned by current user
                # Check if it's world-writable (potentially dangerous)
                if file_stat.st_mode & stat.S_IWOTH:
                    return False
        
        # Check for setuid/setgid bits (dangerous)
        if file_stat.st_mode & (stat.S_ISUID | stat.S_ISGID):
            return False
        
        return True
        
    except (OSError, AttributeError):
        # If we can't check permissions, err on the side of caution
        return False


def is_binary_file(path: Path, sample_size: int = 1024) -> bool:
    """
    Check if a file is binary.
    
    Args:
        path: Path to the file
        sample_size: Number of bytes to sample
        
    Returns:
        True if file is binary, False if text
    """
    if not path.exists() or not path.is_file():
        return False
    
    # Check extension first
    if path.suffix.lower() in BINARY_EXTENSIONS:
        return True
    
    # Check file content
    try:
        with open(path, 'rb') as f:
            sample = f.read(sample_size)
        
        if not sample:
            return False  # Empty file is considered text
        
        # Check for null bytes (common in binary files)
        if b'\x00' in sample:
            return True
        
        # Check for high ratio of non-printable characters
        non_printable = sum(1 for byte in sample if byte < 32 and byte not in (9, 10, 13))
        ratio = non_printable / len(sample)
        
        return ratio > 0.3
        
    except (OSError, IOError):
        return True  # If we can't read it, assume it's binary


def validate_file_size(path: Path, max_size: int = MAX_SAFE_FILE_SIZE) -> bool:
    """
    Validate if file size is within safe limits.
    
    Args:
        path: Path to the file
        max_size: Maximum allowed file size in bytes
        
    Returns:
        True if file size is safe, False otherwise
    """
    try:
        if not path.exists():
            return True  # New files are OK
        
        file_size = path.stat().st_size
        return file_size <= max_size
        
    except (OSError, IOError):
        return False


def get_safe_backup_path(original_path: Path, backup_dir: Path = None) -> Path:
    """
    Generate a safe backup path for a file.
    
    Args:
        original_path: Original file path
        backup_dir: Directory for backups (default: same as original)
        
    Returns:
        Safe backup path
    """
    if backup_dir is None:
        backup_dir = original_path.parent
    
    # Ensure backup directory exists and is safe
    backup_dir = backup_dir.resolve()
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate unique backup filename
    timestamp = int(time.time())
    backup_name = f"{original_path.stem}.{timestamp}.backup{original_path.suffix}"
    backup_path = backup_dir / backup_name
    
    # Ensure backup path doesn't already exist
    counter = 1
    while backup_path.exists():
        backup_name = f"{original_path.stem}.{timestamp}.{counter}.backup{original_path.suffix}"
        backup_path = backup_dir / backup_name
        counter += 1
    
    return backup_path


def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename to make it safe for filesystem use.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    import re
    
    # Remove or replace dangerous characters
    # Keep alphanumeric, dots, hyphens, underscores
    sanitized = re.sub(r'[^\w\-_\.]', '_', filename)
    
    # Remove leading/trailing dots and spaces
    sanitized = sanitized.strip('. ')
    
    # Ensure filename is not empty
    if not sanitized:
        sanitized = 'unnamed_file'
    
    # Limit length
    if len(sanitized) > 255:
        name, ext = os.path.splitext(sanitized)
        max_name_len = 255 - len(ext)
        sanitized = name[:max_name_len] + ext
    
    return sanitized


def check_disk_space(path: Path, required_bytes: int) -> bool:
    """
    Check if there's enough disk space for an operation.
    
    Args:
        path: Path to check (file or directory)
        required_bytes: Required space in bytes
        
    Returns:
        True if enough space available, False otherwise
    """
    try:
        if path.is_file():
            check_path = path.parent
        else:
            check_path = path
        
        stat_result = os.statvfs(str(check_path))
        available_bytes = stat_result.f_bavail * stat_result.f_frsize
        
        return available_bytes >= required_bytes
        
    except (OSError, AttributeError):
        # If we can't check, assume there's enough space
        return True


# Import time for backup path generation
import time
