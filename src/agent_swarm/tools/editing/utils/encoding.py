"""
Encoding detection and handling utilities.

Provides robust encoding detection and text normalization for file editing.
"""

import re
from pathlib import Path
from typing import Optional

try:
    import chardet
    CHARDET_AVAILABLE = True
except ImportError:
    CHARDET_AVAILABLE = False


def detect_encoding(file_path: Path, sample_size: int = 8192) -> str:
    """
    Detect file encoding using multiple methods.
    
    Args:
        file_path: Path to the file
        sample_size: Number of bytes to sample for detection
        
    Returns:
        Detected encoding name (defaults to 'utf-8')
    """
    if not file_path.exists() or not file_path.is_file():
        return 'utf-8'
    
    # Try to read a sample of the file
    try:
        with open(file_path, 'rb') as f:
            sample = f.read(sample_size)
    except (OSError, IOError):
        return 'utf-8'
    
    if not sample:
        return 'utf-8'
    
    # Method 1: Check for BOM (Byte Order Mark)
    bom_encoding = _detect_bom(sample)
    if bom_encoding:
        return bom_encoding
    
    # Method 2: Use chardet if available
    if CHARDET_AVAILABLE:
        chardet_result = chardet.detect(sample)
        if chardet_result and chardet_result['confidence'] > 0.7:
            encoding = chardet_result['encoding']
            if encoding:
                return _normalize_encoding_name(encoding)
    
    # Method 3: Try common encodings
    common_encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252', 'ascii']
    for encoding in common_encodings:
        try:
            sample.decode(encoding)
            return encoding
        except UnicodeDecodeError:
            continue
    
    # Fallback to utf-8
    return 'utf-8'


def _detect_bom(data: bytes) -> Optional[str]:
    """Detect encoding from Byte Order Mark."""
    if data.startswith(b'\xef\xbb\xbf'):
        return 'utf-8-sig'
    elif data.startswith(b'\xff\xfe'):
        return 'utf-16-le'
    elif data.startswith(b'\xfe\xff'):
        return 'utf-16-be'
    elif data.startswith(b'\xff\xfe\x00\x00'):
        return 'utf-32-le'
    elif data.startswith(b'\x00\x00\xfe\xff'):
        return 'utf-32-be'
    return None


def _normalize_encoding_name(encoding: str) -> str:
    """Normalize encoding name to standard form."""
    encoding = encoding.lower().replace('_', '-')
    
    # Common aliases
    aliases = {
        'iso-8859-1': 'latin-1',
        'windows-1252': 'cp1252',
        'us-ascii': 'ascii',
    }
    
    return aliases.get(encoding, encoding)


def normalize_line_endings(content: str, target: str = '\n') -> str:
    """
    Normalize line endings in text content.
    
    Args:
        content: Text content to normalize
        target: Target line ending ('\n', '\r\n', or '\r')
        
    Returns:
        Content with normalized line endings
    """
    # Replace all line ending variations with target
    content = re.sub(r'\r\n|\r|\n', target, content)
    return content


def detect_line_ending(content: str) -> str:
    """
    Detect the predominant line ending style in content.
    
    Args:
        content: Text content to analyze
        
    Returns:
        Detected line ending ('\n', '\r\n', or '\r')
    """
    crlf_count = content.count('\r\n')
    lf_count = content.count('\n') - crlf_count
    cr_count = content.count('\r') - crlf_count
    
    if crlf_count > lf_count and crlf_count > cr_count:
        return '\r\n'
    elif cr_count > lf_count and cr_count > crlf_count:
        return '\r'
    else:
        return '\n'


def is_text_file(file_path: Path, sample_size: int = 1024) -> bool:
    """
    Check if a file is likely a text file.
    
    Args:
        file_path: Path to the file
        sample_size: Number of bytes to sample
        
    Returns:
        True if file appears to be text, False otherwise
    """
    if not file_path.exists() or not file_path.is_file():
        return False
    
    try:
        with open(file_path, 'rb') as f:
            sample = f.read(sample_size)
    except (OSError, IOError):
        return False
    
    if not sample:
        return True  # Empty file is considered text
    
    # Check for null bytes (common in binary files)
    if b'\x00' in sample:
        return False
    
    # Check for high ratio of printable characters
    printable_chars = sum(1 for byte in sample if 32 <= byte <= 126 or byte in (9, 10, 13))
    ratio = printable_chars / len(sample)
    
    return ratio > 0.7


def safe_decode(data: bytes, encoding: str = 'utf-8', errors: str = 'replace') -> str:
    """
    Safely decode bytes to string with error handling.
    
    Args:
        data: Bytes to decode
        encoding: Encoding to use
        errors: Error handling strategy
        
    Returns:
        Decoded string
    """
    try:
        return data.decode(encoding, errors=errors)
    except (UnicodeDecodeError, LookupError):
        # Fallback to latin-1 which can decode any byte sequence
        return data.decode('latin-1', errors='replace')


def safe_encode(text: str, encoding: str = 'utf-8', errors: str = 'replace') -> bytes:
    """
    Safely encode string to bytes with error handling.
    
    Args:
        text: String to encode
        encoding: Encoding to use
        errors: Error handling strategy
        
    Returns:
        Encoded bytes
    """
    try:
        return text.encode(encoding, errors=errors)
    except (UnicodeEncodeError, LookupError):
        # Fallback to utf-8 with replacement
        return text.encode('utf-8', errors='replace')


def get_file_encoding_info(file_path: Path) -> dict:
    """
    Get comprehensive encoding information for a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Dictionary with encoding information
    """
    info = {
        'encoding': 'utf-8',
        'confidence': 0.0,
        'bom': None,
        'line_ending': '\n',
        'is_text': False,
        'file_size': 0,
    }
    
    if not file_path.exists() or not file_path.is_file():
        return info
    
    try:
        file_size = file_path.stat().st_size
        info['file_size'] = file_size
        
        if file_size == 0:
            info['is_text'] = True
            return info
        
        # Read sample for analysis
        sample_size = min(8192, file_size)
        with open(file_path, 'rb') as f:
            sample = f.read(sample_size)
        
        # Check if it's a text file
        info['is_text'] = is_text_file(file_path)
        
        if info['is_text']:
            # Detect encoding
            info['encoding'] = detect_encoding(file_path)
            
            # Detect BOM
            info['bom'] = _detect_bom(sample)
            
            # Detect line endings (read as text)
            try:
                text_content = sample.decode(info['encoding'])
                info['line_ending'] = detect_line_ending(text_content)
            except UnicodeDecodeError:
                pass
            
            # Get confidence if chardet is available
            if CHARDET_AVAILABLE:
                chardet_result = chardet.detect(sample)
                if chardet_result:
                    info['confidence'] = chardet_result.get('confidence', 0.0)
    
    except (OSError, IOError):
        pass
    
    return info
