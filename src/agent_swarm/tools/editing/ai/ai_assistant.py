"""
AI-powered editing assistant implementation.

Provides intelligent code editing, suggestions, and analysis using AI models.
"""

import re
from pathlib import Path
from typing import List, Dict, Any, Optional

from ..core.interfaces import IAIAssistant
from ..core.models import <PERSON><PERSON><PERSON><PERSON>, EditContext


class AIEditingAssistant(IAIAssistant):
    """
    AI-powered editing assistant with natural language processing.
    
    Features:
    - Natural language to code conversion
    - Code explanation and documentation
    - Smart refactoring suggestions
    - Bug detection and fixes
    - Code review and quality analysis
    """
    
    def __init__(self, provider: str = "ollama", model: str = "llama3.2:3b"):
        """
        Initialize the AI editing assistant.
        
        Args:
            provider: AI provider (ollama, openai, anthropic, etc.)
            model: Model name to use
        """
        self.provider = provider
        self.model = model
        self._llm = None
    
    async def initialize(self) -> None:
        """Initialize the AI model connection."""
        try:
            if self.provider == "ollama":
                from ....backends.ollama_backend import create_ollama_llm
                self._llm = create_ollama_llm(self.model)
            elif self.provider == "openai":
                from ....backends.openai_backend import create_openai_llm
                self._llm = create_openai_llm(self.model)
            # Add other providers as needed
            else:
                raise ValueError(f"Unsupported AI provider: {self.provider}")
                
        except ImportError as e:
            raise ImportError(f"AI provider {self.provider} not available: {e}")
    
    async def suggest_edit(self, instruction: str, content: str, context: EditContext) -> str:
        """
        Generate edit suggestion based on natural language instruction.
        
        Args:
            instruction: Natural language instruction
            content: Current file content
            context: Edit context information
            
        Returns:
            Suggested new content
        """
        if not self._llm:
            await self.initialize()
        
        # Build context-aware prompt
        prompt = self._build_edit_prompt(instruction, content, context)
        
        try:
            from ....backends.base import Message
            
            response = await self._llm.generate(
                messages=[Message(role="user", content=prompt)],
                temperature=0.1,  # Low temperature for consistent code generation
                max_tokens=4000
            )
            
            # Extract code from response
            suggested_content = self._extract_code_from_response(response.content)
            
            return suggested_content or content  # Fallback to original if extraction fails
            
        except Exception as e:
            print(f"AI suggestion failed: {e}")
            return content
    
    async def explain_change(self, change: FileChange) -> str:
        """
        Explain what a change does in natural language.
        
        Args:
            change: FileChange object to explain
            
        Returns:
            Natural language explanation
        """
        if not self._llm:
            await self.initialize()
        
        # Generate diff for context
        diff_lines = []
        if change.diff_result:
            for hunk in change.diff_result.hunks:
                for line in hunk.lines:
                    prefix = {
                        "added": "+",
                        "removed": "-",
                        "context": " "
                    }.get(line.line_type.value, " ")
                    diff_lines.append(f"{prefix}{line.content}")
        
        diff_text = "\n".join(diff_lines[:50])  # Limit diff size
        
        prompt = f"""
Explain what this code change does in clear, simple language:

File: {change.file_path}
Description: {change.description}

Diff:
```
{diff_text}
```

Provide a concise explanation of:
1. What was changed
2. Why this change might be useful
3. Any potential impacts

Keep the explanation under 3 sentences and use simple language.
"""
        
        try:
            from ....backends.base import Message
            
            response = await self._llm.generate(
                messages=[Message(role="user", content=prompt)],
                temperature=0.3,
                max_tokens=200
            )
            
            return response.content.strip()
            
        except Exception as e:
            return f"Unable to explain change: {e}"
    
    async def review_code(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """
        Review code and provide suggestions for improvement.
        
        Args:
            content: Code content to review
            file_path: Path to the file
            
        Returns:
            List of review suggestions
        """
        if not self._llm:
            await self.initialize()
        
        file_type = file_path.suffix.lower()
        language = self._get_language_name(file_type)
        
        prompt = f"""
Review this {language} code and provide specific suggestions for improvement:

File: {file_path}
```{language}
{content[:2000]}  # Limit content size
```

Focus on:
1. Code quality and best practices
2. Potential bugs or issues
3. Performance improvements
4. Security concerns
5. Readability and maintainability

Provide suggestions in this JSON format:
[
  {{
    "type": "suggestion|warning|error",
    "line": line_number_or_null,
    "message": "Description of the issue",
    "suggestion": "How to fix or improve"
  }}
]

Limit to the 5 most important suggestions.
"""
        
        try:
            from ....backends.base import Message
            
            response = await self._llm.generate(
                messages=[Message(role="user", content=prompt)],
                temperature=0.2,
                max_tokens=1000
            )
            
            # Try to parse JSON response
            suggestions = self._parse_review_response(response.content)
            return suggestions
            
        except Exception as e:
            return [{
                "type": "error",
                "line": None,
                "message": f"Code review failed: {e}",
                "suggestion": "Manual review recommended"
            }]
    
    async def generate_documentation(self, content: str, file_path: Path) -> str:
        """
        Generate documentation for code.
        
        Args:
            content: Code content
            file_path: Path to the file
            
        Returns:
            Generated documentation
        """
        if not self._llm:
            await self.initialize()
        
        file_type = file_path.suffix.lower()
        language = self._get_language_name(file_type)
        
        prompt = f"""
Generate clear, concise documentation for this {language} code:

File: {file_path}
```{language}
{content[:1500]}  # Limit content size
```

Generate:
1. A brief description of what the code does
2. Key functions/classes and their purposes
3. Usage examples if applicable
4. Any important notes or considerations

Format as markdown and keep it concise but informative.
"""
        
        try:
            from ....backends.base import Message
            
            response = await self._llm.generate(
                messages=[Message(role="user", content=prompt)],
                temperature=0.3,
                max_tokens=800
            )
            
            return response.content.strip()
            
        except Exception as e:
            return f"# Documentation\n\nUnable to generate documentation: {e}"
    
    def _build_edit_prompt(self, instruction: str, content: str, context: EditContext) -> str:
        """Build a context-aware prompt for edit suggestions."""
        file_type = context.file_type or "text"
        language = context.language or self._get_language_name(file_type)
        
        prompt = f"""
You are an expert {language} developer. Edit the following code according to the instruction.

Instruction: {instruction}

Current code:
```{language}
{content}
```

Requirements:
1. Follow the instruction precisely
2. Maintain code quality and best practices
3. Preserve existing functionality unless instructed otherwise
4. Use proper {language} syntax and conventions
5. Add comments where helpful

Provide ONLY the complete updated code without explanations.
"""
        
        return prompt
    
    def _extract_code_from_response(self, response: str) -> Optional[str]:
        """Extract code content from AI response."""
        # Look for code blocks
        code_block_pattern = r'```(?:\w+)?\n(.*?)\n```'
        matches = re.findall(code_block_pattern, response, re.DOTALL)
        
        if matches:
            return matches[0].strip()
        
        # If no code blocks, try to extract code-like content
        lines = response.strip().split('\n')
        
        # Skip explanation lines and find code
        code_lines = []
        in_code = False
        
        for line in lines:
            # Skip common explanation patterns
            if any(phrase in line.lower() for phrase in [
                'here is', 'here\'s', 'the updated', 'the modified',
                'explanation:', 'note:', 'this code'
            ]):
                continue
            
            # Look for code-like patterns
            if any(pattern in line for pattern in ['def ', 'class ', 'import ', 'function ', 'var ', 'const ', 'let ']):
                in_code = True
            
            if in_code:
                code_lines.append(line)
        
        if code_lines:
            return '\n'.join(code_lines)
        
        # Fallback: return the whole response if it looks like code
        if len(response.split('\n')) > 3 and not response.startswith('I '):
            return response.strip()
        
        return None
    
    def _parse_review_response(self, response: str) -> List[Dict[str, Any]]:
        """Parse AI review response into structured suggestions."""
        try:
            import json
            
            # Try to extract JSON from response
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                suggestions = json.loads(json_match.group())
                return suggestions
            
        except (json.JSONDecodeError, AttributeError):
            pass
        
        # Fallback: parse text response
        suggestions = []
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                suggestions.append({
                    "type": "suggestion",
                    "line": None,
                    "message": line,
                    "suggestion": "Review manually"
                })
        
        return suggestions[:5]  # Limit to 5 suggestions
    
    def _get_language_name(self, file_extension: str) -> str:
        """Get language name from file extension."""
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.rs': 'rust',
            '.go': 'go',
            '.rb': 'ruby',
            '.php': 'php',
            '.sh': 'bash',
            '.sql': 'sql',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown',
        }
        
        return language_map.get(file_extension.lower(), 'text')
