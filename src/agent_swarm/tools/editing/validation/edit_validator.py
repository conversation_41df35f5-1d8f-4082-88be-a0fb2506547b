"""
Edit validation implementation for the editing system.

Provides comprehensive validation of edit operations for safety and correctness.
"""

import ast
import json
from pathlib import Path
from typing import List, Optional

from ..core.interfaces import IEditValidator
from ..core.models import EditOperation, ValidationResult
from ..utils.safety import validate_file_path, is_binary_file


class EditValidator(IEditValidator):
    """
    Comprehensive edit validation with safety and syntax checking.
    
    Features:
    - Path safety validation
    - Syntax validation for code files
    - Content safety checks
    - File type validation
    - Size and permission checks
    """
    
    def __init__(self, safe_mode: bool = True):
        """
        Initialize the edit validator.
        
        Args:
            safe_mode: Enable strict safety checks
        """
        self.safe_mode = safe_mode
        
        # Syntax validators for different file types
        self.syntax_validators = {
            '.py': self._validate_python_syntax,
            '.json': self._validate_json_syntax,
            '.js': self._validate_javascript_syntax,
            '.ts': self._validate_typescript_syntax,
        }
    
    async def validate_syntax(self, content: str, file_path: Path) -> bool:
        """
        Validate syntax of the content.
        
        Args:
            content: File content to validate
            file_path: Path to the file
            
        Returns:
            True if syntax is valid, False otherwise
        """
        file_ext = file_path.suffix.lower()
        
        if file_ext in self.syntax_validators:
            try:
                return self.syntax_validators[file_ext](content)
            except Exception:
                return False
        
        # For unknown file types, assume valid
        return True
    
    async def validate_safety(self, operation: EditOperation) -> bool:
        """
        Validate if the operation is safe to apply.
        
        Args:
            operation: Edit operation to validate
            
        Returns:
            True if operation is safe, False otherwise
        """
        if self.safe_mode:
            # Strict safety checks
            if not validate_file_path(operation.file_path):
                return False
            
            if operation.file_path.exists() and is_binary_file(operation.file_path):
                return False
        
        # Check file size limits
        if len(operation.content) > 10 * 1024 * 1024:  # 10MB limit
            return False
        
        return True
    
    async def get_validation_errors(self, content: str, file_path: Path) -> List[str]:
        """
        Get detailed validation errors.
        
        Args:
            content: File content to validate
            file_path: Path to the file
            
        Returns:
            List of validation error messages
        """
        errors = []
        
        # Safety checks
        if self.safe_mode:
            if not validate_file_path(file_path):
                errors.append(f"Unsafe file path: {file_path}")
            
            if file_path.exists() and is_binary_file(file_path):
                errors.append(f"Binary file not supported: {file_path}")
        
        # Size checks
        if len(content) > 10 * 1024 * 1024:
            errors.append(f"Content too large: {len(content)} bytes (max: 10MB)")
        
        # Syntax checks
        file_ext = file_path.suffix.lower()
        if file_ext in self.syntax_validators:
            try:
                if not self.syntax_validators[file_ext](content):
                    errors.append(f"Invalid {file_ext[1:]} syntax")
            except Exception as e:
                errors.append(f"Syntax validation failed: {e}")
        
        return errors
    
    async def validate_operation(self, operation: EditOperation) -> ValidationResult:
        """
        Comprehensive validation of an edit operation.
        
        Args:
            operation: Edit operation to validate
            
        Returns:
            ValidationResult with detailed information
        """
        errors = []
        warnings = []
        suggestions = []
        
        # Safety validation
        if not await self.validate_safety(operation):
            errors.append("Operation failed safety validation")
        
        # Syntax validation
        if not await self.validate_syntax(operation.content, operation.file_path):
            errors.append("Content has syntax errors")
        
        # Get detailed errors
        detailed_errors = await self.get_validation_errors(operation.content, operation.file_path)
        errors.extend(detailed_errors)
        
        # File-specific warnings and suggestions
        file_ext = operation.file_path.suffix.lower()
        
        if file_ext == '.py':
            # Python-specific checks
            if 'import *' in operation.content:
                warnings.append("Wildcard imports detected (import *)")
                suggestions.append("Use explicit imports instead of wildcard imports")
            
            if 'eval(' in operation.content or 'exec(' in operation.content:
                warnings.append("Dynamic code execution detected (eval/exec)")
                suggestions.append("Avoid eval() and exec() for security reasons")
        
        elif file_ext == '.js' or file_ext == '.ts':
            # JavaScript/TypeScript checks
            if 'eval(' in operation.content:
                warnings.append("eval() usage detected")
                suggestions.append("Avoid eval() for security and performance reasons")
        
        # General suggestions
        if len(operation.content.splitlines()) > 1000:
            suggestions.append("Large file detected - consider breaking into smaller modules")
        
        if not operation.description:
            suggestions.append("Consider adding a description to document the change")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            suggestions=suggestions,
            metadata={
                "file_type": file_ext,
                "content_size": len(operation.content),
                "line_count": len(operation.content.splitlines())
            }
        )
    
    def _validate_python_syntax(self, content: str) -> bool:
        """Validate Python syntax."""
        try:
            ast.parse(content)
            return True
        except SyntaxError:
            return False
    
    def _validate_json_syntax(self, content: str) -> bool:
        """Validate JSON syntax."""
        try:
            json.loads(content)
            return True
        except json.JSONDecodeError:
            return False
    
    def _validate_javascript_syntax(self, content: str) -> bool:
        """Validate JavaScript syntax (basic check)."""
        # Basic JavaScript syntax validation
        # In a real implementation, you might use a JavaScript parser
        
        # Check for basic syntax issues
        if content.count('{') != content.count('}'):
            return False
        
        if content.count('(') != content.count(')'):
            return False
        
        if content.count('[') != content.count(']'):
            return False
        
        # Check for unterminated strings (basic)
        single_quotes = content.count("'") - content.count("\\'")
        double_quotes = content.count('"') - content.count('\\"')
        
        if single_quotes % 2 != 0 or double_quotes % 2 != 0:
            return False
        
        return True
    
    def _validate_typescript_syntax(self, content: str) -> bool:
        """Validate TypeScript syntax (basic check)."""
        # TypeScript is a superset of JavaScript, so start with JS validation
        return self._validate_javascript_syntax(content)
