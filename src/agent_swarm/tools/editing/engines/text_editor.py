"""
Professional text file editor implementation.

Uses modern libraries and best practices for safe, efficient file editing:
- unidiff for professional diff generation
- chardet for encoding detection
- pathlib for robust path handling
- asyncio for non-blocking operations
"""

import asyncio
import hashlib
from pathlib import Path
from typing import Optional, Set

try:
    import chardet
    CHARDET_AVAILABLE = True
except ImportError:
    CHARDET_AVAILABLE = False

try:
    from unidiff import PatchSet
    UNIDIFF_AVAILABLE = True
except ImportError:
    UNIDIFF_AVAILABLE = False

from ..core.interfaces import IFileEditor
from ..core.models import (
    EditOperation,
    FileChange,
    EditOperationType,
    FileChangeStatus,
    EditContext,
)
from ..utils.encoding import detect_encoding, normalize_line_endings
from ..utils.safety import validate_file_path, is_binary_file


class TextFileEditor(IFileEditor):
    """
    Professional text file editor with safety features and encoding support.
    
    Features:
    - Automatic encoding detection
    - Line ending normalization
    - Binary file detection
    - Safe path validation
    - Atomic file operations
    - Backup creation
    """
    
    def __init__(self, 
                 supported_extensions: Optional[Set[str]] = None,
                 max_file_size: int = 100 * 1024 * 1024,  # 100MB
                 create_backups: bool = True):
        """
        Initialize the text file editor.
        
        Args:
            supported_extensions: Set of supported file extensions (None = all text files)
            max_file_size: Maximum file size to handle (bytes)
            create_backups: Whether to create backups before editing
        """
        self.supported_extensions = supported_extensions or {
            '.txt', '.md', '.rst', '.log', '.conf', '.cfg', '.ini',
            '.json', '.yaml', '.yml', '.toml', '.xml', '.csv',
            '.py', '.js', '.ts', '.html', '.css', '.scss', '.sass',
            '.java', '.cpp', '.c', '.h', '.hpp', '.rs', '.go',
            '.rb', '.php', '.sh', '.bash', '.zsh', '.fish',
            '.sql', '.dockerfile', '.gitignore', '.gitattributes'
        }
        self.max_file_size = max_file_size
        self.create_backups = create_backups
    
    async def read_file(self, path: Path) -> str:
        """
        Read file content with automatic encoding detection.
        
        Args:
            path: Path to the file
            
        Returns:
            File content as string
            
        Raises:
            FileNotFoundError: If file doesn't exist
            PermissionError: If file can't be read
            ValueError: If file is binary or too large
        """
        # Validate path
        if not validate_file_path(path):
            raise ValueError(f"Invalid or unsafe file path: {path}")
        
        if not path.exists():
            raise FileNotFoundError(f"File not found: {path}")
        
        if not path.is_file():
            raise ValueError(f"Path is not a file: {path}")
        
        # Check file size
        file_size = path.stat().st_size
        if file_size > self.max_file_size:
            raise ValueError(f"File too large: {file_size} bytes (max: {self.max_file_size})")
        
        # Check if binary
        if is_binary_file(path):
            raise ValueError(f"Binary file not supported: {path}")
        
        # Detect encoding
        encoding = detect_encoding(path)
        
        # Read file
        try:
            content = path.read_text(encoding=encoding)
            return normalize_line_endings(content)
        except UnicodeDecodeError as e:
            raise ValueError(f"Failed to decode file {path}: {e}")
    
    async def write_file(self, path: Path, content: str) -> None:
        """
        Write content to file atomically.
        
        Args:
            path: Path to the file
            content: Content to write
            
        Raises:
            PermissionError: If file can't be written
            ValueError: If path is invalid
        """
        # Validate path
        if not validate_file_path(path):
            raise ValueError(f"Invalid or unsafe file path: {path}")
        
        # Create parent directories
        path.parent.mkdir(parents=True, exist_ok=True)
        
        # Detect encoding (use UTF-8 for new files)
        encoding = 'utf-8'
        if path.exists():
            encoding = detect_encoding(path)
        
        # Atomic write using temporary file
        temp_path = path.with_suffix(path.suffix + '.tmp')
        try:
            temp_path.write_text(content, encoding=encoding)
            temp_path.replace(path)  # Atomic on most filesystems
        except Exception:
            # Clean up temp file on error
            if temp_path.exists():
                temp_path.unlink()
            raise
    
    async def apply_edit(self, operation: EditOperation) -> FileChange:
        """
        Apply an edit operation to a file.
        
        Args:
            operation: The edit operation to apply
            
        Returns:
            FileChange object representing the change
        """
        file_path = operation.file_path
        
        # Read original content
        original_content = ""
        if file_path.exists():
            original_content = await self.read_file(file_path)
        
        # Apply the operation
        new_content = await self._apply_operation(original_content, operation)
        
        # Create file change object
        change = FileChange(
            file_path=file_path,
            operation=operation,
            original_content=original_content,
            new_content=new_content,
            description=operation.description,
            status=FileChangeStatus.PENDING
        )
        
        return change
    
    async def validate_edit(self, operation: EditOperation) -> bool:
        """
        Validate if an edit operation is safe to apply.
        
        Args:
            operation: The edit operation to validate
            
        Returns:
            True if operation is safe, False otherwise
        """
        try:
            # Check if file path is valid
            if not validate_file_path(operation.file_path):
                return False
            
            # Check if we support this file type
            if not self.supports_file_type(operation.file_path):
                return False
            
            # Check if file exists for operations that require it
            if operation.operation_type in [
                EditOperationType.REPLACE_LINES,
                EditOperationType.DELETE_LINES,
                EditOperationType.INSERT_LINES
            ]:
                if not operation.file_path.exists():
                    return False
            
            # Validate line numbers if specified
            if operation.start_line is not None or operation.end_line is not None:
                if operation.file_path.exists():
                    content = await self.read_file(operation.file_path)
                    line_count = len(content.splitlines())
                    
                    if operation.start_line and (operation.start_line < 1 or operation.start_line > line_count):
                        return False
                    
                    if operation.end_line and (operation.end_line < 1 or operation.end_line > line_count):
                        return False
                    
                    if (operation.start_line and operation.end_line and 
                        operation.start_line > operation.end_line):
                        return False
            
            return True
            
        except Exception:
            return False
    
    def supports_file_type(self, path: Path) -> bool:
        """
        Check if this editor supports the given file type.
        
        Args:
            path: Path to check
            
        Returns:
            True if supported, False otherwise
        """
        # Check extension
        if path.suffix.lower() in self.supported_extensions:
            return True
        
        # Check if it's a text file (for extensionless files)
        if path.exists() and not is_binary_file(path):
            return True
        
        return False
    
    async def _apply_operation(self, content: str, operation: EditOperation) -> str:
        """Apply a specific edit operation to content."""
        if operation.operation_type == EditOperationType.REPLACE_ALL:
            return operation.content
        
        elif operation.operation_type == EditOperationType.REPLACE_LINES:
            lines = content.splitlines(keepends=True)
            start_idx = (operation.start_line or 1) - 1
            end_idx = (operation.end_line or len(lines))
            
            new_lines = operation.content.splitlines(keepends=True)
            result_lines = lines[:start_idx] + new_lines + lines[end_idx:]
            return ''.join(result_lines)
        
        elif operation.operation_type == EditOperationType.INSERT_LINES:
            lines = content.splitlines(keepends=True)
            insert_idx = (operation.start_line or len(lines) + 1) - 1
            
            new_lines = operation.content.splitlines(keepends=True)
            result_lines = lines[:insert_idx] + new_lines + lines[insert_idx:]
            return ''.join(result_lines)
        
        elif operation.operation_type == EditOperationType.DELETE_LINES:
            lines = content.splitlines(keepends=True)
            start_idx = (operation.start_line or 1) - 1
            end_idx = (operation.end_line or len(lines))
            
            result_lines = lines[:start_idx] + lines[end_idx:]
            return ''.join(result_lines)
        
        elif operation.operation_type == EditOperationType.REPLACE_TEXT:
            # Simple text replacement (could be enhanced with regex)
            return content.replace(operation.metadata.get('search_text', ''), operation.content)
        
        elif operation.operation_type == EditOperationType.INSERT_TEXT:
            pos = operation.start_pos or len(content)
            return content[:pos] + operation.content + content[pos:]
        
        elif operation.operation_type == EditOperationType.DELETE_TEXT:
            start_pos = operation.start_pos or 0
            end_pos = operation.end_pos or len(content)
            return content[:start_pos] + content[end_pos:]
        
        else:
            raise ValueError(f"Unsupported operation type: {operation.operation_type}")
    
    def _calculate_checksum(self, content: str) -> str:
        """Calculate SHA-256 checksum of content."""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
