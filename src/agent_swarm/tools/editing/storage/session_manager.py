"""
Session management implementation for the editing system.

Provides organization of related changes into sessions.
"""

import json
import asyncio
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..core.interfaces import IEditSession
from ..core.models import EditSession, FileChange


class SessionManager(IEditSession):
    """
    Session management with persistent storage.
    
    Features:
    - Named sessions for organizing changes
    - Session metadata and descriptions
    - Change association and tracking
    - Session statistics and reporting
    """
    
    def __init__(self, storage_dir: Path, max_changes_per_session: int = 1000):
        """
        Initialize the session manager.
        
        Args:
            storage_dir: Directory for storing session data
            max_changes_per_session: Maximum changes per session
        """
        self.storage_dir = Path(storage_dir)
        self.sessions_file = self.storage_dir / "sessions.json"
        self.max_changes_per_session = max_changes_per_session
        self._sessions_cache: Dict[str, EditSession] = {}
        self._lock = asyncio.Lock()
    
    async def initialize(self) -> None:
        """Initialize storage directory and load existing sessions."""
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        await self._load_sessions()
    
    async def start_session(self, name: str, description: str = "") -> str:
        """
        Start a new editing session.
        
        Args:
            name: Session name
            description: Optional description
            
        Returns:
            Session ID
        """
        async with self._lock:
            session = EditSession(
                name=name,
                description=description,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                status="active"
            )
            
            self._sessions_cache[session.session_id] = session
            await self._save_sessions()
            
            return session.session_id
    
    async def end_session(self, session_id: str) -> None:
        """
        End an editing session.
        
        Args:
            session_id: ID of session to end
        """
        async with self._lock:
            session = self._sessions_cache.get(session_id)
            if session:
                session.status = "completed"
                session.updated_at = datetime.now()
                await self._save_sessions()
    
    async def add_change_to_session(self, session_id: str, change: FileChange) -> None:
        """
        Add a change to a session.
        
        Args:
            session_id: Session ID
            change: FileChange object
        """
        async with self._lock:
            session = self._sessions_cache.get(session_id)
            if not session:
                return
            
            # Check session limits
            if len(session.changes) >= self.max_changes_per_session:
                raise ValueError(f"Session has reached maximum changes limit: {self.max_changes_per_session}")
            
            # Add change ID to session
            if change.change_id not in session.changes:
                session.add_change(change.change_id)
                
                # Update change with session ID
                change.session_id = session_id
                
                await self._save_sessions()
    
    async def get_session_changes(self, session_id: str) -> List[FileChange]:
        """
        Get all changes in a session.
        
        Args:
            session_id: Session ID
            
        Returns:
            List of FileChange objects (empty list if session not found)
        """
        session = self._sessions_cache.get(session_id)
        if not session:
            return []
        
        # Note: This method returns change IDs only
        # The actual FileChange objects would need to be retrieved
        # from the ChangeTracker using these IDs
        return session.changes
    
    async def apply_session_changes(self, session_id: str, confirm: bool = True) -> bool:
        """
        Apply all changes in a session.
        
        Args:
            session_id: Session ID
            confirm: Whether to require confirmation
            
        Returns:
            True if successful, False otherwise
        """
        session = self._sessions_cache.get(session_id)
        if not session:
            return False
        
        # This would need integration with ChangeTracker
        # to actually apply the changes
        # For now, just mark session as applied
        
        async with self._lock:
            session.status = "applied"
            session.updated_at = datetime.now()
            await self._save_sessions()
        
        return True
    
    async def get_session(self, session_id: str) -> Optional[EditSession]:
        """
        Get a session by ID.
        
        Args:
            session_id: Session ID
            
        Returns:
            EditSession object or None
        """
        return self._sessions_cache.get(session_id)
    
    async def list_sessions(self, status: Optional[str] = None) -> List[EditSession]:
        """
        List all sessions, optionally filtered by status.
        
        Args:
            status: Optional status filter
            
        Returns:
            List of EditSession objects
        """
        sessions = list(self._sessions_cache.values())
        
        if status:
            sessions = [s for s in sessions if s.status == status]
        
        # Sort by updated time (newest first)
        sessions.sort(key=lambda s: s.updated_at, reverse=True)
        
        return sessions
    
    async def delete_session(self, session_id: str) -> bool:
        """
        Delete a session.
        
        Args:
            session_id: Session ID
            
        Returns:
            True if successful, False if session not found
        """
        async with self._lock:
            if session_id in self._sessions_cache:
                del self._sessions_cache[session_id]
                await self._save_sessions()
                return True
            return False
    
    async def get_session_statistics(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get statistics for a session.
        
        Args:
            session_id: Session ID
            
        Returns:
            Dictionary with statistics or None
        """
        session = self._sessions_cache.get(session_id)
        if not session:
            return None
        
        duration = session.updated_at - session.created_at
        
        return {
            "session_id": session_id,
            "name": session.name,
            "description": session.description,
            "status": session.status,
            "created_at": session.created_at.isoformat(),
            "updated_at": session.updated_at.isoformat(),
            "duration_seconds": duration.total_seconds(),
            "change_count": session.change_count,
            "changes": session.changes
        }
    
    async def search_sessions(self, 
                            name_contains: Optional[str] = None,
                            status: Optional[str] = None,
                            created_after: Optional[datetime] = None) -> List[EditSession]:
        """
        Search sessions with filters.
        
        Args:
            name_contains: Filter by name content
            status: Filter by status
            created_after: Filter by creation date
            
        Returns:
            List of matching EditSession objects
        """
        sessions = list(self._sessions_cache.values())
        
        if name_contains:
            sessions = [s for s in sessions if name_contains.lower() in s.name.lower()]
        
        if status:
            sessions = [s for s in sessions if s.status == status]
        
        if created_after:
            sessions = [s for s in sessions if s.created_at > created_after]
        
        # Sort by updated time (newest first)
        sessions.sort(key=lambda s: s.updated_at, reverse=True)
        
        return sessions
    
    async def _load_sessions(self) -> None:
        """Load sessions from disk."""
        if not self.sessions_file.exists():
            return
        
        try:
            with open(self.sessions_file, 'r') as f:
                data = json.load(f)
            
            for session_data in data.get("sessions", []):
                try:
                    # Convert timestamp strings back to datetime
                    for field in ["created_at", "updated_at"]:
                        if isinstance(session_data.get(field), str):
                            session_data[field] = datetime.fromisoformat(session_data[field])
                    
                    session = EditSession(**session_data)
                    self._sessions_cache[session.session_id] = session
                    
                except Exception as e:
                    # Skip invalid session data
                    print(f"Warning: Failed to load session: {e}")
                    continue
                    
        except Exception as e:
            print(f"Warning: Failed to load sessions file: {e}")
    
    async def _save_sessions(self) -> None:
        """Save sessions to disk."""
        try:
            # Convert sessions to serializable format
            sessions_data = []
            for session in self._sessions_cache.values():
                session_dict = session.dict()
                
                # Convert datetime to ISO string
                for field in ["created_at", "updated_at"]:
                    if isinstance(session_dict.get(field), datetime):
                        session_dict[field] = session_dict[field].isoformat()
                
                sessions_data.append(session_dict)
            
            data = {
                "version": "1.0",
                "created": datetime.now().isoformat(),
                "sessions": sessions_data
            }
            
            # Write atomically
            temp_file = self.sessions_file.with_suffix('.tmp')
            with open(temp_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            temp_file.replace(self.sessions_file)
            
        except Exception as e:
            print(f"Warning: Failed to save sessions: {e}")
