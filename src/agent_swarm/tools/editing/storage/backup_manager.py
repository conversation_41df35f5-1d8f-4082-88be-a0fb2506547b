"""
Backup management implementation for the editing system.

Provides automatic backup creation and restoration capabilities.
"""

import shutil
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..core.interfaces import IBackupManager
from ..core.models import BackupInfo


class BackupManager(IBackupManager):
    """
    Backup management with automatic cleanup and restoration.
    
    Features:
    - Automatic backup creation before edits
    - Timestamped backup files
    - Backup metadata tracking
    - Automatic cleanup of old backups
    - Integrity verification with checksums
    """
    
    def __init__(self, backup_dir: Path, retention_days: int = 30):
        """
        Initialize the backup manager.
        
        Args:
            backup_dir: Directory for storing backups
            retention_days: Days to keep backups
        """
        self.backup_dir = Path(backup_dir)
        self.retention_days = retention_days
        self._backups: Dict[str, BackupInfo] = {}
    
    async def initialize(self) -> None:
        """Initialize backup directory."""
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        await self._load_backup_metadata()
    
    async def create_backup(self, path: Path) -> str:
        """
        Create a backup of the file and return backup ID.
        
        Args:
            path: Path to file to backup
            
        Returns:
            Backup ID
        """
        if not path.exists() or not path.is_file():
            raise ValueError(f"File not found or not a file: {path}")
        
        # Generate backup filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        safe_name = self._sanitize_filename(path.name)
        backup_filename = f"{safe_name}_{timestamp}.backup"
        backup_path = self.backup_dir / backup_filename
        
        # Ensure unique backup path
        counter = 1
        while backup_path.exists():
            backup_filename = f"{safe_name}_{timestamp}_{counter}.backup"
            backup_path = self.backup_dir / backup_filename
            counter += 1
        
        # Copy file to backup location
        shutil.copy2(path, backup_path)
        
        # Calculate checksum
        checksum = self._calculate_checksum(backup_path)
        
        # Create backup info
        backup_info = BackupInfo(
            original_path=path,
            backup_path=backup_path,
            created_at=datetime.now(),
            file_size=backup_path.stat().st_size,
            checksum=checksum
        )
        
        # Store backup info
        self._backups[backup_info.backup_id] = backup_info
        await self._save_backup_metadata()
        
        return backup_info.backup_id
    
    async def restore_backup(self, backup_id: str) -> bool:
        """
        Restore a file from backup.
        
        Args:
            backup_id: Backup ID to restore
            
        Returns:
            True if successful, False otherwise
        """
        backup_info = self._backups.get(backup_id)
        if not backup_info:
            return False
        
        if not backup_info.backup_path.exists():
            return False
        
        try:
            # Verify backup integrity
            if not self._verify_backup_integrity(backup_info):
                return False
            
            # Restore file
            backup_info.original_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(backup_info.backup_path, backup_info.original_path)
            
            return True
            
        except Exception:
            return False
    
    async def list_backups(self, path: Optional[Path] = None) -> List[Dict[str, Any]]:
        """
        List available backups.
        
        Args:
            path: Optional path to filter backups
            
        Returns:
            List of backup information dictionaries
        """
        backups = []
        
        for backup_info in self._backups.values():
            if path and backup_info.original_path != path:
                continue
            
            backup_dict = {
                "backup_id": backup_info.backup_id,
                "original_path": str(backup_info.original_path),
                "backup_path": str(backup_info.backup_path),
                "created_at": backup_info.created_at.isoformat(),
                "file_size": backup_info.file_size,
                "checksum": backup_info.checksum,
                "exists": backup_info.backup_path.exists()
            }
            backups.append(backup_dict)
        
        # Sort by creation time (newest first)
        backups.sort(key=lambda b: b["created_at"], reverse=True)
        
        return backups
    
    async def cleanup_old_backups(self, days: int = None) -> int:
        """
        Clean up backups older than specified days.
        
        Args:
            days: Number of days to keep (uses default if None)
            
        Returns:
            Number of backups removed
        """
        if days is None:
            days = self.retention_days
        
        cutoff = datetime.now().timestamp() - (days * 24 * 60 * 60)
        removed_count = 0
        
        # Find old backups
        old_backup_ids = []
        for backup_id, backup_info in self._backups.items():
            if backup_info.created_at.timestamp() < cutoff:
                old_backup_ids.append(backup_id)
        
        # Remove old backups
        for backup_id in old_backup_ids:
            backup_info = self._backups[backup_id]
            
            # Remove backup file
            if backup_info.backup_path.exists():
                try:
                    backup_info.backup_path.unlink()
                    removed_count += 1
                except Exception:
                    pass
            
            # Remove from tracking
            del self._backups[backup_id]
        
        # Save updated metadata
        await self._save_backup_metadata()
        
        return removed_count
    
    async def get_backup_info(self, backup_id: str) -> Optional[BackupInfo]:
        """
        Get backup information by ID.
        
        Args:
            backup_id: Backup ID
            
        Returns:
            BackupInfo object or None
        """
        return self._backups.get(backup_id)
    
    async def verify_all_backups(self) -> Dict[str, bool]:
        """
        Verify integrity of all backups.
        
        Returns:
            Dictionary mapping backup IDs to verification results
        """
        results = {}
        
        for backup_id, backup_info in self._backups.items():
            results[backup_id] = self._verify_backup_integrity(backup_info)
        
        return results
    
    def _verify_backup_integrity(self, backup_info: BackupInfo) -> bool:
        """Verify backup file integrity using checksum."""
        if not backup_info.backup_path.exists():
            return False
        
        try:
            current_checksum = self._calculate_checksum(backup_info.backup_path)
            return current_checksum == backup_info.checksum
        except Exception:
            return False
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """Calculate SHA-256 checksum of a file."""
        sha256_hash = hashlib.sha256()
        
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        
        return sha256_hash.hexdigest()
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe storage."""
        import re
        
        # Replace unsafe characters
        safe_name = re.sub(r'[^\w\-_\.]', '_', filename)
        
        # Limit length
        if len(safe_name) > 100:
            name, ext = safe_name.rsplit('.', 1) if '.' in safe_name else (safe_name, '')
            safe_name = name[:95] + ('.' + ext if ext else '')
        
        return safe_name
    
    async def _load_backup_metadata(self) -> None:
        """Load backup metadata from disk."""
        metadata_file = self.backup_dir / "backup_metadata.json"
        
        if not metadata_file.exists():
            return
        
        try:
            import json
            
            with open(metadata_file, 'r') as f:
                data = json.load(f)
            
            for backup_data in data.get("backups", []):
                try:
                    # Convert paths and timestamps
                    backup_data["original_path"] = Path(backup_data["original_path"])
                    backup_data["backup_path"] = Path(backup_data["backup_path"])
                    backup_data["created_at"] = datetime.fromisoformat(backup_data["created_at"])
                    
                    backup_info = BackupInfo(**backup_data)
                    self._backups[backup_info.backup_id] = backup_info
                    
                except Exception as e:
                    print(f"Warning: Failed to load backup metadata: {e}")
                    continue
                    
        except Exception as e:
            print(f"Warning: Failed to load backup metadata file: {e}")
    
    async def _save_backup_metadata(self) -> None:
        """Save backup metadata to disk."""
        metadata_file = self.backup_dir / "backup_metadata.json"
        
        try:
            import json
            
            # Convert to serializable format
            backups_data = []
            for backup_info in self._backups.values():
                backup_dict = backup_info.dict()
                backup_dict["original_path"] = str(backup_dict["original_path"])
                backup_dict["backup_path"] = str(backup_dict["backup_path"])
                backup_dict["created_at"] = backup_dict["created_at"].isoformat()
                backups_data.append(backup_dict)
            
            data = {
                "version": "1.0",
                "created": datetime.now().isoformat(),
                "backups": backups_data
            }
            
            # Write atomically
            temp_file = metadata_file.with_suffix('.tmp')
            with open(temp_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            temp_file.replace(metadata_file)
            
        except Exception as e:
            print(f"Warning: Failed to save backup metadata: {e}")
