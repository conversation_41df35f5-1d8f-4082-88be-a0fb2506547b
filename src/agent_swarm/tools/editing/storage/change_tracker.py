"""
Change tracking implementation for the editing system.

Provides persistent storage and retrieval of file changes with metadata.
"""

import json
import asyncio
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..core.interfaces import IChangeTracker
from ..core.models import <PERSON>Change, FileChangeStatus


class ChangeTracker(IChangeTracker):
    """
    Persistent change tracking with JSON storage.
    
    Features:
    - Unique change IDs
    - Metadata storage
    - Session association
    - Status tracking
    - Search and filtering
    """
    
    def __init__(self, storage_dir: Path):
        """
        Initialize the change tracker.
        
        Args:
            storage_dir: Directory for storing change data
        """
        self.storage_dir = Path(storage_dir)
        self.changes_file = self.storage_dir / "changes.json"
        self._changes_cache: Dict[str, FileChange] = {}
        self._lock = asyncio.Lock()
    
    async def initialize(self) -> None:
        """Initialize storage directory and load existing changes."""
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        await self._load_changes()
    
    async def track_change(self, change: FileChange) -> str:
        """
        Track a file change and return its unique ID.
        
        Args:
            change: The file change to track
            
        Returns:
            Unique change ID
        """
        async with self._lock:
            # Ensure change has an ID
            if not change.change_id:
                change.change_id = self._generate_change_id()
            
            # Store in cache
            self._changes_cache[change.change_id] = change
            
            # Persist to disk
            await self._save_changes()
            
            return change.change_id
    
    async def get_change(self, change_id: str) -> Optional[FileChange]:
        """
        Retrieve a change by ID.
        
        Args:
            change_id: Unique change identifier
            
        Returns:
            FileChange object or None if not found
        """
        return self._changes_cache.get(change_id)
    
    async def list_changes(self, session_id: Optional[str] = None) -> List[FileChange]:
        """
        List all changes, optionally filtered by session.
        
        Args:
            session_id: Optional session ID to filter by
            
        Returns:
            List of FileChange objects
        """
        changes = list(self._changes_cache.values())
        
        if session_id:
            changes = [c for c in changes if c.session_id == session_id]
        
        # Sort by timestamp (newest first)
        changes.sort(key=lambda c: c.timestamp, reverse=True)
        
        return changes
    
    async def revert_change(self, change_id: str) -> bool:
        """
        Revert a specific change.
        
        Args:
            change_id: ID of change to revert
            
        Returns:
            True if successful, False otherwise
        """
        change = await self.get_change(change_id)
        if not change:
            return False
        
        try:
            # Write original content back to file
            change.file_path.write_text(change.original_content)
            
            # Update change status
            change.status = FileChangeStatus.REVERTED
            
            # Save updated change
            async with self._lock:
                await self._save_changes()
            
            return True
            
        except Exception:
            return False
    
    async def update_change_status(self, change_id: str, status: FileChangeStatus) -> bool:
        """
        Update the status of a change.
        
        Args:
            change_id: ID of change to update
            status: New status
            
        Returns:
            True if successful, False otherwise
        """
        change = await self.get_change(change_id)
        if not change:
            return False
        
        change.status = status
        
        async with self._lock:
            await self._save_changes()
        
        return True
    
    async def search_changes(self, 
                           file_path: Optional[Path] = None,
                           status: Optional[FileChangeStatus] = None,
                           session_id: Optional[str] = None,
                           description_contains: Optional[str] = None) -> List[FileChange]:
        """
        Search changes with various filters.
        
        Args:
            file_path: Filter by file path
            status: Filter by status
            session_id: Filter by session
            description_contains: Filter by description content
            
        Returns:
            List of matching FileChange objects
        """
        changes = list(self._changes_cache.values())
        
        if file_path:
            changes = [c for c in changes if c.file_path == file_path]
        
        if status:
            changes = [c for c in changes if c.status == status]
        
        if session_id:
            changes = [c for c in changes if c.session_id == session_id]
        
        if description_contains:
            changes = [c for c in changes if description_contains.lower() in c.description.lower()]
        
        # Sort by timestamp (newest first)
        changes.sort(key=lambda c: c.timestamp, reverse=True)
        
        return changes
    
    async def get_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about tracked changes.
        
        Returns:
            Dictionary with statistics
        """
        changes = list(self._changes_cache.values())
        
        stats = {
            "total_changes": len(changes),
            "by_status": {},
            "by_file_type": {},
            "recent_changes": 0,
            "total_files_modified": len(set(c.file_path for c in changes))
        }
        
        # Count by status
        for status in FileChangeStatus:
            count = len([c for c in changes if c.status == status])
            stats["by_status"][status.value] = count
        
        # Count by file type
        for change in changes:
            ext = change.file_path.suffix.lower()
            if not ext:
                ext = "no_extension"
            stats["by_file_type"][ext] = stats["by_file_type"].get(ext, 0) + 1
        
        # Count recent changes (last 24 hours)
        recent_cutoff = datetime.now().timestamp() - (24 * 60 * 60)
        stats["recent_changes"] = len([
            c for c in changes 
            if c.timestamp.timestamp() > recent_cutoff
        ])
        
        return stats
    
    async def cleanup_old_changes(self, days: int = 30) -> int:
        """
        Clean up changes older than specified days.
        
        Args:
            days: Number of days to keep
            
        Returns:
            Number of changes removed
        """
        cutoff = datetime.now().timestamp() - (days * 24 * 60 * 60)
        
        async with self._lock:
            old_changes = [
                change_id for change_id, change in self._changes_cache.items()
                if change.timestamp.timestamp() < cutoff
            ]
            
            for change_id in old_changes:
                del self._changes_cache[change_id]
            
            await self._save_changes()
            
            return len(old_changes)
    
    def _generate_change_id(self) -> str:
        """Generate a unique 8-character change ID."""
        import hashlib
        import time
        
        # Use timestamp + random component for uniqueness
        content = f"{time.time()}{len(self._changes_cache)}"
        return hashlib.md5(content.encode()).hexdigest()[:8]
    
    async def _load_changes(self) -> None:
        """Load changes from disk."""
        if not self.changes_file.exists():
            return
        
        try:
            with open(self.changes_file, 'r') as f:
                data = json.load(f)
            
            for change_data in data.get("changes", []):
                try:
                    # Convert timestamp string back to datetime
                    if isinstance(change_data.get("timestamp"), str):
                        change_data["timestamp"] = datetime.fromisoformat(change_data["timestamp"])
                    
                    # Convert file_path string back to Path
                    if isinstance(change_data.get("file_path"), str):
                        change_data["file_path"] = Path(change_data["file_path"])
                    
                    change = FileChange(**change_data)
                    self._changes_cache[change.change_id] = change
                    
                except Exception as e:
                    # Skip invalid change data
                    print(f"Warning: Failed to load change: {e}")
                    continue
                    
        except Exception as e:
            print(f"Warning: Failed to load changes file: {e}")
    
    async def _save_changes(self) -> None:
        """Save changes to disk."""
        try:
            # Convert changes to serializable format
            changes_data = []
            for change in self._changes_cache.values():
                change_dict = change.dict()
                
                # Convert Path to string
                change_dict["file_path"] = str(change_dict["file_path"])
                
                # Convert datetime to ISO string
                if isinstance(change_dict.get("timestamp"), datetime):
                    change_dict["timestamp"] = change_dict["timestamp"].isoformat()
                
                changes_data.append(change_dict)
            
            data = {
                "version": "1.0",
                "created": datetime.now().isoformat(),
                "changes": changes_data
            }
            
            # Write atomically
            temp_file = self.changes_file.with_suffix('.tmp')
            with open(temp_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            temp_file.replace(self.changes_file)
            
        except Exception as e:
            print(f"Warning: Failed to save changes: {e}")
