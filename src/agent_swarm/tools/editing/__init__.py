"""
Professional File Editing System for Agent Swarm

A comprehensive, extensible file editing framework with:
- Modern diff algorithms and visualization
- Plugin-based architecture for extensibility
- Comprehensive change tracking and history
- AI-powered editing capabilities
- Production-ready safety features
- Full test coverage

Architecture:
- Core: Abstract interfaces and base classes
- Engines: Specific editing implementations (text, code, etc.)
- Plugins: Extensible functionality (formatters, linters, etc.)
- Storage: Change persistence and history management
- UI: Rich terminal interfaces and visualizations
"""

from .core.interfaces import (
    IFileEditor,
    IChangeTracker,
    IDiffEngine,
    IEditSession,
    IEditPlugin,
)

from .core.models import (
    FileChange,
    EditSession,
    DiffResult,
    EditContext,
    EditOperation,
)

from .engines.text_editor import TextFileEditor
from .engines.code_editor import CodeFileEditor
from .engines.diff_engine import UnifiedDiffEngine

from .storage.session_manager import SessionManager
from .storage.change_repository import ChangeRepository

from .ui.rich_display import RichDiffDisplay
from .ui.interactive_editor import InteractiveEditor

from .plugins.formatters import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>er,
    <PERSON>ust<PERSON><PERSON>t<PERSON><PERSON>atter,
)

from .plugins.linters import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>int<PERSON>,
)

from .plugins.ai_assistant import AIEditingAssistant

from .factory import EditingSystemFactory

__all__ = [
    # Core interfaces
    "IFileEditor",
    "IChangeTracker", 
    "IDiffEngine",
    "IEditSession",
    "IEditPlugin",
    
    # Models
    "FileChange",
    "EditSession",
    "DiffResult",
    "EditContext",
    "EditOperation",
    
    # Engines
    "TextFileEditor",
    "CodeFileEditor",
    "UnifiedDiffEngine",
    
    # Storage
    "SessionManager",
    "ChangeRepository",
    
    # UI
    "RichDiffDisplay",
    "InteractiveEditor",
    
    # Plugins
    "BlackFormatter",
    "PrettierFormatter", 
    "RustFmtFormatter",
    "FlakeEightLinter",
    "ESLintLinter",
    "ClippyLinter",
    "AIEditingAssistant",
    
    # Factory
    "EditingSystemFactory",
]

# Version info
__version__ = "1.0.0"
__author__ = "Agent Swarm Team"
__description__ = "Professional file editing system with AI integration"
