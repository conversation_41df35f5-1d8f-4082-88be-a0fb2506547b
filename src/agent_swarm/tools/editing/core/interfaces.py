"""
Core interfaces for the file editing system.

Defines the contracts that all editing components must implement,
ensuring extensibility and testability through dependency injection.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Protocol, Union
from pathlib import Path
from datetime import datetime

from .models import (
    FileChange,
    EditSession,
    DiffResult,
    EditContext,
    EditOperation,
    ValidationResult,
    BackupInfo,
)


class IFileEditor(ABC):
    """Interface for file editing operations."""

    @abstractmethod
    async def read_file(self, path: Path) -> str:
        """Read file content."""
        pass

    @abstractmethod
    async def write_file(self, path: Path, content: str) -> None:
        """Write content to file."""
        pass

    @abstractmethod
    async def apply_edit(self, operation: EditOperation) -> FileChange:
        """Apply an edit operation and return the change."""
        pass

    @abstractmethod
    async def validate_edit(self, operation: EditOperation) -> bool:
        """Validate if an edit operation is safe to apply."""
        pass

    @abstractmethod
    def supports_file_type(self, path: Path) -> bool:
        """Check if this editor supports the given file type."""
        pass


class IChangeTracker(ABC):
    """Interface for tracking file changes."""

    @abstractmethod
    async def track_change(self, change: FileChange) -> str:
        """Track a change and return its unique ID."""
        pass

    @abstractmethod
    async def get_change(self, change_id: str) -> Optional[FileChange]:
        """Retrieve a change by ID."""
        pass

    @abstractmethod
    async def list_changes(self, session_id: Optional[str] = None) -> List[FileChange]:
        """List all changes, optionally filtered by session."""
        pass

    @abstractmethod
    async def revert_change(self, change_id: str) -> bool:
        """Revert a specific change."""
        pass


class IDiffEngine(ABC):
    """Interface for generating and displaying diffs."""

    @abstractmethod
    def generate_diff(self, original: str, modified: str, context_lines: int = 3) -> DiffResult:
        """Generate a diff between two text contents."""
        pass

    @abstractmethod
    def apply_diff(self, original: str, diff: str) -> str:
        """Apply a diff to original content."""
        pass

    @abstractmethod
    def parse_diff(self, diff_text: str) -> DiffResult:
        """Parse a diff string into a structured result."""
        pass


class IEditSession(ABC):
    """Interface for managing editing sessions."""

    @abstractmethod
    async def start_session(self, name: str, description: str = "") -> str:
        """Start a new editing session."""
        pass

    @abstractmethod
    async def end_session(self, session_id: str) -> None:
        """End an editing session."""
        pass

    @abstractmethod
    async def add_change_to_session(self, session_id: str, change: FileChange) -> None:
        """Add a change to a session."""
        pass

    @abstractmethod
    async def get_session_changes(self, session_id: str) -> List[FileChange]:
        """Get all changes in a session."""
        pass

    @abstractmethod
    async def apply_session_changes(self, session_id: str, confirm: bool = True) -> bool:
        """Apply all changes in a session."""
        pass


class IEditPlugin(ABC):
    """Interface for editing plugins (formatters, linters, etc.)."""

    @abstractmethod
    def get_name(self) -> str:
        """Get plugin name."""
        pass

    @abstractmethod
    def get_version(self) -> str:
        """Get plugin version."""
        pass

    @abstractmethod
    def supports_file_type(self, path: Path) -> bool:
        """Check if plugin supports the file type."""
        pass

    @abstractmethod
    async def process_file(self, path: Path, content: str, context: EditContext) -> str:
        """Process file content and return modified content."""
        pass


class IFormatter(IEditPlugin):
    """Interface for code formatters."""

    @abstractmethod
    async def format_code(self, content: str, file_path: Path) -> str:
        """Format code content."""
        pass


class ILinter(IEditPlugin):
    """Interface for code linters."""

    @abstractmethod
    async def lint_code(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Lint code and return issues."""
        pass


class IBackupManager(ABC):
    """Interface for managing file backups."""

    @abstractmethod
    async def create_backup(self, path: Path) -> str:
        """Create a backup of the file and return backup ID."""
        pass

    @abstractmethod
    async def restore_backup(self, backup_id: str) -> bool:
        """Restore a file from backup."""
        pass

    @abstractmethod
    async def list_backups(self, path: Optional[Path] = None) -> List[Dict[str, Any]]:
        """List available backups."""
        pass

    @abstractmethod
    async def cleanup_old_backups(self, days: int = 30) -> int:
        """Clean up backups older than specified days."""
        pass


class IFileWatcher(ABC):
    """Interface for watching file changes."""

    @abstractmethod
    async def start_watching(self, paths: List[Path]) -> None:
        """Start watching specified paths."""
        pass

    @abstractmethod
    async def stop_watching(self) -> None:
        """Stop watching files."""
        pass

    @abstractmethod
    def add_change_handler(self, handler: callable) -> None:
        """Add a handler for file change events."""
        pass


class IEditValidator(ABC):
    """Interface for validating edits before applying."""

    @abstractmethod
    async def validate_syntax(self, content: str, file_path: Path) -> bool:
        """Validate syntax of the content."""
        pass

    @abstractmethod
    async def validate_safety(self, operation: EditOperation) -> bool:
        """Validate if the operation is safe to apply."""
        pass

    @abstractmethod
    async def get_validation_errors(self, content: str, file_path: Path) -> List[str]:
        """Get detailed validation errors."""
        pass


class IAIAssistant(ABC):
    """Interface for AI-powered editing assistance."""

    @abstractmethod
    async def suggest_edit(self, instruction: str, content: str, context: EditContext) -> str:
        """Generate edit suggestion based on instruction."""
        pass

    @abstractmethod
    async def explain_change(self, change: FileChange) -> str:
        """Explain what a change does."""
        pass

    @abstractmethod
    async def review_code(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """Review code and provide suggestions."""
        pass


# Protocol for dependency injection
class EditingSystemProtocol(Protocol):
    """Protocol defining the complete editing system interface."""

    file_editor: IFileEditor
    change_tracker: IChangeTracker
    diff_engine: IDiffEngine
    session_manager: IEditSession
    backup_manager: IBackupManager
    validator: IEditValidator
    ai_assistant: Optional[IAIAssistant]
    plugins: List[IEditPlugin]
