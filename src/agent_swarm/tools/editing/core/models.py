"""
Core data models for the file editing system.

Defines the fundamental data structures used throughout the editing system,
using Pydantic for validation and serialization.
"""

from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

from pydantic import BaseModel, Field, validator


class EditOperationType(str, Enum):
    """Types of edit operations."""
    REPLACE_ALL = "replace_all"
    REPLACE_LINES = "replace_lines"
    INSERT_LINES = "insert_lines"
    DELETE_LINES = "delete_lines"
    REPLACE_TEXT = "replace_text"
    INSERT_TEXT = "insert_text"
    DELETE_TEXT = "delete_text"


class FileChangeStatus(str, Enum):
    """Status of a file change."""
    PENDING = "pending"
    APPLIED = "applied"
    REVERTED = "reverted"
    FAILED = "failed"


class DiffLineType(str, Enum):
    """Types of diff lines."""
    CONTEXT = "context"
    ADDED = "added"
    REMOVED = "removed"
    HEADER = "header"
    HUNK_HEADER = "hunk_header"


class EditOperation(BaseModel):
    """Represents a single edit operation."""
    
    operation_type: EditOperationType
    file_path: Path
    content: str
    start_line: Optional[int] = None
    end_line: Optional[int] = None
    start_pos: Optional[int] = None
    end_pos: Optional[int] = None
    description: str = ""
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        arbitrary_types_allowed = True
    
    @validator('file_path')
    def validate_file_path(cls, v):
        return Path(v) if not isinstance(v, Path) else v


class DiffLine(BaseModel):
    """Represents a single line in a diff."""
    
    line_type: DiffLineType
    content: str
    old_line_number: Optional[int] = None
    new_line_number: Optional[int] = None


class DiffHunk(BaseModel):
    """Represents a hunk (section) of a diff."""
    
    old_start: int
    old_count: int
    new_start: int
    new_count: int
    lines: List[DiffLine]
    header: str = ""


class DiffResult(BaseModel):
    """Represents the result of a diff operation."""
    
    old_file: str
    new_file: str
    hunks: List[DiffHunk]
    stats: Dict[str, int] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    @property
    def has_changes(self) -> bool:
        """Check if there are any changes."""
        return len(self.hunks) > 0
    
    @property
    def lines_added(self) -> int:
        """Count of lines added."""
        return sum(1 for hunk in self.hunks for line in hunk.lines if line.line_type == DiffLineType.ADDED)
    
    @property
    def lines_removed(self) -> int:
        """Count of lines removed."""
        return sum(1 for hunk in self.hunks for line in hunk.lines if line.line_type == DiffLineType.REMOVED)


class FileChange(BaseModel):
    """Represents a change to a file."""
    
    change_id: str = Field(default_factory=lambda: str(uuid4())[:8])
    file_path: Path
    operation: EditOperation
    original_content: str
    new_content: str
    diff_result: Optional[DiffResult] = None
    status: FileChangeStatus = FileChangeStatus.PENDING
    timestamp: datetime = Field(default_factory=datetime.now)
    description: str = ""
    author: str = "agent-swarm"
    session_id: Optional[str] = None
    backup_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        arbitrary_types_allowed = True
    
    @validator('file_path')
    def validate_file_path(cls, v):
        return Path(v) if not isinstance(v, Path) else v
    
    @property
    def has_changes(self) -> bool:
        """Check if there are actual changes."""
        return self.original_content != self.new_content
    
    @property
    def file_size_change(self) -> int:
        """Calculate the change in file size."""
        return len(self.new_content) - len(self.original_content)


class EditContext(BaseModel):
    """Context information for edit operations."""
    
    project_root: Optional[Path] = None
    file_type: Optional[str] = None
    language: Optional[str] = None
    encoding: str = "utf-8"
    line_ending: str = "\n"
    tab_size: int = 4
    use_spaces: bool = True
    ai_provider: Optional[str] = None
    user_preferences: Dict[str, Any] = Field(default_factory=dict)
    environment: Dict[str, str] = Field(default_factory=dict)
    
    class Config:
        arbitrary_types_allowed = True
    
    @validator('project_root')
    def validate_project_root(cls, v):
        return Path(v) if v and not isinstance(v, Path) else v


class EditSession(BaseModel):
    """Represents an editing session."""
    
    session_id: str = Field(default_factory=lambda: str(uuid4()))
    name: str
    description: str = ""
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    status: str = "active"
    changes: List[str] = Field(default_factory=list)  # List of change IDs
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    @property
    def change_count(self) -> int:
        """Number of changes in this session."""
        return len(self.changes)
    
    def add_change(self, change_id: str) -> None:
        """Add a change to this session."""
        if change_id not in self.changes:
            self.changes.append(change_id)
            self.updated_at = datetime.now()


class BackupInfo(BaseModel):
    """Information about a file backup."""
    
    backup_id: str = Field(default_factory=lambda: str(uuid4()))
    original_path: Path
    backup_path: Path
    created_at: datetime = Field(default_factory=datetime.now)
    file_size: int
    checksum: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        arbitrary_types_allowed = True
    
    @validator('original_path', 'backup_path')
    def validate_paths(cls, v):
        return Path(v) if not isinstance(v, Path) else v


class ValidationResult(BaseModel):
    """Result of a validation operation."""
    
    is_valid: bool
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    suggestions: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    @property
    def has_errors(self) -> bool:
        """Check if there are validation errors."""
        return len(self.errors) > 0
    
    @property
    def has_warnings(self) -> bool:
        """Check if there are validation warnings."""
        return len(self.warnings) > 0


class PluginInfo(BaseModel):
    """Information about an editing plugin."""
    
    name: str
    version: str
    description: str = ""
    author: str = ""
    supported_file_types: List[str] = Field(default_factory=list)
    dependencies: List[str] = Field(default_factory=list)
    configuration: Dict[str, Any] = Field(default_factory=dict)
    enabled: bool = True
    
    @property
    def supports_file_type(self, file_extension: str) -> bool:
        """Check if plugin supports a file type."""
        return file_extension.lower() in [ext.lower() for ext in self.supported_file_types]
