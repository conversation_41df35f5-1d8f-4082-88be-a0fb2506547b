"""
Factory for creating and configuring the file editing system.

Provides a clean interface for setting up the editing system with
proper dependency injection and configuration.
"""

from pathlib import Path
from typing import Dict, List, Optional, Type

from .core.interfaces import (
    IFileEditor,
    IDiffEngine,
    IChangeTracker,
    IEditSession,
    IBackupManager,
    IEditValidator,
    IAIAssistant,
    IEditPlugin,
)

from .engines.text_editor import TextFileEditor
from .engines.diff_engine import UnifiedDiffEngine
from .storage.change_tracker import ChangeTracker
from .storage.session_manager import SessionManager
from .storage.backup_manager import BackupManager
from .validation.edit_validator import EditValidator
from .ai.ai_assistant import AIEditingAssistant

from .plugins.formatters import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ormatter,
    RustFmtFormatter,
)
from .plugins.linters import (
    <PERSON>lake<PERSON><PERSON><PERSON>inter,
    ESLintLinter,
    ClippyLinter,
)


class EditingSystemConfig:
    """Configuration for the editing system."""
    
    def __init__(self):
        # File editor settings
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        self.create_backups = True
        self.supported_extensions = None  # None = all text files
        
        # Diff engine settings
        self.context_lines = 3
        self.use_unidiff = True
        
        # Storage settings
        self.storage_dir = Path.home() / '.agent-swarm' / 'editing'
        self.backup_retention_days = 30
        self.max_changes_per_session = 1000
        
        # Safety settings
        self.validate_before_apply = True
        self.require_confirmation = True
        self.safe_mode = True
        
        # AI settings
        self.ai_provider = None
        self.ai_model = None
        self.ai_enabled = False
        
        # Plugin settings
        self.enabled_plugins = []
        self.plugin_config = {}


class EditingSystem:
    """Complete file editing system with all components."""
    
    def __init__(self,
                 file_editor: IFileEditor,
                 diff_engine: IDiffEngine,
                 change_tracker: IChangeTracker,
                 session_manager: IEditSession,
                 backup_manager: IBackupManager,
                 validator: IEditValidator,
                 ai_assistant: Optional[IAIAssistant] = None,
                 plugins: Optional[List[IEditPlugin]] = None):
        
        self.file_editor = file_editor
        self.diff_engine = diff_engine
        self.change_tracker = change_tracker
        self.session_manager = session_manager
        self.backup_manager = backup_manager
        self.validator = validator
        self.ai_assistant = ai_assistant
        self.plugins = plugins or []
    
    async def initialize(self) -> None:
        """Initialize all components."""
        # Initialize storage directories
        await self.change_tracker.initialize()
        await self.session_manager.initialize()
        await self.backup_manager.initialize()
        
        # Initialize AI assistant if available
        if self.ai_assistant:
            await self.ai_assistant.initialize()
        
        # Initialize plugins
        for plugin in self.plugins:
            if hasattr(plugin, 'initialize'):
                await plugin.initialize()
    
    async def shutdown(self) -> None:
        """Shutdown all components."""
        # Shutdown plugins
        for plugin in self.plugins:
            if hasattr(plugin, 'shutdown'):
                await plugin.shutdown()
        
        # Shutdown AI assistant
        if self.ai_assistant and hasattr(self.ai_assistant, 'shutdown'):
            await self.ai_assistant.shutdown()
        
        # Shutdown storage components
        if hasattr(self.backup_manager, 'shutdown'):
            await self.backup_manager.shutdown()
        if hasattr(self.session_manager, 'shutdown'):
            await self.session_manager.shutdown()
        if hasattr(self.change_tracker, 'shutdown'):
            await self.change_tracker.shutdown()


class EditingSystemFactory:
    """Factory for creating configured editing systems."""
    
    @staticmethod
    def create_default_system(config: Optional[EditingSystemConfig] = None) -> EditingSystem:
        """Create a default editing system with standard components."""
        if config is None:
            config = EditingSystemConfig()
        
        # Create core components
        file_editor = TextFileEditor(
            supported_extensions=config.supported_extensions,
            max_file_size=config.max_file_size,
            create_backups=config.create_backups
        )
        
        diff_engine = UnifiedDiffEngine(
            context_lines=config.context_lines
        )
        
        change_tracker = ChangeTracker(
            storage_dir=config.storage_dir / 'changes'
        )
        
        session_manager = SessionManager(
            storage_dir=config.storage_dir / 'sessions',
            max_changes_per_session=config.max_changes_per_session
        )
        
        backup_manager = BackupManager(
            backup_dir=config.storage_dir / 'backups',
            retention_days=config.backup_retention_days
        )
        
        validator = EditValidator(
            safe_mode=config.safe_mode
        )
        
        # Create AI assistant if configured
        ai_assistant = None
        if config.ai_enabled and config.ai_provider:
            ai_assistant = AIEditingAssistant(
                provider=config.ai_provider,
                model=config.ai_model
            )
        
        # Create plugins
        plugins = EditingSystemFactory._create_plugins(config)
        
        return EditingSystem(
            file_editor=file_editor,
            diff_engine=diff_engine,
            change_tracker=change_tracker,
            session_manager=session_manager,
            backup_manager=backup_manager,
            validator=validator,
            ai_assistant=ai_assistant,
            plugins=plugins
        )
    
    @staticmethod
    def create_minimal_system() -> EditingSystem:
        """Create a minimal editing system for basic use."""
        config = EditingSystemConfig()
        config.create_backups = False
        config.validate_before_apply = False
        config.ai_enabled = False
        config.enabled_plugins = []
        
        return EditingSystemFactory.create_default_system(config)
    
    @staticmethod
    def create_development_system(project_root: Path) -> EditingSystem:
        """Create an editing system optimized for development."""
        config = EditingSystemConfig()
        config.storage_dir = project_root / '.agent-swarm'
        config.create_backups = True
        config.validate_before_apply = True
        config.ai_enabled = True
        config.enabled_plugins = [
            'black_formatter',
            'prettier_formatter',
            'flake8_linter',
            'eslint_linter'
        ]
        
        return EditingSystemFactory.create_default_system(config)
    
    @staticmethod
    def create_safe_system() -> EditingSystem:
        """Create an editing system with maximum safety features."""
        config = EditingSystemConfig()
        config.create_backups = True
        config.validate_before_apply = True
        config.require_confirmation = True
        config.safe_mode = True
        config.backup_retention_days = 90
        
        return EditingSystemFactory.create_default_system(config)
    
    @staticmethod
    def _create_plugins(config: EditingSystemConfig) -> List[IEditPlugin]:
        """Create plugins based on configuration."""
        plugins = []
        
        # Plugin registry
        plugin_registry = {
            'black_formatter': BlackFormatter,
            'prettier_formatter': PrettierFormatter,
            'rustfmt_formatter': RustFmtFormatter,
            'flake8_linter': FlakeEightLinter,
            'eslint_linter': ESLintLinter,
            'clippy_linter': ClippyLinter,
        }
        
        for plugin_name in config.enabled_plugins:
            if plugin_name in plugin_registry:
                plugin_class = plugin_registry[plugin_name]
                plugin_config = config.plugin_config.get(plugin_name, {})
                
                try:
                    plugin = plugin_class(**plugin_config)
                    plugins.append(plugin)
                except Exception as e:
                    # Log error but continue
                    print(f"Failed to create plugin {plugin_name}: {e}")
        
        return plugins
    
    @staticmethod
    def create_custom_system(
        file_editor_class: Type[IFileEditor] = None,
        diff_engine_class: Type[IDiffEngine] = None,
        change_tracker_class: Type[IChangeTracker] = None,
        session_manager_class: Type[IEditSession] = None,
        backup_manager_class: Type[IBackupManager] = None,
        validator_class: Type[IEditValidator] = None,
        ai_assistant_class: Type[IAIAssistant] = None,
        plugins: List[IEditPlugin] = None,
        config: EditingSystemConfig = None
    ) -> EditingSystem:
        """Create a custom editing system with specific component implementations."""
        
        if config is None:
            config = EditingSystemConfig()
        
        # Use provided classes or defaults
        file_editor = (file_editor_class or TextFileEditor)(
            supported_extensions=config.supported_extensions,
            max_file_size=config.max_file_size,
            create_backups=config.create_backups
        )
        
        diff_engine = (diff_engine_class or UnifiedDiffEngine)(
            context_lines=config.context_lines
        )
        
        change_tracker = (change_tracker_class or ChangeTracker)(
            storage_dir=config.storage_dir / 'changes'
        )
        
        session_manager = (session_manager_class or SessionManager)(
            storage_dir=config.storage_dir / 'sessions'
        )
        
        backup_manager = (backup_manager_class or BackupManager)(
            backup_dir=config.storage_dir / 'backups'
        )
        
        validator = (validator_class or EditValidator)(
            safe_mode=config.safe_mode
        )
        
        ai_assistant = None
        if ai_assistant_class and config.ai_enabled:
            ai_assistant = ai_assistant_class(
                provider=config.ai_provider,
                model=config.ai_model
            )
        
        return EditingSystem(
            file_editor=file_editor,
            diff_engine=diff_engine,
            change_tracker=change_tracker,
            session_manager=session_manager,
            backup_manager=backup_manager,
            validator=validator,
            ai_assistant=ai_assistant,
            plugins=plugins or []
        )
