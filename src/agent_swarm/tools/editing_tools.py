"""
AI Tools for the Professional File Editing System

Provides tools that AI agents can use to edit files autonomously.
These tools integrate with the professional editing system and provide
a clean interface for AI-driven file modifications.
"""

import asyncio
from pathlib import Path
from typing import Dict, Any, List, Optional

from .editing.factory import EditingSystemFactory, EditingSystemConfig
from .editing.core.models import (
    EditOperation,
    EditOperationType,
    FileChangeStatus,
    EditContext,
)


class EditingTools:
    """
    AI-accessible tools for professional file editing.
    
    These tools can be used by AI agents to:
    - Read and write files safely
    - Apply structured edits with validation
    - Track changes and manage sessions
    - Generate diffs and explanations
    - Revert changes when needed
    """
    
    def __init__(self, project_root: Optional[Path] = None):
        """
        Initialize editing tools.
        
        Args:
            project_root: Root directory of the project
        """
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.editing_system = None
        self.current_session_id = None
        self._initialized = False
    
    async def _ensure_initialized(self):
        """Ensure the editing system is initialized."""
        if not self._initialized:
            await self.initialize()
    
    async def initialize(self):
        """Initialize the editing system."""
        try:
            # Create development-optimized editing system
            self.editing_system = EditingSystemFactory.create_development_system(
                self.project_root
            )
            await self.editing_system.initialize()
            
            # Start a default session
            self.current_session_id = await self.editing_system.session_manager.start_session(
                "ai_session", "AI-driven editing session"
            )
            
            self._initialized = True
            
        except Exception as e:
            print(f"Failed to initialize editing system: {e}")
            # Create minimal fallback system
            config = EditingSystemConfig()
            config.ai_enabled = False
            config.create_backups = False
            self.editing_system = EditingSystemFactory.create_minimal_system()
            await self.editing_system.initialize()
            self._initialized = True
    
    async def read_file(self, file_path: str) -> Dict[str, Any]:
        """
        Read a file safely with encoding detection.
        
        Args:
            file_path: Path to the file to read
            
        Returns:
            Dictionary with file content and metadata
        """
        await self._ensure_initialized()
        
        try:
            path = Path(file_path)
            if not path.is_absolute():
                path = self.project_root / path
            
            content = await self.editing_system.file_editor.read_file(path)
            
            return {
                "success": True,
                "content": content,
                "file_path": str(path),
                "size": len(content),
                "lines": len(content.splitlines()),
                "encoding": "utf-8"  # Simplified for now
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }
    
    async def write_file(self, file_path: str, content: str, description: str = "") -> Dict[str, Any]:
        """
        Write content to a file with change tracking.
        
        Args:
            file_path: Path to the file
            content: Content to write
            description: Description of the change
            
        Returns:
            Dictionary with operation result and change ID
        """
        await self._ensure_initialized()
        
        try:
            path = Path(file_path)
            if not path.is_absolute():
                path = self.project_root / path
            
            # Create edit operation
            operation = EditOperation(
                operation_type=EditOperationType.REPLACE_ALL,
                file_path=path,
                content=content,
                description=description or f"AI edit: {path.name}"
            )
            
            # Validate operation
            is_safe = await self.editing_system.validator.validate_safety(operation)
            if not is_safe:
                return {
                    "success": False,
                    "error": "Operation failed safety validation",
                    "file_path": str(path)
                }
            
            # Apply edit
            change = await self.editing_system.file_editor.apply_edit(operation)
            
            # Generate diff
            diff_result = self.editing_system.diff_engine.generate_diff(
                change.original_content, change.new_content
            )
            change.diff_result = diff_result
            
            # Track change
            change_id = await self.editing_system.change_tracker.track_change(change)
            
            # Add to session
            await self.editing_system.session_manager.add_change_to_session(
                self.current_session_id, change
            )
            
            # Apply to filesystem
            await self.editing_system.file_editor.write_file(path, content)
            change.status = FileChangeStatus.APPLIED
            
            return {
                "success": True,
                "change_id": change_id,
                "file_path": str(path),
                "lines_added": diff_result.lines_added,
                "lines_removed": diff_result.lines_removed,
                "has_changes": diff_result.has_changes
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }
    
    async def edit_lines(self, file_path: str, start_line: int, end_line: int, 
                        new_content: str, description: str = "") -> Dict[str, Any]:
        """
        Edit specific lines in a file.
        
        Args:
            file_path: Path to the file
            start_line: Starting line number (1-based)
            end_line: Ending line number (1-based, inclusive)
            new_content: New content for the lines
            description: Description of the change
            
        Returns:
            Dictionary with operation result
        """
        await self._ensure_initialized()
        
        try:
            path = Path(file_path)
            if not path.is_absolute():
                path = self.project_root / path
            
            operation = EditOperation(
                operation_type=EditOperationType.REPLACE_LINES,
                file_path=path,
                content=new_content,
                start_line=start_line,
                end_line=end_line,
                description=description or f"AI edit lines {start_line}-{end_line}: {path.name}"
            )
            
            # Apply and track the edit
            return await self._apply_tracked_edit(operation)
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }
    
    async def insert_lines(self, file_path: str, line_number: int, 
                          content: str, description: str = "") -> Dict[str, Any]:
        """
        Insert lines at a specific position.
        
        Args:
            file_path: Path to the file
            line_number: Line number to insert at (1-based)
            content: Content to insert
            description: Description of the change
            
        Returns:
            Dictionary with operation result
        """
        await self._ensure_initialized()
        
        try:
            path = Path(file_path)
            if not path.is_absolute():
                path = self.project_root / path
            
            operation = EditOperation(
                operation_type=EditOperationType.INSERT_LINES,
                file_path=path,
                content=content,
                start_line=line_number,
                description=description or f"AI insert at line {line_number}: {path.name}"
            )
            
            return await self._apply_tracked_edit(operation)
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }
    
    async def smart_edit(self, file_path: str, instruction: str) -> Dict[str, Any]:
        """
        Apply AI-powered smart editing based on natural language instruction.
        
        Args:
            file_path: Path to the file
            instruction: Natural language instruction
            
        Returns:
            Dictionary with operation result
        """
        await self._ensure_initialized()
        
        try:
            path = Path(file_path)
            if not path.is_absolute():
                path = self.project_root / path
            
            # Read current content
            current_content = await self.editing_system.file_editor.read_file(path)
            
            # Use AI assistant if available
            if self.editing_system.ai_assistant:
                context = EditContext(
                    project_root=self.project_root,
                    file_type=path.suffix,
                    language=self._get_language_from_extension(path.suffix)
                )
                
                new_content = await self.editing_system.ai_assistant.suggest_edit(
                    instruction, current_content, context
                )
                
                return await self.write_file(
                    str(path), new_content, f"Smart edit: {instruction}"
                )
            else:
                return {
                    "success": False,
                    "error": "AI assistant not available",
                    "file_path": str(path)
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }
    
    async def get_diff(self, change_id: str) -> Dict[str, Any]:
        """
        Get diff for a specific change.
        
        Args:
            change_id: ID of the change
            
        Returns:
            Dictionary with diff information
        """
        await self._ensure_initialized()
        
        try:
            change = await self.editing_system.change_tracker.get_change(change_id)
            if not change:
                return {
                    "success": False,
                    "error": f"Change not found: {change_id}"
                }
            
            if change.diff_result:
                return {
                    "success": True,
                    "change_id": change_id,
                    "file_path": str(change.file_path),
                    "lines_added": change.diff_result.lines_added,
                    "lines_removed": change.diff_result.lines_removed,
                    "hunks": len(change.diff_result.hunks),
                    "description": change.description
                }
            else:
                return {
                    "success": False,
                    "error": "No diff available for this change"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def revert_change(self, change_id: str) -> Dict[str, Any]:
        """
        Revert a specific change.
        
        Args:
            change_id: ID of the change to revert
            
        Returns:
            Dictionary with operation result
        """
        await self._ensure_initialized()
        
        try:
            success = await self.editing_system.change_tracker.revert_change(change_id)
            
            return {
                "success": success,
                "change_id": change_id,
                "message": "Change reverted successfully" if success else "Failed to revert change"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "change_id": change_id
            }
    
    async def list_changes(self, limit: int = 10) -> Dict[str, Any]:
        """
        List recent changes in the current session.
        
        Args:
            limit: Maximum number of changes to return
            
        Returns:
            Dictionary with list of changes
        """
        await self._ensure_initialized()
        
        try:
            changes = await self.editing_system.change_tracker.list_changes(
                session_id=self.current_session_id
            )
            
            change_list = []
            for change in changes[:limit]:
                change_info = {
                    "change_id": change.change_id,
                    "file_path": str(change.file_path),
                    "description": change.description,
                    "status": change.status.value,
                    "timestamp": change.timestamp.isoformat(),
                    "has_changes": change.has_changes
                }
                
                if change.diff_result:
                    change_info.update({
                        "lines_added": change.diff_result.lines_added,
                        "lines_removed": change.diff_result.lines_removed
                    })
                
                change_list.append(change_info)
            
            return {
                "success": True,
                "changes": change_list,
                "total_count": len(changes),
                "session_id": self.current_session_id
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _apply_tracked_edit(self, operation: EditOperation) -> Dict[str, Any]:
        """Apply an edit operation with full tracking."""
        # Validate
        is_safe = await self.editing_system.validator.validate_safety(operation)
        if not is_safe:
            return {
                "success": False,
                "error": "Operation failed safety validation",
                "file_path": str(operation.file_path)
            }
        
        # Apply edit
        change = await self.editing_system.file_editor.apply_edit(operation)
        
        # Generate diff
        diff_result = self.editing_system.diff_engine.generate_diff(
            change.original_content, change.new_content
        )
        change.diff_result = diff_result
        
        # Track change
        change_id = await self.editing_system.change_tracker.track_change(change)
        
        # Add to session
        await self.editing_system.session_manager.add_change_to_session(
            self.current_session_id, change
        )
        
        # Apply to filesystem
        await self.editing_system.file_editor.write_file(
            operation.file_path, change.new_content
        )
        change.status = FileChangeStatus.APPLIED
        
        return {
            "success": True,
            "change_id": change_id,
            "file_path": str(operation.file_path),
            "lines_added": diff_result.lines_added,
            "lines_removed": diff_result.lines_removed,
            "has_changes": diff_result.has_changes
        }
    
    def _get_language_from_extension(self, extension: str) -> str:
        """Get language name from file extension."""
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.rs': 'rust',
            '.go': 'go',
            '.rb': 'ruby',
            '.php': 'php',
        }
        return language_map.get(extension.lower(), 'text')


# Global instance for easy access
_editing_tools = None

def get_editing_tools(project_root: Optional[Path] = None) -> EditingTools:
    """Get or create the global editing tools instance."""
    global _editing_tools
    if _editing_tools is None:
        _editing_tools = EditingTools(project_root)
    return _editing_tools
