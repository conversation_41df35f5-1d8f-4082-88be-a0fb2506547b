"""
Linux CLI tools for development workflows.
Comprehensive set of command-line utilities for coding agents.
"""

import asyncio
import os
import platform
import shutil
import subprocess
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import psutil

from ..utils.logging import get_logger

logger = get_logger("tools.cli")


class LinuxCLITool:
    """Base class for Linux CLI tools with safety and logging."""
    
    def __init__(self, timeout: int = 30, safe_mode: bool = True):
        self.timeout = timeout
        self.safe_mode = safe_mode
        self.dangerous_commands = {
            'rm -rf /', 'rm -rf *', 'dd if=', 'mkfs', 'fdisk',
            'shutdown', 'reboot', 'halt', 'poweroff',
            'chmod 777', 'chown -R', 'sudo rm',
        }
    
    async def execute_command(
        self, 
        command: str, 
        cwd: Optional[str] = None,
        env: Optional[Dict[str, str]] = None,
        capture_output: bool = True
    ) -> Dict[str, Any]:
        """Execute a shell command safely."""
        
        if self.safe_mode and self._is_dangerous_command(command):
            return {
                "success": False,
                "error": f"Dangerous command blocked: {command}",
                "stdout": "",
                "stderr": "",
                "return_code": -1
            }
        
        logger.info(f"Executing command: {command}")
        
        try:
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE if capture_output else None,
                stderr=asyncio.subprocess.PIPE if capture_output else None,
                cwd=cwd,
                env=env
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), 
                timeout=self.timeout
            )
            
            return {
                "success": process.returncode == 0,
                "stdout": stdout.decode('utf-8') if stdout else "",
                "stderr": stderr.decode('utf-8') if stderr else "",
                "return_code": process.returncode,
                "command": command
            }
            
        except asyncio.TimeoutError:
            return {
                "success": False,
                "error": f"Command timed out after {self.timeout}s",
                "stdout": "",
                "stderr": "",
                "return_code": -1
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "stdout": "",
                "stderr": "",
                "return_code": -1
            }
    
    def _is_dangerous_command(self, command: str) -> bool:
        """Check if command is potentially dangerous."""
        command_lower = command.lower().strip()
        return any(dangerous in command_lower for dangerous in self.dangerous_commands)
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "execute_command": {
                "description": "Execute a shell command safely with timeout and safety checks",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "command": {
                            "type": "string",
                            "description": "The shell command to execute"
                        },
                        "cwd": {
                            "type": "string",
                            "description": "Working directory for the command (optional)"
                        },
                        "env": {
                            "type": "object",
                            "description": "Environment variables for the command (optional)"
                        },
                        "capture_output": {
                            "type": "boolean",
                            "description": "Whether to capture command output (default: true)"
                        }
                    },
                    "required": ["command"]
                }
            }
        }


class FileSystemTool(LinuxCLITool):
    """Advanced file system operations for development."""
    
    async def find_files(
        self, 
        pattern: str, 
        directory: str = ".", 
        file_type: str = "f"
    ) -> Dict[str, Any]:
        """Find files matching pattern."""
        command = f"find {directory} -type {file_type} -name '{pattern}'"
        return await self.execute_command(command)
    
    async def grep_in_files(
        self, 
        pattern: str, 
        directory: str = ".", 
        file_pattern: str = "*",
        recursive: bool = True
    ) -> Dict[str, Any]:
        """Search for pattern in files."""
        flags = "-r" if recursive else ""
        command = f"grep {flags} -n '{pattern}' {directory}/{file_pattern}"
        return await self.execute_command(command)
    
    async def get_file_info(self, path: str) -> Dict[str, Any]:
        """Get detailed file information."""
        try:
            path_obj = Path(path)
            if not path_obj.exists():
                return {"success": False, "error": f"Path does not exist: {path}"}
            
            stat = path_obj.stat()
            return {
                "success": True,
                "path": str(path_obj.absolute()),
                "size": stat.st_size,
                "modified": stat.st_mtime,
                "permissions": oct(stat.st_mode)[-3:],
                "is_file": path_obj.is_file(),
                "is_directory": path_obj.is_dir(),
                "is_symlink": path_obj.is_symlink()
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "analyze_code": {
                "description": "Analyze code quality and structure",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to analyze"}
                    },
                    "required": ["file_path"]
                }
            },
            "format_code": {
                "description": "Format code using appropriate formatter",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to format"},
                        "formatter": {"type": "string", "description": "Formatter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            },
            "run_tests": {
                "description": "Run tests for a project",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "test_path": {"type": "string", "description": "Path to test file or directory"},
                        "test_framework": {"type": "string", "description": "Test framework to use (auto-detect if not specified)"}
                    }
                }
            },
            "lint_code": {
                "description": "Lint code for style and quality issues",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to lint"},
                        "linter": {"type": "string", "description": "Linter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            }
        }
    
    async def create_directory_structure(self, structure: Dict[str, Any]) -> Dict[str, Any]:
        """Create directory structure from dict."""
        try:
            for path, content in structure.items():
                path_obj = Path(path)
                if isinstance(content, dict):
                    # It's a directory
                    path_obj.mkdir(parents=True, exist_ok=True)
                    if content:  # Has subdirectories/files
                        sub_result = await self.create_directory_structure({
                            str(path_obj / k): v for k, v in content.items()
                        })
                        if not sub_result["success"]:
                            return sub_result
                else:
                    # It's a file
                    path_obj.parent.mkdir(parents=True, exist_ok=True)
                    path_obj.write_text(str(content))
            
            return {"success": True, "message": "Directory structure created"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "analyze_code": {
                "description": "Analyze code quality and structure",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to analyze"}
                    },
                    "required": ["file_path"]
                }
            },
            "format_code": {
                "description": "Format code using appropriate formatter",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to format"},
                        "formatter": {"type": "string", "description": "Formatter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            },
            "run_tests": {
                "description": "Run tests for a project",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "test_path": {"type": "string", "description": "Path to test file or directory"},
                        "test_framework": {"type": "string", "description": "Test framework to use (auto-detect if not specified)"}
                    }
                }
            },
            "lint_code": {
                "description": "Lint code for style and quality issues",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to lint"},
                        "linter": {"type": "string", "description": "Linter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            }
        }
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "read_file": {
                "description": "Read contents of a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to the file to read"}
                    },
                    "required": ["file_path"]
                }
            },
            "write_file": {
                "description": "Write content to a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to the file to write"},
                        "content": {"type": "string", "description": "Content to write to the file"},
                        "mode": {"type": "string", "description": "Write mode (w, a, etc.)", "default": "w"}
                    },
                    "required": ["file_path", "content"]
                }
            },
            "list_directory": {
                "description": "List contents of a directory",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "directory_path": {"type": "string", "description": "Path to the directory to list"},
                        "show_hidden": {"type": "boolean", "description": "Include hidden files", "default": False}
                    },
                    "required": ["directory_path"]
                }
            },
            "create_directory_structure": {
                "description": "Create a complex directory structure with files",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "structure": {"type": "object", "description": "Directory structure definition"}
                    },
                    "required": ["structure"]
                }
            }
        }


class ProcessTool(LinuxCLITool):
    """Process management and monitoring."""
    
    async def list_processes(self, filter_name: Optional[str] = None) -> Dict[str, Any]:
        """List running processes."""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    if filter_name is None or filter_name.lower() in proc.info['name'].lower():
                        processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            return {"success": True, "processes": processes}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "analyze_code": {
                "description": "Analyze code quality and structure",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to analyze"}
                    },
                    "required": ["file_path"]
                }
            },
            "format_code": {
                "description": "Format code using appropriate formatter",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to format"},
                        "formatter": {"type": "string", "description": "Formatter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            },
            "run_tests": {
                "description": "Run tests for a project",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "test_path": {"type": "string", "description": "Path to test file or directory"},
                        "test_framework": {"type": "string", "description": "Test framework to use (auto-detect if not specified)"}
                    }
                }
            },
            "lint_code": {
                "description": "Lint code for style and quality issues",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to lint"},
                        "linter": {"type": "string", "description": "Linter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            }
        }
    
    async def kill_process(self, pid: int, force: bool = False) -> Dict[str, Any]:
        """Kill a process by PID."""
        try:
            proc = psutil.Process(pid)
            if force:
                proc.kill()
            else:
                proc.terminate()
            
            return {"success": True, "message": f"Process {pid} terminated"}
        except psutil.NoSuchProcess:
            return {"success": False, "error": f"Process {pid} not found"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "analyze_code": {
                "description": "Analyze code quality and structure",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to analyze"}
                    },
                    "required": ["file_path"]
                }
            },
            "format_code": {
                "description": "Format code using appropriate formatter",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to format"},
                        "formatter": {"type": "string", "description": "Formatter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            },
            "run_tests": {
                "description": "Run tests for a project",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "test_path": {"type": "string", "description": "Path to test file or directory"},
                        "test_framework": {"type": "string", "description": "Test framework to use (auto-detect if not specified)"}
                    }
                }
            },
            "lint_code": {
                "description": "Lint code for style and quality issues",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to lint"},
                        "linter": {"type": "string", "description": "Linter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            }
        }
    
    async def get_system_resources(self) -> Dict[str, Any]:
        """Get system resource usage."""
        try:
            return {
                "success": True,
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory": psutil.virtual_memory()._asdict(),
                "disk": psutil.disk_usage('/')._asdict(),
                "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "analyze_code": {
                "description": "Analyze code quality and structure",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to analyze"}
                    },
                    "required": ["file_path"]
                }
            },
            "format_code": {
                "description": "Format code using appropriate formatter",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to format"},
                        "formatter": {"type": "string", "description": "Formatter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            },
            "run_tests": {
                "description": "Run tests for a project",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "test_path": {"type": "string", "description": "Path to test file or directory"},
                        "test_framework": {"type": "string", "description": "Test framework to use (auto-detect if not specified)"}
                    }
                }
            },
            "lint_code": {
                "description": "Lint code for style and quality issues",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to lint"},
                        "linter": {"type": "string", "description": "Linter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            }
        }
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "list_processes": {
                "description": "List running processes with optional filtering",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "filter_name": {"type": "string", "description": "Filter processes by name (optional)"}
                    }
                }
            },
            "kill_process": {
                "description": "Kill a process by PID",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "pid": {"type": "integer", "description": "Process ID to kill"},
                        "force": {"type": "boolean", "description": "Force kill the process", "default": False}
                    },
                    "required": ["pid"]
                }
            },
            "get_process_info": {
                "description": "Get detailed information about a process",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "pid": {"type": "integer", "description": "Process ID to get info for"}
                    },
                    "required": ["pid"]
                }
            }
        }


class NetworkTool(LinuxCLITool):
    """Network utilities for development."""
    
    async def ping_host(self, host: str, count: int = 4) -> Dict[str, Any]:
        """Ping a host."""
        command = f"ping -c {count} {host}"
        return await self.execute_command(command)
    
    async def check_port(self, host: str, port: int) -> Dict[str, Any]:
        """Check if a port is open."""
        command = f"nc -z -v {host} {port}"
        result = await self.execute_command(command)
        result["port_open"] = result["success"]
        return result
    
    async def get_network_interfaces(self) -> Dict[str, Any]:
        """Get network interface information."""
        try:
            interfaces = {}
            for interface, addrs in psutil.net_if_addrs().items():
                interfaces[interface] = [
                    {
                        "family": addr.family.name,
                        "address": addr.address,
                        "netmask": addr.netmask,
                        "broadcast": addr.broadcast
                    }
                    for addr in addrs
                ]
            
            return {"success": True, "interfaces": interfaces}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "analyze_code": {
                "description": "Analyze code quality and structure",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to analyze"}
                    },
                    "required": ["file_path"]
                }
            },
            "format_code": {
                "description": "Format code using appropriate formatter",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to format"},
                        "formatter": {"type": "string", "description": "Formatter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            },
            "run_tests": {
                "description": "Run tests for a project",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "test_path": {"type": "string", "description": "Path to test file or directory"},
                        "test_framework": {"type": "string", "description": "Test framework to use (auto-detect if not specified)"}
                    }
                }
            },
            "lint_code": {
                "description": "Lint code for style and quality issues",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to lint"},
                        "linter": {"type": "string", "description": "Linter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            }
        }
    
    async def curl_request(
        self, 
        url: str, 
        method: str = "GET", 
        headers: Optional[Dict[str, str]] = None,
        data: Optional[str] = None
    ) -> Dict[str, Any]:
        """Make HTTP request using curl."""
        command = f"curl -s -w '%{{http_code}}' -X {method}"
        
        if headers:
            for key, value in headers.items():
                command += f" -H '{key}: {value}'"
        
        if data:
            command += f" -d '{data}'"
        
        command += f" '{url}'"
        
        return await self.execute_command(command)


class SystemInfoTool(LinuxCLITool):
    """System information and diagnostics."""
    
    async def get_system_info(self) -> Dict[str, Any]:
        """Get comprehensive system information."""
        try:
            return {
                "success": True,
                "platform": platform.platform(),
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "python_version": sys.version,
                "cpu_count": psutil.cpu_count(),
                "memory_total": psutil.virtual_memory().total,
                "disk_total": psutil.disk_usage('/').total
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "analyze_code": {
                "description": "Analyze code quality and structure",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to analyze"}
                    },
                    "required": ["file_path"]
                }
            },
            "format_code": {
                "description": "Format code using appropriate formatter",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to format"},
                        "formatter": {"type": "string", "description": "Formatter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            },
            "run_tests": {
                "description": "Run tests for a project",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "test_path": {"type": "string", "description": "Path to test file or directory"},
                        "test_framework": {"type": "string", "description": "Test framework to use (auto-detect if not specified)"}
                    }
                }
            },
            "lint_code": {
                "description": "Lint code for style and quality issues",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to lint"},
                        "linter": {"type": "string", "description": "Linter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            }
        }
    
    async def get_environment_variables(self, filter_prefix: Optional[str] = None) -> Dict[str, Any]:
        """Get environment variables."""
        try:
            env_vars = {}
            for key, value in os.environ.items():
                if filter_prefix is None or key.startswith(filter_prefix):
                    env_vars[key] = value
            
            return {"success": True, "environment": env_vars}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "analyze_code": {
                "description": "Analyze code quality and structure",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to analyze"}
                    },
                    "required": ["file_path"]
                }
            },
            "format_code": {
                "description": "Format code using appropriate formatter",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to format"},
                        "formatter": {"type": "string", "description": "Formatter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            },
            "run_tests": {
                "description": "Run tests for a project",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "test_path": {"type": "string", "description": "Path to test file or directory"},
                        "test_framework": {"type": "string", "description": "Test framework to use (auto-detect if not specified)"}
                    }
                }
            },
            "lint_code": {
                "description": "Lint code for style and quality issues",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to lint"},
                        "linter": {"type": "string", "description": "Linter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            }
        }
    
    async def check_dependencies(self, dependencies: List[str]) -> Dict[str, Any]:
        """Check if system dependencies are installed."""
        results = {}
        
        for dep in dependencies:
            result = await self.execute_command(f"which {dep}")
            results[dep] = {
                "installed": result["success"],
                "path": result["stdout"].strip() if result["success"] else None
            }
        
        return {"success": True, "dependencies": results}


class DevelopmentTool(LinuxCLITool):
    """Development-specific utilities."""
    
    async def create_project_structure(self, project_name: str, project_type: str = "python") -> Dict[str, Any]:
        """Create a new project structure."""
        structures = {
            "python": {
                project_name: {
                    "src": {
                        project_name: {
                            "__init__.py": "",
                            "main.py": "#!/usr/bin/env python3\n\ndef main():\n    print('Hello World!')\n\nif __name__ == '__main__':\n    main()\n"
                        }
                    },
                    "tests": {
                        "__init__.py": "",
                        "test_main.py": "import pytest\n\ndef test_main():\n    assert True\n"
                    },
                    "docs": {},
                    "README.md": f"# {project_name}\n\nA Python project.\n",
                    "requirements.txt": "",
                    "pyproject.toml": f'[project]\nname = "{project_name}"\nversion = "0.1.0"\n',
                    ".gitignore": "__pycache__/\n*.pyc\n.venv/\n"
                }
            },
            "node": {
                project_name: {
                    "src": {
                        "index.js": "console.log('Hello World!');\n"
                    },
                    "tests": {},
                    "package.json": f'{{\n  "name": "{project_name}",\n  "version": "1.0.0",\n  "main": "src/index.js"\n}}',
                    "README.md": f"# {project_name}\n\nA Node.js project.\n",
                    ".gitignore": "node_modules/\n*.log\n"
                }
            }
        }
        
        if project_type not in structures:
            return {"success": False, "error": f"Unknown project type: {project_type}"}
        
        fs_tool = FileSystemTool()
        return await fs_tool.create_directory_structure(structures[project_type])
    
    async def run_tests(self, test_command: str = "pytest", directory: str = ".") -> Dict[str, Any]:
        """Run tests in a project."""
        return await self.execute_command(test_command, cwd=directory)
    
    async def install_dependencies(self, package_manager: str = "pip", requirements_file: str = "requirements.txt") -> Dict[str, Any]:
        """Install project dependencies."""
        commands = {
            "pip": f"pip install -r {requirements_file}",
            "npm": "npm install",
            "yarn": "yarn install",
            "poetry": "poetry install"
        }
        
        if package_manager not in commands:
            return {"success": False, "error": f"Unknown package manager: {package_manager}"}
        
        return await self.execute_command(commands[package_manager])
    
    async def format_code(self, formatter: str = "black", target: str = ".") -> Dict[str, Any]:
        """Format code using specified formatter."""
        formatters = {
            "black": f"black {target}",
            "autopep8": f"autopep8 --in-place --recursive {target}",
            "prettier": f"prettier --write {target}",
            "rustfmt": f"rustfmt {target}"
        }
        
        if formatter not in formatters:
            return {"success": False, "error": f"Unknown formatter: {formatter}"}
        
        return await self.execute_command(formatters[formatter])
    
    async def lint_code(self, linter: str = "flake8", target: str = ".") -> Dict[str, Any]:
        """Lint code using specified linter."""
        linters = {
            "flake8": f"flake8 {target}",
            "pylint": f"pylint {target}",
            "eslint": f"eslint {target}",
            "clippy": f"cargo clippy -- -D warnings"
        }
        
        if linter not in linters:
            return {"success": False, "error": f"Unknown linter: {linter}"}
        
        return await self.execute_command(linters[linter])
