"""
Validation utilities for Agent Swarm.
"""

from __future__ import annotations

import os
from typing import Dict, List, Optional

import aiohttp


async def check_ollama_service(
    base_url: str = "http://localhost:11434",
) -> Dict[str, any]:
    """
    Check if Ollama service is available and get model information.

    Args:
        base_url: Ollama service URL

    Returns:
        Dictionary with service status and available models
    """
    result = {"available": False, "models": [], "error": None}

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/api/tags", timeout=5) as response:
                if response.status == 200:
                    data = await response.json()
                    result["available"] = True
                    result["models"] = [
                        model["name"] for model in data.get("models", [])
                    ]
                else:
                    result["error"] = f"HTTP {response.status}"
    except aiohttp.ClientError as e:
        result["error"] = f"Connection error: {e}"
    except Exception as e:
        result["error"] = f"Unexpected error: {e}"

    return result


def validate_api_keys(required_keys: Optional[List[str]] = None) -> Dict[str, bool]:
    """
    Validate that required API keys are available in environment.

    Args:
        required_keys: List of environment variable names to check

    Returns:
        Dictionary mapping key names to availability status
    """
    if required_keys is None:
        required_keys = ["ANTHROPIC_API_KEY", "GOOGLE_API_KEY", "OPENAI_API_KEY"]

    return {key: bool(os.getenv(key)) for key in required_keys}


def check_model_availability(
    models: List[str], available_models: List[str]
) -> Dict[str, bool]:
    """
    Check which models from a list are available.

    Args:
        models: List of model names to check
        available_models: List of available model names

    Returns:
        Dictionary mapping model names to availability status
    """
    return {
        model: any(model in available for available in available_models)
        for model in models
    }


async def validate_system_setup() -> Dict[str, any]:
    """
    Comprehensive system validation.

    Returns:
        Dictionary with validation results
    """
    results = {
        "ollama": await check_ollama_service(),
        "api_keys": validate_api_keys(),
        "recommended_models": {},
        "overall_status": "unknown",
    }

    # Check recommended models
    recommended_models = ["deepseek-r1:7b", "qwen2.5-coder:7b"]
    if results["ollama"]["available"]:
        results["recommended_models"] = check_model_availability(
            recommended_models, results["ollama"]["models"]
        )

    # Determine overall status
    has_local_llm = (
        any(results["recommended_models"].values())
        if results["recommended_models"]
        else False
    )
    has_cloud_llm = any(results["api_keys"].values())

    if has_local_llm and has_cloud_llm:
        results["overall_status"] = "excellent"
    elif has_local_llm:
        results["overall_status"] = "good"
    elif has_cloud_llm:
        results["overall_status"] = "limited"
    else:
        results["overall_status"] = "needs_setup"

    return results


def get_setup_recommendations(validation_results: Dict[str, any]) -> List[str]:
    """
    Get setup recommendations based on validation results.

    Args:
        validation_results: Results from validate_system_setup()

    Returns:
        List of recommendation strings
    """
    recommendations = []

    # Ollama recommendations
    if not validation_results["ollama"]["available"]:
        recommendations.append(
            "Install Ollama: curl -fsSL https://ollama.ai/install.sh | sh"
        )
        recommendations.append("Start Ollama service: ollama serve")

    # Model recommendations
    recommended_models = ["deepseek-r1:7b", "qwen2.5-coder:7b"]
    missing_models = [
        model
        for model, available in validation_results.get("recommended_models", {}).items()
        if not available
    ]

    if missing_models:
        for model in missing_models:
            recommendations.append(f"Pull model: ollama pull {model}")

    # API key recommendations
    missing_keys = [
        key
        for key, available in validation_results["api_keys"].items()
        if not available
    ]

    if missing_keys:
        recommendations.append(
            f"Add API keys to environment: {', '.join(missing_keys)}"
        )
        recommendations.append(
            "Create .env file: cp ~/.agent-swarm/.env.example ~/.agent-swarm/.env"
        )

    # Overall recommendations
    status = validation_results["overall_status"]
    if status == "needs_setup":
        recommendations.append("Run setup script: agent-swarm setup")
    elif status == "limited":
        recommendations.append("Consider adding local LLMs for cost efficiency")
    elif status == "good":
        recommendations.append("Consider adding cloud LLMs for complex tasks")

    return recommendations
