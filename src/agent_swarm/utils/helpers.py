import re

def format_file_size(bytes: int) -> str:
    """
    Convert bytes to human-readable format (KB, MB, GB).

    Args:
        bytes: The number of bytes to convert.

    Returns:
        A string representation of the file size in human-readable format.
    """

    if not isinstance(bytes, int):
        raise TypeError("Bytes must be an integer.")
    if bytes < 0:
        raise ValueError("Bytes cannot be negative")
    elif bytes == 0:
        return "0 B"

    units = ["B", "KB", "MB", "GB"]
    index = 0
    while bytes >= 1024 and index < len(units) - 1:
        bytes /= 1024
        index += 1

    return f"{bytes:.2f} {units[index]}"

def validate_model_name(name: str) -> bool:
    """
    Check if a model name follows a valid format.

    Args:
        name: The model name to validate.

    Returns:
        True if the model name is valid, False otherwise.
    """

    pattern = r"^[a-zA-Z0-9_\-:.]{3,50}$"
    return bool(re.match(pattern, name))

def safe_filename(text: str) -> str:
    """
    Convert text to a safe filename by removing/replace invalid characters.

    Args:
        text: The input text to convert.

    Returns:
        A safe representation of the input text.
    """

    pattern = r"[^a-zA-Z0-9_-]"
    return re.sub(pattern, "_", text)
