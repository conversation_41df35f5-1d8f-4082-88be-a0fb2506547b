"""
Standardized error handling for Agent Swarm.
Provides consistent error handling patterns across the framework.
"""

import asyncio
import functools
import traceback
from typing import Any, Callable, Optional, Type, Union, Dict, List
from enum import Enum
import logging
from ..utils.logging import get_logger

logger = get_logger("utils.error_handling")


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AgentSwarmError(Exception):
    """Base exception for Agent Swarm framework."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.severity = severity
        self.context = context or {}
        self.cause = cause
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for logging/serialization."""
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "severity": self.severity.value,
            "context": self.context,
            "cause": str(self.cause) if self.cause else None
        }


class LLMError(AgentSwarmError):
    """LLM-related errors."""
    pass


class ContextError(AgentSwarmError):
    """Context engine errors."""
    pass


class ConfigurationError(AgentSwarmError):
    """Configuration-related errors."""
    pass


class NetworkError(AgentSwarmError):
    """Network-related errors."""
    pass


class FileSystemError(AgentSwarmError):
    """File system operation errors."""
    pass


class ValidationError(AgentSwarmError):
    """Data validation errors."""
    pass


class TimeoutError(AgentSwarmError):
    """Operation timeout errors."""
    pass


class ErrorHandler:
    """
    Centralized error handler with consistent patterns.
    """
    
    def __init__(self, logger_name: Optional[str] = None):
        self.logger = get_logger(logger_name or "error_handler")
        self.error_counts: Dict[str, int] = {}
        self.error_callbacks: List[Callable] = []
    
    def add_error_callback(self, callback: Callable[[AgentSwarmError], None]) -> None:
        """Add callback to be called on errors."""
        self.error_callbacks.append(callback)
    
    def handle_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        reraise: bool = True
    ) -> Optional[AgentSwarmError]:
        """
        Handle an error with standardized logging and processing.
        
        Args:
            error: The exception to handle
            context: Additional context information
            reraise: Whether to reraise the error
            
        Returns:
            AgentSwarmError instance if not reraising
        """
        # Convert to AgentSwarmError if needed
        if isinstance(error, AgentSwarmError):
            agent_error = error
        else:
            agent_error = AgentSwarmError(
                message=str(error),
                context=context,
                cause=error
            )
        
        # Update error counts
        error_type = agent_error.__class__.__name__
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # Log the error
        self._log_error(agent_error)
        
        # Call error callbacks
        for callback in self.error_callbacks:
            try:
                callback(agent_error)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
        
        if reraise:
            raise agent_error
        else:
            return agent_error
    
    def _log_error(self, error: AgentSwarmError) -> None:
        """Log error with appropriate level based on severity."""
        error_dict = error.to_dict()
        
        if error.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"CRITICAL ERROR: {error.message}", extra=error_dict)
        elif error.severity == ErrorSeverity.HIGH:
            self.logger.error(f"HIGH SEVERITY: {error.message}", extra=error_dict)
        elif error.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(f"MEDIUM SEVERITY: {error.message}", extra=error_dict)
        else:
            self.logger.info(f"LOW SEVERITY: {error.message}", extra=error_dict)
        
        # Log stack trace for high/critical errors
        if error.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            if error.cause:
                self.logger.error(f"Caused by: {error.cause}")
                self.logger.error(traceback.format_exc())
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics."""
        return {
            "total_errors": sum(self.error_counts.values()),
            "error_counts": self.error_counts.copy(),
            "most_common": max(self.error_counts.items(), key=lambda x: x[1]) if self.error_counts else None
        }


# Global error handler instance
_global_error_handler = ErrorHandler()


def get_error_handler() -> ErrorHandler:
    """Get the global error handler."""
    return _global_error_handler


def handle_errors(
    error_types: Union[Type[Exception], tuple] = Exception,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    reraise: bool = True,
    context: Optional[Dict[str, Any]] = None
):
    """
    Decorator for standardized error handling.
    
    Args:
        error_types: Exception types to catch
        severity: Error severity level
        reraise: Whether to reraise the error
        context: Additional context information
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except error_types as e:
                error_context = context.copy() if context else {}
                error_context.update({
                    "function": func.__name__,
                    "args": str(args)[:200],  # Limit length
                    "kwargs": str(kwargs)[:200]
                })
                
                agent_error = AgentSwarmError(
                    message=f"Error in {func.__name__}: {str(e)}",
                    severity=severity,
                    context=error_context,
                    cause=e
                )
                
                return _global_error_handler.handle_error(agent_error, reraise=reraise)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except error_types as e:
                error_context = context.copy() if context else {}
                error_context.update({
                    "function": func.__name__,
                    "args": str(args)[:200],
                    "kwargs": str(kwargs)[:200]
                })
                
                agent_error = AgentSwarmError(
                    message=f"Error in {func.__name__}: {str(e)}",
                    severity=severity,
                    context=error_context,
                    cause=e
                )
                
                return _global_error_handler.handle_error(agent_error, reraise=reraise)
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


def safe_execute(
    func: Callable,
    *args,
    default_return: Any = None,
    error_types: Union[Type[Exception], tuple] = Exception,
    severity: ErrorSeverity = ErrorSeverity.LOW,
    **kwargs
) -> Any:
    """
    Safely execute a function with error handling.
    
    Args:
        func: Function to execute
        *args: Function arguments
        default_return: Value to return on error
        error_types: Exception types to catch
        severity: Error severity level
        **kwargs: Function keyword arguments
        
    Returns:
        Function result or default_return on error
    """
    try:
        return func(*args, **kwargs)
    except error_types as e:
        agent_error = AgentSwarmError(
            message=f"Safe execution failed for {func.__name__}: {str(e)}",
            severity=severity,
            context={"function": func.__name__},
            cause=e
        )
        
        _global_error_handler.handle_error(agent_error, reraise=False)
        return default_return


async def safe_execute_async(
    func: Callable,
    *args,
    default_return: Any = None,
    error_types: Union[Type[Exception], tuple] = Exception,
    severity: ErrorSeverity = ErrorSeverity.LOW,
    **kwargs
) -> Any:
    """
    Safely execute an async function with error handling.
    
    Args:
        func: Async function to execute
        *args: Function arguments
        default_return: Value to return on error
        error_types: Exception types to catch
        severity: Error severity level
        **kwargs: Function keyword arguments
        
    Returns:
        Function result or default_return on error
    """
    try:
        return await func(*args, **kwargs)
    except error_types as e:
        agent_error = AgentSwarmError(
            message=f"Safe async execution failed for {func.__name__}: {str(e)}",
            severity=severity,
            context={"function": func.__name__},
            cause=e
        )
        
        _global_error_handler.handle_error(agent_error, reraise=False)
        return default_return


class ErrorContext:
    """Context manager for error handling."""
    
    def __init__(
        self,
        error_types: Union[Type[Exception], tuple] = Exception,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        reraise: bool = True,
        context: Optional[Dict[str, Any]] = None
    ):
        self.error_types = error_types
        self.severity = severity
        self.reraise = reraise
        self.context = context or {}
        self.error: Optional[AgentSwarmError] = None
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type and issubclass(exc_type, self.error_types):
            agent_error = AgentSwarmError(
                message=str(exc_val),
                severity=self.severity,
                context=self.context,
                cause=exc_val
            )
            
            self.error = _global_error_handler.handle_error(
                agent_error, 
                reraise=self.reraise
            )
            
            return not self.reraise  # Suppress exception if not reraising
        
        return False  # Don't suppress other exceptions


# Convenience functions for common error patterns
def validate_required(value: Any, name: str) -> Any:
    """Validate that a required value is not None/empty."""
    if value is None or (isinstance(value, (str, list, dict)) and not value):
        raise ValidationError(
            f"Required parameter '{name}' is missing or empty",
            error_code="MISSING_REQUIRED_PARAMETER",
            severity=ErrorSeverity.HIGH,
            context={"parameter": name, "value": value}
        )
    return value


def validate_type(value: Any, expected_type: Type, name: str) -> Any:
    """Validate that a value is of the expected type."""
    if not isinstance(value, expected_type):
        raise ValidationError(
            f"Parameter '{name}' must be of type {expected_type.__name__}, got {type(value).__name__}",
            error_code="INVALID_TYPE",
            severity=ErrorSeverity.HIGH,
            context={"parameter": name, "expected_type": expected_type.__name__, "actual_type": type(value).__name__}
        )
    return value


def validate_range(value: Union[int, float], min_val: Optional[Union[int, float]], max_val: Optional[Union[int, float]], name: str) -> Union[int, float]:
    """Validate that a numeric value is within range."""
    if min_val is not None and value < min_val:
        raise ValidationError(
            f"Parameter '{name}' must be >= {min_val}, got {value}",
            error_code="VALUE_TOO_LOW",
            severity=ErrorSeverity.HIGH,
            context={"parameter": name, "value": value, "min_val": min_val}
        )
    
    if max_val is not None and value > max_val:
        raise ValidationError(
            f"Parameter '{name}' must be <= {max_val}, got {value}",
            error_code="VALUE_TOO_HIGH",
            severity=ErrorSeverity.HIGH,
            context={"parameter": name, "value": value, "max_val": max_val}
        )
    
    return value