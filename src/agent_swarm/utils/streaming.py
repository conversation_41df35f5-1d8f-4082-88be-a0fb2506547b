"""
Streaming utilities for Agent Swarm.
Provides efficient streaming support for large file operations and data processing.
"""

import asyncio
import os
from pathlib import Path
from typing import AsyncGenerator, Optional, Union, BinaryIO, TextIO
import aiofiles
from ..utils.logging import get_logger

logger = get_logger("utils.streaming")


class StreamingFileReader:
    """
    Streaming file reader for large files.
    Reads files in chunks to avoid loading everything into memory.
    """
    
    def __init__(self, chunk_size: int = 8192):
        self.chunk_size = chunk_size
    
    async def read_text_stream(
        self, 
        file_path: Union[str, Path],
        encoding: str = 'utf-8'
    ) -> AsyncGenerator[str, None]:
        """
        Stream text file content in chunks.
        
        Args:
            file_path: Path to the file
            encoding: Text encoding
            
        Yields:
            Text chunks
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        logger.debug(f"Streaming text file: {file_path} (chunk_size={self.chunk_size})")
        
        try:
            async with aiofiles.open(file_path, 'r', encoding=encoding) as file:
                while True:
                    chunk = await file.read(self.chunk_size)
                    if not chunk:
                        break
                    yield chunk
        except Exception as e:
            logger.error(f"Error streaming file {file_path}: {e}")
            raise
    
    async def read_binary_stream(
        self, 
        file_path: Union[str, Path]
    ) -> AsyncGenerator[bytes, None]:
        """
        Stream binary file content in chunks.
        
        Args:
            file_path: Path to the file
            
        Yields:
            Binary chunks
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        logger.debug(f"Streaming binary file: {file_path} (chunk_size={self.chunk_size})")
        
        try:
            async with aiofiles.open(file_path, 'rb') as file:
                while True:
                    chunk = await file.read(self.chunk_size)
                    if not chunk:
                        break
                    yield chunk
        except Exception as e:
            logger.error(f"Error streaming file {file_path}: {e}")
            raise
    
    async def read_lines_stream(
        self, 
        file_path: Union[str, Path],
        encoding: str = 'utf-8'
    ) -> AsyncGenerator[str, None]:
        """
        Stream file content line by line.
        
        Args:
            file_path: Path to the file
            encoding: Text encoding
            
        Yields:
            Individual lines
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        logger.debug(f"Streaming lines from: {file_path}")
        
        try:
            async with aiofiles.open(file_path, 'r', encoding=encoding) as file:
                async for line in file:
                    yield line.rstrip('\n\r')
        except Exception as e:
            logger.error(f"Error streaming lines from {file_path}: {e}")
            raise


class StreamingFileWriter:
    """
    Streaming file writer for large data.
    Writes data in chunks to handle large datasets efficiently.
    """
    
    def __init__(self, chunk_size: int = 8192):
        self.chunk_size = chunk_size
    
    async def write_text_stream(
        self,
        file_path: Union[str, Path],
        data_stream: AsyncGenerator[str, None],
        encoding: str = 'utf-8',
        mode: str = 'w'
    ) -> int:
        """
        Write text data stream to file.
        
        Args:
            file_path: Output file path
            data_stream: Async generator of text chunks
            encoding: Text encoding
            mode: File open mode
            
        Returns:
            Total bytes written
        """
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.debug(f"Streaming write to: {file_path}")
        
        total_written = 0
        
        try:
            async with aiofiles.open(file_path, mode, encoding=encoding) as file:
                async for chunk in data_stream:
                    await file.write(chunk)
                    total_written += len(chunk.encode(encoding))
            
            logger.debug(f"Streaming write complete: {total_written} bytes")
            return total_written
            
        except Exception as e:
            logger.error(f"Error streaming write to {file_path}: {e}")
            raise
    
    async def write_binary_stream(
        self,
        file_path: Union[str, Path],
        data_stream: AsyncGenerator[bytes, None],
        mode: str = 'wb'
    ) -> int:
        """
        Write binary data stream to file.
        
        Args:
            file_path: Output file path
            data_stream: Async generator of binary chunks
            mode: File open mode
            
        Returns:
            Total bytes written
        """
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.debug(f"Streaming binary write to: {file_path}")
        
        total_written = 0
        
        try:
            async with aiofiles.open(file_path, mode) as file:
                async for chunk in data_stream:
                    await file.write(chunk)
                    total_written += len(chunk)
            
            logger.debug(f"Streaming binary write complete: {total_written} bytes")
            return total_written
            
        except Exception as e:
            logger.error(f"Error streaming binary write to {file_path}: {e}")
            raise


class StreamingProcessor:
    """
    Streaming data processor for large datasets.
    Processes data in chunks to avoid memory issues.
    """
    
    def __init__(self, chunk_size: int = 8192):
        self.chunk_size = chunk_size
    
    async def process_text_stream(
        self,
        input_stream: AsyncGenerator[str, None],
        processor_func,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Process text stream with a function.
        
        Args:
            input_stream: Input text stream
            processor_func: Function to process each chunk
            **kwargs: Additional arguments for processor
            
        Yields:
            Processed text chunks
        """
        async for chunk in input_stream:
            try:
                if asyncio.iscoroutinefunction(processor_func):
                    processed = await processor_func(chunk, **kwargs)
                else:
                    processed = processor_func(chunk, **kwargs)
                
                if processed:
                    yield processed
                    
            except Exception as e:
                logger.error(f"Error processing chunk: {e}")
                # Continue processing other chunks
                continue
    
    async def batch_process_stream(
        self,
        input_stream: AsyncGenerator[str, None],
        batch_size: int,
        processor_func,
        **kwargs
    ) -> AsyncGenerator[list, None]:
        """
        Process stream in batches.
        
        Args:
            input_stream: Input stream
            batch_size: Number of items per batch
            processor_func: Function to process each batch
            **kwargs: Additional arguments for processor
            
        Yields:
            Processed batches
        """
        batch = []
        
        async for item in input_stream:
            batch.append(item)
            
            if len(batch) >= batch_size:
                try:
                    if asyncio.iscoroutinefunction(processor_func):
                        result = await processor_func(batch, **kwargs)
                    else:
                        result = processor_func(batch, **kwargs)
                    
                    if result:
                        yield result
                        
                except Exception as e:
                    logger.error(f"Error processing batch: {e}")
                
                batch = []
        
        # Process remaining items
        if batch:
            try:
                if asyncio.iscoroutinefunction(processor_func):
                    result = await processor_func(batch, **kwargs)
                else:
                    result = processor_func(batch, **kwargs)
                
                if result:
                    yield result
                    
            except Exception as e:
                logger.error(f"Error processing final batch: {e}")


# Convenience functions
async def stream_file_lines(
    file_path: Union[str, Path],
    encoding: str = 'utf-8'
) -> AsyncGenerator[str, None]:
    """Stream file lines efficiently."""
    reader = StreamingFileReader()
    async for line in reader.read_lines_stream(file_path, encoding):
        yield line


async def stream_file_chunks(
    file_path: Union[str, Path],
    chunk_size: int = 8192,
    encoding: str = 'utf-8'
) -> AsyncGenerator[str, None]:
    """Stream file chunks efficiently."""
    reader = StreamingFileReader(chunk_size)
    async for chunk in reader.read_text_stream(file_path, encoding):
        yield chunk


async def write_stream_to_file(
    file_path: Union[str, Path],
    data_stream: AsyncGenerator[str, None],
    encoding: str = 'utf-8'
) -> int:
    """Write stream to file efficiently."""
    writer = StreamingFileWriter()
    return await writer.write_text_stream(file_path, data_stream, encoding)


# Context managers for streaming operations
class StreamingContext:
    """Context manager for streaming operations."""
    
    def __init__(self, chunk_size: int = 8192):
        self.chunk_size = chunk_size
        self.reader = StreamingFileReader(chunk_size)
        self.writer = StreamingFileWriter(chunk_size)
        self.processor = StreamingProcessor(chunk_size)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Cleanup if needed
        pass