"""
Logging utilities for Agent Swarm.
"""

import logging
import sys
from pathlib import Path
from typing import Optional

from rich.console import <PERSON>sole
from rich.logging import <PERSON><PERSON><PERSON><PERSON>


def setup_logging(
    level: str = "INFO",
    log_file: Optional[Path] = None,
    rich_console: bool = True,
) -> logging.Logger:
    """
    Set up logging for Agent Swarm.

    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Optional file to write logs to
        rich_console: Whether to use rich console formatting

    Returns:
        Configured logger instance
    """
    # Create logger
    logger = logging.getLogger("agent_swarm")
    logger.setLevel(getattr(logging, level.upper()))

    # Clear existing handlers
    logger.handlers.clear()

    # Console handler
    if rich_console:
        console = Console(stderr=True)
        console_handler = RichHandler(
            console=console,
            show_time=True,
            show_path=False,
            markup=True,
        )
        console_format = "%(message)s"
    else:
        console_handler = logging.StreamHandler(sys.stderr)
        console_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    console_handler.setLevel(getattr(logging, level.upper()))
    console_formatter = logging.Formatter(console_format)
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # File handler (if specified)
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)  # Always debug level for files
        file_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)

    # Prevent duplicate logs
    logger.propagate = False

    return logger


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance for a specific module."""
    return logging.getLogger(f"agent_swarm.{name}")


# Convenience loggers for different components
backends_logger = get_logger("backends")
agents_logger = get_logger("agents")
router_logger = get_logger("router")
cli_logger = get_logger("cli")
