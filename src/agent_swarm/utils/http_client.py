"""
Centralized HTTP client with connection pooling for Agent Swarm.
Provides efficient, reusable HTTP sessions across the framework.
"""

import asyncio
from typing import Optional, Dict, Any
import aiohttp
from ..utils.logging import get_logger

logger = get_logger("utils.http_client")


class HTTPClientManager:
    """
    Centralized HTTP client manager with connection pooling.
    
    Features:
    - Connection pooling and reuse
    - Automatic session lifecycle management
    - Configurable timeouts and limits
    - Graceful cleanup on shutdown
    """
    
    _instance: Optional['HTTPClientManager'] = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        self._sessions: Dict[str, aiohttp.ClientSession] = {}
        self._default_session: Optional[aiohttp.ClientSession] = None
        self._closed = False
        
        # Default configuration
        self._default_config = {
            'timeout': aiohttp.ClientTimeout(total=30, connect=10),
            'connector': aiohttp.TCPConnector(
                limit=100,  # Total connection pool size
                limit_per_host=30,  # Per-host connection limit
                ttl_dns_cache=300,  # DNS cache TTL
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            ),
            'headers': {
                'User-Agent': 'Agent-Swarm/0.2.0'
            }
        }
    
    @classmethod
    async def get_instance(cls) -> 'HTTPClientManager':
        """Get singleton instance of HTTP client manager."""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
                    await cls._instance._initialize()
        return cls._instance
    
    async def _initialize(self) -> None:
        """Initialize the HTTP client manager."""
        logger.debug("Initializing HTTP client manager")
        # Default session will be created on first use
        
    async def get_session(
        self, 
        name: str = "default",
        **session_kwargs
    ) -> aiohttp.ClientSession:
        """
        Get or create a named HTTP session.
        
        Args:
            name: Session name for reuse
            **session_kwargs: Additional session configuration
            
        Returns:
            Configured aiohttp.ClientSession
        """
        if self._closed:
            raise RuntimeError("HTTP client manager is closed")
            
        if name not in self._sessions or self._sessions[name].closed:
            # Merge default config with custom kwargs
            config = self._default_config.copy()
            config.update(session_kwargs)
            
            logger.debug(f"Creating new HTTP session: {name}")
            self._sessions[name] = aiohttp.ClientSession(**config)
            
        return self._sessions[name]
    
    async def get_default_session(self) -> aiohttp.ClientSession:
        """Get the default HTTP session."""
        return await self.get_session("default")
    
    async def create_session(
        self,
        timeout: Optional[float] = None,
        max_connections: int = 100,
        max_connections_per_host: int = 30,
        **kwargs
    ) -> aiohttp.ClientSession:
        """
        Create a new HTTP session with custom configuration.
        
        Args:
            timeout: Request timeout in seconds
            max_connections: Maximum total connections
            max_connections_per_host: Maximum connections per host
            **kwargs: Additional aiohttp.ClientSession arguments
            
        Returns:
            Configured aiohttp.ClientSession
        """
        if self._closed:
            raise RuntimeError("HTTP client manager is closed")
            
        # Build custom configuration
        session_timeout = aiohttp.ClientTimeout(total=timeout or 30)
        connector = aiohttp.TCPConnector(
            limit=max_connections,
            limit_per_host=max_connections_per_host,
            ttl_dns_cache=300,
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        config = {
            'timeout': session_timeout,
            'connector': connector,
            'headers': {'User-Agent': 'Agent-Swarm/0.2.0'},
            **kwargs
        }
        
        return aiohttp.ClientSession(**config)
    
    async def close_session(self, name: str) -> None:
        """Close a named session."""
        if name in self._sessions and not self._sessions[name].closed:
            logger.debug(f"Closing HTTP session: {name}")
            await self._sessions[name].close()
            del self._sessions[name]
    
    async def close_all(self) -> None:
        """Close all sessions and cleanup."""
        if self._closed:
            return
            
        logger.debug("Closing all HTTP sessions")
        self._closed = True
        
        # Close all named sessions
        for name, session in list(self._sessions.items()):
            if not session.closed:
                await session.close()
        
        self._sessions.clear()
        
        # Reset singleton
        HTTPClientManager._instance = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close_all()


# Convenience functions for easy access
async def get_http_session(name: str = "default") -> aiohttp.ClientSession:
    """Get a named HTTP session with connection pooling."""
    manager = await HTTPClientManager.get_instance()
    return await manager.get_session(name)


async def create_http_session(**kwargs) -> aiohttp.ClientSession:
    """Create a new HTTP session with custom configuration."""
    manager = await HTTPClientManager.get_instance()
    return await manager.create_session(**kwargs)


async def cleanup_http_sessions() -> None:
    """Cleanup all HTTP sessions."""
    if HTTPClientManager._instance:
        await HTTPClientManager._instance.close_all()


# Context manager for temporary sessions
class TemporaryHTTPSession:
    """Context manager for temporary HTTP sessions."""
    
    def __init__(self, **session_kwargs):
        self.session_kwargs = session_kwargs
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self) -> aiohttp.ClientSession:
        """Create temporary session."""
        manager = await HTTPClientManager.get_instance()
        self.session = await manager.create_session(**self.session_kwargs)
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Close temporary session."""
        if self.session and not self.session.closed:
            await self.session.close()