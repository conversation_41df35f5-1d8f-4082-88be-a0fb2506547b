"""
Configuration management utilities for Agent Swarm.

Enhanced configuration system supporting multiple cloud AI providers,
local models, and comprehensive settings management.
"""

import json
import os
import yaml
from pathlib import Path
from typing import Dict, Optional
from enum import Enum

from pydantic import BaseModel, Field

from ..backends import LLMTier


class ProviderType(str, Enum):
    """Supported LLM provider types."""
    OLLAMA = "ollama"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    DEEPSEEK = "deepseek"
    GROQ = "groq"
    TOGETHER = "together"


class ModelConfig(BaseModel):
    """Configuration for a specific model."""
    name: str
    provider: ProviderType
    api_key_env: Optional[str] = None
    base_url: Optional[str] = None
    max_tokens: int = 4096
    temperature: float = 0.3
    timeout: int = 30
    tier: LLMTier = LLMTier.LOCAL_FAST
    enabled: bool = True


class RAGConfig(BaseModel):
    """Configuration for RAG (Retrieval-Augmented Generation) system."""
    chunk_size: int = Field(default=1000, description="Size of text chunks for indexing")
    chunk_overlap: int = Field(default=200, description="Overlap between chunks")
    vector_store: str = Field(default="memory", description="Vector store backend (memory, chromadb, pinecone)")
    embedding_model: str = Field(default="all-MiniLM-L6-v2", description="Embedding model for vector search")
    max_results: int = Field(default=5, description="Maximum search results to return")
    similarity_threshold: float = Field(default=0.7, description="Minimum similarity score for results")


class CLIConfig(BaseModel):
    """Configuration for CLI interface."""
    auto_index: bool = Field(default=False, description="Automatically index projects on startup")
    verbose: bool = Field(default=False, description="Enable verbose output")
    theme: str = Field(default="dark", description="CLI theme (dark, light)")
    max_history: int = Field(default=1000, description="Maximum command history entries")
    auto_save_config: bool = Field(default=True, description="Automatically save configuration changes")


class AgentSwarmConfig(BaseModel):
    """Main configuration for Agent Swarm."""

    # Version and metadata
    version: str = Field(default="0.2.0", description="Configuration version")

    # Model configurations
    models: Dict[str, ModelConfig] = Field(default_factory=dict, description="Available model configurations")

    # Default model settings
    default_provider: ProviderType = Field(default=ProviderType.OLLAMA, description="Default LLM provider")
    default_model: str = Field(default="deepseek-r1:7b", description="Default model name")
    fallback_model: str = Field(default="qwen2.5-coder:7b", description="Fallback model if default fails")

    # Global model settings
    max_concurrent_requests: int = Field(default=2, description="Maximum concurrent LLM requests")
    default_temperature: float = Field(default=0.3, description="Default temperature for generation")
    default_max_tokens: int = Field(default=4096, description="Default maximum tokens")
    request_timeout: int = Field(default=30, description="Request timeout in seconds")

    # Task routing configuration
    task_routing: Dict[str, str] = Field(
        default_factory=lambda: {
            "simple_coding": "qwen-coder",
            "coding": "deepseek-r1",
            "complex_coding": "deepseek-chat",
            "architecture": "claude-3-sonnet",
            "code_review": "deepseek-r1",
            "documentation": "deepseek-chat",
            "large_codebase_analysis": "gemini-pro",
            "creative_writing": "claude-3-haiku",
            "data_analysis": "gpt-4",
        },
        description="Task-specific model routing"
    )

    # Component configurations
    rag: RAGConfig = Field(default_factory=RAGConfig, description="RAG system configuration")
    cli: CLIConfig = Field(default_factory=CLIConfig, description="CLI interface configuration")

    # Logging configuration
    log_level: str = Field(default="INFO", description="Logging level")
    log_file: Optional[str] = Field(default=None, description="Log file path")
    log_format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", description="Log format")

    # Environment and paths
    config_dir: Optional[str] = Field(default=None, description="Configuration directory path")
    models_dir: Optional[str] = Field(default=None, description="Models directory path")
    cache_dir: Optional[str] = Field(default=None, description="Cache directory path")

    class Config:
        """Pydantic config."""
        env_prefix = "AGENT_SWARM_"


def get_config_dir() -> Path:
    """Get the configuration directory."""
    config_dir = Path.home() / ".agent-swarm"
    config_dir.mkdir(exist_ok=True)
    return config_dir


def get_config_file() -> Path:
    """Get the main configuration file path."""
    return get_config_dir() / "config.yaml"


def get_default_models() -> Dict[str, ModelConfig]:
    """Get default model configurations."""
    return {
        # Local Ollama models
        "deepseek-r1": ModelConfig(
            name="deepseek-r1:7b",
            provider=ProviderType.OLLAMA,
            base_url="http://localhost:11434",
            tier=LLMTier.LOCAL_QUALITY,
            max_tokens=4096,
            temperature=0.3
        ),
        "qwen-coder": ModelConfig(
            name="qwen2.5-coder:7b",
            provider=ProviderType.OLLAMA,
            base_url="http://localhost:11434",
            tier=LLMTier.LOCAL_FAST,
            max_tokens=4096,
            temperature=0.2
        ),

        # DeepSeek Cloud API
        "deepseek-chat": ModelConfig(
            name="deepseek-chat",
            provider=ProviderType.DEEPSEEK,
            api_key_env="DEEPSEEK_API_KEY",
            base_url="https://api.deepseek.com/v1",
            tier=LLMTier.CLOUD_PREMIUM,
            max_tokens=4096,
            temperature=0.3
        ),
        "deepseek-coder": ModelConfig(
            name="deepseek-coder",
            provider=ProviderType.DEEPSEEK,
            api_key_env="DEEPSEEK_API_KEY",
            base_url="https://api.deepseek.com/v1",
            tier=LLMTier.CLOUD_PREMIUM,
            max_tokens=4096,
            temperature=0.2
        ),

        # OpenAI models
        "gpt-4": ModelConfig(
            name="gpt-4",
            provider=ProviderType.OPENAI,
            api_key_env="OPENAI_API_KEY",
            tier=LLMTier.CLOUD_PREMIUM,
            max_tokens=4096,
            temperature=0.3
        ),
        "gpt-3.5-turbo": ModelConfig(
            name="gpt-3.5-turbo",
            provider=ProviderType.OPENAI,
            api_key_env="OPENAI_API_KEY",
            tier=LLMTier.CLOUD_PREMIUM,
            max_tokens=4096,
            temperature=0.3
        ),

        # Anthropic models
        "claude-3-sonnet": ModelConfig(
            name="claude-3-sonnet-20240229",
            provider=ProviderType.ANTHROPIC,
            api_key_env="ANTHROPIC_API_KEY",
            tier=LLMTier.CLOUD_PREMIUM,
            max_tokens=4096,
            temperature=0.3
        ),
        "claude-3-haiku": ModelConfig(
            name="claude-3-haiku-20240307",
            provider=ProviderType.ANTHROPIC,
            api_key_env="ANTHROPIC_API_KEY",
            tier=LLMTier.CLOUD_PREMIUM,
            max_tokens=4096,
            temperature=0.3
        ),

        # Google models
        "gemini-pro": ModelConfig(
            name="gemini-pro",
            provider=ProviderType.GOOGLE,
            api_key_env="GOOGLE_API_KEY",
            tier=LLMTier.CLOUD_PREMIUM,
            max_tokens=4096,
            temperature=0.3
        ),
    }


def get_default_config() -> AgentSwarmConfig:
    """Get default configuration with predefined models."""
    config = AgentSwarmConfig()
    config.models = get_default_models()
    return config


def load_config(config_file: Optional[Path] = None) -> AgentSwarmConfig:
    """
    Load configuration from file and environment.

    Args:
        config_file: Optional path to config file

    Returns:
        Loaded configuration
    """
    if config_file is None:
        config_file = get_config_file()

    # Start with default configuration
    config = get_default_config()
    config_data = config.model_dump()

    # Load from file if it exists
    if config_file.exists():
        try:
            with open(config_file, "r") as f:
                if config_file.suffix.lower() == '.yaml' or config_file.suffix.lower() == '.yml':
                    file_data = yaml.safe_load(f)
                else:
                    file_data = json.load(f)

                if file_data:
                    # Merge file data with defaults
                    config_data.update(file_data)

        except (json.JSONDecodeError, yaml.YAMLError, IOError) as e:
            print(f"Warning: Could not load config file {config_file}: {e}")

    # Apply environment variable overrides
    env_overrides = {}

    # Global settings
    if os.getenv("AGENT_SWARM_LOG_LEVEL"):
        env_overrides["log_level"] = os.getenv("AGENT_SWARM_LOG_LEVEL")
    if os.getenv("AGENT_SWARM_DEFAULT_PROVIDER"):
        env_overrides["default_provider"] = os.getenv("AGENT_SWARM_DEFAULT_PROVIDER")
    if os.getenv("AGENT_SWARM_DEFAULT_MODEL"):
        env_overrides["default_model"] = os.getenv("AGENT_SWARM_DEFAULT_MODEL")

    # Apply overrides
    for key, value in env_overrides.items():
        if value is not None:
            config_data[key] = value

    try:
        return AgentSwarmConfig(**config_data)
    except Exception as e:
        print(f"Warning: Invalid configuration, using defaults: {e}")
        return get_default_config()


def save_config(config: AgentSwarmConfig, config_file: Optional[Path] = None, format: str = "yaml") -> None:
    """
    Save configuration to file.

    Args:
        config: Configuration to save
        config_file: Optional path to config file
        format: File format ('yaml' or 'json')
    """
    if config_file is None:
        config_file = get_config_file()

    # Prepare data for saving (don't save sensitive data)
    config_dict = config.model_dump()

    # Remove API keys from models (they should be in environment)
    if "models" in config_dict:
        for model_name, model_data in config_dict["models"].items():
            if "api_key_env" in model_data:
                # Keep the env var name but not the actual key
                pass

    config_file.parent.mkdir(parents=True, exist_ok=True)

    with open(config_file, "w") as f:
        if format.lower() == "yaml" or config_file.suffix.lower() in ['.yaml', '.yml']:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
        else:
            json.dump(config_dict, f, indent=2)


def validate_api_key(provider: ProviderType, api_key: str) -> bool:
    """
    Validate an API key for a given provider.

    Args:
        provider: The LLM provider
        api_key: The API key to validate

    Returns:
        True if the API key appears valid
    """
    if not api_key or not api_key.strip():
        return False

    # Basic validation based on known patterns
    if provider == ProviderType.DEEPSEEK:
        return api_key.startswith("sk-") and len(api_key) > 20
    elif provider == ProviderType.OPENAI:
        return api_key.startswith("sk-") and len(api_key) > 40
    elif provider == ProviderType.ANTHROPIC:
        return api_key.startswith("sk-ant-") and len(api_key) > 40
    elif provider == ProviderType.GOOGLE:
        return len(api_key) > 20  # Google API keys vary in format

    return True  # For unknown providers, assume valid


def get_api_key(provider: ProviderType, model_config: ModelConfig) -> Optional[str]:
    """
    Get API key for a provider from environment or config.

    Args:
        provider: The LLM provider
        model_config: Model configuration

    Returns:
        API key if found, None otherwise
    """
    if model_config.api_key_env:
        return os.getenv(model_config.api_key_env)

    # Fallback to common environment variable names
    env_map = {
        ProviderType.DEEPSEEK: "DEEPSEEK_API_KEY",
        ProviderType.OPENAI: "OPENAI_API_KEY",
        ProviderType.ANTHROPIC: "ANTHROPIC_API_KEY",
        ProviderType.GOOGLE: "GOOGLE_API_KEY",
    }

    if provider in env_map:
        return os.getenv(env_map[provider])

    return None


def create_example_config() -> None:
    """Create example configuration and environment files."""
    config_dir = get_config_dir()

    # Create example YAML config
    example_file = config_dir / "config.example.yaml"
    default_config = get_default_config()

    with open(example_file, "w") as f:
        yaml.dump(default_config.model_dump(), f, default_flow_style=False, indent=2)

    # Create .env example with DeepSeek support
    env_example = config_dir / ".env.example"
    env_content = """# Agent Swarm Environment Variables
# Copy this file to .env and fill in your API keys

# DeepSeek API (Recommended for coding tasks)
DEEPSEEK_API_KEY=sk-your_deepseek_api_key_here

# Other Cloud LLM API Keys (optional)
OPENAI_API_KEY=sk-your_openai_api_key_here
ANTHROPIC_API_KEY=sk-ant-your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# Local LLM Configuration
OLLAMA_BASE_URL=http://localhost:11434

# Agent Swarm Configuration
AGENT_SWARM_DEFAULT_PROVIDER=deepseek
AGENT_SWARM_DEFAULT_MODEL=deepseek-chat
AGENT_SWARM_LOG_LEVEL=INFO

# Optional: Custom paths
AGENT_SWARM_CONFIG_PATH=~/.agent-swarm/config.yaml
AGENT_SWARM_MODELS_PATH=/mnt/dev8/Models
"""

    with open(env_example, "w") as f:
        f.write(env_content)

    print(f"✅ Example configuration created at {example_file}")
    print(f"✅ Example environment file created at {env_example}")
    print(f"💡 Copy {env_example} to .env and add your API keys")
