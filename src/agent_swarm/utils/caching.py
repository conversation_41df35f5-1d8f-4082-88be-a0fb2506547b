"""
Enhanced caching strategy for Agent Swarm.
Provides multiple caching backends with TTL, LRU, and async support.
"""

import asyncio
import time
import hashlib
import pickle
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Union, List
from pathlib import Path
from collections import OrderedDict
from dataclasses import dataclass
from enum import Enum

from ..utils.logging import get_logger

logger = get_logger("utils.caching")


class CacheBackend(Enum):
    """Available cache backends."""
    MEMORY = "memory"
    DISK = "disk"
    REDIS = "redis"  # Future implementation
    HYBRID = "hybrid"


@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    value: Any
    created_at: float
    accessed_at: float
    ttl: Optional[float] = None
    access_count: int = 0
    size_bytes: int = 0
    
    @property
    def is_expired(self) -> bool:
        """Check if entry is expired."""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    @property
    def age(self) -> float:
        """Get age in seconds."""
        return time.time() - self.created_at


class CacheBackendInterface(ABC):
    """Abstract interface for cache backends."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        pass
    
    @abstractmethod
    async def put(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """Put value in cache."""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete key from cache."""
        pass
    
    @abstractmethod
    async def clear(self) -> None:
        """Clear all cache entries."""
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        pass


class MemoryCacheBackend(CacheBackendInterface):
    """In-memory cache with LRU eviction and TTL support."""
    
    def __init__(self, max_size: int = 1000, default_ttl: Optional[float] = None):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = asyncio.Lock()
        
        # Statistics
        self._hits = 0
        self._misses = 0
        self._evictions = 0
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from memory cache."""
        async with self._lock:
            if key not in self._cache:
                self._misses += 1
                return None
            
            entry = self._cache[key]
            
            # Check expiration
            if entry.is_expired:
                del self._cache[key]
                self._misses += 1
                return None
            
            # Update access info and move to end (LRU)
            entry.accessed_at = time.time()
            entry.access_count += 1
            self._cache.move_to_end(key)
            
            self._hits += 1
            return entry.value
    
    async def put(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """Put value in memory cache."""
        async with self._lock:
            # Calculate size (approximate)
            try:
                size_bytes = len(pickle.dumps(value))
            except Exception:
                size_bytes = 0
            
            # Create entry
            entry = CacheEntry(
                value=value,
                created_at=time.time(),
                accessed_at=time.time(),
                ttl=ttl or self.default_ttl,
                size_bytes=size_bytes
            )
            
            # Add to cache
            self._cache[key] = entry
            self._cache.move_to_end(key)
            
            # Evict if necessary
            await self._evict_if_needed()
    
    async def delete(self, key: str) -> bool:
        """Delete key from memory cache."""
        async with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        async with self._lock:
            self._cache.clear()
            self._hits = 0
            self._misses = 0
            self._evictions = 0
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        async with self._lock:
            if key not in self._cache:
                return False
            
            entry = self._cache[key]
            if entry.is_expired:
                del self._cache[key]
                return False
            
            return True
    
    async def _evict_if_needed(self) -> None:
        """Evict entries if cache is full."""
        while len(self._cache) > self.max_size:
            # Remove oldest entry (LRU)
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]
            self._evictions += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self._hits + self._misses
        hit_rate = self._hits / total_requests if total_requests > 0 else 0
        
        total_size = sum(entry.size_bytes for entry in self._cache.values())
        
        return {
            "backend": "memory",
            "size": len(self._cache),
            "max_size": self.max_size,
            "hits": self._hits,
            "misses": self._misses,
            "hit_rate": hit_rate,
            "evictions": self._evictions,
            "total_size_bytes": total_size,
            "avg_entry_size": total_size / len(self._cache) if self._cache else 0
        }


class DiskCacheBackend(CacheBackendInterface):
    """Disk-based cache with persistence."""
    
    def __init__(self, cache_dir: Path, max_size_mb: int = 100):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self._lock = asyncio.Lock()
        
        # Statistics
        self._hits = 0
        self._misses = 0
    
    def _get_file_path(self, key: str) -> Path:
        """Get file path for cache key."""
        # Hash key to create safe filename
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.cache"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from disk cache."""
        file_path = self._get_file_path(key)
        
        if not file_path.exists():
            self._misses += 1
            return None
        
        try:
            async with self._lock:
                with open(file_path, 'rb') as f:
                    entry_data = pickle.load(f)
                
                entry = CacheEntry(**entry_data)
                
                # Check expiration
                if entry.is_expired:
                    file_path.unlink(missing_ok=True)
                    self._misses += 1
                    return None
                
                # Update access time
                entry.accessed_at = time.time()
                entry.access_count += 1
                
                # Save updated entry
                with open(file_path, 'wb') as f:
                    pickle.dump(entry.__dict__, f)
                
                self._hits += 1
                return entry.value
                
        except Exception as e:
            logger.warning(f"Error reading cache file {file_path}: {e}")
            file_path.unlink(missing_ok=True)
            self._misses += 1
            return None
    
    async def put(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """Put value in disk cache."""
        file_path = self._get_file_path(key)
        
        try:
            # Create entry
            entry = CacheEntry(
                value=value,
                created_at=time.time(),
                accessed_at=time.time(),
                ttl=ttl
            )
            
            async with self._lock:
                with open(file_path, 'wb') as f:
                    pickle.dump(entry.__dict__, f)
                
                # Update size
                entry.size_bytes = file_path.stat().st_size
                
                # Check if we need to evict
                await self._evict_if_needed()
                
        except Exception as e:
            logger.error(f"Error writing cache file {file_path}: {e}")
    
    async def delete(self, key: str) -> bool:
        """Delete key from disk cache."""
        file_path = self._get_file_path(key)
        if file_path.exists():
            file_path.unlink()
            return True
        return False
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        async with self._lock:
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink(missing_ok=True)
            self._hits = 0
            self._misses = 0
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in disk cache."""
        file_path = self._get_file_path(key)
        return file_path.exists()
    
    async def _evict_if_needed(self) -> None:
        """Evict old files if cache is too large."""
        total_size = sum(f.stat().st_size for f in self.cache_dir.glob("*.cache"))
        
        if total_size <= self.max_size_bytes:
            return
        
        # Get all cache files with their access times
        cache_files = []
        for cache_file in self.cache_dir.glob("*.cache"):
            try:
                with open(cache_file, 'rb') as f:
                    entry_data = pickle.load(f)
                cache_files.append((cache_file, entry_data.get('accessed_at', 0)))
            except Exception:
                # Remove corrupted files
                cache_file.unlink(missing_ok=True)
        
        # Sort by access time (oldest first)
        cache_files.sort(key=lambda x: x[1])
        
        # Remove oldest files until under limit
        for cache_file, _ in cache_files:
            cache_file.unlink(missing_ok=True)
            total_size -= cache_file.stat().st_size if cache_file.exists() else 0
            if total_size <= self.max_size_bytes:
                break
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self._hits + self._misses
        hit_rate = self._hits / total_requests if total_requests > 0 else 0
        
        cache_files = list(self.cache_dir.glob("*.cache"))
        total_size = sum(f.stat().st_size for f in cache_files if f.exists())
        
        return {
            "backend": "disk",
            "size": len(cache_files),
            "hits": self._hits,
            "misses": self._misses,
            "hit_rate": hit_rate,
            "total_size_bytes": total_size,
            "max_size_bytes": self.max_size_bytes,
            "cache_dir": str(self.cache_dir)
        }


class CacheManager:
    """High-level cache manager with multiple backends."""
    
    def __init__(
        self,
        backend: CacheBackend = CacheBackend.MEMORY,
        memory_max_size: int = 1000,
        disk_cache_dir: Optional[Path] = None,
        disk_max_size_mb: int = 100,
        default_ttl: Optional[float] = None
    ):
        self.backend_type = backend
        self.default_ttl = default_ttl
        
        if backend == CacheBackend.MEMORY:
            self._backend = MemoryCacheBackend(memory_max_size, default_ttl)
        elif backend == CacheBackend.DISK:
            cache_dir = disk_cache_dir or Path.cwd() / ".cache"
            self._backend = DiskCacheBackend(cache_dir, disk_max_size_mb)
        elif backend == CacheBackend.HYBRID:
            # Use memory as L1, disk as L2
            self._memory_backend = MemoryCacheBackend(memory_max_size // 2, default_ttl)
            cache_dir = disk_cache_dir or Path.cwd() / ".cache"
            self._disk_backend = DiskCacheBackend(cache_dir, disk_max_size_mb)
        else:
            raise ValueError(f"Unsupported cache backend: {backend}")
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if self.backend_type == CacheBackend.HYBRID:
            # Try memory first, then disk
            value = await self._memory_backend.get(key)
            if value is not None:
                return value
            
            value = await self._disk_backend.get(key)
            if value is not None:
                # Promote to memory cache
                await self._memory_backend.put(key, value, self.default_ttl)
            return value
        else:
            return await self._backend.get(key)
    
    async def put(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """Put value in cache."""
        if self.backend_type == CacheBackend.HYBRID:
            # Store in both memory and disk
            await self._memory_backend.put(key, value, ttl)
            await self._disk_backend.put(key, value, ttl)
        else:
            await self._backend.put(key, value, ttl)
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache."""
        if self.backend_type == CacheBackend.HYBRID:
            memory_deleted = await self._memory_backend.delete(key)
            disk_deleted = await self._disk_backend.delete(key)
            return memory_deleted or disk_deleted
        else:
            return await self._backend.delete(key)
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        if self.backend_type == CacheBackend.HYBRID:
            await self._memory_backend.clear()
            await self._disk_backend.clear()
        else:
            await self._backend.clear()
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        if self.backend_type == CacheBackend.HYBRID:
            return (await self._memory_backend.exists(key) or 
                   await self._disk_backend.exists(key))
        else:
            return await self._backend.exists(key)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        if self.backend_type == CacheBackend.HYBRID:
            return {
                "backend": "hybrid",
                "memory": self._memory_backend.get_stats(),
                "disk": self._disk_backend.get_stats()
            }
        else:
            return self._backend.get_stats()


# Global cache manager instance
_global_cache: Optional[CacheManager] = None


def get_cache_manager() -> CacheManager:
    """Get global cache manager instance."""
    global _global_cache
    if _global_cache is None:
        _global_cache = CacheManager()
    return _global_cache


def configure_cache(
    backend: CacheBackend = CacheBackend.MEMORY,
    **kwargs
) -> CacheManager:
    """Configure global cache manager."""
    global _global_cache
    _global_cache = CacheManager(backend, **kwargs)
    return _global_cache


# Decorator for caching function results
def cached(
    ttl: Optional[float] = None,
    key_func: Optional[callable] = None
):
    """Decorator to cache function results."""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            cache = get_cache_manager()
            
            # Generate cache key
            if key_func:
                key = key_func(*args, **kwargs)
            else:
                key = f"{func.__name__}:{hash((args, tuple(sorted(kwargs.items()))))}"
            
            # Try to get from cache
            result = await cache.get(key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache.put(key, result, ttl)
            return result
        
        def sync_wrapper(*args, **kwargs):
            # For sync functions, we can't use async cache directly
            # This would need a sync cache implementation
            return func(*args, **kwargs)
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator