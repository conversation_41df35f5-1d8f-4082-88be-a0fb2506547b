"""
Unified Context Engine for Agent Swarm.
Orchestrates smart project analysis, RAG retrieval, and development features.
"""

from __future__ import annotations

import asyncio
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from ..utils.logging import get_logger

# Import all the existing components
from .context_engine import (
    ContextEngine as SmartEngine,
    QueryIntent, ResponseMode, ContextDepth,
    SmartContextResult, ProjectStructure
)
from .rag_system import RAGSystem, create_rag_system, DocumentChunk
from .dev_rag import DevelopmentRAG, setup_project_rag

logger = get_logger("context.unified")


class UnifiedContext:
    """
    Unified context system that orchestrates all context capabilities.

    Combines:
    - Smart project analysis and intent detection
    - Modern RAG retrieval with multiple backends
    - Development-specific features and code analysis
    """

    def __init__(self, project_path: Optional[str] = None, config: Optional[Dict[str, Any]] = None):
        self.project_path = Path(project_path) if project_path else None
        self.config = config or {}

        # Core components
        self.smart_engine = SmartEngine()  # Always available
        self.rag_system: Optional[RAGSystem] = None
        self.dev_rag: Optional[DevelopmentRAG] = None

        # State
        self._initialized = False
        self.project_structure: Optional[ProjectStructure] = None
    
    @property
    def initialized(self) -> bool:
        """Check if the context is initialized."""
        return self._initialized

    async def initialize(
        self,
        project_path: Optional[str] = None,
        enable_rag: bool = True,
        enable_dev_features: bool = True,
        rag_backend: str = "memory",
        auto_index: bool = True
    ) -> None:
        """
        Initialize the unified context system.

        Args:
            project_path: Path to project for analysis
            enable_rag: Enable RAG retrieval system
            enable_dev_features: Enable development-specific features
            rag_backend: RAG backend to use (memory, chroma, pinecone)
            auto_index: Automatically index project files
        """
        if self._initialized:
            return

        # Set project path
        if project_path:
            self.project_path = Path(project_path)

        logger.info(f"Initializing unified context system for: {self.project_path}")

        # Initialize smart engine (always enabled)
        logger.debug("UnifiedContext: Initializing smart engine...")
        await self.smart_engine.initialize(str(self.project_path) if self.project_path else None)
        self.project_structure = self.smart_engine.project_structure
        logger.debug(f"UnifiedContext: Smart engine initialized, project_structure: {self.project_structure is not None}")

        # Initialize RAG system
        if enable_rag and self.project_path:
            logger.debug(f"UnifiedContext: Initializing RAG system, enable_dev_features={enable_dev_features}")
            if enable_dev_features:
                # Use development RAG for code-aware features
                logger.debug("UnifiedContext: Setting up development RAG...")
                self.dev_rag = await setup_project_rag(
                    str(self.project_path),
                    auto_index=auto_index
                )
                self.rag_system = self.dev_rag.rag_system
                logger.debug(f"UnifiedContext: Development RAG setup complete, rag_system: {self.rag_system is not None}")
            else:
                # Use basic RAG system
                logger.debug("UnifiedContext: Setting up basic RAG system...")
                self.rag_system = await create_rag_system(vector_store=rag_backend)
                logger.debug(f"UnifiedContext: Basic RAG system setup complete, rag_system: {self.rag_system is not None}")

            # Register RAG as a provider for smart engine
            if self.rag_system:
                logger.debug("UnifiedContext: Registering RAG provider with smart engine...")
                from .providers.rag_provider import RAGProvider
                rag_provider = RAGProvider(self.rag_system)
                self.smart_engine.register_provider("rag", rag_provider)
                logger.debug("UnifiedContext: RAG provider registered successfully")
            else:
                logger.warning("UnifiedContext: No RAG system available to register as provider")
        else:
            logger.debug(f"UnifiedContext: Skipping RAG initialization (enable_rag={enable_rag}, project_path={self.project_path is not None})")

        self._initialized = True
        logger.info("Unified context system initialized successfully")

    async def get_context(
        self,
        query: str,
        intent: Union[str, QueryIntent] = QueryIntent.GENERAL,
        response_mode: Union[str, ResponseMode] = ResponseMode.DETAILED,
        context_depth: Union[str, ContextDepth] = ContextDepth.MEDIUM,
        max_results: Optional[int] = None,
        include_code: bool = True,
        include_docs: bool = True,
        include_tests: bool = False,
        language: Optional[str] = None,
        file_pattern: Optional[str] = None
    ) -> List[SmartContextResult]:
        """
        Get intelligent context for a query using all available systems.

        Args:
            query: The query to search for
            intent: Intent behind the query (debug, feature, understand, etc.)
            response_mode: Speed vs detail tradeoff (fast, detailed, comprehensive)
            context_depth: Depth of analysis (shallow, medium, deep)
            max_results: Maximum number of results to return
            include_code: Include code files in results
            include_docs: Include documentation in results
            include_tests: Include test files in results
            language: Filter by programming language
            file_pattern: Filter by file path pattern

        Returns:
            List of smart context results with prioritization and analysis
        """
        if not self._initialized:
            await self.initialize()

        # Convert string enums to proper types
        if isinstance(intent, str):
            intent = QueryIntent(intent)
        if isinstance(response_mode, str):
            response_mode = ResponseMode(response_mode)
        if isinstance(context_depth, str):
            context_depth = ContextDepth(context_depth)

        logger.info(f"Getting context for: '{query}' with intent: {intent.value}")

        # Use smart engine if available (preferred)
        if self.smart_engine and self.project_structure:
            results = await self.smart_engine.get_smart_context(
                query=query,
                intent=intent,
                response_mode=response_mode,
                context_depth=context_depth,
                max_results=max_results,
                include_tests=include_tests,
                include_docs=include_docs
            )

            # Apply additional filters if specified
            if language or file_pattern:
                results = self._apply_filters(results, language, file_pattern)

            return results

        # Fallback to development RAG if available
        elif self.dev_rag:
            chunks = await self.dev_rag.search_code(
                query=query,
                language=language,
                file_type="code" if include_code else None,
                file_pattern=file_pattern
            )

            # Convert to smart context results
            return self._convert_chunks_to_smart_results(chunks, query)

        # Fallback to basic RAG
        elif self.rag_system:
            result = await self.rag_system.retrieve(query, top_k=max_results or 5)
            return self._convert_chunks_to_smart_results(result.chunks, query)

        else:
            logger.warning("No context systems available")
            return []

    async def search_code(
        self,
        query: str,
        language: Optional[str] = None,
        file_type: Optional[str] = None,
        file_pattern: Optional[str] = None,
        max_results: int = 10
    ) -> List[DocumentChunk]:
        """
        Search specifically for code with development-aware features.

        Args:
            query: Code search query
            language: Programming language filter
            file_type: File type filter (code, documentation, config)
            file_pattern: File path pattern filter
            max_results: Maximum results to return

        Returns:
            List of document chunks with code-specific metadata
        """
        if not self._initialized:
            await self.initialize()

        if self.dev_rag:
            return await self.dev_rag.search_code(
                query=query,
                language=language,
                file_type=file_type,
                file_pattern=file_pattern
            )
        elif self.rag_system:
            result = await self.rag_system.retrieve(query, top_k=max_results)
            return result.chunks
        else:
            return []

    async def get_project_analysis(self) -> Optional[ProjectStructure]:
        """Get comprehensive project structure analysis."""
        if not self._initialized:
            await self.initialize()

        return self.project_structure

    async def get_related_files(self, file_path: str) -> List[DocumentChunk]:
        """Find files related to the given file."""
        if not self._initialized:
            await self.initialize()

        if self.dev_rag:
            return await self.dev_rag.get_related_files(file_path)
        else:
            return []

    async def explain_code(self, code_snippet: str, context_file: Optional[str] = None) -> str:
        """Get explanation for code snippet using project context."""
        if not self._initialized:
            await self.initialize()

        if self.dev_rag:
            return await self.dev_rag.explain_code(code_snippet, context_file)
        else:
            return f"Code explanation not available (no development RAG system)"

    async def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics about the context system."""
        if not self._initialized:
            return {"initialized": False}

        stats = {
            "initialized": True,
            "project_path": str(self.project_path) if self.project_path else None,
            "components": {
                "smart_engine": self.smart_engine is not None,
                "rag_system": self.rag_system is not None,
                "dev_rag": self.dev_rag is not None
            }
        }

        # Add project structure info
        if self.project_structure:
            stats["project"] = {
                "type": self.project_structure.project_type.value,
                "language": self.project_structure.main_language,
                "frameworks": self.project_structure.frameworks,
                "confidence": self.project_structure.confidence_score
            }

        # Add RAG stats
        if self.dev_rag:
            dev_stats = await self.dev_rag.get_project_stats()
            stats["indexing"] = dev_stats
        elif self.rag_system:
            rag_stats = await self.rag_system.get_stats()
            stats["rag"] = rag_stats

        return stats

    def _apply_filters(
        self,
        results: List[SmartContextResult],
        language: Optional[str],
        file_pattern: Optional[str]
    ) -> List[SmartContextResult]:
        """Apply additional filters to smart context results."""
        filtered = []

        for result in results:
            # Language filter
            if language and language not in result.file_path.lower():
                continue

            # File pattern filter
            if file_pattern and file_pattern not in result.file_path:
                continue

            filtered.append(result)

        return filtered

    def _convert_chunks_to_smart_results(
        self,
        chunks: List[DocumentChunk],
        query: str
    ) -> List[SmartContextResult]:
        """Convert basic document chunks to smart context results."""
        from .context_engine import ContextPriority

        results = []
        for chunk in chunks:
            result = SmartContextResult(
                content=chunk.content,
                file_path=chunk.metadata.get('file_path', 'unknown'),
                relevance_score=chunk.score or 0.0,
                priority=ContextPriority.MEDIUM,
                context_type=chunk.metadata.get('file_type', 'general'),
                summary=f"Content from {chunk.metadata.get('file_path', 'unknown')}",
                key_concepts=[],
                related_files=[],
                metadata=chunk.metadata
            )
            results.append(result)

        return results


# Convenience functions for backward compatibility
async def create_unified_context(
    project_path: str,
    enable_rag: bool = True,
    enable_dev_features: bool = True,
    auto_index: bool = True
) -> UnifiedContext:
    """Create and initialize a unified context system."""
    context = UnifiedContext(project_path)
    await context.initialize(
        enable_rag=enable_rag,
        enable_dev_features=enable_dev_features,
        auto_index=auto_index
    )
    return context
