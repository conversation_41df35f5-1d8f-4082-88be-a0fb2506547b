"""
RAG (Retrieval-Augmented Generation) components for Agent Swarm.

This module provides:
- Core RAG system with pluggable backends
- Development-specific RAG features
- Code-aware indexing and retrieval
"""

# Import from the parent directory for now (backward compatibility)
from ..rag_system import (
    RAGSystem, RAGConfig, RAGResult, RAGBackend, RAGStrategy,
    DocumentChunk, InMemoryRAGBackend, create_rag_system
)

from ..dev_rag import (
    DevelopmentRAG, create_development_rag, setup_project_rag
)

__all__ = [
    # Core RAG
    "RAGSystem", "RAGConfig", "RAGResult", "RAGBackend", "RAGStrategy",
    "DocumentChunk", "InMemoryRAGBackend", "create_rag_system",
    
    # Development RAG
    "DevelopmentRAG", "create_development_rag", "setup_project_rag"
]
