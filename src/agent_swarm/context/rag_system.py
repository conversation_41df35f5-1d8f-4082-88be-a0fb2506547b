"""
Modern RAG (Retrieval-Augmented Generation) System for Agent Swarm.
Flexible, pluggable architecture supporting multiple backends and strategies.
"""

from __future__ import annotations

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field

from ..utils.logging import get_logger

logger = get_logger("context.rag")


class RAGStrategy(str, Enum):
    """RAG retrieval strategies."""
    SEMANTIC = "semantic"
    KEYWORD = "keyword"
    HYBRID = "hybrid"
    GRAPH = "graph"
    RERANK = "rerank"


@dataclass
class DocumentChunk:
    """A chunk of a document with metadata."""
    content: str
    metadata: Dict[str, Any]
    chunk_id: str
    document_id: str
    embedding: Optional[List[float]] = None
    score: Optional[float] = None


class RAGConfig(BaseModel):
    """Configuration for RAG system."""

    # Retrieval settings
    strategy: RAGStrategy = RAGStrategy.HYBRID
    top_k: int = Field(default=5, description="Number of chunks to retrieve")
    similarity_threshold: float = Field(default=0.1, description="Minimum similarity score")

    # Chunking settings
    chunk_size: int = Field(default=1000, description="Maximum chunk size in characters")
    chunk_overlap: int = Field(default=200, description="Overlap between chunks")

    # Embedding settings
    embedding_model: str = Field(default="sentence-transformers/all-MiniLM-L6-v2")
    embedding_dimension: int = Field(default=384, description="Embedding vector dimension")

    # Vector store settings
    vector_store_type: str = Field(default="chroma", description="Vector store backend")
    vector_store_config: Dict[str, Any] = Field(default_factory=dict)

    # Reranking settings
    use_reranking: bool = Field(default=False, description="Enable reranking")
    rerank_model: str = Field(default="cross-encoder/ms-marco-MiniLM-L-6-v2")

    # Caching settings
    enable_cache: bool = Field(default=True, description="Enable result caching")
    cache_ttl: int = Field(default=3600, description="Cache TTL in seconds")


class RAGResult(BaseModel):
    """Result from RAG retrieval."""

    query: str = Field(..., description="Original query")
    chunks: List[DocumentChunk] = Field(..., description="Retrieved chunks")
    total_chunks: int = Field(..., description="Total chunks found")
    retrieval_time: float = Field(..., description="Time taken for retrieval")
    strategy_used: RAGStrategy = Field(..., description="Strategy used for retrieval")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class RAGBackend(ABC):
    """Abstract base class for RAG backends."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the RAG backend."""
        pass

    @abstractmethod
    async def add_documents(self, documents: List[DocumentChunk]) -> None:
        """Add documents to the RAG system."""
        pass

    @abstractmethod
    async def retrieve(self, query: str, config: RAGConfig) -> RAGResult:
        """Retrieve relevant documents for a query."""
        pass

    @abstractmethod
    async def update_document(self, document: DocumentChunk) -> None:
        """Update an existing document."""
        pass

    @abstractmethod
    async def delete_document(self, document_id: str) -> None:
        """Delete a document from the system."""
        pass

    @abstractmethod
    async def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the RAG system."""
        pass


class RAGSystem:
    """Main RAG system orchestrator."""

    def __init__(self, config: RAGConfig, backend: Optional[RAGBackend] = None):
        self.config = config
        self.backend = backend
        self._cache: Dict[str, RAGResult] = {}
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the RAG system."""
        if self._initialized:
            return

        if self.backend is None:
            self.backend = await self._create_default_backend()

        await self.backend.initialize()
        self._initialized = True
        logger.info("RAG system initialized")

    async def _create_default_backend(self) -> RAGBackend:
        """Create default RAG backend based on config."""
        if self.config.vector_store_type == "chroma":
            from .backends.chroma_backend import ChromaRAGBackend
            return ChromaRAGBackend(self.config)
        elif self.config.vector_store_type == "pinecone":
            from .backends.pinecone_backend import PineconeRAGBackend
            return PineconeRAGBackend(self.config)
        elif self.config.vector_store_type == "local":
            from .backends.local_backend import LocalRAGBackend
            return LocalRAGBackend(self.config)
        else:
            # Default to in-memory backend
            return InMemoryRAGBackend(self.config)

    async def add_documents(
        self,
        documents: List[str],
        metadata: Optional[List[Dict[str, Any]]] = None
    ) -> None:
        """Add documents to the RAG system."""
        if not self._initialized:
            await self.initialize()

        # Chunk documents
        chunks = await self._chunk_documents(documents, metadata or [])

        # Add to backend
        await self.backend.add_documents(chunks)

        logger.info(f"Added {len(chunks)} chunks from {len(documents)} documents")

    async def retrieve(self, query: str, **kwargs) -> RAGResult:
        """Retrieve relevant documents for a query."""
        if not self._initialized:
            await self.initialize()

        # Check cache
        cache_key = f"{query}:{hash(str(kwargs))}"
        if self.config.enable_cache and cache_key in self._cache:
            logger.debug(f"Cache hit for query: {query}")
            return self._cache[cache_key]

        # Create config with overrides
        config = self.config.model_copy()
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)

        # Retrieve from backend
        result = await self.backend.retrieve(query, config)

        # Cache result
        if self.config.enable_cache:
            self._cache[cache_key] = result

        logger.info(f"Retrieved {len(result.chunks)} chunks for query: {query}")
        return result

    async def _chunk_documents(
        self,
        documents: List[str],
        metadata: List[Dict[str, Any]]
    ) -> List[DocumentChunk]:
        """Chunk documents into smaller pieces."""
        chunks = []

        for i, doc in enumerate(documents):
            doc_metadata = metadata[i] if i < len(metadata) else {}
            doc_id = doc_metadata.get("id", f"doc_{i}")

            # Simple chunking strategy
            chunk_size = self.config.chunk_size
            overlap = self.config.chunk_overlap

            for j in range(0, len(doc), chunk_size - overlap):
                chunk_content = doc[j:j + chunk_size]

                if len(chunk_content.strip()) < 50:  # Skip very small chunks
                    continue

                chunk = DocumentChunk(
                    content=chunk_content,
                    metadata={
                        **doc_metadata,
                        "chunk_index": j // (chunk_size - overlap),
                        "start_char": j,
                        "end_char": min(j + chunk_size, len(doc))
                    },
                    chunk_id=f"{doc_id}_chunk_{j}",
                    document_id=doc_id
                )
                chunks.append(chunk)

        return chunks

    async def get_context_for_task(
        self,
        task: str,
        task_type: str = "general"
    ) -> RAGResult:
        """Get relevant context for a specific task."""
        # Enhance query based on task type
        enhanced_queries = {
            "coding": f"code implementation examples for: {task}",
            "debugging": f"error handling and debugging for: {task}",
            "testing": f"test cases and testing strategies for: {task}",
            "documentation": f"documentation and examples for: {task}",
            "architecture": f"system design and architecture for: {task}"
        }

        query = enhanced_queries.get(task_type, task)
        return await self.retrieve(query, strategy=RAGStrategy.HYBRID)

    async def get_stats(self) -> Dict[str, Any]:
        """Get RAG system statistics."""
        if not self._initialized:
            return {"initialized": False}

        backend_stats = await self.backend.get_stats()

        return {
            "initialized": True,
            "config": self.config.model_dump(),
            "cache_size": len(self._cache),
            "backend_stats": backend_stats
        }

    async def clear_cache(self) -> None:
        """Clear the result cache."""
        self._cache.clear()
        logger.info("RAG cache cleared")


class InMemoryRAGBackend(RAGBackend):
    """Simple in-memory RAG backend for development and testing."""

    def __init__(self, config: RAGConfig):
        self.config = config
        self.documents: List[DocumentChunk] = []
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the in-memory backend."""
        self._initialized = True
        logger.info("In-memory RAG backend initialized")

    async def add_documents(self, documents: List[DocumentChunk]) -> None:
        """Add documents to memory."""
        self.documents.extend(documents)
        logger.debug(f"Added {len(documents)} documents to memory")

    async def retrieve(self, query: str, config: RAGConfig) -> RAGResult:
        """Retrieve documents using simple text matching."""
        import time
        start_time = time.time()

        # Simple keyword-based retrieval
        query_words = set(query.lower().split())
        scored_chunks = []

        for chunk in self.documents:
            content_words = set(chunk.content.lower().split())

            # Calculate simple overlap score
            overlap = len(query_words.intersection(content_words))
            if overlap > 0:
                score = overlap / len(query_words)
                if score >= config.similarity_threshold:
                    chunk.score = score
                    scored_chunks.append(chunk)

        # Sort by score and take top_k
        scored_chunks.sort(key=lambda x: x.score or 0, reverse=True)
        top_chunks = scored_chunks[:config.top_k]

        retrieval_time = time.time() - start_time

        return RAGResult(
            query=query,
            chunks=top_chunks,
            total_chunks=len(scored_chunks),
            retrieval_time=retrieval_time,
            strategy_used=config.strategy,
            metadata={"backend": "in_memory"}
        )

    async def update_document(self, document: DocumentChunk) -> None:
        """Update an existing document."""
        for i, doc in enumerate(self.documents):
            if doc.chunk_id == document.chunk_id:
                self.documents[i] = document
                return
        # If not found, add it
        await self.add_documents([document])

    async def delete_document(self, document_id: str) -> None:
        """Delete a document from memory."""
        self.documents = [
            doc for doc in self.documents
            if doc.document_id != document_id
        ]

    async def get_stats(self) -> Dict[str, Any]:
        """Get backend statistics."""
        return {
            "backend_type": "in_memory",
            "total_documents": len(self.documents),
            "total_chunks": len(self.documents),
            "initialized": self._initialized
        }


# Factory function for easy setup
async def create_rag_system(
    vector_store: str = "memory",
    embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2",
    **config_kwargs
) -> RAGSystem:
    """Create a RAG system with sensible defaults."""

    config = RAGConfig(
        vector_store_type=vector_store,
        embedding_model=embedding_model,
        **config_kwargs
    )

    rag_system = RAGSystem(config)
    await rag_system.initialize()

    return rag_system


# Convenience functions
async def setup_codebase_rag(codebase_path: str) -> RAGSystem:
    """Set up RAG system for a codebase."""
    rag = await create_rag_system(
        strategy=RAGStrategy.HYBRID,
        chunk_size=800,
        chunk_overlap=100,
        top_k=10
    )

    await rag.add_codebase(codebase_path)
    return rag


async def setup_documentation_rag(docs_path: str) -> RAGSystem:
    """Set up RAG system for documentation."""
    rag = await create_rag_system(
        strategy=RAGStrategy.SEMANTIC,
        chunk_size=1500,
        chunk_overlap=300,
        top_k=5
    )

    from .document_loaders import FileSystemLoader
    loader = FileSystemLoader()
    documents = await loader.load(docs_path)

    await rag.add_documents(
        [doc.content for doc in documents],
        [doc.metadata for doc in documents]
    )

    return rag
