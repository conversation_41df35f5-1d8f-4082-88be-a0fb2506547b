"""
Unified Context System for Agent Swarm.

This module provides intelligent context understanding with:
- Smart project analysis and intent detection
- Modern RAG retrieval with multiple backends
- Development-specific features and code analysis

Usage:
    # Simple unified interface (recommended)
    from agent_swarm.context import UnifiedContext

    context = UnifiedContext(project_path)
    await context.initialize()
    results = await context.get_context("explain the codebase")

    # Backward compatible interfaces (existing code continues to work)
    from agent_swarm.context import ContextEngine, setup_project_rag

    engine = ContextEngine()
    dev_rag = await setup_project_rag(project_path)
"""

# New unified interface (recommended)
from .engine import UnifiedContext, create_unified_context

# Backward compatible exports (existing code continues to work)
from .context_engine import (
    ContextEngine,
    ContextProvider,
    ContextResult,
    ContextQuery,
    ProjectStructure,
    ProjectType,
    ProjectAnalyzer,
    QueryIntent,
    ResponseMode,
    ContextDepth,
    SmartContextResult,
    ContextPriority,
)

from .rag_system import (
    RAGSystem,
    RAGBackend,
    RAGConfig,
    RAGResult,
    DocumentChunk,
    RAGStrategy,
    InMemoryRAGBackend,
    create_rag_system,
    setup_codebase_rag,
    setup_documentation_rag,
)

from .dev_rag import (
    DevelopmentRAG,
    create_development_rag,
    setup_project_rag,
)

# Organized sub-modules
from . import rag, analysis, providers

__all__ = [
    # New unified interface (recommended)
    "UnifiedContext", "create_unified_context",

    # Smart context engine (backward compatible)
    "ContextEngine", "ContextProvider", "ContextResult", "ContextQuery",
    "ProjectStructure", "ProjectType", "ProjectAnalyzer",
    "QueryIntent", "ResponseMode", "ContextDepth", "SmartContextResult", "ContextPriority",

    # RAG system (backward compatible)
    "RAGSystem", "RAGBackend", "RAGConfig", "RAGResult", "DocumentChunk", "RAGStrategy",
    "InMemoryRAGBackend", "create_rag_system", "setup_codebase_rag", "setup_documentation_rag",

    # Development RAG (backward compatible)
    "DevelopmentRAG", "create_development_rag", "setup_project_rag",

    # Sub-modules
    "rag", "analysis", "providers"
]
