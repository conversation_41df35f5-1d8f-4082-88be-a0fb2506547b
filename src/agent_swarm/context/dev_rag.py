"""
Development-focused RAG system for Agent Swarm.
Handles code, documentation, and development knowledge effectively.
"""

from __future__ import annotations

import ast
import os
import subprocess
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from .rag_system import RAGSystem, create_rag_system, DocumentChunk
from ..utils.logging import get_logger

logger = get_logger("context.dev_rag")


class DevelopmentRAG:
    """RAG system optimized for development workflows."""

    def __init__(self, project_path: str, rag_system: Optional[RAGSystem] = None):
        self.project_path = Path(project_path)
        self.rag_system = rag_system
        self.indexed_files: Set[str] = set()
        self.file_hashes: Dict[str, str] = {}

        # Development-specific settings
        self.code_extensions = {
            '.py', '.js', '.ts', '.java', '.cpp', '.c', '.h', '.hpp',
            '.rs', '.go', '.rb', '.php', '.swift', '.kt', '.scala'
        }
        self.doc_extensions = {
            '.md', '.rst', '.txt', '.adoc', '.org'
        }
        self.config_extensions = {
            '.json', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf'
        }
        self.ignore_patterns = {
            '__pycache__', '.git', '.svn', 'node_modules', '.venv', 'venv',
            'build', 'dist', '.pytest_cache', '.mypy_cache', '.tox'
        }

    async def initialize(self) -> None:
        """Initialize the development RAG system."""
        if self.rag_system is None:
            self.rag_system = await create_rag_system(
                vector_store="memory",
                chunk_size=800,
                chunk_overlap=100,
                top_k=10,
                similarity_threshold=0.1
            )

        logger.info(f"Development RAG initialized for: {self.project_path}")

    async def index_project(self, force_reindex: bool = False) -> Dict[str, Any]:
        """Index the entire project for RAG retrieval."""
        if not self.rag_system:
            await self.initialize()

        logger.info(f"Indexing project: {self.project_path}")

        stats = {
            "files_processed": 0,
            "files_skipped": 0,
            "files_updated": 0,
            "total_chunks": 0,
            "errors": []
        }

        # Find all relevant files
        files_to_process = self._find_project_files()

        documents = []
        metadata = []

        for file_path in files_to_process:
            try:
                # Check if file needs updating
                if not force_reindex and not self._file_needs_update(file_path):
                    stats["files_skipped"] += 1
                    continue

                # Process the file
                content, file_metadata = await self._process_file(file_path)

                if content:
                    documents.append(content)
                    metadata.append(file_metadata)
                    stats["files_processed"] += 1

                    # Update tracking
                    self.indexed_files.add(str(file_path))
                    self.file_hashes[str(file_path)] = self._get_file_hash(file_path)

            except Exception as e:
                stats["errors"].append(f"{file_path}: {e}")
                logger.error(f"Error processing {file_path}: {e}")

        # Add to RAG system
        if documents:
            await self.rag_system.add_documents(documents, metadata)
            stats["total_chunks"] = len(documents)

        logger.info(f"Project indexing complete: {stats}")
        return stats

    def _find_project_files(self) -> List[Path]:
        """Find all relevant files in the project."""
        files = []

        for root, dirs, filenames in os.walk(self.project_path):
            # Skip ignored directories
            dirs[:] = [d for d in dirs if d not in self.ignore_patterns]

            for filename in filenames:
                file_path = Path(root) / filename

                # Check if file should be indexed
                if self._should_index_file(file_path):
                    files.append(file_path)

        return files

    def _should_index_file(self, file_path: Path) -> bool:
        """Check if a file should be indexed."""
        # Check extension
        if file_path.suffix not in (self.code_extensions | self.doc_extensions | self.config_extensions):
            return False

        # Check size (skip very large files)
        try:
            if file_path.stat().st_size > 1024 * 1024:  # 1MB limit
                return False
        except OSError:
            return False

        # Check if it's a text file
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                f.read(1024)  # Try to read first 1KB
            return True
        except (UnicodeDecodeError, OSError):
            return False

    def _file_needs_update(self, file_path: Path) -> bool:
        """Check if file needs to be reindexed."""
        file_str = str(file_path)

        # New file
        if file_str not in self.indexed_files:
            return True

        # Check if file was modified
        current_hash = self._get_file_hash(file_path)
        return current_hash != self.file_hashes.get(file_str)

    def _get_file_hash(self, file_path: Path) -> str:
        """Get a simple hash of file modification time and size."""
        try:
            stat = file_path.stat()
            return f"{stat.st_mtime}_{stat.st_size}"
        except OSError:
            return "0_0"

    async def _process_file(self, file_path: Path) -> tuple[str, Dict[str, Any]]:
        """Process a single file for indexing."""
        try:
            content = file_path.read_text(encoding='utf-8')
        except Exception as e:
            logger.debug(f"Could not read {file_path}: {e}")
            return "", {}

        # Determine file type and extract metadata
        file_type = self._get_file_type(file_path)
        metadata = {
            "file_path": str(file_path.relative_to(self.project_path)),
            "file_name": file_path.name,
            "file_type": file_type,
            "extension": file_path.suffix,
            "size": len(content),
            "project_path": str(self.project_path)
        }

        # Add language-specific metadata
        if file_type == "code":
            code_metadata = self._extract_code_metadata(file_path, content)
            metadata.update(code_metadata)

        # Enhance content with context
        enhanced_content = self._enhance_content(file_path, content, metadata)

        return enhanced_content, metadata

    def _get_file_type(self, file_path: Path) -> str:
        """Determine the type of file."""
        if file_path.suffix in self.code_extensions:
            return "code"
        elif file_path.suffix in self.doc_extensions:
            return "documentation"
        elif file_path.suffix in self.config_extensions:
            return "configuration"
        else:
            return "other"

    def _extract_code_metadata(self, file_path: Path, content: str) -> Dict[str, Any]:
        """Extract metadata from code files."""
        metadata = {}

        # Language detection
        language_map = {
            '.py': 'python', '.js': 'javascript', '.ts': 'typescript',
            '.java': 'java', '.cpp': 'cpp', '.c': 'c', '.h': 'c',
            '.rs': 'rust', '.go': 'go', '.rb': 'ruby', '.php': 'php'
        }
        metadata["language"] = language_map.get(file_path.suffix, "unknown")

        # Python-specific analysis
        if file_path.suffix == '.py':
            try:
                tree = ast.parse(content)

                # Extract classes and functions
                classes = [node.name for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]
                functions = [node.name for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]

                metadata.update({
                    "classes": classes,
                    "functions": functions,
                    "imports": self._extract_python_imports(tree)
                })
            except SyntaxError:
                pass  # Invalid Python syntax

        return metadata

    def _extract_python_imports(self, tree: ast.AST) -> List[str]:
        """Extract import statements from Python AST."""
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module)
        return imports

    def _enhance_content(self, file_path: Path, content: str, metadata: Dict[str, Any]) -> str:
        """Enhance content with additional context for better retrieval."""

        # Add file context header
        context_header = f"""
File: {metadata['file_path']}
Type: {metadata['file_type']}
Language: {metadata.get('language', 'unknown')}
"""

        # Add code-specific context
        if metadata['file_type'] == 'code':
            if metadata.get('classes'):
                context_header += f"Classes: {', '.join(metadata['classes'])}\n"
            if metadata.get('functions'):
                context_header += f"Functions: {', '.join(metadata['functions'])}\n"
            if metadata.get('imports'):
                context_header += f"Imports: {', '.join(metadata['imports'])}\n"

        return context_header + "\n" + content

    async def search_code(
        self,
        query: str,
        language: Optional[str] = None,
        file_type: Optional[str] = None,
        file_pattern: Optional[str] = None
    ) -> List[DocumentChunk]:
        """Search for code with specific filters."""
        if not self.rag_system:
            await self.initialize()

        # Build enhanced query
        enhanced_query = query
        if language:
            enhanced_query += f" language:{language}"
        if file_type:
            enhanced_query += f" type:{file_type}"

        result = await self.rag_system.retrieve(enhanced_query, top_k=10)

        # Filter results based on criteria
        filtered_chunks = []
        for chunk in result.chunks:
            metadata = chunk.metadata

            # Apply filters
            if language and metadata.get('language') != language:
                continue
            if file_type and metadata.get('file_type') != file_type:
                continue
            if file_pattern and file_pattern not in metadata.get('file_path', ''):
                continue

            filtered_chunks.append(chunk)

        return filtered_chunks

    async def get_related_files(self, file_path: str) -> List[DocumentChunk]:
        """Find files related to the given file."""
        if not self.rag_system:
            await self.initialize()

        # Use file name and directory as query
        path_obj = Path(file_path)
        query_parts = [
            path_obj.stem,  # filename without extension
            str(path_obj.parent),  # directory
        ]

        query = " ".join(query_parts)
        result = await self.rag_system.retrieve(query, top_k=15)

        # Filter out the original file
        related = [
            chunk for chunk in result.chunks
            if chunk.metadata.get('file_path') != file_path
        ]

        return related[:10]

    async def explain_code(self, code_snippet: str, context_file: Optional[str] = None) -> str:
        """Get explanation for a code snippet using project context."""
        if not self.rag_system:
            await self.initialize()

        # Search for related code patterns
        result = await self.rag_system.retrieve(code_snippet, top_k=5)

        explanation = f"Code snippet analysis:\n\n"
        explanation += f"```\n{code_snippet}\n```\n\n"

        if result.chunks:
            explanation += "Related code in project:\n"
            for i, chunk in enumerate(result.chunks[:3], 1):
                file_path = chunk.metadata.get('file_path', 'unknown')
                explanation += f"\n{i}. From {file_path}:\n"
                explanation += f"   {chunk.content[:200]}...\n"
        else:
            explanation += "No related code found in project.\n"

        return explanation

    async def get_context_for_task(
        self,
        task: str,
        task_type: str = "general"
    ) -> Any:
        """Get relevant context for a specific task."""
        if not self.rag_system:
            await self.initialize()

        # Use the RAG system's method
        return await self.rag_system.get_context_for_task(task, task_type)

    async def get_project_stats(self) -> Dict[str, Any]:
        """Get statistics about the indexed project."""
        if not self.rag_system:
            await self.initialize()

        rag_stats = await self.rag_system.get_stats()

        # Count files by type
        file_types = {}
        languages = {}

        for file_path in self.indexed_files:
            path_obj = Path(file_path)
            ext = path_obj.suffix

            # Count by extension
            file_types[ext] = file_types.get(ext, 0) + 1

            # Count by language (for code files)
            if ext in {'.py': 'python', '.js': 'javascript', '.ts': 'typescript'}:
                lang = {'.py': 'python', '.js': 'javascript', '.ts': 'typescript'}[ext]
                languages[lang] = languages.get(lang, 0) + 1

        return {
            "project_path": str(self.project_path),
            "total_files_indexed": len(self.indexed_files),
            "file_types": file_types,
            "languages": languages,
            "rag_stats": rag_stats
        }


# Factory functions
async def create_development_rag(project_path: str) -> DevelopmentRAG:
    """Create a development RAG system for a project."""
    dev_rag = DevelopmentRAG(project_path)
    await dev_rag.initialize()
    return dev_rag


async def setup_project_rag(project_path: str, auto_index: bool = True) -> DevelopmentRAG:
    """Set up and optionally index a project for RAG."""
    dev_rag = await create_development_rag(project_path)

    if auto_index:
        await dev_rag.index_project()

    return dev_rag
