"""
Intelligent Context Engine for Agent Swarm.
Provides smart, project-aware context understanding for different types of codebases.
"""

from __future__ import annotations

import asyncio
import os
import re
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

from pydantic import BaseModel, Field

from ..utils.logging import get_logger

logger = get_logger("context.engine")


class ProjectType(Enum):
    """Types of projects the context engine can understand."""
    PYTHON_PACKAGE = "python_package"
    WEB_APP = "web_app"
    API_SERVICE = "api_service"
    CLI_TOOL = "cli_tool"
    LIBRARY = "library"
    FRAMEWORK = "framework"
    DATA_SCIENCE = "data_science"
    MACHINE_LEARNING = "machine_learning"
    UNKNOWN = "unknown"


class ContextPriority(Enum):
    """Priority levels for context results."""
    CRITICAL = "critical"      # Core architecture, main classes
    HIGH = "high"             # Important implementations, key modules
    MEDIUM = "medium"         # Supporting code, utilities
    LOW = "low"              # Tests, examples, documentation


class ResponseMode(Enum):
    """Response speed and detail modes."""
    FAST = "fast"             # Quick response, essential context only
    DETAILED = "detailed"     # Balanced response with good context
    COMPREHENSIVE = "comprehensive"  # Deep analysis, maximum context


class ContextDepth(Enum):
    """Depth of context analysis."""
    SHALLOW = "shallow"       # File-level context only
    MEDIUM = "medium"         # Module-level with dependencies
    DEEP = "deep"            # Project-wide with full analysis


class QueryIntent(Enum):
    """Intent behind the context query."""
    DEBUG = "debug"           # Focus on error handling, tests, debugging
    FEATURE = "feature"       # Focus on similar implementations, patterns
    REFACTOR = "refactor"     # Focus on dependencies, architecture
    UNDERSTAND = "understand" # Focus on overview, documentation, design
    DOCUMENT = "document"     # Focus on public APIs, examples, usage
    GENERAL = "general"       # Balanced context for general questions


@dataclass
class ProjectStructure:
    """Analysis of project structure and characteristics."""
    project_type: ProjectType
    main_language: str
    frameworks: List[str]
    entry_points: List[str]
    core_modules: List[str]
    config_files: List[str]
    documentation_files: List[str]
    test_directories: List[str]
    package_structure: Dict[str, List[str]]
    dependencies: List[str]
    confidence_score: float


@dataclass
class SmartContextResult:
    """Enhanced context result with intelligence and prioritization."""
    content: str
    file_path: str
    relevance_score: float
    priority: ContextPriority
    context_type: str  # "architecture", "implementation", "documentation", "test", "config"
    summary: str
    key_concepts: List[str]
    related_files: List[str]
    metadata: Dict[str, Any]


@dataclass
class ContextQuery:
    """Enhanced query for context information with speed and depth controls."""
    query: str
    intent: QueryIntent = QueryIntent.GENERAL
    response_mode: ResponseMode = ResponseMode.DETAILED
    context_depth: ContextDepth = ContextDepth.MEDIUM
    max_results: int = 5
    include_tests: bool = True
    include_docs: bool = True
    focus_files: Optional[List[str]] = None
    filters: Optional[Dict[str, Any]] = None


@dataclass
class ContextResult:
    """Result from context retrieval."""
    query: str
    results: List[Dict[str, Any]]
    total_found: int
    retrieval_time: float
    source: str


class ContextProvider(ABC):
    """Abstract base class for context providers."""

    @abstractmethod
    async def get_context(self, query: ContextQuery) -> ContextResult:
        """Get context for a query."""
        pass

    @abstractmethod
    async def add_context(self, content: str, metadata: Dict[str, Any]) -> None:
        """Add context to the provider."""
        pass


class ProjectAnalyzer:
    """Intelligent project analyzer that understands different types of codebases."""

    def __init__(self):
        from .project_analyzer_helpers import ProjectAnalyzerHelpers
        self.helpers = ProjectAnalyzerHelpers()

        self.project_patterns = {
            ProjectType.PYTHON_PACKAGE: {
                "indicators": ["setup.py", "pyproject.toml", "src/", "__init__.py"],
            },
            ProjectType.WEB_APP: {
                "indicators": ["package.json", "index.html", "src/", "public/"],
            },
            ProjectType.API_SERVICE: {
                "indicators": ["api/", "routes/", "endpoints/", "swagger"],
            },
            ProjectType.CLI_TOOL: {
                "indicators": ["cli/", "commands/", "argparse", "click"],
            },
            ProjectType.FRAMEWORK: {
                "indicators": ["framework", "core/", "base/", "abstract"],
            }
        }

    async def analyze_project(self, project_path: str) -> ProjectStructure:
        """Analyze a project and determine its structure and type."""
        project_path = Path(project_path)

        if not project_path.exists():
            raise ValueError(f"Project path does not exist: {project_path}")

        # Collect all files
        all_files = []
        for root, dirs, files in os.walk(project_path):
            # Skip common ignore directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', 'venv', 'env']]

            for file in files:
                if not file.startswith('.'):
                    file_path = Path(root) / file
                    rel_path = file_path.relative_to(project_path)
                    all_files.append(str(rel_path))

        # Analyze project characteristics using helpers
        project_type = await self._detect_project_type(project_path, all_files)
        main_language = self.helpers.detect_main_language(all_files)
        frameworks = await self.helpers.detect_frameworks(project_path, all_files)
        entry_points = self.helpers.find_entry_points(all_files, main_language)
        core_modules = await self.helpers.identify_core_modules(project_path, all_files, project_type)
        config_files = self.helpers.find_config_files(all_files)
        documentation_files = self.helpers.find_documentation_files(all_files)
        test_directories = self.helpers.find_test_directories(all_files)
        package_structure = self.helpers.analyze_package_structure(all_files, main_language)
        dependencies = await self.helpers.extract_dependencies(project_path, all_files)

        # Calculate confidence score
        confidence_score = self.helpers.calculate_confidence(project_type, frameworks, core_modules)

        return ProjectStructure(
            project_type=project_type,
            main_language=main_language,
            frameworks=frameworks,
            entry_points=entry_points,
            core_modules=core_modules,
            config_files=config_files,
            documentation_files=documentation_files,
            test_directories=test_directories,
            package_structure=package_structure,
            dependencies=dependencies,
            confidence_score=confidence_score
        )

    async def _detect_project_type(self, project_path: Path, files: List[str]) -> ProjectType:
        """Detect the type of project based on file patterns."""
        scores = {project_type: 0 for project_type in ProjectType}

        for project_type, patterns in self.project_patterns.items():
            for indicator in patterns["indicators"]:
                if any(indicator in file for file in files):
                    scores[project_type] += 1

        # Special logic for specific project types
        if any("setup.py" in file or "pyproject.toml" in file for file in files):
            scores[ProjectType.PYTHON_PACKAGE] += 3

        if any("package.json" in file for file in files):
            scores[ProjectType.WEB_APP] += 2

        if any("api" in file.lower() or "routes" in file.lower() for file in files):
            scores[ProjectType.API_SERVICE] += 2

        # Check for framework indicators
        if any("agent" in file.lower() or "llm" in file.lower() or "rag" in file.lower() for file in files):
            scores[ProjectType.FRAMEWORK] += 2

        # Return the highest scoring type
        best_type = max(scores.items(), key=lambda x: x[1])
        return best_type[0] if best_type[1] > 0 else ProjectType.UNKNOWN


class ContextEngine:
    """Enhanced context engine with intelligent project understanding."""

    def __init__(self):
        self.providers: Dict[str, ContextProvider] = {}
        self.analyzer = ProjectAnalyzer()
        self.project_structure: Optional[ProjectStructure] = None
        self._initialized = False

        # Import enhanced helpers
        from .enhanced_context_helpers import EnhancedContextHelpers
        self.enhanced_helpers = EnhancedContextHelpers()

    async def initialize(self, project_path: Optional[str] = None) -> None:
        """Initialize the context engine with optional project analysis."""
        if self._initialized:
            return

        if project_path:
            try:
                self.project_structure = await self.analyzer.analyze_project(project_path)
                logger.info(f"Analyzed project: {self.project_structure.project_type.value} "
                           f"({self.project_structure.main_language}) "
                           f"confidence: {self.project_structure.confidence_score:.2f}")
            except Exception as e:
                logger.error(f"Failed to analyze project: {e}")
                self.project_structure = None

        self._initialized = True
        logger.info("Context engine initialized")

    def register_provider(self, name: str, provider: ContextProvider) -> None:
        """Register a context provider."""
        self.providers[name] = provider
        logger.info(f"Registered context provider: {name}")

    async def get_context(
        self,
        query: str,
        task_type: str = "general",
        providers: Optional[List[str]] = None
    ) -> List[ContextResult]:
        """Get context from multiple providers."""
        if not self._initialized:
            await self.initialize()

        context_query = ContextQuery(
            query=query,
            intent=QueryIntent.GENERAL,
            max_results=5
        )

        # Use specified providers or all available
        provider_names = providers or list(self.providers.keys())

        logger.debug(f"SmartEngine.get_context: Available providers: {list(self.providers.keys())}")
        logger.debug(f"SmartEngine.get_context: Using providers: {provider_names}")

        results = []
        for provider_name in provider_names:
            if provider_name in self.providers:
                try:
                    logger.debug(f"SmartEngine.get_context: Querying provider '{provider_name}'")
                    result = await self.providers[provider_name].get_context(context_query)
                    logger.debug(f"SmartEngine.get_context: Provider '{provider_name}' returned {result.total_found} results")
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error getting context from {provider_name}: {e}")
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")
            else:
                logger.warning(f"SmartEngine.get_context: Provider '{provider_name}' not found")

        logger.debug(f"SmartEngine.get_context: Total results from {len(results)} providers")
        return results

    async def add_context(
        self,
        content: str,
        metadata: Dict[str, Any],
        provider: Optional[str] = None
    ) -> None:
        """Add context to providers."""
        if provider:
            if provider in self.providers:
                await self.providers[provider].add_context(content, metadata)
        else:
            # Add to all providers
            for provider_obj in self.providers.values():
                await provider_obj.add_context(content, metadata)

    async def get_smart_context(
        self,
        query: str,
        intent: QueryIntent = QueryIntent.GENERAL,
        response_mode: ResponseMode = ResponseMode.DETAILED,
        context_depth: ContextDepth = ContextDepth.MEDIUM,
        max_results: Optional[int] = None,
        include_tests: bool = True,
        include_docs: bool = True,
        focus_files: Optional[List[str]] = None
    ) -> List[SmartContextResult]:
        """Get intelligent, prioritized context with speed and depth controls."""
        if not self._initialized:
            await self.initialize()

        logger.debug(f"SmartEngine.get_smart_context: Starting with query='{query}', intent={intent.value}")
        logger.debug(f"SmartEngine.get_smart_context: project_structure available: {self.project_structure is not None}")
        logger.debug(f"SmartEngine.get_smart_context: providers available: {list(self.providers.keys())}")

        # Adjust max_results based on response mode
        if max_results is None:
            max_results = {
                ResponseMode.FAST: 3,
                ResponseMode.DETAILED: 5,
                ResponseMode.COMPREHENSIVE: 10
            }[response_mode]

        # Create enhanced context query
        context_query = ContextQuery(
            query=query,
            intent=intent,
            response_mode=response_mode,
            context_depth=context_depth,
            max_results=max_results,
            include_tests=include_tests,
            include_docs=include_docs,
            focus_files=focus_files
        )

        # Get basic context from providers
        logger.debug(f"SmartEngine.get_smart_context: Getting basic context from providers...")
        basic_results = await self.get_context(query, intent.value)
        logger.debug(f"SmartEngine.get_smart_context: Got {len(basic_results)} basic results")

        total_items = sum(len(result.results) for result in basic_results)
        logger.debug(f"SmartEngine.get_smart_context: Total items across all results: {total_items}")

        # If we have project structure, enhance the results
        if self.project_structure:
            logger.debug(f"SmartEngine.get_smart_context: Enhancing with project intelligence...")
            enhanced_results = await self._enhance_context_with_intelligence(
                context_query, basic_results
            )
            logger.debug(f"SmartEngine.get_smart_context: Enhanced to {len(enhanced_results)} smart results")
            return enhanced_results
        else:
            logger.debug(f"SmartEngine.get_smart_context: No project structure, using basic conversion...")
            # Fallback to basic context conversion
            converted_results = self._convert_basic_to_smart_context(basic_results, max_results)
            logger.debug(f"SmartEngine.get_smart_context: Converted to {len(converted_results)} smart results")
            return converted_results

    async def _enhance_context_with_intelligence(
        self,
        context_query: ContextQuery,
        basic_results: List[ContextResult]
    ) -> List[SmartContextResult]:
        """Enhance context results with project intelligence and user preferences."""
        smart_results = []

        # Filter results based on user preferences
        filtered_results = self.enhanced_helpers.filter_results_by_preferences(basic_results, context_query)

        # Prioritize results based on project structure and intent
        for result in filtered_results:
            for item in result.results:
                file_path = item.get('file_path', '')
                content = item.get('content', '')

                # Skip if focus_files specified and this file not in focus
                if context_query.focus_files and file_path not in context_query.focus_files:
                    continue

                # Determine priority based on project structure and intent
                priority = self.enhanced_helpers.determine_priority_with_intent(
                    file_path, content, context_query.intent, self.project_structure
                )

                # Determine context type
                context_type = self._determine_context_type(file_path, content)

                # Calculate enhanced relevance score
                relevance_score = self.enhanced_helpers.calculate_enhanced_relevance_with_intent(
                    context_query, content, file_path, self.project_structure
                )

                # Generate summary and key concepts based on response mode
                summary = self.enhanced_helpers.generate_summary_with_mode(
                    content, file_path, context_query.response_mode
                )
                key_concepts = self.enhanced_helpers.extract_key_concepts_with_intent(
                    content, file_path, context_query.intent
                )
                related_files = self.enhanced_helpers.find_related_files_with_depth(
                    file_path, context_query.context_depth, self.project_structure
                )

                smart_result = SmartContextResult(
                    content=content,
                    file_path=file_path,
                    relevance_score=relevance_score,
                    priority=priority,
                    context_type=context_type,
                    summary=summary,
                    key_concepts=key_concepts,
                    related_files=related_files,
                    metadata=item
                )
                smart_results.append(smart_result)

        # Sort by priority, intent relevance, and general relevance
        smart_results.sort(key=lambda x: (
            x.priority.value == "critical",
            x.priority.value == "high",
            x.relevance_score
        ), reverse=True)

        return smart_results[:context_query.max_results]

    def _determine_priority(self, file_path: str, content: str) -> ContextPriority:
        """Determine the priority of a context result."""
        if not self.project_structure:
            return ContextPriority.MEDIUM

        # Critical: Core modules, main entry points
        if file_path in self.project_structure.core_modules:
            return ContextPriority.CRITICAL

        if file_path in self.project_structure.entry_points:
            return ContextPriority.CRITICAL

        # High: Important implementations
        if any(keyword in content.lower() for keyword in ['class', 'def ', 'async def']):
            if any(pattern in file_path.lower() for pattern in ['core', 'main', 'base', 'engine']):
                return ContextPriority.HIGH

        # Medium: Supporting code
        if file_path.endswith('.py') and not file_path.startswith('test'):
            return ContextPriority.MEDIUM

        # Low: Tests, docs, config
        if any(pattern in file_path.lower() for pattern in ['test', 'doc', 'readme', 'config']):
            return ContextPriority.LOW

        return ContextPriority.MEDIUM

    def _determine_context_type(self, file_path: str, content: str) -> str:
        """Determine the type of context."""
        if any(pattern in file_path.lower() for pattern in ['test', 'spec']):
            return "test"

        if any(pattern in file_path.lower() for pattern in ['doc', 'readme', 'md']):
            return "documentation"

        if any(pattern in file_path.lower() for pattern in ['config', 'settings', 'toml', 'json']):
            return "config"

        if any(keyword in content.lower() for keyword in ['class', 'def ', 'function']):
            return "implementation"

        if any(keyword in content.lower() for keyword in ['architecture', 'design', 'overview']):
            return "architecture"

        return "general"

    def _calculate_enhanced_relevance(
        self, query: str, content: str, file_path: str, task_type: str
    ) -> float:
        """Calculate enhanced relevance score."""
        base_score = 0.0
        query_words = set(query.lower().split())
        content_words = set(content.lower().split())

        # Basic keyword matching
        overlap = len(query_words.intersection(content_words))
        if len(query_words) > 0:
            base_score = overlap / len(query_words)

        # Boost for project-specific patterns
        if self.project_structure:
            # Boost for core modules
            if file_path in self.project_structure.core_modules:
                base_score += 0.3

            # Boost for framework-specific content
            for framework in self.project_structure.frameworks:
                if framework.lower() in content.lower():
                    base_score += 0.2

        # Task-specific boosts
        task_boosts = {
            "architecture": ["class", "abstract", "base", "interface"],
            "implementation": ["def ", "function", "method"],
            "debugging": ["error", "exception", "try", "catch"],
            "testing": ["test", "assert", "mock"],
        }

        if task_type in task_boosts:
            for keyword in task_boosts[task_type]:
                if keyword in content.lower():
                    base_score += 0.1

        return min(1.0, base_score)

    def _generate_summary(self, content: str, file_path: str) -> str:
        """Generate a brief summary of the content."""
        # Simple summary generation
        lines = content.split('\n')

        # Look for docstrings or comments
        for line in lines[:10]:
            line = line.strip()
            if line.startswith('"""') or line.startswith("'''"):
                # Extract docstring
                docstring_lines = []
                in_docstring = True
                for next_line in lines[lines.index(line):]:
                    if in_docstring:
                        docstring_lines.append(next_line.strip())
                        if next_line.strip().endswith('"""') or next_line.strip().endswith("'''"):
                            break
                return ' '.join(docstring_lines)[:200] + "..."

        # Fallback: first few lines
        first_lines = [line.strip() for line in lines[:3] if line.strip()]
        return ' '.join(first_lines)[:200] + "..."

    def _extract_key_concepts(self, content: str, file_path: str) -> List[str]:
        """Extract key concepts from the content."""
        concepts = []

        # Extract class names
        import re
        class_matches = re.findall(r'class\s+(\w+)', content)
        concepts.extend(class_matches)

        # Extract function names
        func_matches = re.findall(r'def\s+(\w+)', content)
        concepts.extend(func_matches[:5])  # Limit to first 5

        # Extract imports
        import_matches = re.findall(r'from\s+(\w+)', content)
        concepts.extend(import_matches[:3])  # Limit to first 3

        return list(set(concepts))[:10]  # Return unique concepts, max 10

    def _find_related_files(self, file_path: str) -> List[str]:
        """Find files related to the given file."""
        if not self.project_structure:
            return []

        related = []
        file_dir = str(Path(file_path).parent)

        # Find files in the same directory
        for module_files in self.project_structure.package_structure.values():
            for module_file in module_files:
                if str(Path(module_file).parent) == file_dir and module_file != file_path:
                    related.append(module_file)

        return related[:5]  # Limit to 5 related files

    def _convert_basic_to_smart_context(
        self, basic_results: List[ContextResult], max_results: int
    ) -> List[SmartContextResult]:
        """Convert basic context results to smart context results."""
        smart_results = []

        for result in basic_results:
            for item in result.results:
                smart_result = SmartContextResult(
                    content=item.get('content', ''),
                    file_path=item.get('file_path', ''),
                    relevance_score=0.5,  # Default score
                    priority=ContextPriority.MEDIUM,
                    context_type="general",
                    summary=item.get('content', '')[:200] + "...",
                    key_concepts=[],
                    related_files=[],
                    metadata=item
                )
                smart_results.append(smart_result)

        return smart_results[:max_results]

    def get_stats(self) -> Dict[str, Any]:
        """Get context engine statistics."""
        stats = {
            "initialized": self._initialized,
            "providers": list(self.providers.keys()),
            "provider_count": len(self.providers)
        }

        if self.project_structure:
            stats["project"] = {
                "type": self.project_structure.project_type.value,
                "language": self.project_structure.main_language,
                "frameworks": self.project_structure.frameworks,
                "core_modules": len(self.project_structure.core_modules),
                "confidence": self.project_structure.confidence_score
            }

        return stats
