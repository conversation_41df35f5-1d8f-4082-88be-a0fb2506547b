"""
Helper methods for the ProjectAnalyzer class.
Contains utility functions for analyzing different aspects of codebases.
"""

import re
from pathlib import Path
from typing import Dict, List, Set

from .context_engine import ProjectType


class ProjectAnalyzerHelpers:
    """Helper methods for project analysis."""
    
    @staticmethod
    def detect_main_language(files: List[str]) -> str:
        """Detect the main programming language of the project."""
        language_extensions = {
            'python': ['.py', '.pyx', '.pyi'],
            'javascript': ['.js', '.jsx', '.mjs'],
            'typescript': ['.ts', '.tsx'],
            'java': ['.java'],
            'cpp': ['.cpp', '.cc', '.cxx', '.c++'],
            'c': ['.c', '.h'],
            'go': ['.go'],
            'rust': ['.rs'],
            'php': ['.php'],
            'ruby': ['.rb'],
            'swift': ['.swift'],
            'kotlin': ['.kt'],
            'scala': ['.scala'],
            'r': ['.r', '.R'],
            'julia': ['.jl'],
        }
        
        language_counts = {lang: 0 for lang in language_extensions}
        
        for file in files:
            file_ext = Path(file).suffix.lower()
            for lang, extensions in language_extensions.items():
                if file_ext in extensions:
                    language_counts[lang] += 1
                    break
        
        # Return the language with the most files
        main_language = max(language_counts.items(), key=lambda x: x[1])
        return main_language[0] if main_language[1] > 0 else 'unknown'
    
    @staticmethod
    async def detect_frameworks(project_path: Path, files: List[str]) -> List[str]:
        """Detect frameworks and libraries used in the project."""
        frameworks = set()
        
        # Framework detection patterns
        framework_patterns = {
            'fastapi': [r'from fastapi', r'FastAPI\(', r'@app\.'],
            'django': [r'django', r'models\.py', r'views\.py', r'urls\.py'],
            'flask': [r'from flask', r'Flask\(', r'@app\.route'],
            'react': [r'import React', r'useState', r'useEffect', r'\.jsx'],
            'vue': [r'<template>', r'Vue\.js', r'v-if', r'v-for'],
            'express': [r'express', r'app\.get', r'app\.post'],
            'pytest': [r'test_', r'conftest\.py', r'pytest'],
            'asyncio': [r'async def', r'await ', r'asyncio'],
            'pydantic': [r'from pydantic', r'BaseModel'],
            'sqlalchemy': [r'from sqlalchemy', r'declarative_base'],
            'click': [r'import click', r'@click\.command'],
            'typer': [r'import typer', r'typer\.run'],
            'streamlit': [r'import streamlit', r'st\.'],
            'pandas': [r'import pandas', r'pd\.'],
            'numpy': [r'import numpy', r'np\.'],
            'tensorflow': [r'import tensorflow', r'tf\.'],
            'pytorch': [r'import torch', r'torch\.'],
            'scikit-learn': [r'from sklearn', r'sklearn'],
        }
        
        # Check key files for framework indicators
        key_files = [f for f in files if any(f.endswith(ext) for ext in ['.py', '.js', '.ts', '.jsx', '.tsx'])]
        
        for file_path in key_files[:20]:  # Limit to first 20 files for performance
            try:
                full_path = project_path / file_path
                if full_path.exists() and full_path.stat().st_size < 100000:  # Skip very large files
                    content = full_path.read_text(encoding='utf-8', errors='ignore')
                    
                    for framework, patterns in framework_patterns.items():
                        for pattern in patterns:
                            if re.search(pattern, content, re.IGNORECASE):
                                frameworks.add(framework)
                                break
            except Exception:
                continue  # Skip files that can't be read
        
        return list(frameworks)
    
    @staticmethod
    def find_entry_points(files: List[str], main_language: str) -> List[str]:
        """Find potential entry points of the application."""
        entry_points = []
        
        # Common entry point patterns
        entry_patterns = {
            'python': ['main.py', 'app.py', 'run.py', '__main__.py', 'cli.py', 'server.py'],
            'javascript': ['index.js', 'app.js', 'main.js', 'server.js'],
            'typescript': ['index.ts', 'app.ts', 'main.ts', 'server.ts'],
            'java': ['Main.java', 'Application.java', 'App.java'],
            'go': ['main.go'],
            'rust': ['main.rs'],
        }
        
        patterns = entry_patterns.get(main_language, [])
        
        for file in files:
            file_name = Path(file).name
            if file_name in patterns:
                entry_points.append(file)
            elif file_name.startswith('main') or file_name.startswith('app'):
                entry_points.append(file)
        
        return entry_points
    
    @staticmethod
    async def identify_core_modules(project_path: Path, files: List[str], project_type: ProjectType) -> List[str]:
        """Identify core modules based on project type and structure."""
        core_modules = []
        
        # Core module patterns by project type
        core_patterns = {
            ProjectType.PYTHON_PACKAGE: [
                r'src/.*/__init__\.py',
                r'.*/(core|main|base|engine)\.py',
                r'.*/agent.*\.py',
                r'.*/model.*\.py',
                r'.*/api\.py',
                r'.*/cli\.py',
            ],
            ProjectType.FRAMEWORK: [
                r'.*/core/.*\.py',
                r'.*/base/.*\.py',
                r'.*/abstract.*\.py',
                r'.*/engine.*\.py',
                r'.*/framework.*\.py',
            ],
            ProjectType.API_SERVICE: [
                r'.*/api/.*\.py',
                r'.*/routes/.*\.py',
                r'.*/endpoints/.*\.py',
                r'.*/models/.*\.py',
                r'.*/schemas/.*\.py',
            ],
            ProjectType.CLI_TOOL: [
                r'.*/cli/.*\.py',
                r'.*/commands/.*\.py',
                r'.*/(main|app)\.py',
            ],
        }
        
        patterns = core_patterns.get(project_type, [])
        
        for file in files:
            for pattern in patterns:
                if re.match(pattern, file):
                    core_modules.append(file)
                    break
        
        # Also look for files with important class definitions
        important_files = []
        python_files = [f for f in files if f.endswith('.py')]
        
        for file_path in python_files[:30]:  # Limit for performance
            try:
                full_path = project_path / file_path
                if full_path.exists() and full_path.stat().st_size < 50000:
                    content = full_path.read_text(encoding='utf-8', errors='ignore')
                    
                    # Look for important class patterns
                    important_patterns = [
                        r'class.*Agent',
                        r'class.*Engine',
                        r'class.*Manager',
                        r'class.*Service',
                        r'class.*Controller',
                        r'class.*Handler',
                        r'class.*Factory',
                        r'class.*Builder',
                        r'class.*Config',
                        r'class.*Router',
                    ]
                    
                    for pattern in important_patterns:
                        if re.search(pattern, content, re.IGNORECASE):
                            important_files.append(file_path)
                            break
            except Exception:
                continue
        
        # Combine and deduplicate
        all_core = list(set(core_modules + important_files))
        return all_core[:15]  # Limit to top 15 core modules
    
    @staticmethod
    def find_config_files(files: List[str]) -> List[str]:
        """Find configuration files."""
        config_patterns = [
            r'.*\.toml$',
            r'.*\.yaml$',
            r'.*\.yml$',
            r'.*\.json$',
            r'.*\.ini$',
            r'.*\.cfg$',
            r'.*\.conf$',
            r'.*config.*',
            r'.*settings.*',
            r'requirements.*\.txt$',
            r'package\.json$',
            r'Dockerfile',
            r'docker-compose.*',
            r'Makefile',
            r'setup\.py$',
            r'pyproject\.toml$',
        ]
        
        config_files = []
        for file in files:
            for pattern in config_patterns:
                if re.match(pattern, file, re.IGNORECASE):
                    config_files.append(file)
                    break
        
        return config_files
    
    @staticmethod
    def find_documentation_files(files: List[str]) -> List[str]:
        """Find documentation files."""
        doc_patterns = [
            r'README.*',
            r'CHANGELOG.*',
            r'LICENSE.*',
            r'CONTRIBUTING.*',
            r'.*\.md$',
            r'.*\.rst$',
            r'docs/.*',
            r'documentation/.*',
        ]
        
        doc_files = []
        for file in files:
            for pattern in doc_patterns:
                if re.match(pattern, file, re.IGNORECASE):
                    doc_files.append(file)
                    break
        
        return doc_files
    
    @staticmethod
    def find_test_directories(files: List[str]) -> List[str]:
        """Find test directories and files."""
        test_patterns = [
            r'test.*/',
            r'tests/',
            r'.*test.*\.py$',
            r'.*_test\.py$',
            r'conftest\.py$',
        ]
        
        test_files = []
        for file in files:
            for pattern in test_patterns:
                if re.match(pattern, file, re.IGNORECASE):
                    test_files.append(file)
                    break
        
        return test_files
    
    @staticmethod
    def analyze_package_structure(files: List[str], main_language: str) -> Dict[str, List[str]]:
        """Analyze the package/module structure."""
        structure = {}
        
        if main_language == 'python':
            # Group Python files by directory
            for file in files:
                if file.endswith('.py'):
                    parts = Path(file).parts
                    if len(parts) > 1:
                        package = parts[0]
                        if package not in structure:
                            structure[package] = []
                        structure[package].append(file)
        
        elif main_language in ['javascript', 'typescript']:
            # Group JS/TS files by directory
            for file in files:
                if any(file.endswith(ext) for ext in ['.js', '.ts', '.jsx', '.tsx']):
                    parts = Path(file).parts
                    if len(parts) > 1:
                        package = parts[0]
                        if package not in structure:
                            structure[package] = []
                        structure[package].append(file)
        
        return structure
    
    @staticmethod
    async def extract_dependencies(project_path: Path, files: List[str]) -> List[str]:
        """Extract project dependencies."""
        dependencies = []
        
        # Check common dependency files
        dependency_files = {
            'requirements.txt': 'python',
            'pyproject.toml': 'python',
            'package.json': 'javascript',
            'Cargo.toml': 'rust',
            'go.mod': 'go',
            'pom.xml': 'java',
        }
        
        for file in files:
            file_name = Path(file).name
            if file_name in dependency_files:
                try:
                    full_path = project_path / file
                    if full_path.exists():
                        content = full_path.read_text(encoding='utf-8', errors='ignore')
                        
                        if file_name == 'requirements.txt':
                            # Parse requirements.txt
                            for line in content.split('\n'):
                                line = line.strip()
                                if line and not line.startswith('#'):
                                    dep = line.split('==')[0].split('>=')[0].split('<=')[0]
                                    dependencies.append(dep.strip())
                        
                        elif file_name == 'package.json':
                            # Parse package.json dependencies
                            import json
                            try:
                                data = json.loads(content)
                                deps = data.get('dependencies', {})
                                dev_deps = data.get('devDependencies', {})
                                dependencies.extend(list(deps.keys()))
                                dependencies.extend(list(dev_deps.keys()))
                            except json.JSONDecodeError:
                                pass
                
                except Exception:
                    continue
        
        return list(set(dependencies))  # Remove duplicates
    
    @staticmethod
    def calculate_confidence(project_type: ProjectType, frameworks: List[str], core_modules: List[str]) -> float:
        """Calculate confidence score for project analysis."""
        score = 0.0
        
        # Base score for project type detection
        if project_type != ProjectType.UNKNOWN:
            score += 0.3
        
        # Score for framework detection
        if frameworks:
            score += min(0.4, len(frameworks) * 0.1)
        
        # Score for core module identification
        if core_modules:
            score += min(0.3, len(core_modules) * 0.02)
        
        return min(1.0, score)
