"""
Enhanced context helpers with speed/depth controls and intent-based analysis.
Inspired by TypeScript context engine concepts.
"""

from typing import Dict, List, Optional
from .context_engine import (
    Context<PERSON><PERSON>y, ContextResult, ContextPriority, QueryIntent, 
    ResponseMode, ContextDepth, ProjectStructure
)


class EnhancedContextHelpers:
    """Enhanced context processing with speed and intent controls."""
    
    @staticmethod
    def filter_results_by_preferences(
        results: List[ContextResult], 
        context_query: ContextQuery
    ) -> List[ContextResult]:
        """Filter results based on user preferences."""
        filtered_results = []
        
        for result in results:
            filtered_items = []
            
            for item in result.results:
                file_path = item.get('file_path', '')
                
                # Filter by include_tests preference
                if not context_query.include_tests:
                    if any(pattern in file_path.lower() for pattern in ['test', 'spec', 'conftest']):
                        continue
                
                # Filter by include_docs preference  
                if not context_query.include_docs:
                    if any(pattern in file_path.lower() for pattern in ['doc', 'readme', 'md', 'rst']):
                        continue
                
                filtered_items.append(item)
            
            if filtered_items:
                filtered_result = ContextResult(
                    query=result.query,
                    results=filtered_items,
                    total_found=len(filtered_items),
                    retrieval_time=result.retrieval_time,
                    source=result.source
                )
                filtered_results.append(filtered_result)
        
        return filtered_results
    
    @staticmethod
    def determine_priority_with_intent(
        file_path: str, 
        content: str, 
        intent: QueryIntent,
        project_structure: Optional[ProjectStructure] = None
    ) -> ContextPriority:
        """Determine priority based on intent and project structure."""
        
        # Intent-specific priority boosts
        intent_patterns = {
            QueryIntent.DEBUG: {
                'critical': ['error', 'exception', 'try', 'catch', 'test'],
                'high': ['debug', 'log', 'assert', 'mock'],
            },
            QueryIntent.FEATURE: {
                'critical': ['class', 'def ', 'async def', 'function'],
                'high': ['import', 'from', 'api', 'endpoint'],
            },
            QueryIntent.REFACTOR: {
                'critical': ['class', 'interface', 'abstract', 'base'],
                'high': ['import', 'dependency', 'coupling'],
            },
            QueryIntent.UNDERSTAND: {
                'critical': ['readme', 'doc', 'overview', 'architecture'],
                'high': ['class', 'main', 'core', 'engine'],
            },
            QueryIntent.DOCUMENT: {
                'critical': ['public', 'api', 'interface', 'export'],
                'high': ['example', 'usage', 'demo'],
            }
        }
        
        # Base priority from project structure
        base_priority = ContextPriority.MEDIUM
        if project_structure:
            if file_path in project_structure.core_modules:
                base_priority = ContextPriority.CRITICAL
            elif file_path in project_structure.entry_points:
                base_priority = ContextPriority.CRITICAL
        
        # Adjust based on intent
        patterns = intent_patterns.get(intent, {})
        content_lower = content.lower()
        file_lower = file_path.lower()
        
        # Check for critical patterns
        if any(pattern in content_lower or pattern in file_lower 
               for pattern in patterns.get('critical', [])):
            if base_priority == ContextPriority.MEDIUM:
                return ContextPriority.HIGH
            elif base_priority == ContextPriority.HIGH:
                return ContextPriority.CRITICAL
        
        # Check for high patterns
        if any(pattern in content_lower or pattern in file_lower 
               for pattern in patterns.get('high', [])):
            if base_priority == ContextPriority.LOW:
                return ContextPriority.MEDIUM
            elif base_priority == ContextPriority.MEDIUM:
                return ContextPriority.HIGH
        
        return base_priority
    
    @staticmethod
    def calculate_enhanced_relevance_with_intent(
        context_query: ContextQuery,
        content: str,
        file_path: str,
        project_structure: Optional[ProjectStructure] = None
    ) -> float:
        """Calculate relevance score with intent and mode considerations."""
        
        query_words = set(context_query.query.lower().split())
        content_words = set(content.lower().split())
        
        # Base keyword matching
        overlap = len(query_words.intersection(content_words))
        base_score = overlap / len(query_words) if query_words else 0.0
        
        # Intent-specific boosts
        intent_boosts = {
            QueryIntent.DEBUG: {
                'keywords': ['error', 'exception', 'bug', 'fix', 'test', 'assert'],
                'boost': 0.3
            },
            QueryIntent.FEATURE: {
                'keywords': ['implement', 'add', 'create', 'build', 'feature'],
                'boost': 0.2
            },
            QueryIntent.REFACTOR: {
                'keywords': ['refactor', 'improve', 'optimize', 'clean', 'restructure'],
                'boost': 0.2
            },
            QueryIntent.UNDERSTAND: {
                'keywords': ['explain', 'understand', 'how', 'what', 'why', 'overview'],
                'boost': 0.1
            },
            QueryIntent.DOCUMENT: {
                'keywords': ['document', 'example', 'usage', 'api', 'guide'],
                'boost': 0.2
            }
        }
        
        intent_config = intent_boosts.get(context_query.intent, {})
        for keyword in intent_config.get('keywords', []):
            if keyword in content.lower():
                base_score += intent_config.get('boost', 0.1)
        
        # Project structure boosts
        if project_structure:
            if file_path in project_structure.core_modules:
                base_score += 0.3
            
            for framework in project_structure.frameworks:
                if framework.lower() in content.lower():
                    base_score += 0.2
        
        # Response mode adjustments
        if context_query.response_mode == ResponseMode.FAST:
            # Prefer shorter, more direct content for fast responses
            if len(content) < 1000:
                base_score += 0.1
        elif context_query.response_mode == ResponseMode.COMPREHENSIVE:
            # Prefer detailed content for comprehensive responses
            if len(content) > 500:
                base_score += 0.1
        
        return min(1.0, base_score)
    
    @staticmethod
    def generate_summary_with_mode(
        content: str, 
        file_path: str, 
        response_mode: ResponseMode
    ) -> str:
        """Generate summary based on response mode."""
        
        lines = content.split('\n')
        
        # Look for docstrings first
        for i, line in enumerate(lines[:10]):
            line = line.strip()
            if line.startswith('"""') or line.startswith("'''"):
                docstring_lines = []
                for j in range(i, min(i + 10, len(lines))):
                    docstring_lines.append(lines[j].strip())
                    if j > i and (lines[j].strip().endswith('"""') or lines[j].strip().endswith("'''")):
                        break
                
                docstring = ' '.join(docstring_lines)
                
                # Adjust length based on response mode
                if response_mode == ResponseMode.FAST:
                    return docstring[:100] + "..." if len(docstring) > 100 else docstring
                elif response_mode == ResponseMode.COMPREHENSIVE:
                    return docstring[:400] + "..." if len(docstring) > 400 else docstring
                else:  # DETAILED
                    return docstring[:200] + "..." if len(docstring) > 200 else docstring
        
        # Fallback to first few lines
        first_lines = [line.strip() for line in lines[:5] if line.strip()]
        summary = ' '.join(first_lines)
        
        # Adjust length based on response mode
        if response_mode == ResponseMode.FAST:
            return summary[:80] + "..." if len(summary) > 80 else summary
        elif response_mode == ResponseMode.COMPREHENSIVE:
            return summary[:300] + "..." if len(summary) > 300 else summary
        else:  # DETAILED
            return summary[:150] + "..." if len(summary) > 150 else summary
    
    @staticmethod
    def extract_key_concepts_with_intent(
        content: str, 
        file_path: str, 
        intent: QueryIntent
    ) -> List[str]:
        """Extract key concepts based on intent."""
        import re
        concepts = []
        
        # Intent-specific concept extraction
        if intent == QueryIntent.DEBUG:
            # Focus on error handling, test functions
            error_patterns = [
                r'except\s+(\w+)',
                r'raise\s+(\w+)',
                r'def\s+(test_\w+)',
                r'assert\s+(\w+)',
            ]
            for pattern in error_patterns:
                matches = re.findall(pattern, content)
                concepts.extend(matches)
        
        elif intent == QueryIntent.FEATURE:
            # Focus on classes, functions, APIs
            feature_patterns = [
                r'class\s+(\w+)',
                r'def\s+(\w+)',
                r'async\s+def\s+(\w+)',
                r'@(\w+)\.',  # decorators
            ]
            for pattern in feature_patterns:
                matches = re.findall(pattern, content)
                concepts.extend(matches)
        
        elif intent == QueryIntent.UNDERSTAND:
            # Focus on main concepts, imports, architecture
            understand_patterns = [
                r'class\s+(\w+)',
                r'from\s+(\w+)',
                r'import\s+(\w+)',
                r'def\s+(\w+)',
            ]
            for pattern in understand_patterns:
                matches = re.findall(pattern, content)
                concepts.extend(matches[:3])  # Limit for overview
        
        else:
            # General extraction
            general_patterns = [
                r'class\s+(\w+)',
                r'def\s+(\w+)',
                r'from\s+(\w+)',
            ]
            for pattern in general_patterns:
                matches = re.findall(pattern, content)
                concepts.extend(matches)
        
        # Remove duplicates and limit
        unique_concepts = list(set(concepts))
        return unique_concepts[:8]  # Limit to 8 concepts
    
    @staticmethod
    def find_related_files_with_depth(
        file_path: str, 
        context_depth: ContextDepth,
        project_structure: Optional[ProjectStructure] = None
    ) -> List[str]:
        """Find related files based on context depth."""
        if not project_structure:
            return []
        
        related = []
        
        if context_depth == ContextDepth.SHALLOW:
            # Only files in the same directory
            from pathlib import Path
            file_dir = str(Path(file_path).parent)
            
            for module_files in project_structure.package_structure.values():
                for module_file in module_files:
                    if str(Path(module_file).parent) == file_dir and module_file != file_path:
                        related.append(module_file)
                        if len(related) >= 2:  # Limit for shallow
                            break
        
        elif context_depth == ContextDepth.MEDIUM:
            # Files in same package + direct dependencies
            from pathlib import Path
            file_dir = str(Path(file_path).parent)
            
            for module_files in project_structure.package_structure.values():
                for module_file in module_files:
                    if str(Path(module_file).parent) == file_dir and module_file != file_path:
                        related.append(module_file)
            
            # Add some core modules
            related.extend(project_structure.core_modules[:3])
            related = list(set(related))[:5]  # Limit for medium
        
        elif context_depth == ContextDepth.DEEP:
            # Comprehensive related files
            related.extend(project_structure.core_modules)
            related.extend(project_structure.entry_points)
            
            # Add files from same package
            from pathlib import Path
            file_dir = str(Path(file_path).parent)
            
            for module_files in project_structure.package_structure.values():
                for module_file in module_files:
                    if str(Path(module_file).parent) == file_dir:
                        related.append(module_file)
            
            related = list(set(related))[:10]  # Limit for deep
        
        return [f for f in related if f != file_path]
