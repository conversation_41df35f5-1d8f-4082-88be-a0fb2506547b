"""
RAG provider for the context engine.
Bridges RAG systems with the smart context engine.
"""

from __future__ import annotations

import time
from typing import Any, Dict

from ..context_engine import <PERSON><PERSON><PERSON><PERSON><PERSON>, ContextQuery, ContextResult
from ..rag_system import RAGSystem
from ...utils.logging import get_logger

logger = get_logger("context.rag_provider")


class RAGProvider(ContextProvider):
    """Context provider that uses RAG systems for retrieval."""

    def __init__(self, rag_system: RAGSystem):
        self.rag_system = rag_system

    async def get_context(self, query: ContextQuery) -> ContextResult:
        """Get context from the RAG system."""
        start_time = time.time()

        logger.debug(f"RAGProvider.get_context: Starting with query='{query.query}', max_results={query.max_results}")

        try:
            # Convert context query to RAG query
            strategy = self._get_rag_strategy(query)
            logger.debug(f"RAGProvider.get_context: Using strategy='{strategy}'")

            rag_result = await self.rag_system.retrieve(
                query.query,
                top_k=query.max_results,
                strategy=strategy
            )

            logger.debug(f"RAGProvider.get_context: RAG system returned {rag_result.total_chunks} chunks")

            # Convert RAG chunks to context format
            context_items = []
            for chunk in rag_result.chunks:
                context_item = {
                    'content': chunk.content,
                    'file_path': chunk.metadata.get('file_path', 'unknown'),
                    'score': chunk.score or 0.0,
                    'metadata': chunk.metadata,
                    'chunk_id': chunk.chunk_id,
                    'document_id': chunk.document_id
                }
                context_items.append(context_item)

            logger.debug(f"RAGProvider.get_context: Converted to {len(context_items)} context items")

            result = ContextResult(
                query=query.query,
                results=context_items,
                total_found=rag_result.total_chunks,
                retrieval_time=time.time() - start_time,
                source="rag"
            )

            logger.debug(f"RAGProvider.get_context: Returning result with {result.total_found} total found")
            return result

        except Exception as e:
            logger.error(f"RAG provider error: {e}")
            import traceback
            logger.error(f"RAG provider traceback: {traceback.format_exc()}")
            return ContextResult(
                query=query.query,
                results=[],
                total_found=0,
                retrieval_time=time.time() - start_time,
                source="rag_error"
            )

    async def add_context(self, content: str, metadata: Dict[str, Any]) -> None:
        """Add context to the RAG system."""
        try:
            await self.rag_system.add_documents([content], [metadata])
            logger.debug(f"Added context to RAG system: {metadata.get('file_path', 'unknown')}")
        except Exception as e:
            logger.error(f"Failed to add context to RAG: {e}")

    def _get_rag_strategy(self, query: ContextQuery) -> str:
        """Determine RAG strategy based on query intent and response mode."""
        from ..rag_system import RAGStrategy
        from ..context_engine import QueryIntent, ResponseMode

        # Map intent to strategy
        intent_strategy_map = {
            QueryIntent.DEBUG: RAGStrategy.KEYWORD,      # Precise matching for errors
            QueryIntent.FEATURE: RAGStrategy.SEMANTIC,   # Semantic similarity for features
            QueryIntent.UNDERSTAND: RAGStrategy.HYBRID, # Best of both for understanding
            QueryIntent.REFACTOR: RAGStrategy.GRAPH,    # Relationship-aware for refactoring
            QueryIntent.DOCUMENT: RAGStrategy.SEMANTIC, # Semantic for documentation
            QueryIntent.GENERAL: RAGStrategy.HYBRID     # Balanced approach
        }

        strategy = intent_strategy_map.get(query.intent, RAGStrategy.HYBRID)

        # Adjust for response mode
        if query.response_mode == ResponseMode.FAST:
            # Use simpler strategies for speed
            if strategy == RAGStrategy.HYBRID:
                strategy = RAGStrategy.KEYWORD
        elif query.response_mode == ResponseMode.COMPREHENSIVE:
            # Use more sophisticated strategies for depth
            if strategy == RAGStrategy.KEYWORD:
                strategy = RAGStrategy.HYBRID

        return strategy.value
