"""
CLI Commands Indexer for RAG Context

Automatically indexes all CLI commands and their documentation
so agents can recommend and understand available commands.
"""

import inspect
import re
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass


@dataclass
class CommandInfo:
    """Information about a CLI command."""
    name: str
    description: str
    usage: str
    examples: List[str]
    category: str
    aliases: List[str]
    parameters: List[str]
    source_file: str
    source_line: int


class CLICommandsIndexer:
    """
    Indexes CLI commands for RAG context.
    
    This ensures that agents always know about available commands
    and can recommend them appropriately.
    """
    
    def __init__(self):
        """Initialize the CLI commands indexer."""
        self.commands: Dict[str, CommandInfo] = {}
        self.categories = {
            "core": "Core functionality commands",
            "development": "Development and coding commands", 
            "project": "Project management commands",
            "system": "System and configuration commands",
            "editing": "File editing commands",
            "search": "Search and analysis commands",
            "ai": "AI and agent commands"
        }
    
    def index_shell_commands(self, shell_instance) -> Dict[str, CommandInfo]:
        """
        Index all commands from an interactive shell instance.
        
        Args:
            shell_instance: InteractiveShell instance
            
        Returns:
            Dictionary of indexed commands
        """
        self.commands.clear()
        
        # Index built-in commands
        self._index_builtin_commands(shell_instance)
        
        # Index dynamic commands from shell.commands
        self._index_dynamic_commands(shell_instance)
        
        # Index editing commands if available
        self._index_editing_commands()
        
        return self.commands
    
    def _index_builtin_commands(self, shell_instance):
        """Index built-in shell commands."""
        builtin_commands = [
            {
                "name": "help",
                "description": "Show help information and available commands",
                "usage": "/help [command]",
                "examples": ["/help", "/help search", "/help edit"],
                "category": "core",
                "aliases": ["h", "?"]
            },
            {
                "name": "exit",
                "description": "Exit the shell gracefully",
                "usage": "/exit",
                "examples": ["/exit", "Ctrl+C twice"],
                "category": "core",
                "aliases": ["quit", "q"]
            },
            {
                "name": "clear",
                "description": "Clear conversation history",
                "usage": "/clear",
                "examples": ["/clear"],
                "category": "core",
                "aliases": ["cls"]
            },
            {
                "name": "history",
                "description": "Show conversation history",
                "usage": "/history [count]",
                "examples": ["/history", "/history 10"],
                "category": "core",
                "aliases": ["hist"]
            },
            {
                "name": "project",
                "description": "Set or show current project directory",
                "usage": "/project [path]",
                "examples": ["/project", "/project /path/to/project", "/project ."],
                "category": "project",
                "aliases": ["proj", "cd"]
            },
            {
                "name": "model",
                "description": "Switch AI model",
                "usage": "/model <model_name>",
                "examples": ["/model llama3.2:3b", "/model codellama:7b"],
                "category": "ai",
                "aliases": ["m"]
            },
            {
                "name": "models",
                "description": "List available AI models",
                "usage": "/models",
                "examples": ["/models"],
                "category": "ai",
                "aliases": ["list-models"]
            },
            {
                "name": "search",
                "description": "Search for code, files, or content in the project",
                "usage": "/search <query>",
                "examples": ["/search async def", "/search config.py", "/search error handling"],
                "category": "search",
                "aliases": ["find", "grep"]
            },
            {
                "name": "edit",
                "description": "Professional file editing with diff preview",
                "usage": "/edit <file> [options]",
                "examples": ["/edit src/main.py", "/edit config.py --lines 10:20"],
                "category": "editing",
                "aliases": ["e"]
            },
            {
                "name": "smart-edit",
                "description": "AI-powered smart editing with natural language instructions",
                "usage": "/smart-edit <file> <instruction>",
                "examples": ["/smart-edit main.py 'add error handling'", "/smart-edit config.py 'update database settings'"],
                "category": "editing",
                "aliases": ["se", "ai-edit"]
            },
            {
                "name": "diff",
                "description": "Show differences for file changes",
                "usage": "/diff [change_id]",
                "examples": ["/diff", "/diff abc123"],
                "category": "editing",
                "aliases": ["d"]
            },
            {
                "name": "revert",
                "description": "Revert file changes",
                "usage": "/revert <change_id>",
                "examples": ["/revert abc123"],
                "category": "editing",
                "aliases": ["undo"]
            },
            {
                "name": "analyze",
                "description": "Analyze current project structure and statistics",
                "usage": "/analyze",
                "examples": ["/analyze"],
                "category": "project",
                "aliases": ["stats", "info"]
            },
            {
                "name": "index",
                "description": "Index project files for better context retrieval",
                "usage": "/index",
                "examples": ["/index"],
                "category": "project",
                "aliases": ["idx"]
            },
            {
                "name": "config",
                "description": "Manage configuration settings",
                "usage": "/config [action] [key] [value]",
                "examples": ["/config show", "/config set api_key abc123"],
                "category": "system",
                "aliases": ["cfg"]
            },
            {
                "name": "version",
                "description": "Show Agent Swarm version information",
                "usage": "/version",
                "examples": ["/version"],
                "category": "system",
                "aliases": ["v", "ver"]
            }
        ]
        
        for cmd_info in builtin_commands:
            command = CommandInfo(
                name=cmd_info["name"],
                description=cmd_info["description"],
                usage=cmd_info["usage"],
                examples=cmd_info["examples"],
                category=cmd_info["category"],
                aliases=cmd_info["aliases"],
                parameters=self._extract_parameters(cmd_info["usage"]),
                source_file="interactive_shell.py",
                source_line=0
            )
            self.commands[cmd_info["name"]] = command
    
    def _index_dynamic_commands(self, shell_instance):
        """Index dynamically registered commands."""
        if not hasattr(shell_instance, 'commands'):
            return
        
        for cmd_name, cmd_func in shell_instance.commands.items():
            if cmd_name in self.commands:
                continue  # Skip already indexed commands
            
            # Extract information from function
            description = self._extract_function_description(cmd_func)
            usage = f"/{cmd_name}"
            category = self._categorize_command(cmd_name)
            
            command = CommandInfo(
                name=cmd_name,
                description=description,
                usage=usage,
                examples=[f"/{cmd_name}"],
                category=category,
                aliases=[],
                parameters=[],
                source_file=self._get_function_source_file(cmd_func),
                source_line=self._get_function_source_line(cmd_func)
            )
            self.commands[cmd_name] = command
    
    def _index_editing_commands(self):
        """Index professional editing commands."""
        editing_commands = [
            {
                "name": "file",
                "description": "File operations (read, write, create, delete)",
                "usage": "/file <action> <path> [options]",
                "examples": ["/file read main.py", "/file write config.py", "/file create new.py"],
                "category": "editing",
                "aliases": ["f"]
            },
            {
                "name": "create",
                "description": "Create new files or directories",
                "usage": "/create <type> <path>",
                "examples": ["/create file main.py", "/create dir src/utils"],
                "category": "editing",
                "aliases": ["new", "touch"]
            },
            {
                "name": "delete",
                "description": "Delete files or directories",
                "usage": "/delete <path>",
                "examples": ["/delete old_file.py", "/delete temp/"],
                "category": "editing",
                "aliases": ["del", "rm"]
            }
        ]
        
        for cmd_info in editing_commands:
            if cmd_info["name"] not in self.commands:
                command = CommandInfo(
                    name=cmd_info["name"],
                    description=cmd_info["description"],
                    usage=cmd_info["usage"],
                    examples=cmd_info["examples"],
                    category=cmd_info["category"],
                    aliases=cmd_info["aliases"],
                    parameters=self._extract_parameters(cmd_info["usage"]),
                    source_file="editing_commands.py",
                    source_line=0
                )
                self.commands[cmd_info["name"]] = command
    
    def _extract_parameters(self, usage: str) -> List[str]:
        """Extract parameters from usage string."""
        # Extract parameters in <> and []
        params = re.findall(r'<([^>]+)>|\[([^\]]+)\]', usage)
        return [param[0] or param[1] for param in params]
    
    def _extract_function_description(self, func) -> str:
        """Extract description from function docstring."""
        if hasattr(func, '__doc__') and func.__doc__:
            # Get first line of docstring
            return func.__doc__.strip().split('\n')[0]
        return f"Execute {getattr(func, '__name__', 'command')}"
    
    def _categorize_command(self, cmd_name: str) -> str:
        """Categorize command based on name."""
        if cmd_name in ['help', 'exit', 'clear', 'history']:
            return "core"
        elif cmd_name in ['edit', 'create', 'delete', 'file', 'diff', 'revert']:
            return "editing"
        elif cmd_name in ['search', 'find', 'grep', 'analyze']:
            return "search"
        elif cmd_name in ['project', 'index', 'stats']:
            return "project"
        elif cmd_name in ['model', 'models', 'ai']:
            return "ai"
        elif cmd_name in ['config', 'version', 'update']:
            return "system"
        else:
            return "development"
    
    def _get_function_source_file(self, func) -> str:
        """Get source file of function."""
        try:
            return inspect.getfile(func)
        except:
            return "unknown"
    
    def _get_function_source_line(self, func) -> int:
        """Get source line of function."""
        try:
            return inspect.getsourcelines(func)[1]
        except:
            return 0
    
    def generate_commands_documentation(self) -> str:
        """Generate comprehensive commands documentation for RAG."""
        doc = "# Agent Swarm CLI Commands Reference\n\n"
        doc += "This is a comprehensive reference of all available CLI commands in Agent Swarm.\n\n"
        
        # Group by category
        by_category = {}
        for cmd in self.commands.values():
            if cmd.category not in by_category:
                by_category[cmd.category] = []
            by_category[cmd.category].append(cmd)
        
        for category, commands in by_category.items():
            doc += f"## {category.title()} Commands\n\n"
            doc += f"{self.categories.get(category, 'Commands in this category')}\n\n"
            
            for cmd in sorted(commands, key=lambda x: x.name):
                doc += f"### /{cmd.name}\n\n"
                doc += f"**Description:** {cmd.description}\n\n"
                doc += f"**Usage:** `{cmd.usage}`\n\n"
                
                if cmd.aliases:
                    doc += f"**Aliases:** {', '.join(f'/{alias}' for alias in cmd.aliases)}\n\n"
                
                if cmd.parameters:
                    doc += f"**Parameters:** {', '.join(cmd.parameters)}\n\n"
                
                if cmd.examples:
                    doc += "**Examples:**\n"
                    for example in cmd.examples:
                        doc += f"- `{example}`\n"
                    doc += "\n"
                
                doc += "---\n\n"
        
        # Add usage patterns
        doc += "## Common Usage Patterns\n\n"
        doc += "### Natural Language + Commands\n"
        doc += "You can use both natural language and explicit commands:\n"
        doc += "- Natural: 'search for async functions'\n"
        doc += "- Explicit: '/search async functions'\n\n"
        
        doc += "### File Operations\n"
        doc += "- `/edit main.py` - Edit file with professional diff preview\n"
        doc += "- `/smart-edit main.py 'add error handling'` - AI-powered editing\n"
        doc += "- `/file read config.py` - Read file content\n\n"
        
        doc += "### Project Management\n"
        doc += "- `/project /path/to/project` - Set project directory\n"
        doc += "- `/analyze` - Analyze project structure\n"
        doc += "- `/index` - Index files for better context\n\n"
        
        return doc
    
    def get_command_suggestions(self, partial_command: str) -> List[CommandInfo]:
        """Get command suggestions for autocomplete."""
        partial = partial_command.lower().lstrip('/')
        
        suggestions = []
        
        # Exact matches first
        for cmd in self.commands.values():
            if cmd.name.startswith(partial):
                suggestions.append(cmd)
        
        # Alias matches
        for cmd in self.commands.values():
            for alias in cmd.aliases:
                if alias.startswith(partial):
                    suggestions.append(cmd)
        
        # Sort by relevance (exact match, then alphabetical)
        suggestions.sort(key=lambda x: (not x.name.startswith(partial), x.name))
        
        return suggestions[:10]  # Limit to 10 suggestions
    
    def get_commands_for_rag(self) -> str:
        """Get commands documentation formatted for RAG context."""
        return self.generate_commands_documentation()
