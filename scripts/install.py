#!/usr/bin/env python3
"""
Agent Swarm Installation Script

This script installs Agent Swarm and its dependencies.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {cmd}")
        print(f"   Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def install_agent_swarm():
    """Install Agent Swarm."""
    print("🚀 Installing Agent Swarm")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Check if we're in development mode
    current_dir = Path(__file__).parent
    if (current_dir / "src" / "agent_swarm").exists():
        print("🔧 Development installation detected")
        print("📦 Installing in development mode...")
        
        # Install in development mode
        if not run_command("pip install -e .", "Installing Agent Swarm (development mode)"):
            return False
            
        # Install optional dependencies
        if not run_command("pip install -e .[rag,cli]", "Installing optional dependencies"):
            print("⚠️ Some optional dependencies failed to install")
            
    else:
        print("📦 Installing from PyPI...")
        
        # Install from PyPI
        if not run_command("pip install agent-swarm[rag,cli]", "Installing Agent Swarm"):
            return False
    
    # Verify installation
    try:
        import agent_swarm
        print(f"✅ Agent Swarm v{agent_swarm.__version__} installed successfully!")
        
        # Test CLI
        result = subprocess.run(["agent-swarm", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ CLI is working correctly")
        else:
            print("⚠️ CLI installation may have issues")
            
    except ImportError:
        print("❌ Installation verification failed")
        return False
    
    print("\n🎉 Installation Complete!")
    print("=" * 50)
    print("💡 Quick Start:")
    print("   agent-swarm --help                    # Show help")
    print("   agent-swarm --project /path/to/code   # Start with project")
    print("   agent-swarm-shell                     # Alternative command")
    print()
    print("📚 Documentation: https://agent-swarm.readthedocs.io")
    print("🐛 Issues: https://github.com/agent-swarm/agent-swarm/issues")
    
    return True


if __name__ == "__main__":
    success = install_agent_swarm()
    sys.exit(0 if success else 1)
