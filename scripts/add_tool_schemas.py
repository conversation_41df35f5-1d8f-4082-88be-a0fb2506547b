#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to add get_schema methods to all tool classes.
"""

import re
from pathlib import Path


def add_process_tool_schema():
    """Add schema method to ProcessTool."""
    file_path = Path("src/agent_swarm/tools/cli_tools.py")
    content = file_path.read_text()
    
    # Find the end of ProcessTool class (before NetworkTool)
    pattern = r'(            return \{"success": False, "error": str\(e\)\}\n\n\nclass NetworkTool)'
    replacement = '''            return {"success": False, "error": str(e)}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "list_processes": {
                "description": "List running processes with optional filtering",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "filter_name": {"type": "string", "description": "Filter processes by name (optional)"}
                    }
                }
            },
            "kill_process": {
                "description": "Kill a process by PID",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "pid": {"type": "integer", "description": "Process ID to kill"},
                        "force": {"type": "boolean", "description": "Force kill the process", "default": False}
                    },
                    "required": ["pid"]
                }
            },
            "get_process_info": {
                "description": "Get detailed information about a process",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "pid": {"type": "integer", "description": "Process ID to get info for"}
                    },
                    "required": ["pid"]
                }
            }
        }


class NetworkTool'''
    
    new_content = re.sub(pattern, replacement, content)
    if new_content != content:
        file_path.write_text(new_content)
        print("✅ Added ProcessTool schema")
        return True
    return False


def add_network_tool_schema():
    """Add schema method to NetworkTool."""
    file_path = Path("src/agent_swarm/tools/cli_tools.py")
    content = file_path.read_text()
    
    # Find the end of NetworkTool class (before SystemInfoTool)
    pattern = r'(            return \{"success": False, "error": str\(e\)\}\n\n\nclass SystemInfoTool)'
    replacement = '''            return {"success": False, "error": str(e)}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "ping_host": {
                "description": "Ping a host to test connectivity",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "host": {"type": "string", "description": "Host to ping"},
                        "count": {"type": "integer", "description": "Number of ping packets", "default": 4}
                    },
                    "required": ["host"]
                }
            },
            "check_port": {
                "description": "Check if a port is open on a host",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "host": {"type": "string", "description": "Host to check"},
                        "port": {"type": "integer", "description": "Port number to check"},
                        "timeout": {"type": "integer", "description": "Connection timeout", "default": 5}
                    },
                    "required": ["host", "port"]
                }
            },
            "get_network_info": {
                "description": "Get network interface information",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            }
        }


class SystemInfoTool'''
    
    new_content = re.sub(pattern, replacement, content)
    if new_content != content:
        file_path.write_text(new_content)
        print("✅ Added NetworkTool schema")
        return True
    return False


def add_system_info_tool_schema():
    """Add schema method to SystemInfoTool."""
    file_path = Path("src/agent_swarm/tools/cli_tools.py")
    content = file_path.read_text()
    
    # Find the end of SystemInfoTool class (before DevelopmentTool)
    pattern = r'(            return \{"success": False, "error": str\(e\)\}\n\n\nclass DevelopmentTool)'
    replacement = '''            return {"success": False, "error": str(e)}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "get_system_info": {
                "description": "Get comprehensive system information",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            },
            "get_disk_usage": {
                "description": "Get disk usage information",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Path to check disk usage for", "default": "/"}
                    }
                }
            },
            "get_memory_info": {
                "description": "Get memory usage information",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            }
        }


class DevelopmentTool'''
    
    new_content = re.sub(pattern, replacement, content)
    if new_content != content:
        file_path.write_text(new_content)
        print("✅ Added SystemInfoTool schema")
        return True
    return False


def add_development_tool_schema():
    """Add schema method to DevelopmentTool."""
    file_path = Path("src/agent_swarm/tools/cli_tools.py")
    content = file_path.read_text()
    
    # Find the end of DevelopmentTool class (end of file)
    pattern = r'(            return \{"success": False, "error": str\(e\)\}$)'
    replacement = '''            return {"success": False, "error": str(e)}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for MCP integration."""
        return {
            "analyze_code": {
                "description": "Analyze code quality and structure",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to analyze"}
                    },
                    "required": ["file_path"]
                }
            },
            "format_code": {
                "description": "Format code using appropriate formatter",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to format"},
                        "formatter": {"type": "string", "description": "Formatter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            },
            "run_tests": {
                "description": "Run tests for a project",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "test_path": {"type": "string", "description": "Path to test file or directory"},
                        "test_framework": {"type": "string", "description": "Test framework to use (auto-detect if not specified)"}
                    }
                }
            },
            "lint_code": {
                "description": "Lint code for style and quality issues",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "Path to code file to lint"},
                        "linter": {"type": "string", "description": "Linter to use (auto-detect if not specified)"}
                    },
                    "required": ["file_path"]
                }
            }
        }'''
    
    new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    if new_content != content:
        file_path.write_text(new_content)
        print("✅ Added DevelopmentTool schema")
        return True
    return False


def main():
    """Add all missing tool schemas."""
    print("🔧 Adding get_schema methods to all tool classes...")
    
    changes = 0
    
    # Add schemas to each tool class
    if add_process_tool_schema():
        changes += 1
    
    if add_network_tool_schema():
        changes += 1
    
    if add_system_info_tool_schema():
        changes += 1
    
    if add_development_tool_schema():
        changes += 1
    
    print(f"\n📊 Summary: Added {changes} schema methods")
    
    if changes > 0:
        print("✅ All tool schemas added successfully!")
    else:
        print("⏭️  No changes needed - schemas already exist")


if __name__ == "__main__":
    main()