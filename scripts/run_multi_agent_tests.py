#!/usr/bin/env python3
"""
Comprehensive Multi-Agent System Test Runner

This script runs all multi-agent tests and demonstrations to showcase
the revolutionary capabilities of the Agent Swarm multi-agent system.
"""

import asyncio
import subprocess
import sys
import time
from pathlib import Path
from typing import List, Dict, Any
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn

console = Console()


class MultiAgentTestRunner:
    """Comprehensive test runner for multi-agent system."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.test_results = []
        
    def run_command(self, command: str, description: str, timeout: int = 300) -> Dict[str, Any]:
        """Run a command and capture results."""
        console.print(f"🔄 [yellow]{description}[/yellow]")
        
        try:
            start_time = time.time()
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.project_root
            )
            execution_time = time.time() - start_time
            
            success = result.returncode == 0
            
            test_result = {
                "description": description,
                "command": command,
                "success": success,
                "execution_time": execution_time,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode
            }
            
            if success:
                console.print(f"✅ [green]{description} - PASSED[/green] ({execution_time:.2f}s)")
            else:
                console.print(f"❌ [red]{description} - FAILED[/red] ({execution_time:.2f}s)")
                if result.stderr:
                    console.print(f"[red]Error: {result.stderr[:200]}...[/red]")
            
            self.test_results.append(test_result)
            return test_result
            
        except subprocess.TimeoutExpired:
            console.print(f"⏰ [red]{description} - TIMEOUT[/red]")
            test_result = {
                "description": description,
                "command": command,
                "success": False,
                "execution_time": timeout,
                "stdout": "",
                "stderr": "Command timed out",
                "return_code": -1
            }
            self.test_results.append(test_result)
            return test_result
            
        except Exception as e:
            console.print(f"💥 [red]{description} - ERROR: {e}[/red]")
            test_result = {
                "description": description,
                "command": command,
                "success": False,
                "execution_time": 0,
                "stdout": "",
                "stderr": str(e),
                "return_code": -1
            }
            self.test_results.append(test_result)
            return test_result
    
    def run_unit_tests(self):
        """Run unit tests for multi-agent components."""
        console.print("\n🧪 [bold blue]Running Unit Tests[/bold blue]")
        
        unit_tests = [
            {
                "command": "python -m pytest tests/unit/test_multi_agent_communication.py -v",
                "description": "Multi-Agent Communication Hub Tests"
            },
            {
                "command": "python -m pytest tests/unit/test_multi_agent_consensus_stage.py -v",
                "description": "Multi-Agent Consensus Stage Tests"
            },
            {
                "command": "python -m pytest tests/unit/test_multi_agent_communication.py::TestMessage -v",
                "description": "Message System Tests"
            },
            {
                "command": "python -m pytest tests/unit/test_multi_agent_communication.py::TestConsensusAlgorithms -v",
                "description": "Consensus Algorithms Tests"
            },
            {
                "command": "python -m pytest tests/unit/test_multi_agent_consensus_stage.py::TestVirtualAgent -v",
                "description": "Virtual Agent Tests"
            }
        ]
        
        for test in unit_tests:
            self.run_command(test["command"], test["description"])
    
    def run_integration_tests(self):
        """Run integration tests for multi-agent system."""
        console.print("\n🔗 [bold blue]Running Integration Tests[/bold blue]")
        
        integration_tests = [
            {
                "command": "python -m pytest tests/integration/test_multi_agent_system_integration.py -v",
                "description": "Complete Multi-Agent System Integration"
            },
            {
                "command": "python -m pytest tests/integration/test_multi_agent_system_integration.py::TestMultiAgentSystemIntegration::test_technology_decision_consensus -v",
                "description": "Technology Decision Consensus"
            },
            {
                "command": "python -m pytest tests/integration/test_multi_agent_system_integration.py::TestMultiAgentSystemIntegration::test_business_strategy_consensus -v",
                "description": "Business Strategy Consensus"
            },
            {
                "command": "python -m pytest tests/integration/test_multi_agent_system_integration.py::TestMultiAgentSystemIntegration::test_security_risk_assessment -v",
                "description": "Security Risk Assessment"
            },
            {
                "command": "python -m pytest tests/integration/test_multi_agent_system_integration.py::TestMultiAgentSystemIntegration::test_complex_multi_domain_decision -v",
                "description": "Complex Multi-Domain Decision"
            },
            {
                "command": "python -m pytest tests/integration/test_multi_agent_system_integration.py::TestMultiAgentSystemIntegration::test_conflicting_perspectives_resolution -v",
                "description": "Conflicting Perspectives Resolution"
            }
        ]
        
        for test in integration_tests:
            self.run_command(test["command"], test["description"], timeout=120)
    
    def run_performance_benchmarks(self):
        """Run performance benchmarks."""
        console.print("\n📊 [bold blue]Running Performance Benchmarks[/bold blue]")
        
        benchmark_tests = [
            {
                "command": "python examples/multi_agent_benchmarks.py",
                "description": "Comprehensive Performance Benchmarks"
            }
        ]
        
        for test in benchmark_tests:
            self.run_command(test["command"], test["description"], timeout=600)
    
    def run_scenario_tests(self):
        """Run real-world scenario tests."""
        console.print("\n🌍 [bold blue]Running Real-World Scenarios[/bold blue]")
        
        scenario_tests = [
            {
                "command": "python examples/multi_agent_scenarios.py",
                "description": "Real-World Decision Scenarios"
            }
        ]
        
        for test in scenario_tests:
            self.run_command(test["command"], test["description"], timeout=300)
    
    def run_adaptive_intent_pipeline_tests(self):
        """Run tests for the complete adaptive intent processing pipeline."""
        console.print("\n🧠 [bold blue]Running Adaptive Intent Processing Pipeline Tests[/bold blue]")
        
        pipeline_tests = [
            {
                "command": "python -m pytest tests/unit/test_mathematical_algorithms.py -v",
                "description": "Mathematical Algorithms Tests"
            },
            {
                "command": "python examples/adaptive_intent_processor_demo.py",
                "description": "Adaptive Intent Processor Demo"
            }
        ]
        
        for test in pipeline_tests:
            self.run_command(test["command"], test["description"])
    
    def display_summary(self):
        """Display comprehensive test summary."""
        console.print("\n📋 [bold]Test Execution Summary[/bold]")
        
        # Calculate statistics
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        total_time = sum(result["execution_time"] for result in self.test_results)
        
        # Summary table
        summary_table = Table(title="Test Results Summary")
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Value", style="green")
        summary_table.add_column("Percentage", style="yellow")
        
        summary_table.add_row("Total Tests", str(total_tests), "100%")
        summary_table.add_row("Passed", str(passed_tests), f"{passed_tests/total_tests*100:.1f}%")
        summary_table.add_row("Failed", str(failed_tests), f"{failed_tests/total_tests*100:.1f}%")
        summary_table.add_row("Total Time", f"{total_time:.2f}s", "-")
        summary_table.add_row("Average Time", f"{total_time/total_tests:.2f}s", "-")
        
        console.print(summary_table)
        
        # Detailed results table
        if self.test_results:
            console.print("\n📊 [bold]Detailed Test Results[/bold]")
            
            results_table = Table()
            results_table.add_column("Test", style="cyan", width=40)
            results_table.add_column("Status", style="magenta", width=10)
            results_table.add_column("Time", style="green", width=10)
            results_table.add_column("Details", style="yellow", width=30)
            
            for result in self.test_results:
                status = "✅ PASS" if result["success"] else "❌ FAIL"
                time_str = f"{result['execution_time']:.2f}s"
                
                if result["success"]:
                    details = "Completed successfully"
                else:
                    details = result["stderr"][:50] + "..." if result["stderr"] else "Unknown error"
                
                results_table.add_row(
                    result["description"],
                    status,
                    time_str,
                    details
                )
            
            console.print(results_table)
        
        # Overall assessment
        success_rate = passed_tests / total_tests if total_tests > 0 else 0
        
        if success_rate >= 0.9:
            status_color = "green"
            status_text = "EXCELLENT"
            status_emoji = "🎉"
        elif success_rate >= 0.8:
            status_color = "yellow"
            status_text = "GOOD"
            status_emoji = "👍"
        elif success_rate >= 0.6:
            status_color = "orange"
            status_text = "FAIR"
            status_emoji = "⚠️"
        else:
            status_color = "red"
            status_text = "NEEDS WORK"
            status_emoji = "🔧"
        
        console.print(f"\n{status_emoji} [bold {status_color}]Overall Status: {status_text}[/bold {status_color}]")
        console.print(f"[{status_color}]Success Rate: {success_rate:.1%}[/{status_color}]")
        
        # Recommendations
        if failed_tests > 0:
            console.print(f"\n💡 [bold yellow]Recommendations:[/bold yellow]")
            console.print("• Review failed test details above")
            console.print("• Check system dependencies and environment")
            console.print("• Ensure all required packages are installed")
            console.print("• Run individual failing tests for detailed debugging")
    
    def check_environment(self):
        """Check if the environment is properly set up."""
        console.print("🔍 [bold]Checking Environment[/bold]")
        
        # Check Python version
        python_version = sys.version_info
        if python_version.major >= 3 and python_version.minor >= 8:
            console.print(f"✅ Python {python_version.major}.{python_version.minor} - OK")
        else:
            console.print(f"❌ Python {python_version.major}.{python_version.minor} - Requires Python 3.8+")
        
        # Check required packages
        required_packages = [
            "pytest", "asyncio", "numpy", "scikit-learn", 
            "tiktoken", "gitpython", "aiofiles", "rich"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
                console.print(f"✅ {package} - Available")
            except ImportError:
                console.print(f"❌ {package} - Missing")
                missing_packages.append(package)
        
        if missing_packages:
            console.print(f"\n⚠️ [yellow]Missing packages: {', '.join(missing_packages)}[/yellow]")
            console.print("Install with: pip install " + " ".join(missing_packages))
            return False
        
        # Check if agent_swarm can be imported
        try:
            sys.path.insert(0, str(self.project_root / "src"))
            import agent_swarm
            console.print("✅ Agent Swarm - Available")
            return True
        except ImportError as e:
            console.print(f"❌ Agent Swarm - Import Error: {e}")
            return False
    
    def run_all_tests(self):
        """Run all multi-agent tests and demonstrations."""
        console.print(Panel.fit(
            "[bold cyan]🚀 Multi-Agent System Comprehensive Test Suite[/bold cyan]\n\n"
            "This test suite validates the revolutionary multi-agent consensus system\n"
            "including virtual agents, communication protocols, consensus algorithms,\n"
            "and real-world decision-making scenarios.\n\n"
            "[yellow]Features being tested:[/yellow]\n"
            "• Virtual agent swarm with specialized roles\n"
            "• O(n³) consensus algorithm with Byzantine fault tolerance\n"
            "• Emergent communication protocols\n"
            "• Conflict resolution mechanisms\n"
            "• Real-world decision scenarios\n"
            "• Performance benchmarks and scalability",
            title="Test Suite Overview"
        ))
        
        # Check environment first
        if not self.check_environment():
            console.print("\n❌ [red]Environment check failed. Please fix issues before running tests.[/red]")
            return
        
        console.print("\n🚀 [bold green]Starting Comprehensive Test Execution...[/bold green]")
        
        # Run all test suites
        test_suites = [
            ("Unit Tests", self.run_unit_tests),
            ("Integration Tests", self.run_integration_tests),
            ("Adaptive Intent Pipeline", self.run_adaptive_intent_pipeline_tests),
            ("Performance Benchmarks", self.run_performance_benchmarks),
            ("Real-World Scenarios", self.run_scenario_tests)
        ]
        
        start_time = time.time()
        
        for suite_name, suite_func in test_suites:
            console.print(f"\n🔄 [bold]Starting {suite_name}...[/bold]")
            try:
                suite_func()
            except Exception as e:
                console.print(f"💥 [red]Error in {suite_name}: {e}[/red]")
        
        total_execution_time = time.time() - start_time
        
        console.print(f"\n⏱️ [bold]Total Execution Time: {total_execution_time:.2f} seconds[/bold]")
        
        # Display comprehensive summary
        self.display_summary()
        
        console.print("\n🎯 [bold]Multi-Agent System Test Suite Complete![/bold]")


def main():
    """Main function to run all tests."""
    runner = MultiAgentTestRunner()
    runner.run_all_tests()


if __name__ == "__main__":
    main()