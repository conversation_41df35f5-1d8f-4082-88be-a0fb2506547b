#!/usr/bin/env python3
"""
Comprehensive test runner for Agent Swarm.

Runs unit tests, integration tests, and E2E tests with proper reporting
and dependency checking.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
import time
from typing import List, Dict, Any


class TestRunner:
    """Comprehensive test runner for Agent Swarm."""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.project_root = Path(__file__).parent.parent
        self.test_results: Dict[str, Any] = {}
        
    def run_command(self, cmd: List[str], cwd: Path = None) -> Dict[str, Any]:
        """Run a command and return results."""
        if cwd is None:
            cwd = self.project_root
            
        if self.verbose:
            print(f"🔧 Running: {' '.join(cmd)}")
            print(f"📁 Working directory: {cwd}")
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                cmd,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            duration = time.time() - start_time
            
            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "duration": duration,
                "command": " ".join(cmd)
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "returncode": -1,
                "stdout": "",
                "stderr": "Command timed out after 5 minutes",
                "duration": time.time() - start_time,
                "command": " ".join(cmd)
            }
        except Exception as e:
            return {
                "success": False,
                "returncode": -1,
                "stdout": "",
                "stderr": str(e),
                "duration": time.time() - start_time,
                "command": " ".join(cmd)
            }
    
    def check_dependencies(self) -> bool:
        """Check if required dependencies are available."""
        print("🔍 Checking dependencies...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            print("❌ Python 3.8+ required")
            return False
        
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        
        # Check pytest
        result = self.run_command(["python", "-m", "pytest", "--version"])
        if not result["success"]:
            print("❌ pytest not available")
            print("💡 Install with: pip install pytest pytest-asyncio pytest-cov")
            return False
        
        print("✅ pytest available")
        
        # Check if agent-swarm is installed
        result = self.run_command(["python", "-c", "import agent_swarm; print(agent_swarm.__version__)"])
        if result["success"]:
            version = result["stdout"].strip()
            print(f"✅ agent-swarm {version} installed")
        else:
            print("⚠️  agent-swarm not installed as package")
            print("💡 Running tests in development mode")
        
        # Check optional editing dependencies
        editing_deps = ["unidiff", "chardet", "rich"]
        editing_available = True
        
        for dep in editing_deps:
            result = self.run_command(["python", "-c", f"import {dep}"])
            if result["success"]:
                print(f"✅ {dep} available")
            else:
                print(f"⚠️  {dep} not available")
                editing_available = False
        
        if not editing_available:
            print("💡 Install editing dependencies: pip install agent-swarm[editing]")
        
        return True
    
    def run_unit_tests(self) -> bool:
        """Run unit tests."""
        print("\n🧪 Running Unit Tests...")
        print("=" * 50)
        
        cmd = [
            "python", "-m", "pytest",
            "tests/",
            "-v",
            "--tb=short",
            "--durations=10",
            "-x",  # Stop on first failure
            "--ignore=tests/integration/",
            "--ignore=tests/e2e/"
        ]
        
        if self.verbose:
            cmd.append("-s")
        
        result = self.run_command(cmd)
        self.test_results["unit_tests"] = result
        
        if result["success"]:
            print(f"✅ Unit tests passed ({result['duration']:.2f}s)")
            return True
        else:
            print(f"❌ Unit tests failed ({result['duration']:.2f}s)")
            if self.verbose:
                print("STDOUT:", result["stdout"])
                print("STDERR:", result["stderr"])
            return False
    
    def run_integration_tests(self) -> bool:
        """Run integration tests."""
        print("\n🔗 Running Integration Tests...")
        print("=" * 50)
        
        cmd = [
            "python", "-m", "pytest",
            "tests/integration/",
            "-v",
            "--tb=short",
            "--durations=10"
        ]
        
        if self.verbose:
            cmd.append("-s")
        
        result = self.run_command(cmd)
        self.test_results["integration_tests"] = result
        
        if result["success"]:
            print(f"✅ Integration tests passed ({result['duration']:.2f}s)")
            return True
        else:
            print(f"❌ Integration tests failed ({result['duration']:.2f}s)")
            if self.verbose:
                print("STDOUT:", result["stdout"])
                print("STDERR:", result["stderr"])
            return False
    
    def run_e2e_tests(self) -> bool:
        """Run end-to-end tests."""
        print("\n🎯 Running E2E Tests...")
        print("=" * 50)
        
        cmd = [
            "python", "-m", "pytest",
            "tests/e2e/",
            "-v",
            "--tb=short",
            "--durations=10"
        ]
        
        if self.verbose:
            cmd.append("-s")
        
        result = self.run_command(cmd)
        self.test_results["e2e_tests"] = result
        
        if result["success"]:
            print(f"✅ E2E tests passed ({result['duration']:.2f}s)")
            return True
        else:
            print(f"❌ E2E tests failed ({result['duration']:.2f}s)")
            if self.verbose:
                print("STDOUT:", result["stdout"])
                print("STDERR:", result["stderr"])
            return False
    
    def run_coverage_tests(self) -> bool:
        """Run tests with coverage reporting."""
        print("\n📊 Running Coverage Tests...")
        print("=" * 50)
        
        cmd = [
            "python", "-m", "pytest",
            "tests/",
            "--cov=agent_swarm",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--cov-fail-under=70",  # Require 70% coverage
            "-v"
        ]
        
        result = self.run_command(cmd)
        self.test_results["coverage_tests"] = result
        
        if result["success"]:
            print(f"✅ Coverage tests passed ({result['duration']:.2f}s)")
            print("📊 Coverage report generated in htmlcov/")
            return True
        else:
            print(f"❌ Coverage tests failed ({result['duration']:.2f}s)")
            if self.verbose:
                print("STDOUT:", result["stdout"])
                print("STDERR:", result["stderr"])
            return False
    
    def run_linting(self) -> bool:
        """Run code linting."""
        print("\n🔍 Running Code Linting...")
        print("=" * 50)
        
        # Check if flake8 is available
        result = self.run_command(["python", "-c", "import flake8"])
        if not result["success"]:
            print("⚠️  flake8 not available, skipping linting")
            return True
        
        cmd = [
            "python", "-m", "flake8",
            "src/agent_swarm/",
            "--max-line-length=100",
            "--ignore=E203,W503",  # Ignore some formatting issues
            "--exclude=__pycache__,*.pyc,.git"
        ]
        
        result = self.run_command(cmd)
        self.test_results["linting"] = result
        
        if result["success"]:
            print(f"✅ Linting passed ({result['duration']:.2f}s)")
            return True
        else:
            print(f"❌ Linting failed ({result['duration']:.2f}s)")
            if self.verbose:
                print("STDOUT:", result["stdout"])
                print("STDERR:", result["stderr"])
            return False
    
    def run_type_checking(self) -> bool:
        """Run type checking with mypy."""
        print("\n🔍 Running Type Checking...")
        print("=" * 50)
        
        # Check if mypy is available
        result = self.run_command(["python", "-c", "import mypy"])
        if not result["success"]:
            print("⚠️  mypy not available, skipping type checking")
            return True
        
        cmd = [
            "python", "-m", "mypy",
            "src/agent_swarm/",
            "--ignore-missing-imports",
            "--no-strict-optional"
        ]
        
        result = self.run_command(cmd)
        self.test_results["type_checking"] = result
        
        if result["success"]:
            print(f"✅ Type checking passed ({result['duration']:.2f}s)")
            return True
        else:
            print(f"❌ Type checking failed ({result['duration']:.2f}s)")
            if self.verbose:
                print("STDOUT:", result["stdout"])
                print("STDERR:", result["stderr"])
            return False
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 60)
        print("📋 TEST SUMMARY")
        print("=" * 60)
        
        total_duration = 0
        passed_count = 0
        total_count = 0
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            duration = result["duration"]
            total_duration += duration
            total_count += 1
            
            if result["success"]:
                passed_count += 1
            
            print(f"{test_name:20} {status:8} ({duration:6.2f}s)")
        
        print("-" * 60)
        print(f"Total: {passed_count}/{total_count} passed ({total_duration:.2f}s)")
        
        if passed_count == total_count:
            print("\n🎉 All tests passed! 🎉")
            return True
        else:
            print(f"\n❌ {total_count - passed_count} test suite(s) failed")
            return False
    
    def run_all(self, include_coverage: bool = False, include_linting: bool = False) -> bool:
        """Run all tests."""
        print("🚀 Starting comprehensive test run...")
        
        # Check dependencies first
        if not self.check_dependencies():
            return False
        
        success = True
        
        # Run tests in order
        if not self.run_unit_tests():
            success = False
        
        if not self.run_integration_tests():
            success = False
        
        if not self.run_e2e_tests():
            success = False
        
        if include_coverage:
            if not self.run_coverage_tests():
                success = False
        
        if include_linting:
            if not self.run_linting():
                success = False
            
            if not self.run_type_checking():
                success = False
        
        # Print summary
        return self.print_summary() and success


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Agent Swarm Test Runner")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--coverage", "-c", action="store_true", help="Include coverage testing")
    parser.add_argument("--linting", "-l", action="store_true", help="Include linting and type checking")
    parser.add_argument("--unit", action="store_true", help="Run only unit tests")
    parser.add_argument("--integration", action="store_true", help="Run only integration tests")
    parser.add_argument("--e2e", action="store_true", help="Run only E2E tests")
    
    args = parser.parse_args()
    
    runner = TestRunner(verbose=args.verbose)
    
    if not runner.check_dependencies():
        sys.exit(1)
    
    success = True
    
    if args.unit:
        success = runner.run_unit_tests()
    elif args.integration:
        success = runner.run_integration_tests()
    elif args.e2e:
        success = runner.run_e2e_tests()
    else:
        success = runner.run_all(
            include_coverage=args.coverage,
            include_linting=args.linting
        )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
