#!/usr/bin/env python3
"""
Ollama setup script for Agent Swarm.
Downloads and sets up local LLMs optimized for your system.
"""

import asyncio
import json
import os
import subprocess
import sys
import time
from pathlib import Path

import aiohttp


def run_command(cmd: str, check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command."""
    print(f"🔧 Running: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if check and result.returncode != 0:
        print(f"❌ Command failed: {cmd}")
        print(f"Error: {result.stderr}")
        if not check:
            return result
        sys.exit(1)
    
    return result


def check_ollama_installed() -> bool:
    """Check if Ollama is installed."""
    result = run_command("ollama --version", check=False)
    return result.returncode == 0


async def check_ollama_service() -> bool:
    """Check if Ollama service is running."""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:11434/api/tags", timeout=5) as response:
                return response.status == 200
    except Exception:
        return False


def install_ollama():
    """Install Ollama if not present."""
    if check_ollama_installed():
        print("✅ Ollama already installed")
        return True
    
    print("📦 Installing Ollama...")
    print("⚠️  This requires sudo access")
    
    # Try different installation methods
    install_methods = [
        "curl -fsSL https://ollama.ai/install.sh | sh",
        "wget -qO- https://ollama.ai/install.sh | sh"
    ]
    
    for method in install_methods:
        try:
            result = run_command(method, check=False)
            if result.returncode == 0:
                print("✅ Ollama installed successfully")
                return True
        except Exception as e:
            print(f"⚠️  Installation method failed: {e}")
    
    print("❌ Ollama installation failed")
    print("💡 Manual installation:")
    print("   Visit: https://ollama.ai/download")
    print("   Or use package manager:")
    print("   - Ubuntu/Debian: sudo apt install ollama")
    print("   - macOS: brew install ollama")
    return False


def start_ollama_service():
    """Start Ollama service."""
    print("🚀 Starting Ollama service...")
    
    # Start service in background
    try:
        subprocess.Popen(
            ["ollama", "serve"], 
            stdout=subprocess.DEVNULL, 
            stderr=subprocess.DEVNULL
        )
        print("✅ Ollama service started in background")
        
        # Wait for service to be ready
        for i in range(10):
            time.sleep(1)
            if asyncio.run(check_ollama_service()):
                print("✅ Ollama service is ready")
                return True
            print(f"⏳ Waiting for service... ({i+1}/10)")
        
        print("⚠️  Service may still be starting")
        return True
        
    except Exception as e:
        print(f"❌ Failed to start Ollama service: {e}")
        print("💡 Try manually: ollama serve")
        return False


async def get_available_models() -> list:
    """Get list of available models."""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:11434/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    return [model["name"] for model in data.get("models", [])]
    except Exception:
        pass
    return []


def pull_model(model_name: str) -> bool:
    """Pull a specific model."""
    print(f"📥 Pulling {model_name}...")
    print("⏳ This may take several minutes...")
    
    result = run_command(f"ollama pull {model_name}", check=False)
    
    if result.returncode == 0:
        print(f"✅ {model_name} downloaded successfully")
        return True
    else:
        print(f"❌ Failed to download {model_name}")
        print(f"Error: {result.stderr}")
        return False


async def setup_recommended_models():
    """Set up recommended models for your system."""
    
    # Check current models
    current_models = await get_available_models()
    print(f"📋 Currently installed models: {len(current_models)}")
    for model in current_models:
        print(f"   • {model}")
    
    # Recommended models for RTX 4060 Max-Q (8GB VRAM)
    recommended_models = [
        {
            "name": "deepseek-r1:7b",
            "description": "DeepSeek-R1-7B - SOTA coding model (2025)",
            "size": "~4.5GB",
            "priority": 1
        },
        {
            "name": "qwen2.5-coder:7b", 
            "description": "Qwen2.5-Coder-7B - Fast coding assistant",
            "size": "~4GB",
            "priority": 2
        }
    ]
    
    print("\n🤖 Recommended models for your RTX 4060 Max-Q:")
    for i, model in enumerate(recommended_models, 1):
        status = "✅ Installed" if any(model["name"] in m for m in current_models) else "⬇️  Available"
        print(f"{i}. {model['name']} ({model['size']}) - {status}")
        print(f"   {model['description']}")
    
    # Ask which models to install
    print(f"\n💾 Total VRAM usage: ~8.5GB (perfect fit for your 8GB VRAM)")
    choice = input("\nInstall recommended models? (y/N): ").strip().lower()
    
    if choice != 'y':
        print("⏭️  Skipping model installation")
        return
    
    # Install models
    success_count = 0
    for model in recommended_models:
        if not any(model["name"] in m for m in current_models):
            if pull_model(model["name"]):
                success_count += 1
        else:
            print(f"✅ {model['name']} already installed")
            success_count += 1
    
    print(f"\n🎉 Setup complete! {success_count}/{len(recommended_models)} models ready")


async def test_models():
    """Test installed models."""
    print("\n🧪 Testing installed models...")
    
    models = await get_available_models()
    if not models:
        print("❌ No models available for testing")
        return
    
    test_prompt = "Write a Python function to add two numbers."
    
    for model in models[:2]:  # Test first 2 models
        print(f"\n🤖 Testing {model}...")
        
        cmd = f'ollama run {model} "{test_prompt}"'
        result = run_command(cmd, check=False)
        
        if result.returncode == 0 and len(result.stdout) > 50:
            print(f"✅ {model} working correctly")
            print(f"   Sample: {result.stdout[:100]}...")
        else:
            print(f"❌ {model} test failed")


def create_ollama_config():
    """Create Ollama configuration."""
    config = {
        "ollama_setup": {
            "base_url": "http://localhost:11434",
            "models_installed": [],
            "setup_date": "2025",
            "system_specs": {
                "gpu": "RTX 4060 Max-Q",
                "vram": "8GB",
                "recommended_models": [
                    "deepseek-r1:7b",
                    "qwen2.5-coder:7b"
                ]
            }
        }
    }
    
    config_file = Path.home() / ".agent-swarm" / "ollama_config.json"
    config_file.parent.mkdir(exist_ok=True)
    
    with open(config_file, "w") as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Configuration saved to {config_file}")


async def main():
    """Main setup function."""
    print("🚀 Ollama Setup for Agent Swarm")
    print("=" * 40)
    print("Target: RTX 4060 Max-Q (8GB VRAM)")
    print("Models: DeepSeek-R1-7B + Qwen2.5-Coder-7B")
    print("=" * 40)
    
    # Step 1: Install Ollama
    if not install_ollama():
        return
    
    # Step 2: Start service
    if not start_ollama_service():
        print("⚠️  Continue with manual service start")
    
    # Wait a bit more for service
    await asyncio.sleep(2)
    
    # Step 3: Check service
    if not await check_ollama_service():
        print("❌ Ollama service not responding")
        print("💡 Try manually:")
        print("   1. Open new terminal")
        print("   2. Run: ollama serve")
        print("   3. Run this script again")
        return
    
    # Step 4: Setup models
    await setup_recommended_models()
    
    # Step 5: Test models
    await test_models()
    
    # Step 6: Create config
    create_ollama_config()
    
    print("\n🎉 Ollama setup complete!")
    print("\n📋 Next steps:")
    print("1. Test Agent Swarm: python examples/quick_demo.py")
    print("2. Run integration tests: make test-integration")
    print("3. Start building agents!")
    
    print("\n💡 Useful commands:")
    print("- List models: ollama list")
    print("- Run model: ollama run deepseek-r1:7b")
    print("- Check service: curl http://localhost:11434/api/tags")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Setup interrupted")
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        print("💡 Try manual setup or check the documentation")
