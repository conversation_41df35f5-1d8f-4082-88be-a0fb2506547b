#!/usr/bin/env python3
"""
Development setup script for Agent Swarm.
Sets up the development environment with proper Python practices.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd: str, check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command."""
    print(f"🔧 Running: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if check and result.returncode != 0:
        print(f"❌ Command failed: {cmd}")
        print(f"Error: {result.stderr}")
        sys.exit(1)
    
    return result


def check_python_version():
    """Check Python version."""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 10):
        print("❌ Python 3.10+ required")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")


def setup_virtual_environment():
    """Set up virtual environment if not already in one."""
    print("🏠 Checking virtual environment...")
    
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment detected")
        return
    
    print("⚠️  No virtual environment detected")
    print("💡 Recommendation: Create a virtual environment first:")
    print("   python -m venv venv")
    print("   source venv/bin/activate  # Linux/Mac")
    print("   venv\\Scripts\\activate     # Windows")
    
    choice = input("Continue anyway? (y/N): ").strip().lower()
    if choice != 'y':
        sys.exit(0)


def install_package():
    """Install the package in development mode."""
    print("📦 Installing package in development mode...")
    
    # Install with development dependencies
    run_command("pip install -e '.[dev,cloud]'")
    print("✅ Package installed")


def setup_pre_commit():
    """Set up pre-commit hooks."""
    print("🪝 Setting up pre-commit hooks...")
    
    try:
        run_command("pre-commit install")
        print("✅ Pre-commit hooks installed")
    except subprocess.CalledProcessError:
        print("⚠️  Pre-commit setup failed (optional)")


def run_initial_tests():
    """Run initial tests to verify setup."""
    print("🧪 Running initial tests...")
    
    try:
        # Run unit tests only (no integration tests that require services)
        result = run_command("pytest tests/unit -v", check=False)
        
        if result.returncode == 0:
            print("✅ Unit tests passed")
        else:
            print("⚠️  Some unit tests failed (check output above)")
    
    except subprocess.CalledProcessError:
        print("⚠️  Tests failed to run")


def check_code_quality():
    """Check code quality."""
    print("🔍 Checking code quality...")
    
    try:
        # Format check
        result = run_command("black --check src tests examples", check=False)
        if result.returncode == 0:
            print("✅ Code formatting OK")
        else:
            print("⚠️  Code formatting issues (run: make format)")
        
        # Import sorting check
        result = run_command("isort --check-only src tests examples", check=False)
        if result.returncode == 0:
            print("✅ Import sorting OK")
        else:
            print("⚠️  Import sorting issues (run: make format)")
        
        # Linting
        result = run_command("flake8 src tests examples", check=False)
        if result.returncode == 0:
            print("✅ Linting OK")
        else:
            print("⚠️  Linting issues (check output above)")
        
        # Type checking
        result = run_command("mypy src", check=False)
        if result.returncode == 0:
            print("✅ Type checking OK")
        else:
            print("⚠️  Type checking issues (check output above)")
    
    except subprocess.CalledProcessError:
        print("⚠️  Code quality checks failed")


def create_example_configs():
    """Create example configuration files."""
    print("📝 Creating example configurations...")
    
    try:
        from agent_swarm.utils.config import create_example_config
        create_example_config()
        print("✅ Example configs created in ~/.agent-swarm/")
    except ImportError:
        print("⚠️  Could not create example configs (package not installed)")


def show_next_steps():
    """Show next steps for development."""
    print("\n🎉 Development setup complete!")
    print("\n📋 Next steps:")
    print("1. Set up local LLMs:")
    print("   make setup-ollama")
    print("   # or manually:")
    print("   curl -fsSL https://ollama.ai/install.sh | sh")
    print("   ollama pull deepseek-r1:7b")
    print("   ollama pull qwen2.5-coder:7b")
    print()
    print("2. Add API keys (optional):")
    print("   cp ~/.agent-swarm/.env.example ~/.agent-swarm/.env")
    print("   # Edit .env with your API keys")
    print()
    print("3. Run tests:")
    print("   make test")
    print()
    print("4. Run demo:")
    print("   make demo")
    print()
    print("5. Development commands:")
    print("   make help           # Show all commands")
    print("   make format         # Format code")
    print("   make lint           # Check code quality")
    print("   make test-unit      # Run unit tests")
    print("   make test-integration  # Run integration tests")
    print()
    print("6. Build package:")
    print("   make build")


def main():
    """Main setup function."""
    print("🚀 Agent Swarm Development Setup")
    print("=" * 40)
    
    # Change to project root
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    print(f"📁 Working directory: {project_root}")
    
    # Setup steps
    check_python_version()
    setup_virtual_environment()
    install_package()
    setup_pre_commit()
    create_example_configs()
    run_initial_tests()
    check_code_quality()
    show_next_steps()


if __name__ == "__main__":
    main()
