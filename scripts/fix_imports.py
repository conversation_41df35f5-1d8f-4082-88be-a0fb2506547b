#!/usr/bin/env python3
"""
<PERSON>ript to fix import path issues across the codebase.
Standardizes the import pattern for development mode.
"""

import re
from pathlib import Path


def fix_import_pattern(file_path: Path) -> bool:
    """Fix import pattern in a single file."""
    try:
        content = file_path.read_text(encoding='utf-8')
        
        # Pattern to match sys.path.insert lines
        pattern = r'sys\.path\.insert\(0,.*?\)'
        
        if not re.search(pattern, content):
            return False
            
        # Standard import pattern
        standard_pattern = '''# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))'''
        
        # Replace the old pattern
        # First, find the import section
        lines = content.split('\n')
        new_lines = []
        skip_next = False
        
        for i, line in enumerate(lines):
            if skip_next:
                skip_next = False
                continue
                
            if 'sys.path.insert' in line:
                # Check if there's a comment above
                if i > 0 and ('Add src to path' in lines[i-1] or '# Add src' in lines[i-1]):
                    # Replace both comment and insert line
                    new_lines.pop()  # Remove the comment line
                    new_lines.append(standard_pattern)
                else:
                    # Just replace the insert line
                    new_lines.append(standard_pattern)
            else:
                new_lines.append(line)
        
        new_content = '\n'.join(new_lines)
        
        if new_content != content:
            file_path.write_text(new_content, encoding='utf-8')
            print(f"✅ Fixed imports in {file_path}")
            return True
        else:
            print(f"⏭️  No changes needed in {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False


def main():
    """Fix import patterns across the codebase."""
    print("🔧 Fixing import patterns across Agent Swarm codebase...")
    
    # Find all Python files with sys.path.insert
    root = Path(__file__).parent.parent
    python_files = []
    
    # Search in examples, tests, and scripts
    for pattern in ['examples/**/*.py', 'tests/**/*.py', 'scripts/**/*.py']:
        python_files.extend(root.glob(pattern))
    
    fixed_count = 0
    total_count = 0
    
    for file_path in python_files:
        if file_path.name == 'fix_imports.py':  # Skip this script
            continue
            
        total_count += 1
        if fix_import_pattern(file_path):
            fixed_count += 1
    
    print(f"\n📊 Summary:")
    print(f"   Files processed: {total_count}")
    print(f"   Files fixed: {fixed_count}")
    print(f"   Files unchanged: {total_count - fixed_count}")
    
    if fixed_count > 0:
        print(f"\n✅ Import patterns standardized successfully!")
    else:
        print(f"\n✅ All import patterns were already correct!")


if __name__ == "__main__":
    main()