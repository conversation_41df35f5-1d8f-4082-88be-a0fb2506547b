#!/usr/bin/env python3
"""
Comprehensive status check for Agent Swarm setup.
"""

import asyncio
import os
import subprocess
import sys
from pathlib import Path

import aiohttp


def check_command(cmd: str) -> bool:
    """Check if a command is available."""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0
    except Exception:
        return False


async def check_ollama_service() -> dict:
    """Check Ollama service status."""
    result = {
        "installed": False,
        "running": False,
        "models": [],
        "error": None
    }
    
    # Check if installed
    result["installed"] = check_command("ollama --version")
    
    if not result["installed"]:
        result["error"] = "Ollama not installed"
        return result
    
    # Check if running
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:11434/api/tags", timeout=5) as response:
                if response.status == 200:
                    result["running"] = True
                    data = await response.json()
                    result["models"] = [model["name"] for model in data.get("models", [])]
                else:
                    result["error"] = f"Service responded with {response.status}"
    except Exception as e:
        result["error"] = f"Service not responding: {e}"
    
    return result


def check_python_environment() -> dict:
    """Check Python environment."""
    result = {
        "version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        "virtual_env": False,
        "agent_swarm_installed": False,
        "dependencies": {}
    }
    
    # Check virtual environment
    result["virtual_env"] = (
        hasattr(sys, 'real_prefix') or 
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    )
    
    # Check if agent_swarm is installed
    try:
        import agent_swarm
        result["agent_swarm_installed"] = True
        result["agent_swarm_version"] = agent_swarm.__version__
    except ImportError:
        pass
    
    # Check key dependencies
    deps_to_check = [
        "aiohttp", "pydantic", "typer", "rich", "pytest",
        "anthropic", "google.generativeai"
    ]
    
    for dep in deps_to_check:
        try:
            __import__(dep)
            result["dependencies"][dep] = "✅ Available"
        except ImportError:
            result["dependencies"][dep] = "❌ Missing"
    
    return result


def check_api_keys() -> dict:
    """Check API key availability."""
    keys = {
        "ANTHROPIC_API_KEY": os.getenv("ANTHROPIC_API_KEY"),
        "GOOGLE_API_KEY": os.getenv("GOOGLE_API_KEY"),
        "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY")
    }
    
    return {
        key: "✅ Set" if value else "❌ Not set"
        for key, value in keys.items()
    }


def check_project_structure() -> dict:
    """Check project structure."""
    required_files = [
        "pyproject.toml",
        "src/agent_swarm/__init__.py",
        "src/agent_swarm/backends/__init__.py",
        "src/agent_swarm/agents/__init__.py",
        "tests/conftest.py",
        "Makefile",
        "requirements.txt"
    ]
    
    result = {}
    for file_path in required_files:
        path = Path(file_path)
        result[file_path] = "✅ Exists" if path.exists() else "❌ Missing"
    
    return result


async def run_basic_tests() -> dict:
    """Run basic functionality tests."""
    result = {
        "unit_tests": "❌ Failed",
        "import_test": "❌ Failed",
        "cli_test": "❌ Failed"
    }
    
    # Test imports
    try:
        from agent_swarm import LLMRouter, Message, LLMTier
        result["import_test"] = "✅ Passed"
    except Exception as e:
        result["import_test"] = f"❌ Failed: {e}"
    
    # Test CLI
    try:
        cmd_result = subprocess.run(
            ["agent-swarm", "version"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        if cmd_result.returncode == 0:
            result["cli_test"] = "✅ Passed"
        else:
            result["cli_test"] = f"❌ Failed: {cmd_result.stderr}"
    except Exception as e:
        result["cli_test"] = f"❌ Failed: {e}"
    
    # Test unit tests
    try:
        cmd_result = subprocess.run(
            ["python", "-m", "pytest", "tests/unit", "-q", "--tb=no"],
            capture_output=True,
            text=True,
            timeout=30
        )
        if cmd_result.returncode == 0:
            result["unit_tests"] = "✅ Passed"
        else:
            result["unit_tests"] = f"❌ Failed: {cmd_result.returncode} failures"
    except Exception as e:
        result["unit_tests"] = f"❌ Failed: {e}"
    
    return result


def print_status_report(
    python_env: dict,
    ollama_status: dict,
    api_keys: dict,
    project_structure: dict,
    test_results: dict
):
    """Print comprehensive status report."""
    
    print("🔍 Agent Swarm Status Report")
    print("=" * 50)
    
    # Python Environment
    print("\n🐍 Python Environment:")
    print(f"   Version: {python_env['version']}")
    print(f"   Virtual Env: {'✅ Active' if python_env['virtual_env'] else '❌ Not active'}")
    print(f"   Agent Swarm: {'✅ Installed v' + python_env.get('agent_swarm_version', '') if python_env['agent_swarm_installed'] else '❌ Not installed'}")
    
    print("\n📦 Dependencies:")
    for dep, status in python_env["dependencies"].items():
        print(f"   {dep}: {status}")
    
    # Ollama Status
    print("\n🤖 Ollama Status:")
    print(f"   Installed: {'✅ Yes' if ollama_status['installed'] else '❌ No'}")
    print(f"   Service: {'✅ Running' if ollama_status['running'] else '❌ Not running'}")
    if ollama_status['error']:
        print(f"   Error: {ollama_status['error']}")
    print(f"   Models: {len(ollama_status['models'])} installed")
    for model in ollama_status['models']:
        print(f"     • {model}")
    
    # API Keys
    print("\n🔑 API Keys:")
    for key, status in api_keys.items():
        print(f"   {key}: {status}")
    
    # Project Structure
    print("\n📁 Project Structure:")
    for file_path, status in project_structure.items():
        print(f"   {file_path}: {status}")
    
    # Test Results
    print("\n🧪 Test Results:")
    for test, result in test_results.items():
        print(f"   {test}: {result}")
    
    # Overall Assessment
    print("\n📊 Overall Assessment:")
    
    issues = []
    if not python_env['agent_swarm_installed']:
        issues.append("Agent Swarm not installed")
    if not ollama_status['installed']:
        issues.append("Ollama not installed")
    elif not ollama_status['running']:
        issues.append("Ollama service not running")
    elif len(ollama_status['models']) == 0:
        issues.append("No models installed")
    
    if not issues:
        print("   🎉 Everything looks good!")
        print("   ✅ Ready for agent development")
    else:
        print("   ⚠️  Issues found:")
        for issue in issues:
            print(f"     • {issue}")
    
    # Recommendations
    print("\n💡 Next Steps:")
    if not python_env['agent_swarm_installed']:
        print("   1. Install Agent Swarm: pip install -e .")
    if not ollama_status['installed']:
        print("   2. Install Ollama: python scripts/setup_ollama.py")
    elif not ollama_status['running']:
        print("   2. Start Ollama: ollama serve")
    elif len(ollama_status['models']) == 0:
        print("   2. Install models: python scripts/setup_ollama.py")
    
    if all(status == "❌ Not set" for status in api_keys.values()):
        print("   3. Add API keys (optional): cp .env.example .env")
    
    print("   4. Run demo: python examples/quick_demo.py")
    print("   5. Run tests: make test")


async def main():
    """Main status check function."""
    print("🔍 Checking Agent Swarm setup status...")
    
    # Run all checks
    python_env = check_python_environment()
    ollama_status = await check_ollama_service()
    api_keys = check_api_keys()
    project_structure = check_project_structure()
    test_results = await run_basic_tests()
    
    # Print report
    print_status_report(
        python_env,
        ollama_status,
        api_keys,
        project_structure,
        test_results
    )


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Status check interrupted")
    except Exception as e:
        print(f"\n❌ Status check failed: {e}")
        import traceback
        traceback.print_exc()
