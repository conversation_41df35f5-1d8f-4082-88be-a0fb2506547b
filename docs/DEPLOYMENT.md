# 🚀 Deployment Guide

Production deployment strategies for Agent Swarm applications.

## 🎯 Deployment Options

### **1. Local Development Server**
Perfect for development and testing.

```bash
# Quick local setup
make setup
python your_agent_app.py
```

### **2. Docker Container**
Containerized deployment for consistency.

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install -e ".[all]"

CMD ["python", "your_agent_app.py"]
```

### **3. Cloud Platforms**
Scalable cloud deployment options.

- **AWS Lambda**: Serverless functions
- **Google Cloud Run**: Containerized services
- **Azure Container Instances**: Managed containers
- **Heroku**: Platform-as-a-Service

### **4. Kubernetes**
Enterprise-scale orchestration.

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-swarm-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: agent-swarm
  template:
    metadata:
      labels:
        app: agent-swarm
    spec:
      containers:
      - name: agent-swarm
        image: your-registry/agent-swarm:latest
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: openai
```

## ⚙️ Configuration Management

### **Environment Variables**
```bash
# LLM Configuration
export AGENT_SWARM_MODEL="llama3.2:7b"
export OPENAI_API_KEY="sk-..."
export ANTHROPIC_API_KEY="sk-ant-..."

# Performance Settings
export AGENT_SWARM_MAX_WORKERS=4
export AGENT_SWARM_TIMEOUT=300

# Logging
export AGENT_SWARM_LOG_LEVEL="INFO"
export AGENT_SWARM_LOG_FORMAT="json"
```

### **Configuration Files**
```yaml
# config/production.yaml
llm:
  default_model: "gpt-4"
  fallback_model: "gpt-3.5-turbo"
  timeout: 300
  max_retries: 3

context:
  vector_store: "pinecone"
  chunk_size: 1000
  max_results: 20

logging:
  level: "INFO"
  format: "json"
  handlers:
    - "console"
    - "file"
```

### **Secrets Management**
```python
# Use environment-specific secret management
import os
from agent_swarm.utils import load_config

def get_api_key(service: str) -> str:
    """Get API key from secure storage."""
    if os.getenv("ENVIRONMENT") == "production":
        # Use AWS Secrets Manager, Azure Key Vault, etc.
        return get_secret_from_vault(f"agent-swarm/{service}")
    else:
        # Use environment variables for development
        return os.getenv(f"{service.upper()}_API_KEY")
```

## 🐳 Docker Deployment

### **Production Dockerfile**
```dockerfile
FROM python:3.11-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Production stage
FROM python:3.11-slim

# Copy virtual environment
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Create non-root user
RUN useradd --create-home --shell /bin/bash agent
USER agent
WORKDIR /home/<USER>

# Copy application
COPY --chown=agent:agent . .

# Install application
RUN pip install -e .

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "from agent_swarm import LLMRouter; print('healthy')"

# Run application
CMD ["python", "app.py"]
```

### **Docker Compose**
```yaml
version: '3.8'

services:
  agent-swarm:
    build: .
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - postgres
    volumes:
      - ./data:/app/data
    ports:
      - "8000:8000"

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=agent_swarm
      - POSTGRES_USER=agent
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  redis_data:
  postgres_data:
```

## ☁️ Cloud Deployment

### **AWS Lambda**
```python
# lambda_handler.py
import json
from agent_swarm import create_coding_agent

async def lambda_handler(event, context):
    """AWS Lambda handler for Agent Swarm."""
    
    # Parse request
    body = json.loads(event['body'])
    task = body.get('task')
    
    # Create agent
    agent = await create_coding_agent(
        name="LambdaBot",
        enable_rag=False  # Use lightweight config for Lambda
    )
    
    # Process task
    result = await agent.process_task(task)
    
    return {
        'statusCode': 200,
        'body': json.dumps({
            'result': result,
            'model_used': agent.last_model_used
        })
    }
```

### **Google Cloud Run**
```yaml
# cloudbuild.yaml
steps:
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '-t', 'gcr.io/$PROJECT_ID/agent-swarm', '.']
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'gcr.io/$PROJECT_ID/agent-swarm']
- name: 'gcr.io/cloud-builders/gcloud'
  args:
  - 'run'
  - 'deploy'
  - 'agent-swarm'
  - '--image'
  - 'gcr.io/$PROJECT_ID/agent-swarm'
  - '--platform'
  - 'managed'
  - '--region'
  - 'us-central1'
  - '--allow-unauthenticated'
```

### **Azure Container Instances**
```bash
# Deploy to Azure
az container create \
  --resource-group myResourceGroup \
  --name agent-swarm \
  --image myregistry.azurecr.io/agent-swarm:latest \
  --cpu 2 \
  --memory 4 \
  --environment-variables \
    OPENAI_API_KEY=$OPENAI_API_KEY \
    ANTHROPIC_API_KEY=$ANTHROPIC_API_KEY \
  --ports 8000
```

## 📊 Monitoring & Observability

### **Application Metrics**
```python
# monitoring.py
import time
import logging
from prometheus_client import Counter, Histogram, start_http_server

# Metrics
REQUEST_COUNT = Counter('agent_requests_total', 'Total requests', ['agent_type', 'status'])
REQUEST_DURATION = Histogram('agent_request_duration_seconds', 'Request duration')
LLM_USAGE = Counter('llm_tokens_total', 'Token usage', ['model', 'type'])

class MonitoredAgent:
    def __init__(self, agent):
        self.agent = agent
        self.logger = logging.getLogger(__name__)
    
    async def process_task(self, task: str):
        start_time = time.time()
        
        try:
            result = await self.agent.process_task(task)
            REQUEST_COUNT.labels(agent_type=self.agent.name, status='success').inc()
            return result
        except Exception as e:
            REQUEST_COUNT.labels(agent_type=self.agent.name, status='error').inc()
            self.logger.error(f"Task failed: {e}")
            raise
        finally:
            REQUEST_DURATION.observe(time.time() - start_time)

# Start metrics server
start_http_server(8001)
```

### **Logging Configuration**
```python
# logging_config.py
import logging
import json
from datetime import datetime

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        if hasattr(record, 'agent_name'):
            log_entry['agent_name'] = record.agent_name
        
        if hasattr(record, 'model_used'):
            log_entry['model_used'] = record.model_used
            
        return json.dumps(log_entry)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('/var/log/agent-swarm.log')
    ]
)

# Use JSON formatter for production
if os.getenv('ENVIRONMENT') == 'production':
    for handler in logging.root.handlers:
        handler.setFormatter(JSONFormatter())
```

### **Health Checks**
```python
# health.py
from fastapi import FastAPI, HTTPException
from agent_swarm import LLMRouter

app = FastAPI()

@app.get("/health")
async def health_check():
    """Basic health check endpoint."""
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with component status."""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "components": {}
    }
    
    # Check LLM router
    try:
        router = LLMRouter()
        health_status["components"]["llm_router"] = {
            "status": "healthy",
            "available_llms": len(router.llms)
        }
    except Exception as e:
        health_status["components"]["llm_router"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "unhealthy"
    
    if health_status["status"] == "unhealthy":
        raise HTTPException(status_code=503, detail=health_status)
    
    return health_status
```

## 🔒 Security Considerations

### **API Key Management**
```python
# security.py
import os
from cryptography.fernet import Fernet

class SecureConfig:
    def __init__(self):
        self.cipher = Fernet(os.getenv('ENCRYPTION_KEY').encode())
    
    def get_api_key(self, service: str) -> str:
        """Get encrypted API key."""
        encrypted_key = os.getenv(f'{service.upper()}_API_KEY_ENCRYPTED')
        if encrypted_key:
            return self.cipher.decrypt(encrypted_key.encode()).decode()
        return os.getenv(f'{service.upper()}_API_KEY')
```

### **Input Validation**
```python
# validation.py
from pydantic import BaseModel, validator

class TaskRequest(BaseModel):
    task: str
    max_tokens: int = 1000
    temperature: float = 0.7
    
    @validator('task')
    def validate_task(cls, v):
        if len(v) > 10000:
            raise ValueError('Task too long')
        return v
    
    @validator('max_tokens')
    def validate_max_tokens(cls, v):
        if v > 4000:
            raise ValueError('Max tokens too high')
        return v
```

### **Rate Limiting**
```python
# rate_limiting.py
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

limiter = Limiter(key_func=get_remote_address)

@app.post("/process")
@limiter.limit("10/minute")
async def process_task(request: Request, task_request: TaskRequest):
    """Process task with rate limiting."""
    # Implementation
    pass
```

## 📈 Scaling Strategies

### **Horizontal Scaling**
```python
# worker.py
import asyncio
from celery import Celery
from agent_swarm import create_coding_agent

app = Celery('agent-swarm')

@app.task
async def process_task_async(task_data):
    """Process task in background worker."""
    agent = await create_coding_agent("WorkerBot")
    result = await agent.process_task(task_data['task'])
    return result
```

### **Load Balancing**
```nginx
# nginx.conf
upstream agent_swarm {
    server app1:8000;
    server app2:8000;
    server app3:8000;
}

server {
    listen 80;
    
    location / {
        proxy_pass http://agent_swarm;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔧 Performance Optimization

### **Caching Strategy**
```python
# caching.py
import redis
import json
from functools import wraps

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expiry=3600):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Create cache key
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # Try to get from cache
            cached = redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Cache result
            redis_client.setex(cache_key, expiry, json.dumps(result))
            
            return result
        return wrapper
    return decorator
```

### **Connection Pooling**
```python
# connection_pool.py
import asyncio
from aiohttp import ClientSession, TCPConnector

class LLMConnectionPool:
    def __init__(self, max_connections=100):
        connector = TCPConnector(limit=max_connections)
        self.session = ClientSession(connector=connector)
    
    async def close(self):
        await self.session.close()

# Use singleton pattern
connection_pool = LLMConnectionPool()
```

---

**Ready to deploy?** Start with [Docker deployment](#docker-deployment) for most use cases! 🚀

**Next:** [Troubleshooting](TROUBLESHOOTING.md) | [Contributing](CONTRIBUTING.md) | [API Reference](API_REFERENCE.md)
