# 📦 Agent Swarm Installation Guide

**Version:** 0.2.0  
**Last Updated:** January 6, 2025  
**Status:** Production Ready

---

## 🎯 **Quick Start**

### **Automated Installation (Recommended)**
```bash
# Download and run the installation script
curl -sSL https://raw.githubusercontent.com/agent-swarm/agent-swarm/main/scripts/install.py | python3

# Or clone and install locally
git clone https://github.com/agent-swarm/agent-swarm.git
cd agent-swarm
python scripts/install.py
```

### **Verify Installation**
```bash
# Check version
agent-swarm --version

# Start interactive shell
agent-swarm

# Test with project
agent-swarm --project /path/to/your/code --auto-index
```

---

## 🛠 **Installation Methods**

### **Method 1: Automated Installation Script** ⭐ **Recommended**

The installation script automatically detects your environment and installs Agent Swarm with all dependencies.

```bash
# Download the repository
git clone https://github.com/agent-swarm/agent-swarm.git
cd agent-swarm

# Run the installation script
python scripts/install.py
```

**What the script does:**
- ✅ Checks Python version compatibility (3.8+)
- ✅ Detects development vs production installation
- ✅ Installs core dependencies and optional features
- ✅ Verifies CLI functionality
- ✅ Provides troubleshooting information

### **Method 2: pip Installation** (When Published)

```bash
# Install with all features
pip install agent-swarm[rag,cli]

# Install core only
pip install agent-swarm

# Install with specific features
pip install agent-swarm[rag]     # RAG and context features
pip install agent-swarm[cli]     # CLI tools
pip install agent-swarm[dev]     # Development dependencies
```

### **Method 3: Development Installation**

For contributors and advanced users who want to modify the code:

```bash
# Clone the repository
git clone https://github.com/agent-swarm/agent-swarm.git
cd agent-swarm

# Install in development mode
pip install -e .

# Install with development dependencies
pip install -e .[dev,rag,cli]
```

---

## 📋 **System Requirements**

### **Minimum Requirements**
- **Python:** 3.8 or higher
- **Memory:** 4GB RAM (8GB recommended)
- **Storage:** 2GB free space
- **OS:** Linux, macOS, Windows (WSL recommended)

### **Recommended Requirements**
- **Python:** 3.10 or higher
- **Memory:** 8GB RAM or more
- **Storage:** 5GB free space for models and cache
- **OS:** Linux or macOS for best performance

### **Dependencies**
```toml
# Core dependencies (automatically installed)
aiohttp>=3.8.0
pydantic>=2.0.0
rich>=13.0.0
click>=8.0.0
python-dotenv>=1.0.0

# RAG and Context features
tiktoken>=0.5.0
numpy>=1.21.0
scikit-learn>=1.0.0
sentence-transformers>=2.2.0
chromadb>=0.4.0

# Development tools
tree-sitter>=0.20.0
tree-sitter-python>=0.20.0
tree-sitter-javascript>=0.20.0
tree-sitter-typescript>=0.20.0
gitpython>=3.1.0
```

---

## 🔧 **Configuration**

### **Environment Variables**
```bash
# Optional: Set default configuration
export AGENT_SWARM_CONFIG_PATH="~/.agent-swarm/config.yaml"
export AGENT_SWARM_MODELS_PATH="/mnt/dev8/Models"
export AGENT_SWARM_LOG_LEVEL="INFO"

# API Keys (if using external services)
export OPENAI_API_KEY="your-openai-key"
export ANTHROPIC_API_KEY="your-anthropic-key"
```

### **Configuration File**
Agent Swarm will create a configuration file at `~/.agent-swarm/config.yaml`:

```yaml
# Agent Swarm Configuration
version: "0.2.0"

# Model settings
models:
  default_provider: "ollama"
  ollama_base_url: "http://localhost:11434"
  
# RAG settings
rag:
  chunk_size: 1000
  chunk_overlap: 200
  vector_store: "memory"  # or "chromadb", "pinecone"
  
# CLI settings
cli:
  auto_index: false
  verbose: false
  theme: "dark"
```

---

## 🚀 **Post-Installation Setup**

### **1. Verify Installation**
```bash
# Check version and features
agent-swarm --version

# Expected output:
# 🤖 Agent Swarm v0.2.0
# ========================================
# 🚀 Multi-LLM Agent Development Framework
# 
# ✨ Features:
#    • Smart context-aware AI responses
#    • Multi-LLM routing and orchestration
#    • Interactive development shell
#    • Project indexing and analysis
#    • Extensible plugin architecture
```

### **2. Test Basic Functionality**
```bash
# Start interactive shell
agent-swarm

# In the shell, try these commands:
/help          # Show available commands
/version       # Show detailed version info
/config        # Show current configuration
```

### **3. Test with a Project**
```bash
# Navigate to a code project
cd /path/to/your/project

# Start with auto-indexing
agent-swarm --auto-index

# Ask a project-specific question
"How can I improve the error handling in this codebase?"
```

### **4. Configure for Your Environment**
```bash
# In the interactive shell
/config wizard    # Run configuration wizard
/config set models.default_provider ollama
/config set rag.vector_store chromadb
```

---

## 🔄 **Updates and Maintenance**

### **Updating Agent Swarm**

#### **Automatic Update (Recommended)**
```bash
# In the interactive shell
/update

# Or from command line
agent-swarm --update
```

#### **Manual Update**
```bash
# For pip installations
pip install --upgrade agent-swarm

# For development installations
cd /path/to/agent-swarm
git pull origin main
pip install -e .
```

### **Checking for Updates**
```bash
# Check current version
agent-swarm --version

# Check for available updates
agent-swarm --check-updates
```

---

## 🐛 **Troubleshooting**

### **Common Issues**

#### **Python Version Issues**
```bash
# Check Python version
python --version

# If using Python 3.8-3.9, you may need:
pip install --upgrade pip setuptools wheel
```

#### **Permission Issues**
```bash
# On Linux/macOS, you might need:
sudo pip install agent-swarm

# Or use user installation:
pip install --user agent-swarm
```

#### **Dependency Conflicts**
```bash
# Create a virtual environment
python -m venv agent-swarm-env
source agent-swarm-env/bin/activate  # Linux/macOS
# agent-swarm-env\Scripts\activate   # Windows

# Install in clean environment
pip install agent-swarm[rag,cli]
```

#### **CLI Not Found**
```bash
# Check if CLI is in PATH
which agent-swarm

# If not found, try:
python -m agent_swarm.cli.interactive_shell

# Or add to PATH (Linux/macOS):
export PATH="$HOME/.local/bin:$PATH"
```

### **Getting Help**

#### **Built-in Help**
```bash
# Command line help
agent-swarm --help

# Interactive shell help
agent-swarm
/help
```

#### **Diagnostic Information**
```bash
# Generate diagnostic report
agent-swarm --diagnose

# Verbose output for debugging
agent-swarm --verbose
```

#### **Community Support**
- **GitHub Issues:** https://github.com/agent-swarm/agent-swarm/issues
- **Documentation:** https://agent-swarm.readthedocs.io
- **Discussions:** https://github.com/agent-swarm/agent-swarm/discussions

---

## 🔧 **Advanced Installation**

### **Custom Installation Paths**
```bash
# Install to custom location
pip install --target /custom/path agent-swarm

# Set custom config path
export AGENT_SWARM_CONFIG_PATH="/custom/config/path"
```

### **Offline Installation**
```bash
# Download dependencies
pip download agent-swarm[rag,cli] -d ./downloads

# Install offline
pip install --find-links ./downloads --no-index agent-swarm[rag,cli]
```

### **Docker Installation**
```dockerfile
# Dockerfile
FROM python:3.10-slim

RUN pip install agent-swarm[rag,cli]
WORKDIR /workspace

CMD ["agent-swarm"]
```

```bash
# Build and run
docker build -t agent-swarm .
docker run -it -v $(pwd):/workspace agent-swarm
```

---

## ✅ **Installation Checklist**

- [ ] Python 3.8+ installed and accessible
- [ ] Agent Swarm installed via preferred method
- [ ] CLI commands working (`agent-swarm --version`)
- [ ] Interactive shell starts successfully
- [ ] Configuration file created
- [ ] Project indexing works with test project
- [ ] All required dependencies installed
- [ ] Environment variables set (if needed)
- [ ] Updates working (`/update` command)

---

**🎉 Congratulations! Agent Swarm is now installed and ready to use. Start with `agent-swarm --help` or jump into the interactive shell with `agent-swarm`.**
