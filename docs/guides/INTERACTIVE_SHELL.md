# 🤖 Interactive Shell Guide

The Agent Swarm Interactive Shell provides a beautiful, ChatGPT-style command-line interface for AI-powered development. It features Rich formatting, syntax highlighting, and intuitive conversation flow.

## 🚀 Quick Start

### Launch the Shell
```bash
# Start interactive shell in current directory
make shell

# Or directly
python shell.py

# With specific project
python shell.py --project /path/to/your/project

# With different model
python shell.py --model qwen2.5-coder:7b

# Verbose mode for debugging
python shell.py --verbose
```

### First Time Setup
```bash
# Make sure <PERSON>lla<PERSON> is running
ollama serve

# Pull a model if you haven't already
ollama pull llama3.2:3b

# Start the shell
make shell
```

## 🎨 Beautiful Interface Features

### Rich Formatting
- **Colored status messages** with icons (✅❌⚠️ℹ️🤖)
- **Beautiful panels** with borders for conversations
- **Syntax highlighting** for code blocks
- **Progress indicators** during AI processing
- **Status bars** showing current model, project, and session info

### Conversation Flow
```
┌─ 👤 You ─────────────────────────────────────┐
│ How do I implement async error handling?     │
└──────────────────────────────────────────────┘

┌─ 🤖 Assistant ──────────────────────────────┐
│ Here's how to handle errors in async        │
│ functions:                                   │
│                                              │
│ [Code: python]                               │
│ async def safe_fetch(url):                   │
│     try:                                     │
│         async with aiohttp.ClientSession()  │
│             as session:                      │
│             return await session.get(url)   │
│     except aiohttp.ClientError as e:         │
│         print(f"Network error: {e}")         │
│         return None                          │
└──────────────────────────────────────────────┘
Model: ollama/llama3.2:3b | Cost: $0.0000 | Time: 2.1s
```

## 💬 Chat Commands

### Natural Language
Just type your questions directly:
```
🤖 [llama3.2:3b] > How do I create a REST API with FastAPI?
🤖 [llama3.2:3b] > Explain the difference between async and sync
🤖 [llama3.2:3b] > Show me how to implement a binary search
```

### Slash Commands
Use `/` prefix for shell commands:

#### Setup Commands
```bash
/project <path>     # Set project directory
/model <name>       # Switch LLM model
/models             # List available models
```

#### Analysis Commands
```bash
/search <query>     # Search codebase
/analyze            # Analyze project
/stats              # Show project statistics
/files              # List project files
/context <file>     # Show file context
```

#### Development Commands
```bash
/implement <desc>   # Implement feature
/fix <description>  # Fix bug
/review <file>      # Review code
/explain <code>     # Explain code
```

#### Utility Commands
```bash
/history            # Show conversation
/clear              # Clear conversation
/help               # Show help
/exit, /quit        # Exit shell
```

## 🔧 Advanced Usage

### Project Analysis
```bash
🤖 [llama3.2:3b] > /analyze

📊 Project Analysis:
   Files indexed: 57
   Languages: Python, Markdown
   File types: .py (48), .md (5), .txt (4)

🔍 Component Analysis:
   async functions: 45
   agent classes: 15
   LLM classes: 8
   test functions: 23
```

### Code Search
```bash
🤖 [llama3.2:3b] > /search async def

📊 Found 45 matches:
1. coding_agent.py (score: 0.95)
   async def implement_feature(self, feature_description...
   
2. ollama_backend.py (score: 0.92)
   async def generate(self, messages: List[Message]...
```

### Model Switching
```bash
🤖 [llama3.2:3b] > /models

🤖 Available Models:
1. llama3.2:3b (current)
2. qwen2.5-coder:7b
3. deepseek-r1:7b

🤖 [llama3.2:3b] > /model qwen2.5-coder:7b
✅ Switched to model: qwen2.5-coder:7b
```

### Feature Implementation
```bash
🤖 [qwen2.5-coder:7b] > /implement a utility function to validate email addresses

🚀 Implementing: a utility function to validate email addresses
⏳ This may take a moment...

📊 Implementation Results:
   Success: ✅
   Files created: 2
   📄 Created: src/utils/email_validator.py
   📄 Created: tests/test_email_validator.py
```

## 🎯 Use Cases

### Daily Development
```bash
# Quick code explanations
> Explain this regex: r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

# Debug issues
> /fix "Getting AttributeError: 'NoneType' object has no attribute 'get'"

# Code review
> /review src/auth.py

# Search patterns
> /search "def test_"
```

### Learning and Exploration
```bash
# Understand concepts
> What's the difference between __str__ and __repr__?

# Explore codebase
> /search "class Agent"
> /context src/agents/coding_agent.py

# Get implementation ideas
> How would you implement rate limiting in Python?
```

### Project Management
```bash
# Analyze project health
> /analyze

# Find improvement opportunities
> /search "TODO"
> /search "FIXME"

# Review architecture
> /search "abstract"
> /search "@dataclass"
```

## ⚙️ Configuration

### Environment Variables
```bash
export AGENT_SWARM_MODEL="qwen2.5-coder:7b"
export AGENT_SWARM_TEMPERATURE="0.3"
export AGENT_SWARM_MAX_TOKENS="2048"
```

### Command Line Options
```bash
python shell.py --help

usage: shell.py [-h] [--project PROJECT] [--model MODEL] [--verbose]

Agent Swarm Interactive Shell

options:
  -h, --help            show this help message and exit
  --project PROJECT, -p PROJECT
                        Project directory path
  --model MODEL, -m MODEL
                        Ollama model to use
  --verbose, -v         Verbose logging
```

### Shell History
The shell automatically saves command history to `~/.agent_swarm_history` and supports:
- **Tab completion** for commands
- **Arrow key navigation** through history
- **Persistent history** across sessions

## 🎨 Customization

### Rich Themes
The shell uses Rich for formatting. You can customize colors by modifying the shell's style settings:

```python
# In interactive_shell.py
self.console = Console(theme=Theme({
    "success": "green",
    "error": "red",
    "warning": "yellow",
    "info": "blue",
    "thinking": "cyan"
}))
```

### Custom Commands
Add your own commands by extending the shell:

```python
class CustomShell(AgentSwarmShell):
    def __init__(self):
        super().__init__()
        self.commands['custom'] = self.cmd_custom
    
    async def cmd_custom(self, args):
        """Your custom command."""
        self.print_status("Custom command executed!", "success")
```

## 🚀 Tips and Tricks

### Keyboard Shortcuts
- **Ctrl+C**: Interrupt current operation (doesn't exit)
- **Ctrl+D**: Exit shell gracefully
- **Tab**: Command completion
- **↑/↓**: Navigate command history

### Efficient Workflows
```bash
# Set up your environment
/project /path/to/your/project
/model qwen2.5-coder:7b

# Quick analysis
/analyze
/search "your_pattern"

# Interactive development
> Implement a function to parse JSON with error handling
/implement the suggested function
/review the generated code
```

### Best Practices
1. **Use specific models** for different tasks (coding vs general)
2. **Clear conversation** periodically to maintain context
3. **Use /search** before asking about existing code
4. **Review generated code** before using in production
5. **Save important conversations** by copying output

## 🔧 Troubleshooting

### Common Issues

#### Shell Won't Start
```bash
# Check Ollama is running
ollama list

# Check model availability
ollama pull llama3.2:3b

# Check Python environment
source venv/bin/activate
pip install rich
```

#### Rich Formatting Not Working
```bash
# Install Rich
pip install rich

# Check terminal compatibility
echo $TERM

# Force color output
export FORCE_COLOR=1
```

#### Model Not Found
```bash
# List available models
ollama list

# Pull missing model
ollama pull model-name

# Check model name spelling
/models
```

### Debug Mode
```bash
# Run with verbose logging
python shell.py --verbose

# Check logs
tail -f ~/.agent_swarm/logs/shell.log
```

## 🎉 What's Next?

The interactive shell is your gateway to AI-powered development. Try these advanced workflows:

1. **Project Onboarding**: Use `/analyze` and `/search` to understand new codebases
2. **Code Generation**: Use `/implement` for rapid prototyping
3. **Debugging**: Use `/fix` and conversation for troubleshooting
4. **Learning**: Ask questions about patterns you find with `/search`
5. **Code Review**: Use `/review` for quality assurance

**Happy coding with your AI assistant!** 🚀
