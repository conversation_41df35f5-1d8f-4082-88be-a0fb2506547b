# MCP Integration Guide for Agent Swarm

## 🔗 **What is MCP?**

**Model Context Protocol (MCP)** is an open standard for connecting AI assistants to data sources and tools. It provides:

- **Standardized tool calling** across all LLM providers
- **Secure resource access** with proper boundaries
- **Composable tools** that work with any MCP-compatible LLM
- **Rich ecosystem** of pre-built tools and servers

## 🏗️ **Agent Swarm MCP Architecture**

```
┌─────────────────────────────────────────────────────────────┐
│                    MCP-ENABLED AGENTS                       │
├─────────────────────────────────────────────────────────────┤
│  Junior Dev    │  Senior Dev   │  Architect    │  Doc Writer │
│  + MC<PERSON> Tools   │  + MCP Tools  │  + MCP Tools  │  + MCP Tools│
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    MCP TOOL REGISTRY                        │
├─────────────────────────────────────────────────────────────┤
│  • Centralized tool management                              │
│  • Cross-agent tool sharing                                 │
│  • Automatic tool discovery                                 │
│  • Security and access control                              │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    MCP SERVERS                              │
├─────────────────────────────────────────────────────────────┤
│  FileSystem  │  Database   │  Web Search │  Git Tools      │
│  Code Exec   │  Cloud APIs │  Custom     │  Third-party    │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 **Quick Start with MCP**

### **1. Install MCP Dependencies**
```bash
# Install with MCP support
pip install -e ".[mcp]"

# Or install MCP SDK separately
pip install mcp
```

### **2. Basic MCP Agent Usage**
```python
from agent_swarm import LL<PERSON>outer
from agent_swarm.mcp import MCP<PERSON>nabledAgent, setup_default_mcp_tools

# Set up MCP tools
mcp_registry = await setup_default_mcp_tools()

# Create MCP-enabled agent
router = LLMRouter()
agent = MCPEnabledAgent("CodeBot", "Developer", router, mcp_registry)

# Process task with tools
result = await agent.process_task_with_tools(
    "Create a file called 'hello.py' with a simple hello world function"
)
```

### **3. Available Built-in Tools**

#### **File System Tools**
- `read_file` - Read file contents
- `write_file` - Write to files
- `list_directory` - List directory contents

#### **Web Tools**
- `search_web` - Search the internet
- `fetch_url` - Fetch web page content

#### **Development Tools**
- `execute_python` - Run Python code safely
- `git_status` - Get Git repository status
- `git_log` - View commit history

#### **Database Tools**
- `query_sqlite` - Execute SQLite queries
- `query_postgres` - Execute PostgreSQL queries (with setup)

## 🛠️ **Creating Custom MCP Tools**

### **1. Define Tool Schema**
```python
from agent_swarm.mcp.tools import CustomTool

class MyCustomTool:
    @staticmethod
    def get_schema():
        return {
            "my_tool": {
                "description": "My custom tool description",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "input": {"type": "string", "description": "Tool input"}
                    },
                    "required": ["input"]
                }
            }
        }
    
    @staticmethod
    async def execute(tool_name: str, arguments: dict):
        if tool_name == "my_tool":
            return {"result": f"Processed: {arguments['input']}"}
```

### **2. Register Custom Tool**
```python
from agent_swarm.mcp import MCPClient, MCPToolRegistry

# Create custom MCP client
client = MCPClient()
await client.initialize()

# Register your custom tool
await client.connect_server("my_tools", {
    "type": "custom",
    "tools": MyCustomTool.get_schema()
})

# Add to registry
registry = MCPToolRegistry()
await registry.register_client("my_tools", client)
```

## 🎯 **Advanced MCP Patterns**

### **1. Multi-Agent Tool Sharing**
```python
# Shared MCP registry across agents
shared_registry = await setup_default_mcp_tools()

# Multiple agents using same tools
researcher = MCPEnabledAgent("Alice", "Researcher", router, shared_registry)
writer = MCPEnabledAgent("Bob", "Writer", router, shared_registry)
reviewer = MCPEnabledAgent("Carol", "Reviewer", router, shared_registry)

# Collaborative workflow
research_data = await researcher.process_task_with_tools(
    "Research AI trends and save to 'research.txt'"
)

summary = await writer.process_task_with_tools(
    "Read 'research.txt' and create 'summary.txt'"
)

feedback = await reviewer.process_task_with_tools(
    "Review 'summary.txt' and provide feedback"
)
```

### **2. Conditional Tool Access**
```python
# Different tools for different agent roles
async def create_role_specific_agent(name, role, router):
    if role == "developer":
        # Developers get code execution tools
        tools = ["filesystem", "git", "code_execution"]
    elif role == "researcher":
        # Researchers get web search tools
        tools = ["filesystem", "web_search", "database"]
    elif role == "writer":
        # Writers get documentation tools
        tools = ["filesystem", "web_search"]
    
    registry = MCPToolRegistry()
    for tool_type in tools:
        client = await setup_tool_client(tool_type)
        await registry.register_client(tool_type, client)
    
    return MCPEnabledAgent(name, role, router, registry)
```

### **3. Tool Usage Analytics**
```python
# Track tool usage across agents
agent = MCPEnabledAgent("DataBot", "Analyst", router, registry)

# Process multiple tasks
await agent.process_task_with_tools("Analyze data in 'data.csv'")
await agent.process_task_with_tools("Create visualization")

# Get usage statistics
stats = agent.get_tool_usage_stats()
print(f"Tools used: {stats['tools_used']}")
print(f"Success rate: {stats['success_rate']:.1%}")
print(f"Total calls: {stats['total_calls']}")
```

## 🔒 **Security and Best Practices**

### **1. Tool Access Control**
```python
# Restrict tools by agent role
class SecureMCPRegistry(MCPToolRegistry):
    def __init__(self, agent_role: str):
        super().__init__()
        self.agent_role = agent_role
        self.allowed_tools = self._get_allowed_tools(agent_role)
    
    def _get_allowed_tools(self, role: str) -> set:
        permissions = {
            "junior_dev": {"read_file", "write_file", "search_web"},
            "senior_dev": {"read_file", "write_file", "execute_python", "git_status"},
            "architect": {"read_file", "write_file", "query_database", "search_web"}
        }
        return permissions.get(role, set())
    
    async def call_tool(self, tool_name: str, arguments: dict):
        if tool_name not in self.allowed_tools:
            raise PermissionError(f"Tool {tool_name} not allowed for {self.agent_role}")
        return await super().call_tool(tool_name, arguments)
```

### **2. Sandboxed Execution**
```python
# Safe code execution with timeouts and limits
class SafeCodeExecutor:
    @staticmethod
    async def execute_python(code: str, timeout: int = 30):
        # Use Docker or other sandboxing
        # Limit resource usage
        # Monitor execution
        pass
```

### **3. Audit Logging**
```python
# Log all tool usage for security auditing
class AuditedMCPRegistry(MCPToolRegistry):
    async def call_tool(self, tool_name: str, arguments: dict):
        # Log the tool call
        logger.info(f"Tool call: {tool_name} with args: {arguments}")
        
        result = await super().call_tool(tool_name, arguments)
        
        # Log the result
        logger.info(f"Tool result: success={result.success}")
        
        return result
```

## 🌐 **MCP Ecosystem Integration**

### **1. Popular MCP Servers**
- **Filesystem**: File operations
- **Database**: SQL query execution
- **Web**: Search and scraping
- **Git**: Version control operations
- **Cloud**: AWS, GCP, Azure APIs
- **Slack**: Team communication
- **GitHub**: Repository management

### **2. Third-party Tools**
```python
# Connect to external MCP servers
await client.connect_server("github", {
    "type": "github",
    "api_token": os.getenv("GITHUB_TOKEN"),
    "base_url": "https://api.github.com"
})

await client.connect_server("slack", {
    "type": "slack", 
    "bot_token": os.getenv("SLACK_BOT_TOKEN"),
    "workspace": "your-workspace"
})
```

## 📊 **Performance Optimization**

### **1. Tool Caching**
```python
# Cache tool results for repeated calls
class CachedMCPRegistry(MCPToolRegistry):
    def __init__(self):
        super().__init__()
        self.cache = {}
    
    async def call_tool(self, tool_name: str, arguments: dict):
        cache_key = f"{tool_name}:{hash(str(arguments))}"
        
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        result = await super().call_tool(tool_name, arguments)
        self.cache[cache_key] = result
        
        return result
```

### **2. Parallel Tool Execution**
```python
# Execute multiple tools concurrently
async def parallel_tool_execution(registry, tool_calls):
    tasks = [
        registry.call_tool(call["name"], call["args"])
        for call in tool_calls
    ]
    return await asyncio.gather(*tasks)
```

## 🎉 **Benefits of MCP Integration**

1. **Standardization**: Consistent tool interface across all LLMs
2. **Security**: Proper sandboxing and access control
3. **Ecosystem**: Rich library of pre-built tools
4. **Composability**: Mix and match tools as needed
5. **Scalability**: Easy to add new tools and capabilities
6. **Collaboration**: Agents can share tools seamlessly
7. **Monitoring**: Built-in usage tracking and analytics

## 🚀 **Next Steps**

1. **Try the demo**: `python examples/mcp_demo.py`
2. **Install MCP SDK**: `pip install mcp`
3. **Build custom tools** for your specific use cases
4. **Set up MCP servers** for your infrastructure
5. **Deploy MCP-enabled agents** in production

## 📚 **Resources**

- [MCP Specification](https://spec.modelcontextprotocol.io/)
- [MCP Python SDK](https://github.com/modelcontextprotocol/python-sdk)
- [MCP Server Examples](https://github.com/modelcontextprotocol/servers)
- [Agent Swarm MCP Code](src/agent_swarm/mcp/)

---

**MCP + Agent Swarm = Powerful, Standardized, Secure Agent Development** 🚀
