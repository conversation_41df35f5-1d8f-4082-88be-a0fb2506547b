# 📚 Agent Swarm Documentation

Welcome to the comprehensive documentation for Agent Swarm - a revolutionary multi-LLM agent development framework with mathematical intent processing.

## 🚀 Quick Start

- **[Getting Started](GETTING_STARTED.md)** - Complete setup and first steps
- **[Installation Guide](INSTALLATION_GUIDE.md)** - Detailed installation instructions
- **[API Reference](API_REFERENCE.md)** - Complete API documentation

## 📖 Core Documentation

### 🏗️ Architecture & Features

- **[Mathematical Algorithms](MATHEMATICAL_ALGORITHMS.md)** - Revolutionary algorithm framework
- **[Advanced CLI Features](ADVANCED_CLI_FEATURES.md)** - Comprehensive CLI documentation
- **[File Editing](FILE_EDITING.md)** - File editing capabilities and workflows
- **[AI Tools Integration](AI_TOOLS_INTEGRATION.md)** - Integration with AI development tools

### 🛠️ Setup & Configuration

- **[DeepSeek Setup](DEEPSEEK_SETUP.md)** - DeepSeek API configuration
- **[Deployment](DEPLOYMENT.md)** - Production deployment guide
- **[Troubleshooting](TROUBLESHOOTING.md)** - Common issues and solutions

### 🔧 Interactive Guides

- **[Interactive Shell](guides/INTERACTIVE_SHELL.md)** - Shell usage and features
- **[MCP Integration](guides/MCP_INTEGRATION.md)** - Model Context Protocol integration
- **[RAG Development](guides/RAG_DEVELOPMENT.md)** - RAG system development

## 🤝 Contributing

- **[Contributing](CONTRIBUTING.md)** - How to contribute to the project

---

## 🎯 Documentation Navigation

### For New Users

1. Start with [Getting Started](GETTING_STARTED.md)
2. Follow [Installation Guide](INSTALLATION_GUIDE.md)
3. Review [API Reference](API_REFERENCE.md)

### For Developers

1. Explore [Advanced CLI Features](ADVANCED_CLI_FEATURES.md)
2. Check [Mathematical Algorithms](MATHEMATICAL_ALGORITHMS.md)
3. Review [AI Tools Integration](AI_TOOLS_INTEGRATION.md)

### For Contributors

1. Read [Contributing](CONTRIBUTING.md) guidelines
2. Check [Deployment](DEPLOYMENT.md) procedures
3. Review [Troubleshooting](TROUBLESHOOTING.md) guide

---

*This documentation is continuously updated. For the latest development status, see [ROADMAP.md](../ROADMAP.md).*
