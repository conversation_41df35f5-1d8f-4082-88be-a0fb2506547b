# Getting Started with Agent Swarm

## 🚀 **Quick Start Guide**

### **1. Try the Framework (No Setup Required)**

The easiest way to see what Agent Swarm can do:

```bash
# See the quick start demo
make demo
```

This demo shows all components working together without requiring any LLM setup.

### **2. Explore Individual Components**

```bash
# Try different parts of the framework
make demo-context    # RAG code search and context
make demo-mcp        # Tool integration (file ops, web search)
make demo-shell      # Interactive development environment
make demo-coding     # Coding agent workflows
```

## 🛠️ **Practical Usage Examples**

### **Example 1: Basic Agent Creation**

```python
#!/usr/bin/env python3
import asyncio
from agent_swarm import create_coding_agent

async def main():
    # Create a coding agent for your project
    agent = await create_coding_agent(
        name="MyBot",
        project_path="./my_project",  # Your project directory
        enable_rag=True               # Enable code context
    )

    print(f"✅ Agent created: {agent.name}")
    print(f"📁 Project indexed: {agent.project_path}")

    # Get project statistics
    if agent.dev_rag:
        stats = await agent.dev_rag.get_project_stats()
        print(f"📊 Files indexed: {stats['total_files_indexed']}")
        print(f"🐍 Languages: {list(stats.get('languages', {}).keys())}")

if __name__ == "__main__":
    asyncio.run(main())
```

### **Example 2: Code Search and Context**

```python
#!/usr/bin/env python3
import asyncio
from agent_swarm.context import setup_project_rag

async def search_my_code():
    # Index your project for intelligent search
    rag = await setup_project_rag("./my_project")

    # Search for specific patterns
    results = await rag.search_code("async def", language="python")
    print(f"Found {len(results)} async functions")

    for result in results[:3]:
        file_path = result.metadata.get('file_path')
        print(f"  📄 {file_path}")

    # Find related files
    if results:
        related = await rag.get_related_files(results[0].metadata['file_path'])
        print(f"\n🔗 Related files: {len(related)}")

if __name__ == "__main__":
    asyncio.run(search_my_code())
```

### **Example 3: File Operations with MCP Tools**

```python
#!/usr/bin/env python3
import asyncio
from agent_swarm.mcp import setup_default_mcp_tools

async def file_operations():
    # Set up MCP tools
    mcp_registry = await setup_default_mcp_tools()

    # Write a file
    result = await mcp_registry.call_tool("write_file", {
        "path": "hello.py",
        "content": '''def hello():
    return "Hello, World!"

if __name__ == "__main__":
    print(hello())
'''
    })

    if result.success:
        print("✅ File written successfully")

        # Read it back
        read_result = await mcp_registry.call_tool("read_file", {
            "path": "hello.py"
        })

        if read_result.success:
            content = read_result.result["content"]
            print(f"📄 File content ({len(content.split())} lines):")
            print(content[:100] + "..." if len(content) > 100 else content)

if __name__ == "__main__":
    asyncio.run(file_operations())
```

## 🎯 **Real-World Usage Scenarios**

### **Scenario 1: Analyze an Existing Project**

```bash
# 1. Clone or navigate to your project
cd /path/to/your/project

# 2. Create a simple analysis script
cat > analyze_project.py << 'EOF'
#!/usr/bin/env python3
import asyncio
import sys
from pathlib import Path

# Add agent-swarm to path (adjust path as needed)
sys.path.insert(0, "/path/to/agent-swarm/src")

from agent_swarm.context import setup_project_rag

async def analyze_project():
    print("🔍 Analyzing project...")

    # Index the current directory
    rag = await setup_project_rag(".")

    # Get project statistics
    stats = await rag.get_project_stats()
    print(f"\n📊 Project Analysis:")
    print(f"   Files indexed: {stats['total_files_indexed']}")
    print(f"   Languages: {list(stats.get('languages', {}).keys())}")
    print(f"   File types: {list(stats.get('file_types', {}).keys())}")

    # Search for common patterns
    patterns = [
        ("class", "classes"),
        ("def", "functions"),
        ("import", "imports"),
        ("async def", "async functions"),
        ("test", "tests")
    ]

    print(f"\n🔍 Code Patterns:")
    for pattern, description in patterns:
        results = await rag.search_code(pattern)
        print(f"   {description}: {len(results)}")

if __name__ == "__main__":
    asyncio.run(analyze_project())
EOF

# 3. Run the analysis
python analyze_project.py
```

### **Scenario 2: Interactive Code Assistant**

```python
#!/usr/bin/env python3
import asyncio
from agent_swarm import create_coding_agent

async def interactive_assistant():
    print("🤖 Starting Interactive Code Assistant")

    # Create agent for current directory
    agent = await create_coding_agent(
        name="Assistant",
        project_path=".",
        enable_rag=True
    )

    print("✅ Agent ready! Try these commands:")
    print("   1. Search code: 'search: your query'")
    print("   2. Find related: 'related: filename'")
    print("   3. Analyze file: 'analyze: filename'")
    print("   4. Quit: 'quit'")

    while True:
        try:
            command = input("\n🔍 Enter command: ").strip()

            if command.lower() == 'quit':
                break

            if command.startswith('search:'):
                query = command[7:].strip()
                results = await agent.dev_rag.search_code(query)
                print(f"📊 Found {len(results)} results")
                for i, result in enumerate(results[:3], 1):
                    file_path = result.metadata.get('file_path', 'unknown')
                    print(f"   {i}. {file_path}")

            elif command.startswith('related:'):
                filename = command[8:].strip()
                related = await agent.dev_rag.get_related_files(filename)
                print(f"🔗 Found {len(related)} related files")
                for i, rel in enumerate(related[:5], 1):
                    file_path = rel.metadata.get('file_path', 'unknown')
                    print(f"   {i}. {file_path}")

            elif command.startswith('analyze:'):
                filename = command[8:].strip()
                # Simple file analysis
                try:
                    with open(filename, 'r') as f:
                        content = f.read()
                    lines = len(content.split('\n'))
                    words = len(content.split())
                    print(f"📄 {filename}: {lines} lines, {words} words")
                except FileNotFoundError:
                    print(f"❌ File not found: {filename}")

            else:
                print("❓ Unknown command. Try 'search:', 'related:', 'analyze:', or 'quit'")

        except KeyboardInterrupt:
            break

    print("\n👋 Goodbye!")

if __name__ == "__main__":
    asyncio.run(interactive_assistant())
```

### **Scenario 3: Project Setup Assistant**

```python
#!/usr/bin/env python3
import asyncio
from agent_swarm.mcp import setup_default_mcp_tools

async def setup_python_project(project_name):
    print(f"🚀 Setting up Python project: {project_name}")

    # Set up MCP tools
    mcp = await setup_default_mcp_tools()

    # Create project structure
    files_to_create = {
        f"{project_name}/__init__.py": "",
        f"{project_name}/main.py": '''#!/usr/bin/env python3
"""Main module for {project_name}."""

def main():
    print("Hello from {project_name}!")

if __name__ == "__main__":
    main()
'''.format(project_name=project_name),
        f"{project_name}/utils.py": '''"""Utility functions for {project_name}."""

def helper_function():
    """A helper function."""
    pass
'''.format(project_name=project_name),
        "requirements.txt": "# Add your dependencies here\n",
        "README.md": f'''# {project_name}

A Python project created with Agent Swarm.

## Installation

```bash
pip install -r requirements.txt
```

## Usage

```bash
python -m {project_name}.main
```
''',
        "tests/__init__.py": "",
        f"tests/test_{project_name}.py": f'''"""Tests for {project_name}."""

import unittest
from {project_name}.main import main

class Test{project_name.title()}(unittest.TestCase):
    def test_main(self):
        # Add your tests here
        pass

if __name__ == "__main__":
    unittest.main()
'''
    }

    # Create all files
    for file_path, content in files_to_create.items():
        result = await mcp.call_tool("write_file", {
            "path": file_path,
            "content": content
        })

        if result.success:
            print(f"✅ Created: {file_path}")
        else:
            print(f"❌ Failed to create: {file_path}")

    print(f"\n🎉 Project {project_name} created successfully!")
    print(f"📁 Files created: {len(files_to_create)}")
    print(f"\n🚀 Next steps:")
    print(f"   cd {project_name}")
    print(f"   python main.py")

if __name__ == "__main__":
    import sys
    project_name = sys.argv[1] if len(sys.argv) > 1 else "my_project"
    asyncio.run(setup_python_project(project_name))
```

## 🔧 **Adding LLM Backends (Optional)**

If you want to use actual LLM generation (not just the framework features):

### **Option 1: Local LLMs with Ollama**

```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull recommended models
ollama pull deepseek-r1:7b        # Primary coding model
ollama pull qwen2.5-coder:7b      # Fast backup model

# Test Ollama
ollama list
```

### **Option 2: Cloud APIs**

```bash
# Set up environment variables
export OPENAI_API_KEY="your-openai-key"
export ANTHROPIC_API_KEY="your-anthropic-key"
export GOOGLE_API_KEY="your-google-key"
```

### **Option 3: Use the Framework Without LLMs**

The framework provides tons of value even without LLM backends:
- **RAG Code Search**: Find code patterns across your projects
- **MCP Tools**: File operations, web search, process management
- **CLI Tools**: Development workflow automation
- **Project Analysis**: Understand codebase structure and patterns

## 📚 **Common Use Cases**

### **1. Code Discovery**
```bash
# Find all async functions in your project
make demo-context
# Then search for "async def"
```

### **2. Project Analysis**
```bash
# Analyze any project structure
make demo-context
# Shows file types, languages, patterns
```

### **3. File Operations**
```bash
# Automate file creation and manipulation
make demo-mcp
# Shows file read/write, web search
```

### **4. Development Workflows**
```bash
# See complete development assistant
make demo-shell
# Shows interactive development environment
```

## 🎯 **Next Steps**

1. **Start with demos**: `make demo`
2. **Try on your project**: Create analysis scripts
3. **Add LLM backends**: For actual code generation
4. **Integrate with tools**: IDE plugins, CI/CD
5. **Scale up**: Team deployment, enterprise features

The framework is designed to be useful immediately, even without LLM backends. Start exploring and see what works best for your workflow!
