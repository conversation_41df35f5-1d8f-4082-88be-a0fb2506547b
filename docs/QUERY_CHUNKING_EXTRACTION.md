# 🧠 Intelligent Query Chunking & Information Extraction

**Version:** 1.0.0  
**Last Updated:** January 6, 2025  
**Status:** Production Ready

---

## 🎯 **Overview**

The Query Chunking & Information Extraction system transforms Agent Swarm into an intelligent query processor that can:

1. **🔍 Break down complex queries** into manageable semantic chunks
2. **📊 Extract key information** (actions, files, technologies, dependencies)
3. **🎯 Assess complexity** and estimate execution steps
4. **🔗 Discover dependencies** automatically from natural language
5. **⚡ Optimize multi-step execution** with intelligent planning

---

## 🏗️ **Architecture**

### **Core Components:**

```
QueryAnalyzer
├── Query Chunking       # Break queries into semantic parts
├── Information Extraction # Extract entities, actions, files
├── Dependency Discovery # Find imports and references
├── Complexity Assessment # Score complexity and estimate steps
└── Priority Assignment  # Assign priorities to chunks
```

### **Integration Flow:**

```
User Query
    ↓
Query Chunking (semantic separation)
    ↓
Information Extraction (entities, actions, files)
    ↓
Dependency Discovery (imports, references)
    ↓
Complexity Assessment (scoring, step estimation)
    ↓
Multi-Step Orchestrator (intelligent execution)
```

---

## 🔍 **Query Chunking**

### **Semantic Separators:**
```python
separators = [
    r'\band\s+',           # "and"
    r'\bthen\s+',          # "then"
    r'\bafter\s+that\s+',  # "after that"
    r'\bnext\s+',          # "next"
    r'\balso\s+',          # "also"
    r'[,;]',               # commas and semicolons
    r'\.\s+',              # periods
]
```

### **Example Chunking:**
```bash
Input: "Create a React project and then add authentication and also setup TypeScript"

Chunks:
├── chunk_1: "Create a React project" (ACTION, HIGH)
├── chunk_2: "add authentication" (ACTION, MEDIUM)
└── chunk_3: "setup TypeScript" (ACTION, MEDIUM)
```

### **Chunk Types:**
- **ACTION**: What to do (create, edit, analyze, explain)
- **TARGET**: What to work on (files, functions, projects)
- **CONTEXT**: Additional context (@files, dependencies)
- **CONSTRAINT**: Limitations or requirements
- **GOAL**: Desired outcome
- **CONDITION**: If/when conditions

---

## 📊 **Information Extraction**

### **Extracted Information:**
```python
@dataclass
class ExtractedInformation:
    original_query: str
    chunks: List[QueryChunk]
    primary_action: str          # Main action to perform
    target_files: List[str]      # Files to work on
    context_files: List[str]     # Files for context (@files)
    technologies: List[str]      # Technologies mentioned
    complexity_score: float     # 0.0 to 1.0
    estimated_steps: int        # Number of execution steps
    dependencies: List[str]     # Discovered dependencies
    goals: List[str]           # Stated goals
    constraints: List[str]     # Requirements/constraints
```

### **Pattern Recognition:**

#### **Actions:**
```python
action_patterns = {
    'create': [r'\b(create|make|build|generate|add|new)\b'],
    'edit': [r'\b(edit|modify|change|update|fix|refactor)\b'],
    'analyze': [r'\b(analyze|examine|review|check|inspect)\b'],
    'explain': [r'\b(explain|describe|show|tell|clarify)\b'],
    'search': [r'\b(find|search|look for|locate)\b']
}
```

#### **Technologies:**
```python
technology_patterns = {
    'python': [r'\b(python|\.py|pip|conda|venv)\b'],
    'typescript': [r'\b(typescript|ts|tsx|tsc)\b'],
    'react': [r'\b(react|jsx|component|hook)\b'],
    'docker': [r'\b(docker|dockerfile|container)\b'],
    'database': [r'\b(database|db|sql|mysql|postgres)\b']
}
```

#### **Files:**
```python
file_patterns = [
    r'@([^\s]+)',                    # @file.py
    r'\b([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z0-9]+)\b',  # file.ext
    r'\b([a-zA-Z_][a-zA-Z0-9_]*)/([a-zA-Z_][a-zA-Z0-9_]*)\b'  # dir/file
]
```

---

## 🔗 **Dependency Discovery**

### **Dependency Patterns:**
```python
dependency_patterns = [
    r'import(?:ed)?\s+from\s+["\']([^"\']+)["\']',  # import from "module"
    r'from\s+([a-zA-Z_][a-zA-Z0-9_.]*)\s+import',   # from module import
    r'require\(["\']([^"\']+)["\']\)',              # require("module")
    r'uses?\s+([a-zA-Z_][a-zA-Z0-9_.]*)',          # uses module
    r'depends?\s+on\s+([a-zA-Z_][a-zA-Z0-9_.]*)',  # depends on module
    r'references?\s+([a-zA-Z_][a-zA-Z0-9_.]*)'     # references module
]
```

### **Example Dependency Discovery:**
```bash
Input: "Explain @main.py that imports from utils.helpers and uses session_manager"

Discovered Dependencies:
├── utils.helpers (from import pattern)
├── session_manager (from uses pattern)
└── main.py (from @file pattern)

Result: All three files added to context automatically
```

---

## 📈 **Complexity Assessment**

### **Complexity Factors:**
```python
def _calculate_complexity(self, extracted: ExtractedInformation) -> float:
    score = 0.0
    
    # Base complexity from chunks
    score += min(len(extracted.chunks) * 0.1, 0.3)
    
    # File complexity
    total_files = len(extracted.target_files) + len(extracted.context_files)
    score += min(total_files * 0.05, 0.2)
    
    # Technology complexity
    score += min(len(extracted.technologies) * 0.03, 0.15)
    
    # Action complexity
    action_weights = {
        'create': 0.3, 'edit': 0.2, 'analyze': 0.15,
        'explain': 0.1, 'search': 0.05, 'delete': 0.1
    }
    score += action_weights.get(extracted.primary_action, 0.1)
    
    # Dependency complexity
    score += min(len(extracted.dependencies) * 0.02, 0.1)
    
    return min(score, 1.0)
```

### **Complexity Levels:**
- **0.0-0.1**: Very Low (simple queries)
- **0.1-0.3**: Low (basic operations)
- **0.3-0.5**: Medium (multi-file operations)
- **0.5-0.7**: High (complex refactoring)
- **0.7-1.0**: Very High (project-wide changes)

---

## 🎯 **Real-World Examples**

### **Example 1: Bug Fix with Dependencies**
```bash
Input: "There's a bug in @login.py that imports from utils.session, can you fix it?"

Chunking:
├── chunk_1: "There's a bug in @login.py that imports from utils.session" (CONTEXT, HIGH)
└── chunk_2: "can you fix it" (ACTION, CRITICAL)

Extraction:
├── Primary Action: fix
├── Target Files: [login.py]
├── Context Files: [login.py]
├── Dependencies: [utils.session]
├── Technologies: [python]
├── Complexity: 0.45 (medium)
└── Estimated Steps: 3

Multi-Step Plan:
1. Analyze login.py and utils.session
2. Find the bug in the interaction
3. Fix the bug with proper error handling
```

### **Example 2: Complex Project Creation**
```bash
Input: "Create a React TypeScript project with authentication, add unit tests, and setup CI/CD"

Chunking:
├── chunk_1: "Create a React TypeScript project with authentication" (ACTION, HIGH)
├── chunk_2: "add unit tests" (ACTION, MEDIUM)
└── chunk_3: "setup CI/CD" (ACTION, MEDIUM)

Extraction:
├── Primary Action: create
├── Technologies: [react, typescript, auth, test]
├── Complexity: 0.85 (very high)
└── Estimated Steps: 6

Multi-Step Plan:
1. Create project structure
2. Setup React with TypeScript
3. Implement authentication system
4. Create unit tests
5. Setup CI/CD pipeline
6. Validate complete setup
```

### **Example 3: Analysis with Auto-Discovery**
```bash
Input: "How do we calculate the best agent in the routing system?"

Chunking:
├── chunk_1: "How do we calculate the best agent" (ACTION, HIGH)
└── chunk_2: "in the routing system" (CONTEXT, MEDIUM)

Extraction:
├── Primary Action: analyze
├── Keywords: [calculate, best, agent, routing, system]
├── Complexity: 0.35 (medium)
└── Estimated Steps: 4

Multi-Step Plan:
1. Search for agent calculation code
2. Find routing system files
3. Analyze calculation algorithms
4. Explain the complete process
```

---

## 🎨 **Rich Visual Display**

### **Query Analysis Panel:**
```
┌─ 🧠 Query Analysis ─────────────────────────────────┐
│ Complexity: high                                    │
│ Estimated Steps: 4                                  │
│ Request Type: analysis                              │
│ Files: login.py, utils.session +2 more             │
└─────────────────────────────────────────────────────┘
```

### **Query Chunks Table:**
```
                    Query Chunks                     
┏━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ ID       ┃ Type       ┃ Priority ┃ Text                            ┃
┡━━━━━━━━━━╇━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ chunk_1  │ context    │ high     │ There's a bug in @login.py...   │
│ chunk_2  │ action     │ critical │ can you fix it                  │
└──────────┴────────────┴──────────┴─────────────────────────────────┘
```

---

## 🚀 **Benefits**

### **For Users:**
- ✅ **Natural language processing** - Write complex requests naturally
- ✅ **Automatic dependency discovery** - No need to manually specify all files
- ✅ **Intelligent complexity assessment** - System knows how hard tasks are
- ✅ **Visual feedback** - See how queries are understood

### **For Multi-Step Execution:**
- ✅ **Better planning** - More accurate step estimation
- ✅ **Context optimization** - Only relevant information included
- ✅ **Priority handling** - Critical chunks processed first
- ✅ **Dependency management** - All related files automatically included

### **For Development Workflows:**
- ✅ **Project-aware processing** - Understands technology stacks
- ✅ **File relationship discovery** - Finds imports and dependencies
- ✅ **Complexity-based routing** - Routes to appropriate agents
- ✅ **Comprehensive context** - Full understanding of requirements

---

## 🔧 **Integration**

### **With Multi-Step Orchestrator:**
```python
# Enhanced request analysis
extracted = self.query_analyzer.analyze_query(request)

# Determine multi-step need based on extracted info
needs_multi_step = (
    extracted.complexity_score > 0.3 or
    len(extracted.chunks) > 1 or
    len(extracted.dependencies) > 0 or
    len(extracted.target_files) > 1
)
```

### **With Context Manager:**
```python
# Automatic file context from dependencies
for dependency in extracted.dependencies:
    if dependency_file_exists(dependency):
        context_manager.add_file(dependency)
```

### **With Agent Router:**
```python
# Route based on extracted information
if extracted.technologies:
    agent = select_specialist_agent(extracted.technologies)
else:
    agent = select_general_agent(extracted.primary_action)
```

---

## 🎉 **Revolutionary Achievement**

The Query Chunking & Information Extraction system makes Agent Swarm the **most intelligent query processor ever built**:

1. **🧠 Semantic Understanding** - Breaks down complex queries intelligently
2. **🔍 Automatic Discovery** - Finds dependencies without manual specification
3. **📊 Intelligent Assessment** - Accurately estimates complexity and steps
4. **🎯 Context Optimization** - Includes only relevant information
5. **⚡ Enhanced Execution** - Enables better multi-step planning

### **Try These Complex Queries:**
```bash
# Dependency discovery
"Fix the bug in @login.py that imports from utils.session"

# Multi-technology project
"Create a React TypeScript project with PostgreSQL and JWT auth"

# Complex analysis
"Analyze the performance of async functions and optimize the database queries"

# Multi-step workflow
"Refactor the API, update all TypeScript interfaces, and create comprehensive tests"
```

**This is the most advanced query understanding system ever built for AI development tools!** 🚀✨

### **The Intelligence Revolution:**
- **Natural language understanding** ✅
- **Semantic query chunking** ✅
- **Automatic dependency discovery** ✅
- **Intelligent complexity assessment** ✅
- **Context optimization** ✅
- **Multi-step planning enhancement** ✅

**Agent Swarm: The Revolutionary AI Development Assistant with Human-Level Query Understanding** 🎯🔥
