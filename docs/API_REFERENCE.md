# 📚 API Reference

Complete API documentation for Agent Swarm framework components.

## 🚀 Quick Navigation

### **Core Components**
- [LLM Router](#llm-router) - Multi-LLM routing and management
- [Agents](#agents) - Agent creation and management
- [Context Engine](#context-engine) - RAG and context management
- [MCP Integration](#mcp-integration) - Tool integration

### **Utilities**
- [Configuration](#configuration) - Framework configuration
- [Logging](#logging) - Logging and monitoring
- [Types](#types) - Type definitions and models

## 🤖 LLM Router

### `LLMRouter`

Central component for managing multiple LLM backends with intelligent routing.

```python
from agent_swarm import LLMRouter, LLMTier

router = LLMRouter()
```

#### Methods

##### `register_llm(name: str, llm: BaseLLM) -> None`
Register an LLM backend with the router.

```python
from agent_swarm.backends.ollama import create_ollama_llm

llm = create_ollama_llm("llama3.2:3b")
router.register_llm("local_fast", llm)
```

##### `route_request(messages: List[Message], task_type: str, preferred_tier: LLMTier, **kwargs) -> LLMResponse`
Route a request to the optimal LLM based on task complexity and preferences.

```python
response = await router.route_request(
    messages=[Message(role="user", content="Explain Python lists")],
    task_type="explanation",
    preferred_tier=LLMTier.LOCAL_FAST,
    max_tokens=500
)
```

**Parameters:**
- `messages`: List of conversation messages
- `task_type`: Type of task (e.g., "coding", "explanation", "debug")
- `preferred_tier`: Preferred LLM tier for routing
- `**kwargs`: Additional parameters passed to LLM

**Returns:** `LLMResponse` with content, model used, and usage statistics

##### `get_available_llms() -> Dict[str, LLMConfig]`
Get information about all registered LLMs.

```python
available = router.get_available_llms()
for name, config in available.items():
    print(f"{name}: {config.tier.value}")
```

##### `estimate_cost(task_type: str, prompt_tokens: int, completion_tokens: int) -> float`
Estimate cost for a request based on token usage.

```python
cost = router.estimate_cost("coding", 1000, 500)
print(f"Estimated cost: ${cost:.4f}")
```

## 🤖 Agents

### `create_coding_agent(name: str, **kwargs) -> CodingAgent`

Create a specialized coding agent with development capabilities.

```python
from agent_swarm import create_coding_agent

agent = await create_coding_agent(
    name="DevBot",
    project_path="./my_project",
    enable_rag=True,
    enable_mcp=True
)
```

**Parameters:**
- `name`: Agent name
- `project_path`: Path to project for context
- `enable_rag`: Enable RAG context engine
- `enable_mcp`: Enable MCP tool integration
- `llm_router`: Custom LLM router (optional)

### `CodingAgent`

Specialized agent for development workflows.

#### Methods

##### `implement_feature(feature_description: str, file_path: str, test_required: bool = False) -> Dict`
Implement a complete feature with optional tests.

```python
result = await agent.implement_feature(
    "Create a user authentication module",
    "src/auth.py",
    test_required=True
)
```

##### `fix_bug(bug_description: str, file_path: str, error_message: str = None) -> Dict`
Fix bugs with context awareness.

```python
result = await agent.fix_bug(
    "Login fails with empty password",
    "src/auth.py",
    "ValueError: Password cannot be empty"
)
```

##### `review_code(file_path: str, focus_areas: List[str] = None) -> Dict`
Perform intelligent code review.

```python
result = await agent.review_code(
    "src/auth.py",
    focus_areas=["security", "performance"]
)
```

### `MultiLLMAgent`

General-purpose agent with multi-LLM capabilities.

```python
from agent_swarm import MultiLLMAgent, LLMRouter

router = LLMRouter()
agent = MultiLLMAgent("Assistant", "General Assistant", router)
```

#### Methods

##### `process_task(task: str, preferred_tier: LLMTier = None, **kwargs) -> str`
Process a general task with optimal LLM selection.

```python
response = await agent.process_task(
    "Explain the benefits of async programming",
    preferred_tier=LLMTier.LOCAL_FAST
)
```

## 🧠 Context Engine

### `UnifiedContext`

Unified context management with RAG capabilities.

```python
from agent_swarm.context import UnifiedContext

context = UnifiedContext("./my_project")
await context.initialize(enable_rag=True)
```

#### Methods

##### `get_context(query: str, intent: QueryIntent, response_mode: ResponseMode, **kwargs) -> List[ContextResult]`
Get intelligent context for a query.

```python
from agent_swarm.context import QueryIntent, ResponseMode

results = await context.get_context(
    query="authentication implementation",
    intent=QueryIntent.FEATURE,
    response_mode=ResponseMode.DETAILED,
    max_results=5
)
```

##### `search_code(query: str, language: str = None, **kwargs) -> List[SearchResult]`
Search code with semantic understanding.

```python
results = await context.search_code(
    "async def login",
    language="python"
)
```

##### `get_project_analysis() -> ProjectAnalysis`
Get comprehensive project analysis.

```python
analysis = await context.get_project_analysis()
print(f"Project type: {analysis.project_type.value}")
print(f"Main language: {analysis.main_language}")
```

### `setup_project_rag(project_path: str, **kwargs) -> DevRAG`

Quick setup for project-specific RAG.

```python
from agent_swarm.context import setup_project_rag

rag = await setup_project_rag(
    "./my_project",
    vector_store="chroma",
    chunk_size=1000
)
```

## 🔗 MCP Integration

### `MCPEnabledAgent`

Agent with MCP tool integration capabilities.

```python
from agent_swarm.mcp import MCPEnabledAgent, setup_default_mcp_tools

registry = await setup_default_mcp_tools()
agent = MCPEnabledAgent("ToolBot", "Developer", router, registry)
```

#### Methods

##### `process_task_with_tools(task: str) -> Dict`
Process tasks with automatic tool calling.

```python
result = await agent.process_task_with_tools(
    "Create a file called 'hello.py' with a hello world function"
)
```

### `setup_default_mcp_tools() -> MCPToolRegistry`

Set up default MCP tools for common operations.

```python
from agent_swarm.mcp import setup_default_mcp_tools

registry = await setup_default_mcp_tools()
tools = registry.get_all_tools()
```

### `MCPToolRegistry`

Registry for managing MCP tools.

#### Methods

##### `call_tool(tool_name: str, arguments: dict) -> ToolResult`
Call a registered tool.

```python
result = await registry.call_tool("read_file", {
    "path": "src/main.py"
})
```

##### `get_all_tools() -> List[Tool]`
Get all registered tools.

```python
tools = registry.get_all_tools()
for tool in tools:
    print(f"{tool.name}: {tool.description}")
```

## ⚙️ Configuration

### `load_config() -> AgentSwarmConfig`

Load framework configuration.

```python
from agent_swarm.utils import load_config

config = load_config()
print(f"Default model: {config.default_local_model}")
```

### `AgentSwarmConfig`

Configuration model for the framework.

**Attributes:**
- `default_local_model`: Default local LLM model
- `fallback_local_model`: Fallback local model
- `openai_api_key`: OpenAI API key
- `anthropic_api_key`: Anthropic API key
- `google_api_key`: Google API key

## 📊 Types

### `LLMTier`

Enumeration of LLM tiers for routing.

```python
from agent_swarm import LLMTier

# Available tiers
LLMTier.LOCAL_FAST      # Fast local models
LLMTier.LOCAL_QUALITY   # Quality local models
LLMTier.CLOUD_STANDARD  # Standard cloud models
LLMTier.CLOUD_PREMIUM   # Premium cloud models
```

### `Message`

Message model for LLM conversations.

```python
from agent_swarm import Message

message = Message(
    role="user",
    content="Hello, world!",
    metadata={"timestamp": "2024-01-01T00:00:00Z"}
)
```

### `LLMResponse`

Response model from LLM calls.

**Attributes:**
- `content`: Response content
- `model`: Model that generated the response
- `usage`: Token usage statistics
- `metadata`: Additional metadata

### `QueryIntent`

Intent classification for context queries.

```python
from agent_swarm.context import QueryIntent

QueryIntent.UNDERSTAND   # Understanding code/concepts
QueryIntent.FEATURE      # Feature implementation
QueryIntent.DEBUG        # Debugging issues
QueryIntent.GENERAL      # General queries
```

### `ResponseMode`

Response mode for context retrieval.

```python
from agent_swarm.context import ResponseMode

ResponseMode.FAST           # Quick responses
ResponseMode.DETAILED       # Balanced responses
ResponseMode.COMPREHENSIVE  # Thorough responses
```

## 🔧 Utilities

### `setup_logging(level: str = "INFO") -> None`

Configure framework logging.

```python
from agent_swarm import setup_logging

setup_logging(level="DEBUG")
```

### Error Handling

The framework uses custom exceptions for better error handling:

```python
from agent_swarm.exceptions import (
    AgentSwarmError,
    LLMError,
    ContextError,
    MCPError
)

try:
    response = await router.route_request(messages, "coding")
except LLMError as e:
    print(f"LLM error: {e}")
except AgentSwarmError as e:
    print(f"Framework error: {e}")
```

## 📖 Examples

### Complete Workflow Example

```python
import asyncio
from agent_swarm import create_coding_agent, setup_logging

async def main():
    # Setup
    setup_logging(level="INFO")
    
    # Create agent
    agent = await create_coding_agent(
        name="DevBot",
        project_path="./my_project",
        enable_rag=True,
        enable_mcp=True
    )
    
    # Implement feature
    result = await agent.implement_feature(
        "Create a REST API endpoint for user registration",
        "src/api/users.py",
        test_required=True
    )
    
    print(f"Implementation: {'✅' if result['success'] else '❌'}")
    print(f"Files created: {result.get('files_created', [])}")

if __name__ == "__main__":
    asyncio.run(main())
```

---

**For more examples, see [Examples Directory](../examples/README.md)**

**Next:** [Deployment Guide](DEPLOYMENT.md) | [Troubleshooting](TROUBLESHOOTING.md) | [Contributing](CONTRIBUTING.md)
