# 🔧 Troubleshooting Guide

Common issues and solutions for Agent Swarm framework.

## 🚀 Quick Fixes

### **Installation Issues**

#### "pip install failed"
```bash
# Update pip and try again
pip install --upgrade pip
pip install -e .

# Or use specific Python version
python3.8 -m pip install -e .
```

#### "No module named 'agent_swarm'"
```bash
# Install in development mode
pip install -e .

# Check installation
python -c "import agent_swarm; print('✅ Installed')"
```

### **LLM Connection Issues**

#### "No LLMs available"
```bash
# Option 1: Install Ollama (recommended)
curl -fsSL https://ollama.ai/install.sh | sh
ollama pull llama3.2:3b

# Option 2: Use cloud APIs
export OPENAI_API_KEY="your-key"
export ANTHROPIC_API_KEY="your-key"

# Test connection
make demo
```

#### "Ollama connection failed"
```bash
# Check if Ollama is running
ollama list

# Start Ollama service
ollama serve

# Check available models
ollama list
```

#### "API key invalid"
```bash
# Check environment variables
echo $OPENAI_API_KEY
echo $ANTHROPIC_API_KEY

# Set keys properly
export OPENAI_API_KEY="sk-..."
export ANTHROPIC_API_KEY="sk-ant-..."
```

### **Example/Demo Issues**

#### "Examples not working"
```bash
# Check setup
make test

# Run specific example with verbose output
python examples/quick_start.py --verbose

# Check dependencies
pip install -e ".[all]"
```

#### "Demo commands not found"
```bash
# Use make commands
make demo           # Quick start
make demo-coding    # Coding workflows
make demo-shell     # Interactive shell

# Or run directly
python examples/quick_start.py
```

## 🐛 Common Error Messages

### **Import Errors**

#### `ModuleNotFoundError: No module named 'rich'`
```bash
# Install missing dependencies
pip install rich

# Or install all dependencies
pip install -e ".[all]"
```

#### `ImportError: cannot import name 'create_coding_agent'`
```bash
# Check installation
pip install -e .

# Verify import path
python -c "from agent_swarm import create_coding_agent"
```

### **Runtime Errors**

#### `ConnectionError: Could not connect to Ollama`
```bash
# Start Ollama
ollama serve

# Check if running
curl http://localhost:11434/api/tags

# Pull a model if needed
ollama pull llama3.2:3b
```

#### `ValueError: No LLM backends available`
```bash
# Setup at least one backend
# Option 1: Ollama
ollama pull llama3.2:3b

# Option 2: Cloud API
export OPENAI_API_KEY="your-key"

# Test setup
python -c "
from agent_swarm import LLMRouter
router = LLMRouter()
print(f'Available LLMs: {len(router.llms)}')
"
```

#### `FileNotFoundError: Project path not found`
```bash
# Check project path exists
ls -la /path/to/your/project

# Use absolute path
python examples/context_engine.py --project $(pwd)

# Or run from project directory
cd /path/to/your/project
python /path/to/agent-swarm/examples/context_engine.py
```

### **Performance Issues**

#### "RAG indexing is slow"
```bash
# Use faster vector store for development
# In your code:
rag = await setup_project_rag(
    ".",
    vector_store="memory",  # Faster than chroma
    chunk_size=500         # Smaller chunks
)
```

#### "LLM responses are slow"
```bash
# Use faster local models
export AGENT_SWARM_MODEL="llama3.2:3b"  # Faster than 7b models

# Or reduce max_tokens
response = await router.route_request(
    messages=messages,
    max_tokens=500  # Reduce from default
)
```

## 🔍 Debugging

### **Enable Verbose Logging**
```bash
# Set environment variable
export AGENT_SWARM_LOG_LEVEL=DEBUG

# Or in code
from agent_swarm import setup_logging
setup_logging(level="DEBUG")
```

### **Check System Status**
```bash
# Run system test
make test

# Check individual components
python -c "
import asyncio
from agent_swarm import LLMRouter
from agent_swarm.context import UnifiedContext

async def test():
    # Test LLM router
    router = LLMRouter()
    print(f'LLMs: {len(router.llms)}')
    
    # Test context engine
    context = UnifiedContext('.')
    await context.initialize()
    print('Context engine: ✅')

asyncio.run(test())
"
```

### **Interactive Shell Issues**

#### "Shell won't start"
```bash
# Check dependencies
pip install rich

# Run with verbose mode
python examples/interactive_shell.py --verbose

# Check terminal compatibility
echo $TERM
```

#### "Rich formatting not working"
```bash
# Force color output
export FORCE_COLOR=1

# Check Rich installation
python -c "from rich.console import Console; Console().print('✅ Rich working')"
```

### **MCP Tool Issues**

#### "MCP tools not available"
```bash
# Install MCP SDK
pip install mcp

# Test MCP integration
python -c "
from agent_swarm.mcp import setup_default_mcp_tools
import asyncio

async def test():
    registry = await setup_default_mcp_tools()
    tools = registry.get_all_tools()
    print(f'MCP tools: {len(tools)}')

asyncio.run(test())
"
```

## 🔧 Environment Setup

### **Development Environment**
```bash
# Complete development setup
make install-dev

# Run all tests
make test

# Code quality checks
make format lint type-check
```

### **Production Environment**
```bash
# Install production dependencies
pip install -e ".[all]"

# Set environment variables
export OPENAI_API_KEY="your-key"
export ANTHROPIC_API_KEY="your-key"
export AGENT_SWARM_LOG_LEVEL="INFO"

# Test production setup
python -c "
from agent_swarm import create_coding_agent
import asyncio

async def test():
    agent = await create_coding_agent('TestBot')
    print('✅ Production setup working')

asyncio.run(test())
"
```

## 📊 Performance Optimization

### **Memory Usage**
```bash
# Use memory-efficient settings
export AGENT_SWARM_CHUNK_SIZE=500
export AGENT_SWARM_MAX_RESULTS=10

# Monitor memory usage
python -c "
import psutil
import os
process = psutil.Process(os.getpid())
print(f'Memory: {process.memory_info().rss / 1024 / 1024:.1f} MB')
"
```

### **Speed Optimization**
```bash
# Use faster models for development
export AGENT_SWARM_MODEL="llama3.2:3b"

# Reduce context depth
# In your code:
results = await context.get_context(
    query="your query",
    context_depth=ContextDepth.SHALLOW  # Faster
)
```

## 🆘 Getting Help

### **Check Documentation**
- [Quick Start Guide](QUICK_START.md) - Basic setup
- [API Reference](API_REFERENCE.md) - Complete API docs
- [Interactive Shell Guide](guides/INTERACTIVE_SHELL.md) - Shell usage
- [Examples](../examples/README.md) - Working examples

### **Community Support**
- **GitHub Issues**: [Report bugs and request features](https://github.com/your-repo/agent-swarm/issues)
- **GitHub Discussions**: [Ask questions and share ideas](https://github.com/your-repo/agent-swarm/discussions)
- **Documentation**: [Complete guides and references](README.md)

### **Debug Information to Include**
When reporting issues, please include:

```bash
# System information
python --version
pip list | grep -E "(agent-swarm|rich|ollama)"

# Environment variables
echo "AGENT_SWARM_MODEL: $AGENT_SWARM_MODEL"
echo "OPENAI_API_KEY: ${OPENAI_API_KEY:+SET}"
echo "ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY:+SET}"

# Error logs
python your_script.py 2>&1 | tee error.log
```

## ✅ Quick Health Check

Run this script to check your Agent Swarm setup:

```python
#!/usr/bin/env python3
"""Agent Swarm Health Check"""

import asyncio
import sys
from pathlib import Path

async def health_check():
    print("🔍 Agent Swarm Health Check")
    print("=" * 40)
    
    # Check imports
    try:
        from agent_swarm import LLMRouter, create_coding_agent
        print("✅ Core imports working")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return
    
    # Check LLM router
    try:
        router = LLMRouter()
        print(f"✅ LLM Router: {len(router.llms)} backends available")
    except Exception as e:
        print(f"❌ LLM Router error: {e}")
    
    # Check context engine
    try:
        from agent_swarm.context import UnifiedContext
        context = UnifiedContext(".")
        await context.initialize(auto_index=False)
        print("✅ Context engine working")
    except Exception as e:
        print(f"❌ Context engine error: {e}")
    
    # Check MCP tools
    try:
        from agent_swarm.mcp import setup_default_mcp_tools
        registry = await setup_default_mcp_tools()
        tools = registry.get_all_tools()
        print(f"✅ MCP tools: {len(tools)} available")
    except Exception as e:
        print(f"❌ MCP tools error: {e}")
    
    print("\n🎉 Health check complete!")

if __name__ == "__main__":
    asyncio.run(health_check())
```

Save as `health_check.py` and run: `python health_check.py`

---

**Still having issues?** [Open a GitHub issue](https://github.com/your-repo/agent-swarm/issues) with your error details!
