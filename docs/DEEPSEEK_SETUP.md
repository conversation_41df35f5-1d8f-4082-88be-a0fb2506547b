# 🤖 DeepSeek Setup Guide for Agent Swarm

**Version:** 0.2.0  
**Last Updated:** January 6, 2025  
**Status:** Production Ready

---

## 🎯 **Quick Start**

DeepSeek offers excellent coding capabilities at competitive prices, making it perfect for Agent Swarm's coding tasks.

### **1. Get Your API Key**
1. Visit [DeepSeek Platform](https://platform.deepseek.com/)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy your API key (starts with `sk-`)

### **2. Set Environment Variable**
```bash
# Add to your shell profile (.bashrc, .zshrc, etc.)
export DEEPSEEK_API_KEY="sk-your_actual_api_key_here"

# Or set for current session
export DEEPSEEK_API_KEY="sk-your_actual_api_key_here"
```

### **3. Configure Agent Swarm**
```bash
# Start Agent Swarm
agent-swarm

# Run configuration wizard
/config wizard

# Or manually set DeepSeek as default
/config set default_provider deepseek
/config set default_model deepseek-chat
```

### **4. Test Your Setup**
```bash
# In Agent Swarm shell
How can I optimize this Python function for better performance?

# You should see responses from DeepSeek
```

---

## 🔧 **Detailed Configuration**

### **Available DeepSeek Models**

Agent Swarm comes pre-configured with DeepSeek models:

#### **deepseek-chat** (Recommended)
- **Use Case:** General coding, architecture, documentation
- **Strengths:** Balanced performance, good reasoning
- **Temperature:** 0.3 (default)
- **Max Tokens:** 4096

#### **deepseek-coder** (Specialized)
- **Use Case:** Code generation, debugging, optimization
- **Strengths:** Specialized for coding tasks
- **Temperature:** 0.2 (more focused)
- **Max Tokens:** 4096

### **Configuration Options**

#### **Via Interactive Shell**
```bash
# Show current configuration
/config

# Set DeepSeek as default provider
/config set default_provider deepseek

# Set specific model
/config set default_model deepseek-chat

# Adjust temperature
/config set default_temperature 0.2

# Adjust max tokens
/config set default_max_tokens 8192
```

#### **Via Environment Variables**
```bash
# Provider settings
export AGENT_SWARM_DEFAULT_PROVIDER=deepseek
export AGENT_SWARM_DEFAULT_MODEL=deepseek-chat

# Model parameters
export AGENT_SWARM_DEFAULT_TEMPERATURE=0.3
export AGENT_SWARM_DEFAULT_MAX_TOKENS=4096

# API configuration
export DEEPSEEK_API_KEY=sk-your_key_here
```

#### **Via Configuration File**
```yaml
# ~/.agent-swarm/config.yaml
version: "0.2.0"
default_provider: deepseek
default_model: deepseek-chat
default_temperature: 0.3
default_max_tokens: 4096

models:
  deepseek-chat:
    name: deepseek-chat
    provider: deepseek
    api_key_env: DEEPSEEK_API_KEY
    base_url: https://api.deepseek.com/v1
    tier: cloud_premium
    max_tokens: 4096
    temperature: 0.3
    enabled: true
```

---

## 💡 **Usage Examples**

### **Coding Tasks**
```bash
# Code generation
"Create a Python function to parse JSON with error handling"

# Code review
"Review this function for potential bugs and improvements"

# Debugging
"Why is this code throwing a TypeError?"

# Optimization
"How can I make this algorithm more efficient?"
```

### **Architecture & Design**
```bash
# System design
"Design a microservices architecture for an e-commerce platform"

# Code organization
"How should I structure this Python project?"

# Best practices
"What are the SOLID principles and how do I apply them?"
```

### **Documentation**
```bash
# Generate documentation
"Create comprehensive docstrings for this class"

# Explain code
"Explain what this complex algorithm does"

# Write README
"Create a README for this Python package"
```

---

## 🚀 **Advanced Configuration**

### **Task-Specific Routing**
Configure different models for different types of tasks:

```bash
# Set up task routing
/config set task_routing.coding deepseek-coder
/config set task_routing.architecture deepseek-chat
/config set task_routing.documentation deepseek-chat
/config set task_routing.code_review deepseek-coder
```

### **Performance Tuning**
```bash
# For more creative responses
/config set default_temperature 0.5

# For more focused/deterministic responses
/config set default_temperature 0.1

# For longer responses
/config set default_max_tokens 8192

# For faster responses
/config set default_max_tokens 2048
```

### **Cost Optimization**
```bash
# Use local models for simple tasks
/config set task_routing.simple_coding qwen-coder

# Use DeepSeek for complex tasks only
/config set task_routing.complex_coding deepseek-chat
/config set task_routing.architecture deepseek-chat
```

---

## 🔍 **Troubleshooting**

### **Common Issues**

#### **API Key Not Working**
```bash
# Check if API key is set
echo $DEEPSEEK_API_KEY

# Validate API key format
/config wizard

# Test API key manually
curl -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
     https://api.deepseek.com/v1/models
```

#### **Connection Issues**
```bash
# Check internet connection
ping api.deepseek.com

# Check firewall/proxy settings
curl -v https://api.deepseek.com/v1/models

# Increase timeout
/config set request_timeout 60
```

#### **Rate Limiting**
```bash
# Reduce concurrent requests
/config set max_concurrent_requests 1

# Add delays between requests
# (handled automatically by Agent Swarm)
```

### **Error Messages**

#### **"Invalid API Key"**
- Verify your API key is correct
- Check if key has expired
- Ensure key has proper permissions

#### **"Rate Limit Exceeded"**
- Wait a few minutes before retrying
- Reduce request frequency
- Consider upgrading your DeepSeek plan

#### **"Model Not Found"**
- Check model name spelling
- Verify model is available in your region
- Use `/config` to see available models

---

## 📊 **Monitoring & Analytics**

### **Usage Tracking**
```bash
# View configuration
/config

# Check model status
/config | grep deepseek

# Monitor API key status
/config wizard
```

### **Performance Metrics**
- Response time: Typically 1-3 seconds
- Token usage: Displayed in verbose mode
- Error rates: Logged in debug mode

---

## 🔄 **Migration & Backup**

### **Backup Configuration**
```bash
# Export current configuration
/config export

# Save to file
cp ~/.agent-swarm/config.yaml ~/.agent-swarm/config.backup.yaml
```

### **Restore Configuration**
```bash
# Restore from backup
cp ~/.agent-swarm/config.backup.yaml ~/.agent-swarm/config.yaml

# Reset to defaults
/config reset
```

---

## 🎉 **You're Ready!**

Your DeepSeek integration is now configured and ready to use. Start asking coding questions and experience the power of AI-assisted development!

### **Next Steps**
1. **Try the interactive shell:** `agent-swarm --auto-index`
2. **Ask coding questions:** Get context-aware responses
3. **Explore features:** Use `/help` to see all commands
4. **Optimize settings:** Adjust temperature and tokens for your needs

### **Support**
- **Documentation:** https://agent-swarm.readthedocs.io
- **Issues:** https://github.com/agent-swarm/agent-swarm/issues
- **DeepSeek Docs:** https://platform.deepseek.com/docs
