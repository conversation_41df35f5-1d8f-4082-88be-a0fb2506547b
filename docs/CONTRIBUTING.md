# 🤝 Contributing to <PERSON> Swarm

Thank you for your interest in contributing to Agent Swarm! This guide will help you get started with development and contributions.

## 🚀 Quick Start for Contributors

### **1. Development Setup (5 minutes)**
```bash
# Fork and clone the repository
git clone https://github.com/your-username/agent-swarm.git
cd agent-swarm

# Set up development environment
make install-dev

# Run tests to verify setup
make test
```

### **2. Make Your Changes**
```bash
# Create a feature branch
git checkout -b feature/your-feature-name

# Make your changes
# ... edit code ...

# Test your changes
make test lint type-check
```

### **3. Submit Your Contribution**
```bash
# Commit your changes
git add .
git commit -m "feat: add your feature description"

# Push and create pull request
git push origin feature/your-feature-name
# Then create PR on GitHub
```

## 📋 Development Guidelines

### **Code Standards**

#### **Python Style**
- Follow PEP 8 style guidelines
- Use type hints for all functions
- Write docstrings for all public methods
- Maximum line length: 88 characters (Black default)

```python
async def example_function(param: str, optional: int = 10) -> Dict[str, Any]:
    """
    Example function with proper typing and documentation.
    
    Args:
        param: Description of the parameter
        optional: Optional parameter with default value
        
    Returns:
        Dictionary containing the results
        
    Raises:
        ValueError: When param is invalid
    """
    if not param:
        raise ValueError("param cannot be empty")
    
    return {"result": param, "count": optional}
```

#### **Code Quality Tools**
```bash
# Format code
make format          # Runs black + isort

# Check code quality
make lint           # Runs flake8

# Type checking
make type-check     # Runs mypy

# Run all checks
make format lint type-check
```

### **Testing Requirements**

#### **Test Coverage**
- All new features must have tests
- Aim for >90% test coverage
- Include both unit and integration tests

```python
import pytest
from agent_swarm import LLMRouter

class TestLLMRouter:
    @pytest.fixture
    async def router(self):
        """Create a test router."""
        router = LLMRouter()
        yield router
        # Cleanup if needed
    
    async def test_register_llm(self, router):
        """Test LLM registration."""
        # Test implementation
        assert len(router.llms) == 0
        
        # Add mock LLM
        mock_llm = MockLLM()
        router.register_llm("test", mock_llm)
        
        assert len(router.llms) == 1
        assert "test" in router.llms
```

#### **Running Tests**
```bash
# Run all tests
make test

# Run specific test categories
make test-unit           # Unit tests only
make test-integration    # Integration tests only

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test file
pytest tests/test_llm_router.py -v
```

### **Documentation Requirements**

#### **Code Documentation**
- All public APIs must have docstrings
- Include examples in docstrings
- Update API reference for new features

```python
class NewFeature:
    """
    Brief description of the feature.
    
    This class provides functionality for...
    
    Example:
        >>> feature = NewFeature()
        >>> result = feature.process("input")
        >>> print(result)
        "processed: input"
    
    Attributes:
        config: Configuration for the feature
        state: Current state of the feature
    """
    
    def process(self, input_data: str) -> str:
        """
        Process input data and return result.
        
        Args:
            input_data: The data to process
            
        Returns:
            Processed result as string
            
        Raises:
            ValueError: If input_data is invalid
            
        Example:
            >>> feature = NewFeature()
            >>> result = feature.process("hello")
            >>> print(result)
            "processed: hello"
        """
        return f"processed: {input_data}"
```

#### **Documentation Updates**
- Update relevant guides for new features
- Add examples to demonstrate usage
- Update API reference documentation

## 🎯 Contribution Types

### **Bug Fixes**
1. **Identify the bug**: Create or find existing issue
2. **Write a test**: Reproduce the bug with a failing test
3. **Fix the bug**: Implement the fix
4. **Verify the fix**: Ensure test passes and no regressions

### **New Features**
1. **Discuss the feature**: Open an issue to discuss the feature
2. **Design the API**: Plan the public interface
3. **Implement the feature**: Write the code with tests
4. **Document the feature**: Update docs and examples

### **Documentation Improvements**
1. **Identify gaps**: Find missing or unclear documentation
2. **Improve clarity**: Rewrite for better understanding
3. **Add examples**: Include practical usage examples
4. **Test examples**: Ensure all code examples work

### **Performance Improvements**
1. **Benchmark current performance**: Establish baseline
2. **Implement optimization**: Make the improvement
3. **Measure improvement**: Verify performance gains
4. **Document changes**: Update relevant documentation

## 🏗️ Project Structure

### **Core Components**
```
src/agent_swarm/
├── __init__.py              # Main exports
├── agents/                  # Agent implementations
├── backends/                # LLM backend integrations
├── cli/                     # Command-line interface
├── context/                 # Context and RAG engine
├── mcp/                     # MCP tool integration
├── utils/                   # Utility functions
└── exceptions.py            # Custom exceptions
```

### **Testing Structure**
```
tests/
├── unit/                    # Unit tests
├── integration/             # Integration tests
├── fixtures/                # Test fixtures
└── conftest.py             # Pytest configuration
```

### **Documentation Structure**
```
docs/
├── README.md               # Documentation index
├── QUICK_START.md          # Getting started guide
├── API_REFERENCE.md        # Complete API docs
├── guides/                 # Feature guides
└── examples/               # Code examples
```

## 🔄 Development Workflow

### **Branch Naming**
- `feature/description` - New features
- `fix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

### **Commit Messages**
Follow [Conventional Commits](https://www.conventionalcommits.org/):

```bash
# Feature
git commit -m "feat: add multi-LLM routing support"

# Bug fix
git commit -m "fix: resolve context engine memory leak"

# Documentation
git commit -m "docs: update API reference for new features"

# Breaking change
git commit -m "feat!: redesign agent interface"
```

### **Pull Request Process**
1. **Create descriptive PR title**: Use conventional commit format
2. **Fill out PR template**: Describe changes and testing
3. **Link related issues**: Reference issue numbers
4. **Request review**: Tag relevant maintainers
5. **Address feedback**: Make requested changes
6. **Merge when approved**: Squash and merge

## 🧪 Testing Guidelines

### **Test Categories**

#### **Unit Tests**
- Test individual functions/classes in isolation
- Use mocks for external dependencies
- Fast execution (< 1 second per test)

```python
@pytest.mark.unit
async def test_llm_router_registration():
    """Test LLM registration in isolation."""
    router = LLMRouter()
    mock_llm = Mock()
    
    router.register_llm("test", mock_llm)
    
    assert "test" in router.llms
    assert router.llms["test"] == mock_llm
```

#### **Integration Tests**
- Test component interactions
- Use real dependencies when possible
- May require external services (mark appropriately)

```python
@pytest.mark.integration
@pytest.mark.requires_ollama
async def test_ollama_integration():
    """Test real Ollama integration."""
    from agent_swarm.backends.ollama import create_ollama_llm
    
    llm = create_ollama_llm("llama3.2:3b")
    await llm.initialize()
    
    if await llm.is_available():
        response = await llm.generate("Hello")
        assert response.content
```

### **Test Markers**
```python
# Mark tests that require external services
@pytest.mark.requires_ollama
@pytest.mark.requires_api_keys
@pytest.mark.slow

# Mark test categories
@pytest.mark.unit
@pytest.mark.integration
```

## 📦 Release Process

### **Version Numbering**
Follow [Semantic Versioning](https://semver.org/):
- `MAJOR.MINOR.PATCH`
- `MAJOR`: Breaking changes
- `MINOR`: New features (backward compatible)
- `PATCH`: Bug fixes (backward compatible)

### **Release Checklist**
1. **Update version**: Bump version in `pyproject.toml`
2. **Update changelog**: Document all changes
3. **Run full test suite**: Ensure all tests pass
4. **Update documentation**: Reflect any changes
5. **Create release**: Tag and publish

## 🎉 Recognition

### **Contributors**
All contributors are recognized in:
- `CONTRIBUTORS.md` file
- Release notes
- GitHub contributors page

### **Types of Contributions**
- Code contributions
- Documentation improvements
- Bug reports and testing
- Feature suggestions
- Community support

## 📞 Getting Help

### **Development Questions**
- **GitHub Discussions**: [Ask development questions](https://github.com/your-repo/agent-swarm/discussions)
- **Discord**: Join our development channel
- **Code Review**: Request feedback on draft PRs

### **Maintainer Contact**
- **Issues**: Use GitHub issues for bugs and features
- **Security**: Email <EMAIL> for security issues
- **General**: Use GitHub discussions for general questions

## 📄 License

By contributing to Agent Swarm, you agree that your contributions will be licensed under the MIT License.

---

**Ready to contribute?** Start with a [good first issue](https://github.com/your-repo/agent-swarm/labels/good%20first%20issue)! 🚀
