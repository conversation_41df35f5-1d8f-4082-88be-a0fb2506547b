# 🚀 Quick Start Guide

Get Agent Swarm running in 5 minutes and experience the future of AI-powered development!

## ⚡ 5-Minute Setup

### **Step 1: Install (1 minute)**
```bash
# Clone and enter directory
git clone <your-repo-url>
cd agent-swarm

# Quick setup with all features
make setup
```

### **Step 2: Try Your First Demo (2 minutes)**
```bash
# Run the quick start demo
make demo

# This will:
# ✅ Set up local LLMs (if available)
# ✅ Show multi-LLM routing
# ✅ Demonstrate cost optimization
# ✅ Guide you to next steps
```

### **Step 3: Explore Examples (2 minutes)**
```bash
# Try different capabilities
make demo-coding     # See coding agent workflows
make demo-shell      # Experience interactive development
make demo-context    # Explore RAG and context engine
make demo-mcp        # See tool integration
```

**🎉 Congratulations! You're now running Agent Swarm!**

## 🎯 What You Just Experienced

### **Multi-LLM Intelligence**
- **Smart Routing**: Tasks automatically routed to optimal models
- **Cost Optimization**: Free local models for simple tasks, premium for complex
- **Fallback Chains**: Automatic fallbacks if preferred models unavailable

### **Context-Aware Development**
- **Project Understanding**: AI that knows your codebase
- **Intelligent Search**: Find code patterns across large projects
- **Related Code Discovery**: Automatic detection of related files

### **Tool Integration**
- **File Operations**: Read, write, and manipulate files
- **Web Search**: Access internet for research and examples
- **Code Execution**: Safe execution of code snippets
- **System Integration**: Network, process, and system tools

## 🚀 Next Steps (Choose Your Adventure)

### **🤖 I Want to Build Coding Agents**
```bash
# See advanced coding workflows
make demo-coding

# Then read:
# - API Reference: docs/API_REFERENCE.md
# - Custom Agents: docs/guides/CUSTOM_AGENTS.md
```

### **🐚 I Want Interactive Development**
```bash
# Try the interactive shell
make demo-shell

# Then read:
# - Interactive Shell Guide: docs/guides/INTERACTIVE_SHELL.md
```

### **🧠 I Want Smart Code Search**
```bash
# Explore RAG capabilities
make demo-context

# Then read:
# - RAG Development Guide: docs/guides/RAG_DEVELOPMENT.md
```

### **🔗 I Want Tool Integration**
```bash
# See MCP tool integration
make demo-mcp

# Then read:
# - MCP Integration Guide: docs/guides/MCP_INTEGRATION.md
```

### **🏗️ I Want Production Deployment**
```bash
# Read deployment strategies
# - Deployment Guide: docs/DEPLOYMENT.md
```

## 💡 Quick Usage Examples

### **Basic Agent Creation**
```python
from agent_swarm import create_coding_agent

# Create a coding agent with RAG
agent = await create_coding_agent(
    name="DevBot",
    project_path="./my_project",
    enable_rag=True
)

# Implement a feature
result = await agent.implement_feature(
    "Create a user authentication module",
    "src/auth.py",
    test_required=True
)
```

### **Multi-LLM Routing**
```python
from agent_swarm import LLMRouter, LLMTier

router = LLMRouter()

# Simple task → Fast local model
response = await router.route_request(
    messages=[{"role": "user", "content": "Explain Python lists"}],
    preferred_tier=LLMTier.LOCAL_FAST
)

# Complex task → Premium cloud model
response = await router.route_request(
    messages=[{"role": "user", "content": "Design a microservices architecture"}],
    preferred_tier=LLMTier.CLOUD_PREMIUM
)
```

### **Interactive Shell Usage**
```bash
# Start interactive shell
make demo-shell

# Then in the shell:
> How do I implement async error handling?
/search "async def"
/implement a rate limiter utility
/review src/utils.py
```

### **RAG Code Search**
```python
from agent_swarm.context import setup_project_rag

# Index your project
rag = await setup_project_rag("./my_project")

# Search for patterns
results = await rag.search_code("authentication login")
related = await rag.get_related_files("src/auth.py")
```

## 🛠️ Customization Options

### **Configure Models**
```bash
# Use specific local models
export AGENT_SWARM_MODEL="qwen2.5-coder:7b"

# Add cloud API keys
export OPENAI_API_KEY="your-key"
export ANTHROPIC_API_KEY="your-key"
```

### **Project-Specific Setup**
```bash
# Run shell with your project
make demo-shell --project /path/to/your/project

# Or directly
python examples/interactive_shell.py --project /path/to/your/project
```

### **Development Mode**
```bash
# Install development dependencies
make install-dev

# Run tests
make test

# Code quality checks
make format lint type-check
```

## 🔧 Troubleshooting

### **"No LLMs available"**
```bash
# Install Ollama (recommended)
curl -fsSL https://ollama.ai/install.sh | sh
ollama pull llama3.2:3b

# Or use cloud APIs
export OPENAI_API_KEY="your-key"
```

### **"Import errors"**
```bash
# Ensure proper installation
pip install -e .

# Or with all features
make setup
```

### **"Examples not working"**
```bash
# Check setup
make test

# Verbose mode for debugging
python examples/quick_start.py --verbose
```

### **Need More Help?**
- **[Troubleshooting Guide](TROUBLESHOOTING.md)** - Comprehensive problem solving
- **[API Reference](API_REFERENCE.md)** - Complete documentation
- **[GitHub Issues](https://github.com/your-repo/agent-swarm/issues)** - Community support

## 🎉 You're Ready!

You now have Agent Swarm running and understand the basics. Here's what to explore next:

1. **Try all examples**: `make demo-*` commands
2. **Read the guides**: Dive deep into specific features
3. **Build something**: Create your first custom agent
4. **Join the community**: Share your experience and get help

**Welcome to the future of AI-powered development!** 🚀

---

**Next:** [Explore Examples](../examples/README.md) | [API Reference](API_REFERENCE.md) | [Interactive Shell](guides/INTERACTIVE_SHELL.md)
