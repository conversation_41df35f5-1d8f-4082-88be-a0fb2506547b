# 📝 Advanced File Editing System

**Version:** 0.2.0  
**Last Updated:** January 6, 2025  
**Status:** Production Ready

---

## 🎯 **Overview**

Agent Swarm's advanced file editing system provides sophisticated tools for code modification with:

- **📊 Rich diff visualization** with syntax highlighting
- **🔄 Change tracking and history** with unique IDs
- **🤖 AI-powered smart editing** with natural language instructions
- **⏪ Revert functionality** for safe experimentation
- **📋 Session management** for organizing related changes
- **✅ Interactive confirmation** before applying changes

---

## 🚀 **Quick Start**

### **Basic File Editing**
```bash
# Start Agent Swarm
agent-swarm

# Edit a file with diff preview
/edit src/main.py

# AI-powered editing
/smart-edit src/main.py "Add error handling to the main function"

# Show file with line numbers
/diff src/main.py

# List all changes in current session
/edit list
```

---

## 📚 **Command Reference**

### **Core Editing Commands**

#### **`/edit <file> [options]`**
Advanced file editing with diff preview and change tracking.

**Options:**
- `--auto` - Apply changes without confirmation
- `--no-diff` - Skip diff preview
- `--description "msg"` - Add description to change

**Examples:**
```bash
/edit config.yaml
/edit src/utils.py --auto --description "Fix bug in validation"
/edit README.md --no-diff
```

#### **`/smart-edit <file> <instruction>`**
AI-powered file editing using natural language instructions.

**Examples:**
```bash
/smart-edit src/api.py "Add input validation to all endpoints"
/smart-edit tests/test_main.py "Add test cases for edge cases"
/smart-edit docs/README.md "Update installation instructions"
```

#### **`/diff <file|change_id>`**
Show file differences or specific change diffs.

**Examples:**
```bash
/diff src/main.py              # Show file with line numbers
/diff a1b2c3d4                 # Show diff for specific change
/diff file1.py file2.py        # Compare two files (coming soon)
```

#### **`/revert <change_id>`**
Revert a specific change by its ID.

**Examples:**
```bash
/revert a1b2c3d4               # Revert specific change
```

### **Session Management**

#### **`/edit session <name>`**
Start or switch to an edit session.

**Examples:**
```bash
/edit session feature-auth     # Start session for auth feature
/edit session bugfix-123       # Start session for bug fix
/edit session                  # Show current session
```

#### **`/edit list`**
List all changes in the current session.

#### **`/edit show <file> [--lines start:end]`**
Show file content with optional line highlighting.

**Examples:**
```bash
/edit show src/main.py
/edit show config.py --lines 10:20
```

#### **`/edit apply <change_id>`**
Apply a specific change that hasn't been applied yet.

#### **`/edit interactive <file>`**
Start interactive editing mode with menu-driven interface.

---

## 🛠️ **Advanced Features**

### **Change Tracking**

Every edit creates a `FileChange` object with:
- **Unique 8-character ID** (e.g., `a1b2c3d4`)
- **Original and new content** for complete history
- **Timestamp** of when change was made
- **Description** for documentation
- **Unified diff** for visualization

### **Session Management**

Edit sessions organize related changes:
- **Automatic backups** before any modifications
- **Persistent storage** in `~/.agent-swarm/edit-sessions/`
- **Session switching** for different features/bugs
- **Bulk operations** on session changes

### **Rich Diff Visualization**

Powered by Rich library for beautiful terminal output:
- **Syntax highlighting** for different file types
- **Color-coded diffs** (green for additions, red for deletions)
- **Line numbers** and context
- **File metadata** display

### **AI Integration**

Smart editing leverages your configured AI provider:
- **Natural language instructions** converted to code changes
- **Context-aware modifications** that preserve functionality
- **Code style consistency** maintained
- **Automatic documentation** generation

---

## 💡 **Usage Patterns**

### **Feature Development Workflow**
```bash
# Start a feature session
/edit session feature-user-auth

# Make multiple related changes
/smart-edit src/auth.py "Add JWT token validation"
/smart-edit src/models.py "Add User model with authentication fields"
/smart-edit tests/test_auth.py "Add comprehensive auth tests"

# Review all changes
/edit list

# Apply or revert individual changes as needed
/edit apply a1b2c3d4
/revert b2c3d4e5
```

### **Bug Fix Workflow**
```bash
# Start bug fix session
/edit session bugfix-validation-error

# Identify and fix the issue
/diff src/validator.py
/smart-edit src/validator.py "Fix null pointer exception in email validation"

# Test the fix
/smart-edit tests/test_validator.py "Add test case for null email input"

# Review and apply
/edit list
/edit apply c3d4e5f6
```

### **Code Review and Refactoring**
```bash
# Review existing code
/diff src/legacy_module.py

# Refactor with AI assistance
/smart-edit src/legacy_module.py "Refactor to use modern Python patterns and add type hints"

# Compare before and after
/diff d4e5f6g7

# Apply if satisfied
/edit apply d4e5f6g7
```

---

## 🔧 **Configuration**

### **File Editor Settings**

The file editor can be configured through Agent Swarm's configuration system:

```yaml
# ~/.agent-swarm/config.yaml
file_editor:
  auto_backup: true
  diff_context_lines: 3
  syntax_highlighting: true
  confirm_before_apply: true
  session_storage_path: "~/.agent-swarm/edit-sessions"
```

### **AI Provider Integration**

Smart editing uses your configured AI provider:
```bash
# Configure for DeepSeek (recommended for coding)
/config set default_provider deepseek
/config set default_model deepseek-coder

# Or use other providers
/config set default_provider openai
/config set default_model gpt-4
```

---

## 🚨 **Safety Features**

### **Automatic Backups**
- **Every file is backed up** before modification
- **Timestamped backups** in session directory
- **Easy restoration** from backups

### **Change Tracking**
- **Complete history** of all modifications
- **Reversible changes** with unique IDs
- **Session isolation** prevents conflicts

### **Confirmation Prompts**
- **Interactive confirmation** before applying changes
- **Diff preview** shows exactly what will change
- **Override with `--auto`** for trusted operations

---

## 🎨 **Examples**

### **Basic File Editing**
```bash
# Edit a Python file
/edit src/calculator.py

# The system will:
# 1. Show current file content with line numbers
# 2. Prompt for new content
# 3. Display a rich diff
# 4. Ask for confirmation
# 5. Apply changes and create backup
```

### **AI-Powered Refactoring**
```bash
# Refactor with natural language
/smart-edit src/data_processor.py "Convert this class to use async/await pattern and add proper error handling"

# The system will:
# 1. Send current code + instruction to AI
# 2. Generate improved code
# 3. Show diff with improvements
# 4. Allow confirmation before applying
```

### **Session-Based Development**
```bash
# Start working on a feature
/edit session api-v2-endpoints

# Make several related changes
/smart-edit src/api/v2/users.py "Add CRUD endpoints for user management"
/smart-edit src/api/v2/auth.py "Add OAuth2 authentication"
/smart-edit tests/api/v2/test_users.py "Add comprehensive test coverage"

# Review all changes
/edit list
# Output:
# 📋 Edit Session: api-v2-endpoints
# ┌──────────┬─────────────────────┬─────────────────────────┬──────────┐
# │ ID       │ File                │ Description             │ Time     │
# ├──────────┼─────────────────────┼─────────────────────────┼──────────┤
# │ a1b2c3d4 │ src/api/v2/users.py │ Add CRUD endpoints...   │ 14:30:15 │
# │ b2c3d4e5 │ src/api/v2/auth.py  │ Add OAuth2 auth...      │ 14:32:22 │
# │ c3d4e5f6 │ tests/.../test_...  │ Add comprehensive...    │ 14:35:10 │
# └──────────┴─────────────────────┴─────────────────────────┴──────────┘

# Apply specific changes
/edit apply a1b2c3d4
/edit apply b2c3d4e5
```

---

## 🔮 **Future Enhancements**

### **Planned Features**
- **Multi-file diffs** for comparing related files
- **Merge conflict resolution** with AI assistance
- **Code formatting integration** with Black, Prettier, etc.
- **Git integration** for automatic commits
- **Collaborative editing** with shared sessions
- **Template-based editing** for common patterns

### **AI Improvements**
- **Context-aware suggestions** based on project structure
- **Automatic test generation** for modified code
- **Performance optimization** suggestions
- **Security vulnerability** detection and fixes

---

## 🎉 **Get Started**

The advanced file editing system is ready to use! Try these commands:

```bash
# Start Agent Swarm
agent-swarm

# Try basic editing
/edit --help

# Try AI-powered editing
/smart-edit README.md "Make this documentation more engaging and add emojis"

# Explore session management
/edit session my-first-session
/edit list
```

**Happy coding with intelligent file editing!** 🚀✨
