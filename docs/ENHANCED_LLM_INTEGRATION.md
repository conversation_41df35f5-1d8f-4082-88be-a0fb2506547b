# Enhanced LLM Backend with Thinking Capabilities

## 🚀 Revolutionary AI Model Interaction

The Enhanced LLM Backend represents the next evolution in AI model interaction, combining:

- **🧠 Thinking Capabilities**: Latest Ollama features with adaptive thinking
- **🔺 Mathematical Intent Processing**: Integration with our revolutionary algorithms
- **⚡ Intelligent Model Selection**: Optimal model choice based on requirements
- **📊 Real-time Analytics**: Performance monitoring and optimization
- **🌊 Advanced Streaming**: Streaming responses with thinking process
- **📋 Structured Outputs**: JSON schema validation and tool calling

## 🎯 Key Features

### 1. Adaptive Thinking Based on Complexity

The system automatically enables or disables thinking based on mathematical analysis of request complexity:

```python
from agent_swarm.backends.enhanced_llm_backend import (
    EnhancedLLMBackend, EnhancedLLMRequest, ThinkingConfig, ThinkingMode
)
from agent_swarm.backends.abstract import Message

# Initialize backend
backend = EnhancedLLMBackend()
await backend.initialize()

# Adaptive thinking - automatically decides based on complexity
request = EnhancedLLMRequest(
    messages=[Message(role="user", content="Design a distributed system architecture")],
    model_id="deepseek-r1:8b",
    thinking_config=ThinkingConfig(mode=ThinkingMode.ADAPTIVE),
)

response = await backend.generate(request)
print(f"Thinking enabled: {response.thinking_enabled}")
print(f"Complexity score: {response.complexity_score}")
```

### 2. Latest Ollama Features Integration

#### Thinking Capabilities
```python
# Enable thinking for complex reasoning
thinking_config = ThinkingConfig(
    mode=ThinkingMode.ENABLED,
    hide_thinking=False,  # Show thinking process
    complexity_threshold=0.7,
)

request = EnhancedLLMRequest(
    messages=[Message(role="user", content="Solve this complex math problem...")],
    model_id="deepseek-r1:8b",
    thinking_config=thinking_config,
)

response = await backend.generate(request)
if response.thinking:
    print("Thinking process:", response.thinking)
    print("Thinking time:", response.thinking_time)
```

#### Structured Outputs
```python
# Define JSON schema for structured output
schema = {
    "type": "object",
    "properties": {
        "name": {"type": "string"},
        "age": {"type": "integer"},
        "skills": {"type": "array", "items": {"type": "string"}}
    },
    "required": ["name", "age"]
}

request = EnhancedLLMRequest(
    messages=[Message(role="user", content="Create a developer profile")],
    model_id="deepseek-r1:8b",
    response_schema=schema,
)

response = await backend.generate(request)
if response.structured_data:
    print("Structured data:", response.structured_data)
```

#### Tool Calling
```python
# Define tools for the model to use
tools = [
    {
        "name": "calculate",
        "description": "Perform mathematical calculations",
        "parameters": {
            "type": "object",
            "properties": {
                "expression": {"type": "string"}
            }
        }
    }
]

request = EnhancedLLMRequest(
    messages=[Message(role="user", content="Calculate 15 * 23 + 45")],
    model_id="deepseek-r1:8b",
    tools=tools,
)

response = await backend.generate(request)
if response.tool_calls:
    print("Tool calls:", response.tool_calls)
```

### 3. Intelligent Model Selection

The backend automatically selects the optimal model based on requirements:

```python
# Priority-based selection
scenarios = [
    {
        'priority': 'fast',      # Selects fast models, disables thinking
        'content': 'Quick question',
    },
    {
        'priority': 'quality',   # Selects thinking-capable models
        'content': 'Complex analysis needed',
    },
    {
        'priority': 'normal',    # Adaptive based on complexity
        'content': 'Regular request',
    }
]

for scenario in scenarios:
    request = EnhancedLLMRequest(
        messages=[Message(role="user", content=scenario['content'])],
        model_id="auto",  # Let backend choose
        priority=scenario['priority'],
        thinking_config=ThinkingConfig(mode=ThinkingMode.ADAPTIVE),
    )
    
    response = await backend.generate(request)
    print(f"Selected model: {response.model}")
    print(f"Thinking enabled: {response.thinking_enabled}")
```

### 4. Streaming with Thinking

Stream responses while showing the thinking process:

```python
request = EnhancedLLMRequest(
    messages=[Message(role="user", content="Explain quantum computing")],
    model_id="deepseek-r1:8b",
    thinking_config=ThinkingConfig(mode=ThinkingMode.ENABLED),
    stream=True,
)

print("Thinking process:")
async for chunk in backend.stream_generate(request):
    if chunk.thinking:
        print(f"🧠 {chunk.thinking}", end="", flush=True)
    if chunk.content:
        print(f"💬 {chunk.content}", end="", flush=True)
```

### 5. Performance Analytics

Monitor and optimize performance:

```python
# Get comprehensive analytics
stats = backend.get_performance_stats()

print(f"Total requests: {stats['total_requests']}")
print(f"Thinking usage rate: {stats.get('thinking_usage_rate', 0):.1%}")
print(f"Average response time: {stats['average_response_time']:.2f}s")
print(f"Time saved by adaptive thinking: {stats.get('average_thinking_time_saved', 0):.2f}s")

# Get available models and capabilities
models = backend.get_available_models()
for model_id, info in models.items():
    capabilities = []
    if info['thinking_supported']: capabilities.append("🧠 Thinking")
    if info['structured_output_supported']: capabilities.append("📋 Structured")
    if info['tool_calling_supported']: capabilities.append("🔧 Tools")
    if info['streaming_supported']: capabilities.append("🌊 Streaming")
    
    print(f"{model_id}: {' '.join(capabilities)}")
```

## 🔧 Configuration

### Thinking Modes

```python
from agent_swarm.backends.enhanced_llm_backend import ThinkingMode

# Available thinking modes
ThinkingMode.DISABLED      # Never use thinking (fastest)
ThinkingMode.ENABLED       # Always use thinking (highest quality)
ThinkingMode.ADAPTIVE      # Use thinking based on complexity (balanced)
ThinkingMode.COMPLEX_ONLY  # Only for complex requests
```

### Model Capabilities

The backend automatically detects and uses model capabilities:

- **🧠 Thinking**: DeepSeek-R1, Qwen3 models
- **📋 Structured Output**: Most modern models
- **🔧 Tool Calling**: Selected models with tool support
- **🌊 Streaming**: All supported models

### Priority Levels

```python
# Request priorities affect model selection and thinking
'fast'    # Prioritize speed over quality
'normal'  # Balanced approach with adaptive thinking
'quality' # Prioritize quality, enable thinking
```

## 🧠 Mathematical Integration

The Enhanced LLM Backend integrates with our mathematical intent processing algorithms:

### Intent Complexity Analysis

```python
# The backend automatically analyzes intent complexity
request = EnhancedLLMRequest(
    messages=[Message(role="user", content="Your request here")],
    model_id="deepseek-r1:8b",
    thinking_config=ThinkingConfig(mode=ThinkingMode.ADAPTIVE),
)

response = await backend.generate(request)

# Access mathematical analysis
if response.metadata and 'intent_analysis' in response.metadata:
    analysis = response.metadata['intent_analysis']
    print(f"Flow state: {analysis['flow_state']}")
    print(f"Complexity score: {analysis['complexity_score']}")
    
    if analysis.get('intent_coordinates'):
        coords = analysis['intent_coordinates']
        print(f"3D coordinates: ({coords['clarity']:.2f}, {coords['complexity']:.2f}, {coords['context']:.2f})")
```

### Adaptive Thinking Logic

The system uses mathematical algorithms to determine when thinking is beneficial:

1. **Intent Analysis**: Uses AdaptiveIntentProcessor and IntentFilteringTriangle
2. **Complexity Scoring**: Calculates multi-dimensional complexity score
3. **Flow State Detection**: Determines optimal processing approach
4. **Thinking Decision**: Enables thinking based on mathematical thresholds

## 🚀 Best Practices

### 1. Use Adaptive Thinking by Default
```python
# Recommended configuration for most use cases
thinking_config = ThinkingConfig(
    mode=ThinkingMode.ADAPTIVE,
    complexity_threshold=0.7,
    hide_thinking=False,  # Show thinking for debugging
)
```

### 2. Optimize for Use Case
```python
# For fast responses (chatbots, simple queries)
request = EnhancedLLMRequest(
    messages=messages,
    model_id="qwen2.5-coder:7b",
    priority="fast",
    thinking_config=ThinkingConfig(mode=ThinkingMode.DISABLED),
)

# For complex analysis (research, problem-solving)
request = EnhancedLLMRequest(
    messages=messages,
    model_id="deepseek-r1:8b",
    priority="quality",
    thinking_config=ThinkingConfig(mode=ThinkingMode.ENABLED),
)
```

### 3. Monitor Performance
```python
# Regular performance monitoring
stats = backend.get_performance_stats()
if stats['thinking_usage_rate'] > 0.8:
    print("High thinking usage - consider optimizing complexity thresholds")
if stats['average_response_time'] > 5.0:
    print("Slow responses - consider using faster models for simple requests")
```

## 🔮 Advanced Features

### Custom Thinking Prompts
```python
thinking_config = ThinkingConfig(
    mode=ThinkingMode.ENABLED,
    thinking_prompt="Think step by step about this problem...",
)
```

### Multi-Provider Fallback
The backend automatically falls back to available providers if the preferred model fails.

### Real-time Optimization
The system learns from usage patterns and optimizes thinking decisions over time.

## 📊 Integration with Agent Swarm

The Enhanced LLM Backend seamlessly integrates with Agent Swarm's interactive shell and agent systems, providing revolutionary thinking capabilities throughout the entire platform.

---

**The Enhanced LLM Backend represents the cutting edge of AI model interaction, combining the latest Ollama features with mathematical intent processing for unprecedented intelligence and performance.** 🚀🧠✨
