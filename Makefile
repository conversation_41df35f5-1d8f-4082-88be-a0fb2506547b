.PHONY: help install install-dev test test-unit test-integration lint format type-check clean docs serve-docs build

# Default target
help:
	@echo "Available commands:"
	@echo "  install       Install package in development mode"
	@echo "  install-dev   Install with development dependencies"
	@echo "  test          Run all tests"
	@echo "  test-unit     Run unit tests only"
	@echo "  test-integration  Run integration tests only"
	@echo "  lint          Run linting (flake8)"
	@echo "  format        Format code (black + isort)"
	@echo "  type-check    Run type checking (mypy)"
	@echo "  clean         Clean build artifacts"
	@echo "  docs          Build documentation"
	@echo "  serve-docs    Serve documentation locally"
	@echo "  build         Build package"

# Installation
install:
	pip install -e .

install-dev:
	pip install -e ".[dev,cloud]"
	pre-commit install

# Testing
test:
	pytest

test-unit:
	pytest tests/unit -v

test-integration:
	pytest tests/integration -v

test-slow:
	pytest -m slow

test-requires-ollama:
	pytest -m requires_ollama

test-requires-api-keys:
	pytest -m requires_api_keys

test-mcp:
	pytest -m mcp

test-e2e:
	pytest tests/e2e -v

# Code quality
lint:
	flake8 src tests examples

format:
	black src tests examples
	isort src tests examples

type-check:
	mypy src

check: lint type-check
	@echo "✅ All checks passed"

# Documentation
docs:
	mkdocs build

serve-docs:
	mkdocs serve

# Build and clean
build:
	python -m build

clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .mypy_cache/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

# Development workflow
dev-setup: install-dev
	@echo "🚀 Development environment ready!"
	@echo "Run 'make test' to verify everything works"

# CI/CD helpers
ci-test: lint type-check test
	@echo "✅ All CI checks passed"

# Quick demo
demo:
	python examples/quick_demo.py

# MCP demo
demo-mcp:
	python examples/mcp_demo.py

# CLI agent demo
demo-cli:
	python examples/cli_agent_demo.py

# Development RAG demo
demo-dev-rag:
	python examples/dev_rag_demo.py

# Setup local LLMs
setup-ollama:
	python scripts/setup_ollama.py

# Install with MCP support
install-mcp:
	pip install -e ".[mcp]"

# Install with CLI tools
install-cli:
	pip install -e ".[cli]"

# Install with RAG support
install-rag:
	pip install -e ".[rag]"

# Install everything
install-all:
	pip install -e ".[all]"

# Autocomplete demo
demo-autocomplete:
	python examples/demo_at_autocomplete.py
