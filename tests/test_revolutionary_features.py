#!/usr/bin/env python3
"""
Test Revolutionary Agent-Swarm Features

Tests the core revolutionary features that make Agent Swarm unique:
1. Natural language intent detection
2. Intelligent agent routing
3. Tool selection and visibility
4. Agent switching and recommendations

This focuses on the CORE VALUE PROPOSITION, not just technical implementation.
"""

import pytest
import sys
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


class TestIntentDetection:
    """Test natural language intent detection - the brain of the system."""

    def test_search_intent_detection(self):
        """Test that 'search for X' is detected as search intent."""
        try:
            from agent_swarm.core.intent_detector import IntentDetector, IntentType

            detector = IntentDetector()

            # Test natural language search
            test_cases = [
                "search for async functions",
                "find all the imports",
                "look for error handling",
                "where is the main function",
                "locate the config file"
            ]

            for test_input in test_cases:
                intent = detector.detect_intent(test_input)
                assert intent.intent_type == IntentType.SEARCH, f"Failed for: {test_input}"
                assert intent.confidence > 0.5, f"Low confidence for: {test_input}"
                assert "search" in intent.suggested_agent or "find" in intent.suggested_agent

            print("✅ Natural language search detection works!")

        except ImportError:
            pytest.skip("Intent detector not available")

    def test_edit_intent_detection(self):
        """Test that edit requests are properly detected."""
        try:
            from agent_swarm.core.intent_detector import IntentDetector, IntentType

            detector = IntentDetector()

            test_cases = [
                "edit the main.py file",
                "modify this function",
                "change the configuration",
                "update the imports"
            ]

            for test_input in test_cases:
                intent = detector.detect_intent(test_input)
                assert intent.intent_type == IntentType.EDIT_FILE, f"Failed for: {test_input}"
                assert intent.confidence > 0.3

            print("✅ Edit intent detection works!")

        except ImportError:
            pytest.skip("Intent detector not available")

    def test_explicit_vs_natural_commands(self):
        """Test that both explicit commands and natural language work."""
        try:
            from agent_swarm.core.intent_detector import IntentDetector, IntentType

            detector = IntentDetector()

            # Explicit command should have high confidence
            explicit_intent = detector.detect_intent("/search async functions")
            assert explicit_intent.intent_type == IntentType.SEARCH
            assert explicit_intent.confidence == 1.0  # Explicit commands should be 100%

            # Natural language should also work but with lower confidence
            natural_intent = detector.detect_intent("search for async functions")
            assert natural_intent.intent_type == IntentType.SEARCH
            assert natural_intent.confidence > 0.5

            print("✅ Both explicit and natural language commands work!")

        except ImportError:
            pytest.skip("Intent detector not available")


class TestAgentRouting:
    """Test intelligent agent routing - the revolutionary part."""

    def test_agent_recommendations(self):
        """Test that agent recommendations are provided."""
        try:
            from agent_swarm.core.agent_router import AgentRouter

            router = AgentRouter()

            # Test search request
            recommendations = router.get_agent_recommendations("search for functions")
            assert len(recommendations) > 0, "Should provide agent recommendations"

            # Should recommend search_agent for search tasks
            search_agents = [r for r in recommendations if "search" in r["agent"]]
            assert len(search_agents) > 0, "Should recommend search agent for search tasks"

            # Test coding request
            recommendations = router.get_agent_recommendations("edit the main.py file")
            coding_agents = [r for r in recommendations if "coding" in r["agent"]]
            assert len(coding_agents) > 0, "Should recommend coding agent for edit tasks"

            print("✅ Agent recommendations work!")

        except ImportError:
            pytest.skip("Agent router not available")

    async def test_routing_decision_visibility(self):
        """Test that routing decisions provide clear reasoning."""
        try:
            from agent_swarm.core.agent_router import AgentRouter

            router = AgentRouter()

            # Test routing decision
            decision = await router.route_request("search for async functions")

            assert decision.selected_agent is not None
            assert decision.confidence > 0
            assert decision.reasoning is not None
            assert len(decision.reasoning) > 10  # Should have meaningful reasoning

            # Should provide alternatives
            assert isinstance(decision.alternatives, list)

            print("✅ Routing decisions provide clear visibility!")

        except ImportError:
            pytest.skip("Agent router not available")

    async def test_tool_selection(self):
        """Test that appropriate tools are selected."""
        try:
            from agent_swarm.core.agent_router import AgentRouter

            router = AgentRouter()

            # Test search tools
            decision = await router.route_request("search for functions")
            assert "search" in str(decision.selected_tools).lower()

            # Test edit tools
            decision = await router.route_request("edit main.py")
            tools_str = str(decision.selected_tools).lower()
            assert "edit" in tools_str or "file" in tools_str

            print("✅ Tool selection works correctly!")

        except ImportError:
            pytest.skip("Agent router not available")


class TestShellIntegration:
    """Test shell integration with revolutionary features."""

    def test_shell_has_agent_router(self):
        """Test that shell integrates agent router."""
        try:
            from agent_swarm.cli.interactive_shell import InteractiveShell

            shell = InteractiveShell(verbose=False, auto_index=False)

            # Should have agent router attributes
            assert hasattr(shell, 'agent_router')
            assert hasattr(shell, 'show_agent_info')

            # Should have routing helper methods
            assert hasattr(shell, '_show_agent_recommendations')
            assert hasattr(shell, '_show_routing_decision')
            assert hasattr(shell, '_on_agent_routing')

            print("✅ Shell integrates agent router!")

        except ImportError:
            pytest.skip("Shell not available")

    def test_double_ctrl_c_functionality(self):
        """Test double Ctrl+C graceful quit."""
        try:
            from agent_swarm.cli.interactive_shell import InteractiveShell
            import time

            shell = InteractiveShell(verbose=False, auto_index=False)

            # Test double Ctrl+C detection logic
            shell._last_interrupt_time = time.time()

            # Simulate second Ctrl+C within threshold
            current_time = time.time() + 1.0  # 1 second later
            is_double = (current_time - shell._last_interrupt_time) < shell._interrupt_threshold

            assert is_double, "Should detect double Ctrl+C within threshold"

            # Test beyond threshold
            current_time = time.time() + 3.0  # 3 seconds later
            is_not_double = (current_time - shell._last_interrupt_time) >= shell._interrupt_threshold

            assert is_not_double, "Should not detect double Ctrl+C beyond threshold"

            print("✅ Double Ctrl+C functionality works!")

        except ImportError:
            pytest.skip("Shell not available")


class TestCoreValueProposition:
    """Test the core value proposition - what makes Agent Swarm revolutionary."""

    async def test_natural_language_to_agent_routing(self):
        """Test the complete flow: natural language -> intent -> agent -> tools."""
        try:
            from agent_swarm.core.intent_detector import IntentDetector
            from agent_swarm.core.agent_router import AgentRouter

            # Complete flow test
            user_input = "search for all async functions in the codebase"

            # 1. Detect intent
            detector = IntentDetector()
            intent = detector.detect_intent(user_input)

            # 2. Route to agent
            router = AgentRouter()
            decision = await router.route_request(user_input)

            # 3. Verify the flow works
            assert intent.confidence > 0.5
            assert decision.selected_agent is not None
            assert len(decision.selected_tools) > 0
            assert decision.reasoning is not None

            print("✅ Complete natural language -> agent routing flow works!")
            print(f"   Input: {user_input}")
            print(f"   Intent: {intent.intent_type.value} ({intent.confidence:.1%})")
            print(f"   Agent: {decision.selected_agent}")
            print(f"   Tools: {decision.selected_tools}")
            print(f"   Reasoning: {decision.reasoning}")

        except ImportError:
            pytest.skip("Core components not available")

    def test_agent_visibility_and_transparency(self):
        """Test that users can see which agents are recommended and why."""
        try:
            from agent_swarm.core.agent_router import AgentRouter

            router = AgentRouter()

            # Get system status
            status = router.get_system_status()

            assert "agents" in status
            assert len(status["agents"]) > 0

            # Each agent should have clear information
            for agent_name, agent_info in status["agents"].items():
                assert "description" in agent_info
                assert "specialties" in agent_info
                assert "tools" in agent_info
                assert "current_load" in agent_info

            print("✅ Agent visibility and transparency works!")
            print(f"   Available agents: {list(status['agents'].keys())}")

        except ImportError:
            pytest.skip("Agent router not available")

    async def test_revolutionary_vs_traditional_approach(self):
        """Test that our approach is revolutionary compared to traditional CLIs."""
        try:
            from agent_swarm.core.intent_detector import IntentDetector
            from agent_swarm.core.agent_router import AgentRouter

            # Traditional approach: user must know exact commands
            # Revolutionary approach: natural language works

            natural_inputs = [
                "find all the functions that handle errors",
                "show me the configuration files",
                "edit the main application file",
                "explain what this code does",
                "fix the bug in the authentication"
            ]

            detector = IntentDetector()
            router = AgentRouter()

            successful_routes = 0

            for user_input in natural_inputs:
                intent = detector.detect_intent(user_input)
                decision = await router.route_request(user_input)

                if intent.confidence > 0.3 and decision.selected_agent:
                    successful_routes += 1

            # Should successfully route most natural language inputs
            success_rate = successful_routes / len(natural_inputs)
            assert success_rate >= 0.8, f"Success rate too low: {success_rate:.1%}"

            print(f"✅ Revolutionary natural language routing: {success_rate:.1%} success rate!")

        except ImportError:
            pytest.skip("Core components not available")


def run_revolutionary_tests():
    """Run all revolutionary feature tests."""
    print("🚀 Testing Revolutionary Agent-Swarm Features")
    print("=" * 60)

    test_classes = [
        TestIntentDetection,
        TestAgentRouting,
        TestShellIntegration,
        TestCoreValueProposition
    ]

    total_tests = 0
    passed_tests = 0

    for test_class in test_classes:
        print(f"\n🧪 {test_class.__name__}")
        print("-" * 40)

        instance = test_class()
        test_methods = [method for method in dir(instance) if method.startswith('test_')]

        for test_method in test_methods:
            total_tests += 1
            try:
                method = getattr(instance, test_method)
                if hasattr(method, '__code__') and method.__code__.co_flags & 0x80:  # async
                    import asyncio
                    asyncio.run(method())
                else:
                    method()
                passed_tests += 1
                print(f"  ✅ {test_method}")
            except Exception as e:
                print(f"  ❌ {test_method}: {e}")

    print("\n" + "=" * 60)
    print(f"📊 Results: {passed_tests}/{total_tests} tests passed ({passed_tests/total_tests:.1%})")

    if passed_tests == total_tests:
        print("🎉 All revolutionary features working!")
        return True
    else:
        print(f"❌ {total_tests - passed_tests} tests failed")
        return False


if __name__ == "__main__":
    success = run_revolutionary_tests()
    sys.exit(0 if success else 1)
