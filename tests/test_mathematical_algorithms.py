"""
Tests for the Mathematical Algorithm Framework.

This test suite validates the core functionality of the mathematical
algorithm framework including:

- Algorithm execution and event system
- Configuration management
- Performance monitoring
- Pipeline orchestration
- Built-in algorithms
- Utility functions

Run with: python -m pytest tests/test_mathematical_algorithms.py -v
"""

import pytest
import asyncio
from pathlib import Path
import sys

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm.algorithms import (
    # Core framework
    AlgorithmEngine,
    AlgorithmConfig,
    AlgorithmParameters,
    ParameterDefinition,
    ParameterType,
    ParameterConstraint,

    # Event system
    ExecuteAlgorithmAction,
    BenchmarkAction,
    ValidateAction,

    # Built-in algorithms
    ConfidenceThresholder,
    PatternMatcher,
    AdaptiveIntentProcessor,
    IntentFilteringTriangle,

    # Algorithm stages
    ExplicitCommandStage,

    # Pipeline system
    AlgorithmPipeline,

    # Registry
    AlgorithmRegistry,

    # Utilities
    create_algorithm_engine,
    create_parameter_definition,
    calculate_confidence,
    normalize_scores,
)


class TestAlgorithmFramework:
    """Test the core algorithm framework."""

    def test_algorithm_config_creation(self):
        """Test algorithm configuration creation."""
        config = AlgorithmConfig(algorithm_name="test_algorithm")

        assert config.algorithm_name == "test_algorithm"
        assert config.algorithm_version == "1.0.0"
        assert config.enable_metrics is True
        assert config.max_execution_time == 300.0

    def test_parameter_definition(self):
        """Test parameter definition and validation."""
        param = create_parameter_definition(
            name="threshold",
            param_type="float",
            default_value=0.5,
            description="Test threshold",
            min_value=0.0,
            max_value=1.0,
        )

        assert param.name == "threshold"
        assert param.param_type == ParameterType.FLOAT
        assert param.default_value == 0.5
        assert param.constraints.min_value == 0.0
        assert param.constraints.max_value == 1.0

        # Test validation
        assert param.validate_value(0.7) is True
        assert param.validate_value(-0.1) is False
        assert param.validate_value(1.5) is False

    def test_algorithm_registry(self):
        """Test algorithm registry functionality."""
        registry = AlgorithmRegistry()

        # Register algorithm
        registry.register_algorithm(ConfidenceThresholder)

        # Check registration
        algorithms = registry.list_algorithms()
        assert "confidence_thresholder" in algorithms

        # Get algorithm instance
        algorithm = registry.get_algorithm("confidence_thresholder")
        assert algorithm is not None
        assert isinstance(algorithm, ConfidenceThresholder)

        # Test metadata
        metadata = registry.get_algorithm_metadata("confidence_thresholder")
        assert metadata is not None
        assert metadata.name == "confidence_thresholder"
        assert metadata.category == "thresholding"


class TestConfidenceThresholder:
    """Test the ConfidenceThresholder algorithm."""

    @pytest.mark.asyncio
    async def test_confidence_thresholder_execution(self):
        """Test confidence thresholder algorithm execution."""
        # Create engine
        engine = create_algorithm_engine(
            algorithm_name="confidence_thresholder",
            parameters={
                "threshold": 0.6,
                "strategy": "binary",
            }
        )

        # Register algorithm
        engine.registry.register_algorithm(ConfidenceThresholder)

        # Test data
        confidence_scores = [0.1, 0.3, 0.5, 0.7, 0.8, 0.9]

        # Create action
        action = ExecuteAlgorithmAction(
            algorithm_name="confidence_thresholder",
            input_data=confidence_scores,
        )

        # Execute
        observation = await engine.execute_action(action)

        # Validate results
        assert observation.success is True
        assert observation.execution_time > 0
        assert observation.confidence > 0

        # Check output
        assert hasattr(observation, 'output_data')
        output = observation.output_data
        assert isinstance(output, list)

        # Binary thresholding with threshold 0.6
        expected = [False, False, False, True, True, True]
        assert output == expected

    @pytest.mark.asyncio
    async def test_confidence_thresholder_strategies(self):
        """Test different thresholding strategies."""
        strategies = ["binary", "soft", "sigmoid"]

        for strategy in strategies:
            engine = create_algorithm_engine(
                algorithm_name="confidence_thresholder",
                parameters={
                    "threshold": 0.5,
                    "strategy": strategy,
                }
            )

            engine.registry.register_algorithm(ConfidenceThresholder)

            action = ExecuteAlgorithmAction(
                algorithm_name="confidence_thresholder",
                input_data=[0.3, 0.7],
            )

            observation = await engine.execute_action(action)

            assert observation.success is True
            assert hasattr(observation, 'output_data')
            assert len(observation.output_data) == 2


class TestPatternMatcher:
    """Test the PatternMatcher algorithm."""

    @pytest.mark.asyncio
    async def test_pattern_matcher_execution(self):
        """Test pattern matcher algorithm execution."""
        engine = create_algorithm_engine(
            algorithm_name="pattern_matcher",
            parameters={
                "strategy": "exact",
                "similarity_threshold": 0.5,
            }
        )

        engine.registry.register_algorithm(PatternMatcher)

        # Test data
        input_data = {
            'text': "hello world test",
            'patterns': ["hello", "world", "test", "missing"],
        }

        action = ExecuteAlgorithmAction(
            algorithm_name="pattern_matcher",
            input_data=input_data,
        )

        observation = await engine.execute_action(action)

        assert observation.success is True
        assert hasattr(observation, 'output_data')

        output = observation.output_data
        assert 'matches' in output
        assert 'total_matches' in output

        # Should find 3 matches (hello, world, test)
        matches = output['matches']
        assert len(matches) >= 3


class TestExplicitCommandStage:
    """Test the ExplicitCommandStage."""

    @pytest.mark.asyncio
    async def test_explicit_command_recognition(self):
        """Test explicit command recognition."""
        stage = ExplicitCommandStage()

        # Create context
        config = AlgorithmConfig(algorithm_name="test")
        from agent_swarm.algorithms.core.registry import AlgorithmContext
        context = AlgorithmContext(config)

        # Test explicit command
        result = await stage.process("/help", context)

        assert result.success is True
        assert result.output['is_explicit_command'] is True
        assert result.output['command_recognized'] is True
        assert result.output['command_valid'] is True
        assert result.output['command_name'] == "help"
        assert result.confidence > 0.9

    @pytest.mark.asyncio
    async def test_non_command_input(self):
        """Test non-command input handling."""
        stage = ExplicitCommandStage()

        config = AlgorithmConfig(algorithm_name="test")
        from agent_swarm.algorithms.core.registry import AlgorithmContext
        context = AlgorithmContext(config)

        # Test non-command
        result = await stage.process("hello world", context)

        assert result.success is True
        assert result.output['is_explicit_command'] is False
        assert result.confidence == 0.0

    @pytest.mark.asyncio
    async def test_unknown_command(self):
        """Test unknown command handling."""
        stage = ExplicitCommandStage()

        config = AlgorithmConfig(algorithm_name="test")
        from agent_swarm.algorithms.core.registry import AlgorithmContext
        context = AlgorithmContext(config)

        # Test unknown command
        result = await stage.process("/unknown_command", context)

        assert result.success is True
        assert result.output['is_explicit_command'] is True
        assert result.output['command_recognized'] is False
        assert result.confidence < 0.5


class TestAlgorithmPipeline:
    """Test algorithm pipeline functionality."""

    @pytest.mark.asyncio
    async def test_pipeline_creation_and_execution(self):
        """Test pipeline creation and execution."""
        pipeline = AlgorithmPipeline("test_pipeline")

        # Add stage
        explicit_stage = ExplicitCommandStage()
        pipeline.add_stage(explicit_stage)

        # Validate pipeline
        errors = pipeline.validate_pipeline()
        assert len(errors) == 0

        # Test execution
        config = AlgorithmConfig(algorithm_name="test")
        from agent_swarm.algorithms.core.registry import AlgorithmContext
        context = AlgorithmContext(config)

        result = await pipeline.execute("/help", context)

        assert result.success is True
        assert result.execution_time > 0
        assert result.metadata['stages_executed'] == 1


class TestUtilityFunctions:
    """Test utility functions."""

    def test_calculate_confidence(self):
        """Test confidence calculation."""
        # Test basic confidence calculation
        confidence = calculate_confidence(0.7, threshold=0.5)
        assert 0.0 <= confidence <= 1.0

        # Score below threshold should give 0
        confidence = calculate_confidence(0.3, threshold=0.5)
        assert confidence == 0.0

        # Score above threshold should give positive confidence
        confidence = calculate_confidence(0.8, threshold=0.5)
        assert confidence > 0.0

    def test_normalize_scores(self):
        """Test score normalization."""
        scores = [1, 2, 3, 4, 5]

        # Min-max normalization
        normalized = normalize_scores(scores, method="min_max")
        assert len(normalized) == len(scores)
        assert min(normalized) == 0.0
        assert max(normalized) == 1.0

        # Z-score normalization
        z_normalized = normalize_scores(scores, method="z_score")
        assert len(z_normalized) == len(scores)

        # Test empty list
        empty_normalized = normalize_scores([], method="min_max")
        assert empty_normalized == []

    def test_create_algorithm_engine(self):
        """Test algorithm engine creation utility."""
        engine = create_algorithm_engine(
            algorithm_name="test_algorithm",
            parameters={"threshold": 0.7},
            enable_metrics=True,
        )

        assert engine.config.algorithm_name == "test_algorithm"
        assert engine.config.parameters.get_parameter("threshold") == 0.7
        assert engine.config.enable_metrics is True


class TestPerformanceMonitoring:
    """Test performance monitoring capabilities."""

    @pytest.mark.asyncio
    async def test_metrics_collection(self):
        """Test metrics collection during algorithm execution."""
        config = AlgorithmConfig(
            algorithm_name="test_algorithm",
            enable_metrics=True,
        )

        engine = AlgorithmEngine(config)
        engine.registry.register_algorithm(ConfidenceThresholder)

        # Execute algorithm multiple times
        for i in range(3):
            action = ExecuteAlgorithmAction(
                algorithm_name="confidence_thresholder",
                input_data=[0.1, 0.5, 0.9],
            )

            observation = await engine.execute_action(action)
            assert observation.success is True

        # Check that metrics were collected
        assert hasattr(engine.context, 'metrics')
        metrics = engine.context.metrics

        # Basic metrics validation
        assert hasattr(metrics, 'algorithm_name')


class TestAdaptiveIntentProcessor:
    """Test the AdaptiveIntentProcessor algorithm."""

    @pytest.mark.asyncio
    async def test_adaptive_intent_processor_basic(self):
        """Test basic adaptive intent processor functionality."""
        engine = create_algorithm_engine(
            algorithm_name="adaptive_intent_processor",
            parameters={
                "enable_early_stopping": True,
                "confidence_threshold": 0.8,
                "processing_strategy": "adaptive",
            }
        )

        engine.registry.register_algorithm(AdaptiveIntentProcessor)

        # Test explicit command (should trigger early stopping)
        action = ExecuteAlgorithmAction(
            algorithm_name="adaptive_intent_processor",
            input_data="/help config",
        )

        observation = await engine.execute_action(action)

        assert observation.success is True
        assert observation.execution_time > 0
        assert observation.confidence > 0

        # Check output structure
        assert hasattr(observation, 'output_data')
        output = observation.output_data

        assert 'intent_text' in output
        assert 'final_confidence' in output
        assert 'stages_executed' in output
        assert 'early_stopped' in output
        assert 'processing_strategy' in output

        # Explicit command should trigger early stopping
        assert output['early_stopped'] is True
        assert output['stages_executed'] <= 2  # Should stop early
        assert output['final_confidence'] > 0.8  # High confidence for explicit commands

    @pytest.mark.asyncio
    async def test_adaptive_intent_processor_strategies(self):
        """Test different processing strategies."""
        strategies = ["adaptive", "sequential", "confidence_driven"]
        test_intent = "create a Python function"

        for strategy in strategies:
            engine = create_algorithm_engine(
                algorithm_name="adaptive_intent_processor",
                parameters={
                    "processing_strategy": strategy,
                    "confidence_threshold": 0.7,
                }
            )

            engine.registry.register_algorithm(AdaptiveIntentProcessor)

            action = ExecuteAlgorithmAction(
                algorithm_name="adaptive_intent_processor",
                input_data=test_intent,
            )

            observation = await engine.execute_action(action)

            assert observation.success is True
            assert hasattr(observation, 'output_data')

            output = observation.output_data
            assert output['processing_strategy'] == strategy
            assert output['intent_text'] == test_intent
            assert 'final_confidence' in output
            assert 'stages_executed' in output

    @pytest.mark.asyncio
    async def test_adaptive_intent_processor_confidence_thresholds(self):
        """Test different confidence thresholds."""
        thresholds = [0.6, 0.8, 0.9]
        test_intent = "help me with something"

        for threshold in thresholds:
            engine = create_algorithm_engine(
                algorithm_name="adaptive_intent_processor",
                parameters={
                    "confidence_threshold": threshold,
                    "enable_early_stopping": True,
                    "processing_strategy": "adaptive",
                }
            )

            engine.registry.register_algorithm(AdaptiveIntentProcessor)

            action = ExecuteAlgorithmAction(
                algorithm_name="adaptive_intent_processor",
                input_data=test_intent,
            )

            observation = await engine.execute_action(action)

            assert observation.success is True

            output = observation.output_data

            # Higher thresholds should generally require more stages
            # (though this depends on the specific intent and processing)
            assert 'stages_executed' in output
            assert output['stages_executed'] >= 1
            assert output['stages_executed'] <= 5

    @pytest.mark.asyncio
    async def test_adaptive_intent_processor_complex_intent(self):
        """Test processing of complex, ambiguous intents."""
        engine = create_algorithm_engine(
            algorithm_name="adaptive_intent_processor",
            parameters={
                "enable_early_stopping": False,  # Force full pipeline
                "max_processing_stages": 5,
                "processing_strategy": "adaptive",
            }
        )

        engine.registry.register_algorithm(AdaptiveIntentProcessor)

        # Complex, ambiguous intent
        complex_intent = "I'm not sure what I want to do, maybe something with data analysis or machine learning, but I need to understand the options first"

        action = ExecuteAlgorithmAction(
            algorithm_name="adaptive_intent_processor",
            input_data=complex_intent,
        )

        observation = await engine.execute_action(action)

        assert observation.success is True

        output = observation.output_data

        # Complex intent should use more stages
        assert output['stages_executed'] >= 3
        assert 'confidence_progression' in output
        assert len(output['confidence_progression']) > 0

        # Should have processing context
        assert 'processing_context' in output
        assert output['processing_context']['original_intent'] == complex_intent

    @pytest.mark.asyncio
    async def test_adaptive_intent_processor_performance_stats(self):
        """Test performance statistics collection."""
        engine = create_algorithm_engine(
            algorithm_name="adaptive_intent_processor",
            parameters={
                "enable_early_stopping": True,
                "confidence_threshold": 0.8,
            }
        )

        engine.registry.register_algorithm(AdaptiveIntentProcessor)

        # Get algorithm instance to check stats
        algorithm = engine.registry.get_algorithm("adaptive_intent_processor")

        # Process multiple intents
        test_intents = [
            "/help",           # Should early stop
            "/config",         # Should early stop
            "create function", # May early stop
            "complex analysis task that requires deep reasoning", # Should not early stop
        ]

        for intent in test_intents:
            action = ExecuteAlgorithmAction(
                algorithm_name="adaptive_intent_processor",
                input_data=intent,
            )

            observation = await engine.execute_action(action)
            assert observation.success is True

        # Check statistics
        if hasattr(algorithm, 'get_processing_stats'):
            stats = algorithm.get_processing_stats()

            assert 'total_processed' in stats
            assert stats['total_processed'] == len(test_intents)
            assert 'early_stops' in stats
            assert 'full_pipeline' in stats
            assert 'stage_usage' in stats

            # Should have some early stops from explicit commands
            assert stats['early_stops'] > 0

    @pytest.mark.asyncio
    async def test_adaptive_intent_processor_input_validation(self):
        """Test input validation for adaptive intent processor."""
        engine = create_algorithm_engine(
            algorithm_name="adaptive_intent_processor"
        )

        engine.registry.register_algorithm(AdaptiveIntentProcessor)

        # Test valid inputs
        valid_inputs = [
            "simple text",
            {"intent": "structured intent"},
            {"text": "text field"},
        ]

        for valid_input in valid_inputs:
            action = ExecuteAlgorithmAction(
                algorithm_name="adaptive_intent_processor",
                input_data=valid_input,
            )

            observation = await engine.execute_action(action)
            assert observation.success is True

        # Test invalid inputs
        invalid_inputs = [
            "",           # Empty string
            None,         # None
            123,          # Number
            [],           # Empty list
            {},           # Empty dict
        ]

        for invalid_input in invalid_inputs:
            action = ExecuteAlgorithmAction(
                algorithm_name="adaptive_intent_processor",
                input_data=invalid_input,
            )

            observation = await engine.execute_action(action)
            # Should either fail or handle gracefully
            # (implementation may vary on how it handles edge cases)
            assert hasattr(observation, 'success')

    @pytest.mark.asyncio
    async def test_adaptive_intent_processor_metadata(self):
        """Test algorithm metadata and configuration."""
        algorithm = AdaptiveIntentProcessor()

        # Test metadata
        metadata = algorithm.metadata
        assert metadata.name == "adaptive_intent_processor"
        assert metadata.version == "1.0.0"
        assert metadata.category == "intent_processing"
        assert "adaptive" in metadata.tags
        assert "intent" in metadata.tags
        assert metadata.complexity == "O(1) to O(n³) adaptive"

        # Test parameter definitions
        param_defs = algorithm.get_parameter_definitions()
        assert len(param_defs) > 0

        # Check for key parameters
        param_names = [p.name for p in param_defs]
        assert "enable_early_stopping" in param_names
        assert "confidence_threshold" in param_names
        assert "processing_strategy" in param_names
        assert "max_processing_stages" in param_names

        # Test complexity estimation
        complexity = algorithm.estimate_complexity(100)
        assert isinstance(complexity, float)
        assert complexity > 0


class TestIntentFilteringTriangle:
    """Test the IntentFilteringTriangle algorithm."""

    @pytest.mark.asyncio
    async def test_intent_filtering_triangle_basic(self):
        """Test basic intent filtering triangle functionality."""
        engine = create_algorithm_engine(
            algorithm_name="intent_filtering_triangle",
            parameters={
                "filtering_strategy": "holistic",
                "enable_symbiotic_intelligence": True,
                "enable_flow_state_analysis": True,
            }
        )

        engine.registry.register_algorithm(IntentFilteringTriangle)

        # Test clear, simple intent
        action = ExecuteAlgorithmAction(
            algorithm_name="intent_filtering_triangle",
            input_data="/help config",
        )

        observation = await engine.execute_action(action)

        assert observation.success is True
        assert observation.execution_time > 0
        assert observation.confidence > 0

        # Check output structure
        assert hasattr(observation, 'output_data')
        output = observation.output_data

        # Verify core components
        assert 'intent_coordinates' in output
        assert 'dimensional_analysis' in output
        assert 'holistic_score' in output
        assert 'filtering_recommendations' in output
        assert 'visualization_data' in output

        # Check 3D coordinates
        coords = output['intent_coordinates']
        assert 'clarity' in coords
        assert 'complexity' in coords
        assert 'context' in coords
        assert 0.0 <= coords['clarity'] <= 1.0
        assert 0.0 <= coords['complexity'] <= 1.0
        assert 0.0 <= coords['context'] <= 1.0

        # Explicit command should have high clarity, low complexity
        assert coords['clarity'] > 0.7  # High clarity for explicit command
        assert coords['complexity'] < 0.5  # Low complexity for simple command

    @pytest.mark.asyncio
    async def test_intent_filtering_triangle_strategies(self):
        """Test different filtering strategies."""
        strategies = ["holistic", "dimensional", "geometric", "harmonic", "adaptive"]
        test_intent = "create a Python function to analyze data"

        for strategy in strategies:
            engine = create_algorithm_engine(
                algorithm_name="intent_filtering_triangle",
                parameters={
                    "filtering_strategy": strategy,
                    "enable_symbiotic_intelligence": False,  # Disable for consistent testing
                }
            )

            engine.registry.register_algorithm(IntentFilteringTriangle)

            action = ExecuteAlgorithmAction(
                algorithm_name="intent_filtering_triangle",
                input_data=test_intent,
            )

            observation = await engine.execute_action(action)

            assert observation.success is True
            assert hasattr(observation, 'output_data')

            output = observation.output_data
            holistic_score = output['holistic_score']

            assert holistic_score['strategy_used'] == strategy
            assert 'overall_score' in holistic_score
            assert 'overall_confidence' in holistic_score
            assert 0.0 <= holistic_score['overall_score'] <= 1.0
            assert 0.0 <= holistic_score['overall_confidence'] <= 1.0

    @pytest.mark.asyncio
    async def test_intent_filtering_triangle_symbiotic_intelligence(self):
        """Test symbiotic intelligence functionality."""
        test_intent = "fix this bug"  # Ambiguous but simple

        # Test without symbiotic intelligence
        engine_no_symbiotic = create_algorithm_engine(
            algorithm_name="intent_filtering_triangle",
            parameters={
                "enable_symbiotic_intelligence": False,
                "filtering_strategy": "holistic",
            }
        )

        engine_no_symbiotic.registry.register_algorithm(IntentFilteringTriangle)

        action = ExecuteAlgorithmAction(
            algorithm_name="intent_filtering_triangle",
            input_data=test_intent,
        )

        observation_no_symbiotic = await engine_no_symbiotic.execute_action(action)

        # Test with symbiotic intelligence
        engine_with_symbiotic = create_algorithm_engine(
            algorithm_name="intent_filtering_triangle",
            parameters={
                "enable_symbiotic_intelligence": True,
                "symbiotic_strength": 0.2,
                "filtering_strategy": "holistic",
            }
        )

        engine_with_symbiotic.registry.register_algorithm(IntentFilteringTriangle)

        observation_with_symbiotic = await engine_with_symbiotic.execute_action(action)

        assert observation_no_symbiotic.success is True
        assert observation_with_symbiotic.success is True

        # Check that symbiotic version has adjusted scores
        output_symbiotic = observation_with_symbiotic.output_data
        assert 'symbiotic_analysis' in output_symbiotic

        symbiotic_analysis = output_symbiotic['symbiotic_analysis']
        if symbiotic_analysis:  # May be None if symbiotic disabled
            assert 'adjusted_scores' in symbiotic_analysis
            assert 'symbiotic_adjustments' in symbiotic_analysis

    @pytest.mark.asyncio
    async def test_intent_filtering_triangle_flow_state_analysis(self):
        """Test flow state analysis functionality."""
        # Test intents designed for different flow states
        flow_test_cases = [
            ("/help", "optimal"),  # Should be optimal flow
            ("create a simple function", "good"),  # Should be good flow
            ("what?", "difficult"),  # Should be difficult flow
        ]

        engine = create_algorithm_engine(
            algorithm_name="intent_filtering_triangle",
            parameters={
                "enable_flow_state_analysis": True,
                "filtering_strategy": "holistic",
            }
        )

        engine.registry.register_algorithm(IntentFilteringTriangle)

        for intent, expected_flow_category in flow_test_cases:
            action = ExecuteAlgorithmAction(
                algorithm_name="intent_filtering_triangle",
                input_data=intent,
            )

            observation = await engine.execute_action(action)

            assert observation.success is True

            output = observation.output_data
            assert 'flow_state_analysis' in output

            flow_analysis = output['flow_state_analysis']
            assert 'flow_state' in flow_analysis
            assert 'flow_score' in flow_analysis
            assert 'flow_confidence' in flow_analysis
            assert 'recommendations' in flow_analysis
            assert 'flow_metrics' in flow_analysis

            # Verify flow metrics structure
            flow_metrics = flow_analysis['flow_metrics']
            assert 'flow_efficiency' in flow_metrics
            assert 'processing_readiness' in flow_metrics
            assert 'intent_stability' in flow_metrics
            assert 'optimization_potential' in flow_metrics

    @pytest.mark.asyncio
    async def test_intent_filtering_triangle_dimensional_analysis(self):
        """Test dimensional analysis functionality."""
        # Test intent with known characteristics
        test_intent = "create a machine learning algorithm to optimize database queries"

        engine = create_algorithm_engine(
            algorithm_name="intent_filtering_triangle"
        )

        engine.registry.register_algorithm(IntentFilteringTriangle)

        action = ExecuteAlgorithmAction(
            algorithm_name="intent_filtering_triangle",
            input_data=test_intent,
        )

        observation = await engine.execute_action(action)

        assert observation.success is True

        output = observation.output_data
        dimensional_analysis = output['dimensional_analysis']

        # Check dimensional analysis structure
        assert 'clarity_analysis' in dimensional_analysis
        assert 'complexity_analysis' in dimensional_analysis
        assert 'context_analysis' in dimensional_analysis
        assert 'dimensional_relationships' in dimensional_analysis
        assert 'intent_classification' in dimensional_analysis

        # Check each dimension analysis
        for dimension in ['clarity', 'complexity', 'context']:
            dim_analysis = dimensional_analysis[f'{dimension}_analysis']
            assert 'score' in dim_analysis
            assert 'level' in dim_analysis
            assert 'characteristics' in dim_analysis
            assert 0.0 <= dim_analysis['score'] <= 1.0
            assert dim_analysis['level'] in ['very_low', 'low', 'medium', 'high', 'very_high']

        # Check intent classification
        classification = dimensional_analysis['intent_classification']
        assert 'intent_type' in classification
        assert 'processing_recommendation' in classification
        assert 'confidence_level' in classification

        # This intent should have medium-high complexity due to technical terms
        complexity_score = dimensional_analysis['complexity_analysis']['score']
        assert complexity_score > 0.5  # Should be complex due to ML and optimization

    @pytest.mark.asyncio
    async def test_intent_filtering_triangle_visualization_levels(self):
        """Test different visualization detail levels."""
        detail_levels = ["minimal", "standard", "detailed", "comprehensive"]
        test_intent = "analyze customer data"

        for detail_level in detail_levels:
            engine = create_algorithm_engine(
                algorithm_name="intent_filtering_triangle",
                parameters={
                    "visualization_detail_level": detail_level,
                }
            )

            engine.registry.register_algorithm(IntentFilteringTriangle)

            action = ExecuteAlgorithmAction(
                algorithm_name="intent_filtering_triangle",
                input_data=test_intent,
            )

            observation = await engine.execute_action(action)

            assert observation.success is True

            output = observation.output_data
            visualization = output['visualization_data']

            # All levels should have basic components
            assert 'coordinates_3d' in visualization
            assert 'dimensional_levels' in visualization
            assert 'holistic_score_visual' in visualization

            # Detailed and comprehensive should have more components
            if detail_level in ["detailed", "comprehensive"]:
                assert 'dimensional_characteristics' in visualization
                assert 'relationships_graph' in visualization
                assert 'score_components' in visualization

            # Comprehensive should have the most components
            if detail_level == "comprehensive":
                assert 'intent_space_region' in visualization
                assert 'processing_path_visualization' in visualization
                assert 'optimization_vectors' in visualization

    @pytest.mark.asyncio
    async def test_intent_filtering_triangle_filtering_recommendations(self):
        """Test filtering recommendations generation."""
        test_intent = "help me understand this complex algorithm"

        engine = create_algorithm_engine(
            algorithm_name="intent_filtering_triangle",
            parameters={
                "enable_flow_state_analysis": True,
            }
        )

        engine.registry.register_algorithm(IntentFilteringTriangle)

        action = ExecuteAlgorithmAction(
            algorithm_name="intent_filtering_triangle",
            input_data=test_intent,
        )

        observation = await engine.execute_action(action)

        assert observation.success is True

        output = observation.output_data
        recommendations = output['filtering_recommendations']

        # Check recommendation structure
        assert 'primary_recommendation' in recommendations
        assert 'pipeline_recommendation' in recommendations
        assert 'confidence_threshold' in recommendations
        assert 'resource_allocation' in recommendations
        assert 'processing_priority' in recommendations
        assert 'optimization_suggestions' in recommendations
        assert 'estimated_processing_time' in recommendations

        # Validate recommendation values
        assert recommendations['primary_recommendation'] in [
            'direct_execution', 'standard_processing', 'complex_processing'
        ]
        assert recommendations['pipeline_recommendation'] in [
            'single_stage', 'multi_stage', 'full_pipeline'
        ]
        assert recommendations['resource_allocation'] in [
            'minimal', 'standard', 'intensive'
        ]
        assert recommendations['processing_priority'] in [
            'immediate', 'high', 'normal', 'low', 'deferred'
        ]
        assert recommendations['estimated_processing_time'] in [
            'fast', 'medium', 'slow', 'very_slow'
        ]
        assert 0.0 <= recommendations['confidence_threshold'] <= 1.0

    @pytest.mark.asyncio
    async def test_intent_filtering_triangle_analytics(self):
        """Test filtering analytics functionality."""
        engine = create_algorithm_engine(
            algorithm_name="intent_filtering_triangle"
        )

        engine.registry.register_algorithm(IntentFilteringTriangle)

        # Get algorithm instance for analytics
        algorithm = engine.registry.get_algorithm("intent_filtering_triangle")

        # Process multiple intents
        test_intents = [
            "/help",
            "create function",
            "analyze data",
            "what should I do?",
        ]

        for intent in test_intents:
            action = ExecuteAlgorithmAction(
                algorithm_name="intent_filtering_triangle",
                input_data=intent,
            )

            observation = await engine.execute_action(action)
            assert observation.success is True

        # Check analytics
        if hasattr(algorithm, 'get_filtering_analytics'):
            analytics = algorithm.get_filtering_analytics()

            assert 'total_processed' in analytics
            assert analytics['total_processed'] == len(test_intents)
            assert 'average_score' in analytics
            assert 'average_confidence' in analytics
            assert 'recommendation_distribution' in analytics
            assert 'priority_distribution' in analytics

    @pytest.mark.asyncio
    async def test_intent_filtering_triangle_metadata(self):
        """Test algorithm metadata and configuration."""
        algorithm = IntentFilteringTriangle()

        # Test metadata
        metadata = algorithm.metadata
        assert metadata.name == "intent_filtering_triangle"
        assert metadata.version == "1.0.0"
        assert metadata.category == "intent_filtering"
        assert "intent" in metadata.tags
        assert "filtering" in metadata.tags
        assert "triangle" in metadata.tags
        assert metadata.complexity == "O(n log n)"

        # Test parameter definitions
        param_defs = algorithm.get_parameter_definitions()
        assert len(param_defs) > 0

        # Check for key parameters
        param_names = [p.name for p in param_defs]
        assert "clarity_weight" in param_names
        assert "complexity_weight" in param_names
        assert "context_weight" in param_names
        assert "enable_symbiotic_intelligence" in param_names
        assert "enable_flow_state_analysis" in param_names
        assert "filtering_strategy" in param_names

        # Test complexity estimation
        complexity = algorithm.estimate_complexity(100)
        assert isinstance(complexity, float)
        assert complexity > 0

    @pytest.mark.asyncio
    async def test_intent_filtering_triangle_input_validation(self):
        """Test input validation for intent filtering triangle."""
        engine = create_algorithm_engine(
            algorithm_name="intent_filtering_triangle"
        )

        engine.registry.register_algorithm(IntentFilteringTriangle)

        # Test valid inputs
        valid_inputs = [
            "simple text",
            {"intent": "structured intent"},
            {"text": "text field"},
        ]

        for valid_input in valid_inputs:
            action = ExecuteAlgorithmAction(
                algorithm_name="intent_filtering_triangle",
                input_data=valid_input,
            )

            observation = await engine.execute_action(action)
            assert observation.success is True

        # Test invalid inputs
        invalid_inputs = [
            "",           # Empty string
            None,         # None
            123,          # Number
            [],           # Empty list
            {},           # Empty dict
        ]

        for invalid_input in invalid_inputs:
            action = ExecuteAlgorithmAction(
                algorithm_name="intent_filtering_triangle",
                input_data=invalid_input,
            )

            observation = await engine.execute_action(action)
            # Should either fail or handle gracefully
            assert hasattr(observation, 'success')


class TestEnhancedIntentProcessor:
    """Test the EnhancedIntentProcessor integration."""

    @pytest.mark.asyncio
    async def test_enhanced_intent_processor_initialization(self):
        """Test enhanced intent processor initialization."""
        from agent_swarm.core.enhanced_intent_processor import EnhancedIntentProcessor

        processor = EnhancedIntentProcessor(
            enable_adaptive=True,
            enable_filtering=True,
            verbose=False
        )

        # Test initialization
        await processor.initialize()

        # Check that engines are initialized if algorithms are enabled
        if processor.enable_adaptive:
            assert processor.adaptive_engine is not None
        if processor.enable_filtering:
            assert processor.filtering_engine is not None

        # Check statistics initialization
        assert 'total_processed' in processor.processing_stats
        assert processor.processing_stats['total_processed'] == 0

    @pytest.mark.asyncio
    async def test_enhanced_intent_processor_basic_processing(self):
        """Test basic enhanced intent processing."""
        from agent_swarm.core.enhanced_intent_processor import EnhancedIntentProcessor

        processor = EnhancedIntentProcessor(
            enable_adaptive=True,
            enable_filtering=True,
            verbose=False
        )

        await processor.initialize()

        # Test simple intent
        enhanced_intent = await processor.process_intent("/help config")

        # Check enhanced intent structure
        assert hasattr(enhanced_intent, 'original_intent')
        assert hasattr(enhanced_intent, 'processing_confidence')
        assert hasattr(enhanced_intent, 'processing_time')
        assert hasattr(enhanced_intent, 'algorithms_used')

        # Check that processing occurred
        assert enhanced_intent.processing_time > 0
        assert enhanced_intent.processing_confidence >= 0
        assert enhanced_intent.processing_confidence <= 1.0

        # Check that algorithms were used
        assert len(enhanced_intent.algorithms_used) > 0

        # Check original intent
        assert enhanced_intent.original_intent is not None
        assert hasattr(enhanced_intent.original_intent, 'intent_type')
        assert hasattr(enhanced_intent.original_intent, 'confidence')

    @pytest.mark.asyncio
    async def test_enhanced_intent_processor_with_mathematical_algorithms(self):
        """Test enhanced processing with mathematical algorithms enabled."""
        from agent_swarm.core.enhanced_intent_processor import EnhancedIntentProcessor

        processor = EnhancedIntentProcessor(
            enable_adaptive=True,
            enable_filtering=True,
            verbose=False
        )

        await processor.initialize()

        # Test complex intent that should trigger both algorithms
        enhanced_intent = await processor.process_intent(
            "create a machine learning model to predict customer behavior"
        )

        # Check that mathematical algorithms were used
        assert "AdaptiveIntentProcessor" in enhanced_intent.algorithms_used
        assert "IntentFilteringTriangle" in enhanced_intent.algorithms_used

        # Check mathematical analysis results
        assert enhanced_intent.adaptive_analysis is not None
        assert enhanced_intent.filtering_analysis is not None

        # Check 3D coordinates from filtering triangle
        assert enhanced_intent.intent_coordinates is not None
        assert 'clarity' in enhanced_intent.intent_coordinates
        assert 'complexity' in enhanced_intent.intent_coordinates
        assert 'context' in enhanced_intent.intent_coordinates

        # Check flow state
        assert enhanced_intent.flow_state != 'unknown'

        # Check processing recommendations
        assert enhanced_intent.processing_recommendations is not None

    @pytest.mark.asyncio
    async def test_enhanced_intent_processor_confidence_improvement(self):
        """Test that enhanced processing improves confidence."""
        from agent_swarm.core.enhanced_intent_processor import EnhancedIntentProcessor

        processor = EnhancedIntentProcessor(
            enable_adaptive=True,
            enable_filtering=True,
            verbose=False
        )

        await processor.initialize()

        # Test with an ambiguous intent that should benefit from enhancement
        enhanced_intent = await processor.process_intent("I need help with something")

        original_confidence = enhanced_intent.original_intent.confidence
        enhanced_confidence = enhanced_intent.processing_confidence

        # Enhanced confidence should be calculated
        assert enhanced_confidence >= 0
        assert enhanced_confidence <= 1.0

        # For ambiguous intents, the mathematical algorithms should provide additional insight
        # even if confidence doesn't always improve
        assert enhanced_intent.enhanced_reasoning != ""
        assert len(enhanced_intent.algorithms_used) > 1  # Should use multiple algorithms

    @pytest.mark.asyncio
    async def test_enhanced_intent_processor_analytics(self):
        """Test enhanced intent processor analytics."""
        from agent_swarm.core.enhanced_intent_processor import EnhancedIntentProcessor

        processor = EnhancedIntentProcessor(
            enable_adaptive=True,
            enable_filtering=True,
            verbose=False
        )

        await processor.initialize()

        # Process multiple intents
        test_intents = [
            "/help",
            "create a function",
            "analyze data",
            "what should I do?",
        ]

        for intent in test_intents:
            await processor.process_intent(intent)

        # Check analytics
        analytics = processor.get_processing_analytics()

        assert 'total_processed' in analytics
        assert analytics['total_processed'] == len(test_intents)
        assert 'confidence_improvement_rate' in analytics
        assert 'average_processing_time' in analytics
        assert 'algorithm_usage' in analytics

        # Check algorithm usage
        algorithm_usage = analytics['algorithm_usage']
        assert 'IntentDetector' in algorithm_usage  # Should always be used

        # If mathematical algorithms are enabled, they should appear in usage
        if processor.enable_adaptive:
            assert 'AdaptiveIntentProcessor' in algorithm_usage
        if processor.enable_filtering:
            assert 'IntentFilteringTriangle' in algorithm_usage

    @pytest.mark.asyncio
    async def test_enhanced_intent_processor_explanation(self):
        """Test enhanced intent explanation generation."""
        from agent_swarm.core.enhanced_intent_processor import EnhancedIntentProcessor

        processor = EnhancedIntentProcessor(
            enable_adaptive=True,
            enable_filtering=True,
            verbose=False
        )

        await processor.initialize()

        # Process an intent
        enhanced_intent = await processor.process_intent("create a Python function")

        # Get explanation
        explanation = processor.get_intent_explanation(enhanced_intent)

        # Check explanation content
        assert isinstance(explanation, str)
        assert len(explanation) > 0
        assert "Intent" in explanation
        assert "Confidence" in explanation

        # Should contain mathematical analysis if available
        if enhanced_intent.intent_coordinates:
            assert "3D Coordinates" in explanation
        if enhanced_intent.flow_state != 'unknown':
            assert "Flow State" in explanation

    @pytest.mark.asyncio
    async def test_enhanced_intent_processor_fallback(self):
        """Test enhanced intent processor fallback behavior."""
        from agent_swarm.core.enhanced_intent_processor import EnhancedIntentProcessor

        # Test with mathematical algorithms disabled
        processor = EnhancedIntentProcessor(
            enable_adaptive=False,
            enable_filtering=False,
            verbose=False
        )

        await processor.initialize()

        # Process intent - should fall back to traditional detection
        enhanced_intent = await processor.process_intent("test intent")

        # Should still work with traditional intent detection
        assert enhanced_intent.original_intent is not None
        assert enhanced_intent.processing_confidence >= 0
        assert enhanced_intent.processing_time > 0

        # Mathematical analysis should be None
        assert enhanced_intent.adaptive_analysis is None
        assert enhanced_intent.filtering_analysis is None
        assert enhanced_intent.intent_coordinates is None

        # Should only use traditional detector
        assert enhanced_intent.algorithms_used == ["IntentDetector"]

    @pytest.mark.asyncio
    async def test_enhanced_intent_processor_error_handling(self):
        """Test enhanced intent processor error handling."""
        from agent_swarm.core.enhanced_intent_processor import EnhancedIntentProcessor

        processor = EnhancedIntentProcessor(
            enable_adaptive=True,
            enable_filtering=True,
            verbose=False
        )

        await processor.initialize()

        # Test with various edge cases
        edge_cases = [
            "",  # Empty string
            "   ",  # Whitespace only
            "a" * 1000,  # Very long string
        ]

        for edge_case in edge_cases:
            try:
                enhanced_intent = await processor.process_intent(edge_case)

                # Should handle gracefully
                assert enhanced_intent is not None
                assert hasattr(enhanced_intent, 'original_intent')
                assert hasattr(enhanced_intent, 'processing_confidence')

            except Exception as e:
                # If it fails, it should fail gracefully
                assert isinstance(e, Exception)


class TestEnhancedLLMBackend:
    """Test the Enhanced LLM Backend with thinking capabilities."""

    @pytest.mark.asyncio
    async def test_enhanced_llm_backend_initialization(self):
        """Test enhanced LLM backend initialization."""
        from agent_swarm.backends.enhanced_llm_backend import EnhancedLLMBackend

        backend = EnhancedLLMBackend()

        # Test initialization
        await backend.initialize()

        # Check that providers are initialized
        assert hasattr(backend, 'providers')
        assert hasattr(backend, 'model_capabilities')
        assert hasattr(backend, 'performance_stats')

        # Check statistics initialization
        assert 'total_requests' in backend.performance_stats
        assert backend.performance_stats['total_requests'] == 0

    @pytest.mark.asyncio
    async def test_thinking_configuration(self):
        """Test thinking configuration and modes."""
        from agent_swarm.backends.enhanced_llm_backend import (
            EnhancedLLMBackend, EnhancedLLMRequest, ThinkingConfig, ThinkingMode
        )
        from agent_swarm.backends.abstract import Message

        backend = EnhancedLLMBackend()
        await backend.initialize()

        # Test different thinking modes
        thinking_modes = [
            ThinkingMode.DISABLED,
            ThinkingMode.ENABLED,
            ThinkingMode.ADAPTIVE,
            ThinkingMode.COMPLEX_ONLY,
        ]

        for mode in thinking_modes:
            thinking_config = ThinkingConfig(mode=mode)

            request = EnhancedLLMRequest(
                messages=[Message(role="user", content="Test message")],
                model_id="test",
                thinking_config=thinking_config,
            )

            # Test thinking configuration
            assert request.thinking_config.mode == mode
            assert hasattr(thinking_config, 'complexity_threshold')
            assert hasattr(thinking_config, 'hide_thinking')

    @pytest.mark.asyncio
    async def test_intent_complexity_analysis(self):
        """Test intent complexity analysis integration."""
        from agent_swarm.backends.enhanced_llm_backend import (
            EnhancedLLMBackend, EnhancedLLMRequest
        )
        from agent_swarm.backends.abstract import Message

        backend = EnhancedLLMBackend()
        await backend.initialize()

        # Test with different complexity levels
        test_cases = [
            ("Hello", "simple"),
            ("What is machine learning?", "medium"),
            ("Design a distributed system architecture for handling millions of users", "complex"),
        ]

        for content, expected_complexity in test_cases:
            request = EnhancedLLMRequest(
                messages=[Message(role="user", content=content)],
                model_id="test",
            )

            # Test complexity analysis
            intent_analysis = await backend._analyze_intent_complexity(request)

            if intent_analysis:  # Only test if intent processor is available
                assert 'complexity_score' in intent_analysis
                assert 'overall_confidence' in intent_analysis
                assert 'flow_state' in intent_analysis

                complexity_score = intent_analysis['complexity_score']
                assert 0.0 <= complexity_score <= 1.0

    @pytest.mark.asyncio
    async def test_model_selection(self):
        """Test intelligent model selection."""
        from agent_swarm.backends.enhanced_llm_backend import (
            EnhancedLLMBackend, EnhancedLLMRequest, ThinkingConfig, ThinkingMode
        )
        from agent_swarm.backends.abstract import Message

        backend = EnhancedLLMBackend()
        await backend.initialize()

        # Test priority-based selection
        priorities = ["fast", "normal", "quality"]

        for priority in priorities:
            request = EnhancedLLMRequest(
                messages=[Message(role="user", content="Test message")],
                model_id="auto",
                priority=priority,
                thinking_config=ThinkingConfig(mode=ThinkingMode.ADAPTIVE),
            )

            # Test model selection
            try:
                selected_model = await backend._select_optimal_model(request)

                if selected_model:  # Only test if models are available
                    assert isinstance(selected_model, str)
                    assert selected_model in backend.providers or selected_model is None

            except Exception:
                # Model selection may fail if no providers available
                pass

    @pytest.mark.asyncio
    async def test_thinking_adaptation(self):
        """Test adaptive thinking based on complexity."""
        from agent_swarm.backends.enhanced_llm_backend import (
            EnhancedLLMBackend, EnhancedLLMRequest, ThinkingConfig, ThinkingMode
        )
        from agent_swarm.backends.abstract import Message

        backend = EnhancedLLMBackend()
        await backend.initialize()

        # Test adaptive thinking configuration
        test_cases = [
            {
                'content': 'Hello',
                'expected_thinking': False,  # Simple request
            },
            {
                'content': 'Design a complex distributed system with microservices architecture',
                'expected_thinking': True,  # Complex request
            },
        ]

        for case in test_cases:
            request = EnhancedLLMRequest(
                messages=[Message(role="user", content=case['content'])],
                model_id="test",
                thinking_config=ThinkingConfig(mode=ThinkingMode.ADAPTIVE),
            )

            # Analyze intent complexity
            intent_analysis = await backend._analyze_intent_complexity(request)

            # Configure thinking
            thinking_config = await backend._configure_thinking(request, intent_analysis)

            # Check thinking configuration
            if intent_analysis:  # Only test if analysis is available
                complexity_score = intent_analysis.get('complexity_score', 0)

                if complexity_score > 0.7:
                    # High complexity should enable thinking
                    assert thinking_config.mode in [ThinkingMode.ENABLED, ThinkingMode.ADAPTIVE]
                elif complexity_score < 0.3:
                    # Low complexity might disable thinking
                    pass  # Adaptive behavior may vary

    @pytest.mark.asyncio
    async def test_performance_statistics(self):
        """Test performance statistics tracking."""
        from agent_swarm.backends.enhanced_llm_backend import EnhancedLLMBackend

        backend = EnhancedLLMBackend()
        await backend.initialize()

        # Check initial statistics
        stats = backend.get_performance_stats()

        assert 'total_requests' in stats
        assert 'thinking_requests' in stats
        assert 'average_response_time' in stats
        assert 'thinking_time_saved' in stats
        assert 'available_models' in stats
        assert 'model_capabilities' in stats

        # Check initial values
        assert stats['total_requests'] == 0
        assert stats['thinking_requests'] == 0
        assert stats['average_response_time'] == 0.0

    @pytest.mark.asyncio
    async def test_model_capabilities(self):
        """Test model capability detection."""
        from agent_swarm.backends.enhanced_llm_backend import (
            EnhancedLLMBackend, ModelCapability
        )

        backend = EnhancedLLMBackend()
        await backend.initialize()

        # Check available models
        models = backend.get_available_models()

        for model_id, model_info in models.items():
            # Check model info structure
            assert 'config' in model_info
            assert 'capabilities' in model_info
            assert 'thinking_supported' in model_info
            assert 'structured_output_supported' in model_info
            assert 'tool_calling_supported' in model_info
            assert 'streaming_supported' in model_info

            # Check capability consistency
            capabilities = model_info['capabilities']

            if model_info['thinking_supported']:
                assert ModelCapability.THINKING.value in capabilities
            if model_info['structured_output_supported']:
                assert ModelCapability.STRUCTURED_OUTPUT.value in capabilities
            if model_info['tool_calling_supported']:
                assert ModelCapability.TOOL_CALLING.value in capabilities
            if model_info['streaming_supported']:
                assert ModelCapability.STREAMING.value in capabilities

    @pytest.mark.asyncio
    async def test_enhanced_request_structure(self):
        """Test enhanced request data structure."""
        from agent_swarm.backends.enhanced_llm_backend import (
            EnhancedLLMRequest, ThinkingConfig, ThinkingMode
        )
        from agent_swarm.backends.abstract import Message

        # Test comprehensive request structure
        request = EnhancedLLMRequest(
            messages=[Message(role="user", content="Test message")],
            model_id="test-model",
            temperature=0.7,
            max_tokens=1000,
            thinking_config=ThinkingConfig(
                mode=ThinkingMode.ADAPTIVE,
                complexity_threshold=0.5,
                hide_thinking=False,
            ),
            response_schema={"type": "object"},
            tools=[{"name": "test_tool"}],
            stream=False,
            priority="normal",
        )

        # Check request structure
        assert len(request.messages) == 1
        assert request.model_id == "test-model"
        assert request.temperature == 0.7
        assert request.max_tokens == 1000
        assert request.thinking_config.mode == ThinkingMode.ADAPTIVE
        assert request.response_schema is not None
        assert request.tools is not None
        assert request.stream is False
        assert request.priority == "normal"

    @pytest.mark.asyncio
    async def test_enhanced_response_structure(self):
        """Test enhanced response data structure."""
        from agent_swarm.backends.enhanced_llm_backend import EnhancedLLMResponse

        # Test comprehensive response structure
        response = EnhancedLLMResponse(
            content="Test response",
            model="test-model",
            usage={"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30},
            thinking="Test thinking process",
            thinking_time=1.5,
            structured_data={"key": "value"},
            tool_calls=[{"name": "test_tool"}],
            response_time=2.0,
            thinking_enabled=True,
            confidence=0.85,
            complexity_score=0.6,
            metadata={"test": "data"},
        )

        # Check response structure
        assert response.content == "Test response"
        assert response.model == "test-model"
        assert response.usage["total_tokens"] == 30
        assert response.thinking == "Test thinking process"
        assert response.thinking_time == 1.5
        assert response.structured_data is not None
        assert response.tool_calls is not None
        assert response.response_time == 2.0
        assert response.thinking_enabled is True
        assert response.confidence == 0.85
        assert response.complexity_score == 0.6
        assert response.metadata is not None


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
