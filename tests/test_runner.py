#!/usr/bin/env python3
"""
Comprehensive test runner for Agent Swarm.
"""

import sys
import subprocess
import argparse
import shutil
from pathlib import Path


def get_python_executable():
    """Get the correct Python executable."""
    # Try to use the same Python that's running this script
    if sys.executable:
        return sys.executable

    # Fallback to searching for python executables
    for python_cmd in ['python3', 'python']:
        if shutil.which(python_cmd):
            return python_cmd

    raise RuntimeError("No Python executable found!")


def run_command(cmd, description=""):
    """Run a command and return success status."""
    print(f"🔄 {description}")
    print(f"   Command: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)

        if result.returncode == 0:
            print(f"✅ {description} - PASSED")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} - FAILED")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()}")
            if result.stderr.strip():
                print(f"   Error: {result.stderr.strip()}")
            return False

    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False


def run_tests(test_type="all", verbose=False, coverage=False, markers=None):
    """Run tests based on type and options."""

    # Get correct Python executable
    python_exe = get_python_executable()

    # Base pytest command
    cmd = [python_exe, "-m", "pytest"]

    if verbose:
        cmd.append("-v")

    if coverage:
        cmd.extend([
            "--cov=src/agent_swarm",
            "--cov-report=html:htmlcov",
            "--cov-report=xml:coverage.xml",
            "--cov-report=term-missing"
        ])

    # Add markers if specified
    if markers:
        for marker in markers:
            cmd.extend(["-m", marker])

    # Determine test paths based on type
    if test_type == "unit":
        cmd.append("tests/unit/")
        description = "Unit Tests"
    elif test_type == "integration":
        cmd.append("tests/integration/")
        description = "Integration Tests"
    elif test_type == "e2e":
        cmd.append("tests/e2e/")
        description = "End-to-End Tests"
    elif test_type == "mcp":
        cmd.extend(["-m", "mcp"])
        description = "MCP Tests"
    elif test_type == "all":
        cmd.append("tests/")
        description = "All Tests"
    else:
        print(f"❌ Unknown test type: {test_type}")
        return False

    return run_command(cmd, description)


def run_quality_checks():
    """Run code quality checks."""
    python_exe = get_python_executable()

    checks = [
        ([python_exe, "-m", "black", "--check", "src/", "tests/"], "Black Code Formatting"),
        ([python_exe, "-m", "isort", "--check-only", "src/", "tests/"], "Import Sorting"),
        ([python_exe, "-m", "flake8", "src/", "tests/"], "Flake8 Linting"),
        ([python_exe, "-m", "mypy", "src/agent_swarm"], "Type Checking"),
    ]

    results = []
    for cmd, description in checks:
        results.append(run_command(cmd, description))

    return all(results)


def run_security_checks():
    """Run security checks."""
    python_exe = get_python_executable()

    checks = [
        ([python_exe, "-m", "bandit", "-r", "src/"], "Security Scan (Bandit)"),
        ([python_exe, "-m", "safety", "check"], "Dependency Security Check"),
    ]

    results = []
    for cmd, description in checks:
        results.append(run_command(cmd, description))

    return all(results)


def run_performance_tests():
    """Run performance tests."""
    python_exe = get_python_executable()
    cmd = [python_exe, "-m", "pytest", "-m", "slow", "tests/", "-v"]
    return run_command(cmd, "Performance Tests")


def generate_test_report():
    """Generate comprehensive test report."""
    print("\n" + "="*60)
    print("📊 COMPREHENSIVE TEST REPORT")
    print("="*60)

    # Test structure analysis
    test_files = list(Path("tests").rglob("test_*.py"))
    unit_tests = list(Path("tests/unit").rglob("test_*.py"))
    integration_tests = list(Path("tests/integration").rglob("test_*.py"))
    e2e_tests = list(Path("tests/e2e").rglob("test_*.py"))

    print(f"\n📁 Test Structure:")
    print(f"   Total test files: {len(test_files)}")
    print(f"   Unit tests: {len(unit_tests)}")
    print(f"   Integration tests: {len(integration_tests)}")
    print(f"   E2E tests: {len(e2e_tests)}")

    # List test files with details
    print(f"\n📋 Test Files:")
    for category, files in [
        ("Unit", unit_tests),
        ("Integration", integration_tests),
        ("E2E", e2e_tests)
    ]:
        if files:
            print(f"   {category}:")
            for file in sorted(files):
                print(f"     • {file.relative_to(Path('tests'))}")

    # Coverage analysis
    print(f"\n📈 Coverage Analysis:")
    coverage_file = Path("htmlcov/index.html")
    if coverage_file.exists():
        print(f"   HTML Report: {coverage_file}")

    coverage_xml = Path("coverage.xml")
    if coverage_xml.exists():
        print(f"   XML Report: {coverage_xml}")

    # Test categories breakdown
    print(f"\n🎯 Test Categories:")
    print(f"   • Core Components: {len([f for f in unit_tests if any(x in f.name for x in ['llm', 'router', 'agent', 'context'])])}")
    print(f"   • MCP Tools: {len([f for f in test_files if 'mcp' in f.name])}")
    print(f"   • CLI Tools: {len([f for f in test_files if any(x in f.name for x in ['cli', 'tool'])])}")
    print(f"   • Workflows: {len([f for f in integration_tests if 'workflow' in f.name])}")

    # Recent additions
    print(f"\n🆕 Recent Test Additions:")
    recent_tests = [
        "test_unified_context.py - Comprehensive UnifiedContext testing",
        "test_llm_router.py - Complete LLM routing functionality",
        "test_cli_agent.py - CLI agent comprehensive testing",
        "test_tools.py - All Agent Swarm tools testing",
        "test_agent_workflows.py - End-to-end workflow testing",
        "test_mcp_tools.py - MCP tools unit testing",
        "test_mcp_integration.py - MCP integration testing"
    ]

    for test in recent_tests:
        print(f"   ✅ {test}")

    print("\n" + "="*60)


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="Agent Swarm Test Runner")
    parser.add_argument(
        "test_type",
        choices=["all", "unit", "integration", "e2e", "mcp", "quality", "security", "performance"],
        default="all",
        nargs="?",
        help="Type of tests to run"
    )
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    parser.add_argument("-c", "--coverage", action="store_true", help="Generate coverage report")
    parser.add_argument("-m", "--markers", nargs="+", help="Pytest markers to include")
    parser.add_argument("--report", action="store_true", help="Generate test report")
    parser.add_argument("--all-checks", action="store_true", help="Run all checks (tests + quality + security)")

    args = parser.parse_args()

    print("🚀 Agent Swarm Test Runner")
    print("="*50)

    success = True

    if args.report:
        generate_test_report()
        return

    if args.all_checks:
        # Run everything
        print("\n🔍 Running comprehensive test suite...")

        success &= run_tests("all", args.verbose, args.coverage)
        success &= run_quality_checks()
        success &= run_security_checks()

        generate_test_report()

    elif args.test_type == "quality":
        success = run_quality_checks()
    elif args.test_type == "security":
        success = run_security_checks()
    elif args.test_type == "performance":
        success = run_performance_tests()
    else:
        success = run_tests(args.test_type, args.verbose, args.coverage, args.markers)

    print("\n" + "="*50)
    if success:
        print("🎉 All checks passed!")
        sys.exit(0)
    else:
        print("❌ Some checks failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
