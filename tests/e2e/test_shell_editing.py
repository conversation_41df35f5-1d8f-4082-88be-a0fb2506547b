"""
End-to-end tests for the interactive shell with editing capabilities.

Tests the complete user workflow including shell initialization,
command execution, and file editing operations.
"""

import pytest
import tempfile
import asyncio
import time
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from io import StringIO
import sys


class TestShellE2E:
    """End-to-end tests for the interactive shell."""
    
    @pytest.fixture
    async def temp_project(self):
        """Create a temporary project for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir)
            
            # Create a realistic project structure
            (project_root / "src").mkdir()
            (project_root / "tests").mkdir()
            (project_root / "docs").mkdir()
            
            # Python files
            (project_root / "src" / "__init__.py").write_text("")
            (project_root / "src" / "main.py").write_text("""
#!/usr/bin/env python3
\"\"\"Main application module.\"\"\"

def main():
    print("Hello, <PERSON>!")
    return 0

if __name__ == "__main__":
    exit(main())
""".strip())
            
            (project_root / "src" / "utils.py").write_text("""
\"\"\"Utility functions.\"\"\"

def helper_function(x, y):
    return x + y

def another_helper(data):
    return len(data)
""".strip())
            
            # Configuration files
            (project_root / "pyproject.toml").write_text("""
[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "test-project"
version = "0.1.0"
description = "A test project"
""".strip())
            
            (project_root / "README.md").write_text("""
# Test Project

This is a test project for Agent Swarm E2E testing.

## Features

- Python application
- Modular structure
- Configuration management
""".strip())
            
            yield project_root
    
    @pytest.fixture
    async def mock_shell(self, temp_project):
        """Create a mock shell for E2E testing."""
        from agent_swarm.cli.interactive_shell import InteractiveShell
        
        # Mock the LLM and agent components
        with patch('agent_swarm.cli.interactive_shell.create_ollama_llm') as mock_llm_factory, \
             patch('agent_swarm.cli.interactive_shell.create_coding_agent') as mock_agent_factory, \
             patch('agent_swarm.cli.interactive_shell.LLMRouter') as mock_router, \
             patch('agent_swarm.cli.interactive_shell.UnifiedContext') as mock_context:
            
            # Setup mocks
            mock_llm = AsyncMock()
            mock_llm.list_models.return_value = ["llama3.2:3b", "codellama:7b"]
            mock_llm.is_available.return_value = True
            mock_llm.close.return_value = None
            mock_llm_factory.return_value = mock_llm
            
            mock_agent = AsyncMock()
            mock_agent.dev_rag = Mock()
            mock_agent.dev_rag.get_project_stats.return_value = {"total_files_indexed": 5}
            mock_agent_factory.return_value = mock_agent
            
            mock_router_instance = Mock()
            mock_router.return_value = mock_router_instance
            
            mock_context_instance = AsyncMock()
            mock_context_instance.initialize.return_value = None
            mock_context_instance.get_project_analysis.return_value = None
            mock_context.return_value = mock_context_instance
            
            # Create shell
            shell = InteractiveShell(verbose=True, auto_index=False)
            
            # Initialize with mocked components
            success = await shell.initialize(str(temp_project))
            assert success
            
            yield shell
    
    @pytest.mark.asyncio
    async def test_shell_initialization(self, mock_shell, temp_project):
        """Test shell initialization process."""
        assert mock_shell.project_path == temp_project
        assert mock_shell.agent is not None
        assert mock_shell.context_engine is not None
        assert mock_shell.llm_router is not None
    
    @pytest.mark.asyncio
    async def test_double_ctrl_c_handling(self, mock_shell):
        """Test double Ctrl+C graceful quit functionality."""
        # Test single Ctrl+C
        mock_shell._last_interrupt_time = 0
        current_time = time.time()
        
        # Simulate first Ctrl+C
        mock_shell._last_interrupt_time = current_time
        
        # Simulate second Ctrl+C within threshold
        time_diff = current_time + 1.0  # Within 2 second threshold
        
        # Check if double Ctrl+C would be detected
        is_double_interrupt = (time_diff - mock_shell._last_interrupt_time) < mock_shell._interrupt_threshold
        assert is_double_interrupt
        
        # Test that threshold works correctly
        time_diff_long = current_time + 3.0  # Beyond 2 second threshold
        is_not_double_interrupt = (time_diff_long - mock_shell._last_interrupt_time) >= mock_shell._interrupt_threshold
        assert is_not_double_interrupt
    
    @pytest.mark.asyncio
    async def test_command_help(self, mock_shell):
        """Test help command functionality."""
        # Capture output
        captured_output = StringIO()
        
        with patch('builtins.print', side_effect=lambda *args, **kwargs: captured_output.write(' '.join(map(str, args)) + '\n')):
            await mock_shell.cmd_help([])
        
        output = captured_output.getvalue()
        
        # Check that help contains expected commands
        assert "edit" in output.lower()
        assert "smart-edit" in output.lower() or "smart edit" in output.lower()
        assert "diff" in output.lower()
        assert "revert" in output.lower()
        assert "help" in output.lower()
        assert "exit" in output.lower()
    
    @pytest.mark.asyncio
    async def test_project_command(self, mock_shell, temp_project):
        """Test project command functionality."""
        # Test project info
        captured_output = StringIO()
        
        with patch('builtins.print', side_effect=lambda *args, **kwargs: captured_output.write(' '.join(map(str, args)) + '\n')):
            await mock_shell.cmd_project([str(temp_project)])
        
        # Should not error and should show project path
        assert mock_shell.project_path == temp_project
    
    @pytest.mark.asyncio
    async def test_file_operations_e2e(self, mock_shell, temp_project):
        """Test end-to-end file operations."""
        # Test that we can access files through the shell
        main_file = temp_project / "src" / "main.py"
        assert main_file.exists()
        
        # Test reading file content
        content = main_file.read_text()
        assert "Hello, World!" in content
        assert "def main():" in content
    
    @pytest.mark.asyncio
    async def test_editing_command_availability(self, mock_shell):
        """Test that editing commands are available and callable."""
        # Check that editing commands exist
        assert 'edit' in mock_shell.commands
        assert 'smart-edit' in mock_shell.commands
        assert 'diff' in mock_shell.commands
        assert 'revert' in mock_shell.commands
        
        # Check that they are callable
        assert callable(mock_shell.commands['edit'])
        assert callable(mock_shell.commands['smart-edit'])
        assert callable(mock_shell.commands['diff'])
        assert callable(mock_shell.commands['revert'])
    
    @pytest.mark.asyncio
    async def test_command_error_handling(self, mock_shell):
        """Test command error handling."""
        captured_output = StringIO()
        
        with patch('builtins.print', side_effect=lambda *args, **kwargs: captured_output.write(' '.join(map(str, args)) + '\n')):
            # Test unknown command
            await mock_shell.handle_command("nonexistent_command")
        
        output = captured_output.getvalue()
        assert "unknown command" in output.lower() or "not found" in output.lower()
    
    @pytest.mark.asyncio
    async def test_version_command(self, mock_shell):
        """Test version command."""
        captured_output = StringIO()
        
        with patch('builtins.print', side_effect=lambda *args, **kwargs: captured_output.write(' '.join(map(str, args)) + '\n')):
            await mock_shell.cmd_version([])
        
        output = captured_output.getvalue()
        # Should show version information
        assert len(output) > 0


class TestEditingWorkflowE2E:
    """End-to-end tests for editing workflows."""
    
    @pytest.fixture
    async def temp_project(self):
        """Create a temporary project for editing tests."""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir)
            
            # Create test files
            (project_root / "test.py").write_text("""
def hello():
    print("Hello, World!")

def goodbye():
    print("Goodbye!")
""".strip())
            
            (project_root / "config.json").write_text("""
{
    "name": "test-app",
    "version": "1.0.0",
    "debug": true
}
""".strip())
            
            yield project_root
    
    @pytest.mark.asyncio
    async def test_editing_tools_e2e(self, temp_project):
        """Test editing tools end-to-end workflow."""
        try:
            from agent_swarm.tools.editing_tools import EditingTools
            
            # Initialize editing tools
            tools = EditingTools(temp_project)
            await tools.initialize()
            
            # Test file reading
            result = await tools.read_file("test.py")
            assert result["success"]
            assert "Hello, World!" in result["content"]
            
            # Test file writing
            new_content = result["content"].replace("Hello, World!", "Hello, E2E!")
            write_result = await tools.write_file(
                "test.py", new_content, "E2E: Update greeting"
            )
            assert write_result["success"]
            
            # Test change tracking
            changes_result = await tools.list_changes()
            assert changes_result["success"]
            assert len(changes_result["changes"]) >= 1
            
            # Test revert
            change_id = write_result["change_id"]
            revert_result = await tools.revert_change(change_id)
            assert revert_result["success"]
            
        except ImportError:
            pytest.skip("Editing tools dependencies not available")
    
    @pytest.mark.asyncio
    async def test_professional_editing_e2e(self, temp_project):
        """Test professional editing system end-to-end."""
        try:
            from agent_swarm.tools.editing.factory import EditingSystemFactory, EditingSystemConfig
            
            # Create editing system
            config = EditingSystemConfig()
            config.storage_dir = temp_project / ".agent-swarm"
            config.ai_enabled = False
            
            system = EditingSystemFactory.create_default_system(config)
            await system.initialize()
            
            # Test file operations
            file_path = temp_project / "test.py"
            content = await system.file_editor.read_file(file_path)
            assert "Hello, World!" in content
            
            # Test validation
            from agent_swarm.tools.editing.core.models import EditOperation, EditOperationType
            
            operation = EditOperation(
                operation_type=EditOperationType.REPLACE_ALL,
                file_path=file_path,
                content=content.replace("Hello, World!", "Hello, Professional!")
            )
            
            is_valid = await system.validator.validate_safety(operation)
            assert is_valid
            
            # Test change application
            change = await system.file_editor.apply_edit(operation)
            assert change.has_changes
            
            # Cleanup
            await system.shutdown()
            
        except ImportError:
            pytest.skip("Professional editing dependencies not available")


class TestShellInteractionE2E:
    """End-to-end tests for shell interaction patterns."""
    
    @pytest.mark.asyncio
    async def test_command_parsing(self):
        """Test command parsing functionality."""
        from agent_swarm.cli.interactive_shell import InteractiveShell
        
        shell = InteractiveShell(verbose=False, auto_index=False)
        
        # Test command parsing
        test_cases = [
            ("/help", "help", []),
            ("/edit file.py", "edit", ["file.py"]),
            ("/smart-edit file.py add comments", "smart-edit", ["file.py", "add", "comments"]),
            ("/diff change123", "diff", ["change123"]),
            ("/revert abc12345", "revert", ["abc12345"]),
        ]
        
        for command_line, expected_cmd, expected_args in test_cases:
            # Remove leading slash
            parts = command_line[1:].split()
            cmd = parts[0].lower()
            args = parts[1:] if len(parts) > 1 else []
            
            assert cmd == expected_cmd
            assert args == expected_args
    
    @pytest.mark.asyncio
    async def test_error_recovery(self):
        """Test error recovery in shell operations."""
        from agent_swarm.cli.interactive_shell import InteractiveShell
        
        shell = InteractiveShell(verbose=False, auto_index=False)
        
        # Test that shell can handle errors gracefully
        try:
            # This should not crash the shell
            await shell.handle_command("nonexistent_command with args")
        except Exception as e:
            # Should not raise unhandled exceptions
            pytest.fail(f"Shell should handle errors gracefully, but raised: {e}")
    
    @pytest.mark.asyncio
    async def test_shell_state_management(self):
        """Test shell state management."""
        from agent_swarm.cli.interactive_shell import InteractiveShell
        
        shell = InteractiveShell(verbose=False, auto_index=False)
        
        # Test initial state
        assert shell.conversation_history == []
        assert shell.current_model == "llama3.2:3b"
        assert shell.project_path is None
        
        # Test state updates
        shell.conversation_history.append(Mock())
        assert len(shell.conversation_history) == 1
        
        # Test clear command
        await shell.cmd_clear([])
        assert len(shell.conversation_history) == 0


class TestSystemIntegrationE2E:
    """End-to-end tests for system integration."""
    
    @pytest.mark.asyncio
    async def test_dependency_availability(self):
        """Test that required dependencies are available."""
        # Test core dependencies
        try:
            import agent_swarm
            assert hasattr(agent_swarm, '__version__')
        except ImportError:
            pytest.fail("Core agent_swarm package not available")
        
        # Test optional editing dependencies
        editing_deps_available = True
        try:
            import unidiff
            import chardet
        except ImportError:
            editing_deps_available = False
        
        if not editing_deps_available:
            pytest.skip("Optional editing dependencies not available")
    
    @pytest.mark.asyncio
    async def test_configuration_system(self):
        """Test configuration system integration."""
        try:
            from agent_swarm.utils.config import load_config, create_example_config
            
            # Test that config system works
            config = load_config()
            assert config is not None
            
            # Test example config creation
            example_config = create_example_config()
            assert example_config is not None
            
        except ImportError:
            pytest.skip("Configuration system not available")
    
    @pytest.mark.asyncio
    async def test_cli_tools_integration(self):
        """Test CLI tools integration."""
        try:
            from agent_swarm.cli.tools.file_system import FileSystemTools
            from agent_swarm.cli.tools.development import DevelopmentTools
            from agent_swarm.cli.tools.network import NetworkTools
            from agent_swarm.cli.tools.system import SystemTools
            
            # Test that tools can be instantiated
            fs_tools = FileSystemTools()
            dev_tools = DevelopmentTools()
            network_tools = NetworkTools()
            system_tools = SystemTools()
            
            assert fs_tools is not None
            assert dev_tools is not None
            assert network_tools is not None
            assert system_tools is not None
            
        except ImportError:
            pytest.skip("CLI tools not available")
