#!/usr/bin/env python3
"""
Test the fixes for @ command autocomplete and multi-step response display.
"""

import sys
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_autocomplete_fix():
    """Test that autocomplete is properly configured."""
    print("🧪 Testing Autocomplete Fix...")
    
    try:
        from agent_swarm.cli.autocomplete import AdvancedCompleter
        import readline
        
        # Create completer
        completer = AdvancedCompleter()
        completer.project_path = Path("/home/<USER>/workspace/local-llm/agent-mode")
        
        # Check if @ is removed from delimiters
        delims = readline.get_completer_delims()
        has_at = '@' in delims
        
        print(f"   @ in readline delimiters: {has_at} (should be False)")
        
        # Test @ command detection
        test_cases = [
            ("explain @README", "README", True),
            ("@src/", "src/", True),
            ("regular text", "text", False),
        ]
        
        for line, text, expected in test_cases:
            result = completer._is_at_command_context(line, text)
            status = "✅" if result == expected else "❌"
            print(f"   {status} '{line}' -> {result}")
        
        # Test completion
        completions = completer.get_completions("explain @README", "README")
        print(f"   Completions for '@README': {len(completions)}")
        
        if not has_at and len(completions) > 0:
            print("✅ Autocomplete fix working!")
            return True
        else:
            print("❌ Autocomplete fix needs work")
            return False
            
    except Exception as e:
        print(f"❌ Autocomplete test failed: {e}")
        return False

def test_multi_step_disabled():
    """Test that multi-step orchestrator is disabled."""
    print("\n🧪 Testing Multi-Step Orchestrator Disabled...")
    
    try:
        # Check if the orchestrator is disabled in the code
        with open("src/agent_swarm/cli/interactive_shell.py", "r") as f:
            content = f.read()
        
        # Look for the disabled condition
        if "if False and self.orchestrator:" in content:
            print("✅ Multi-step orchestrator is disabled")
            return True
        else:
            print("❌ Multi-step orchestrator is still enabled")
            return False
            
    except Exception as e:
        print(f"❌ Multi-step test failed: {e}")
        return False

def test_input_method():
    """Test that input method uses regular input() for readline."""
    print("\n🧪 Testing Input Method...")
    
    try:
        # Check if the shell uses regular input() instead of Rich Prompt.ask()
        with open("src/agent_swarm/cli/interactive_shell.py", "r") as f:
            content = f.read()
        
        # Look for the fixed input method
        if "user_input = input(prompt).strip()" in content:
            print("✅ Using regular input() for readline support")
            return True
        else:
            print("❌ Still using Rich Prompt.ask()")
            return False
            
    except Exception as e:
        print(f"❌ Input method test failed: {e}")
        return False

def main():
    """Run all fix tests."""
    print("🔧 Testing Agent Swarm Fixes")
    print("=" * 50)
    
    tests = [
        test_autocomplete_fix,
        test_multi_step_disabled,
        test_input_method,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes working!")
        print("\n💡 What's fixed:")
        print("   ✅ @ command autocomplete with TAB")
        print("   ✅ Multi-step orchestrator disabled (no more empty responses)")
        print("   ✅ Regular input() for readline support")
        print("\n🚀 Try these now:")
        print("   • Start: agent-swarm")
        print("   • Type: explain @README<TAB>")
        print("   • Should complete to: explain @README.md")
        print("   • Press Enter and get a real response!")
        return True
    else:
        print(f"❌ {total - passed} fixes need work")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
