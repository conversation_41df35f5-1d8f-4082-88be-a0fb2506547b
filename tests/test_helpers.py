import pytest
from agent_swarm.utils.helpers import format_file_size, validate_model_name, safe_filename

def test_format_file_size():
    # Test valid file sizes
    assert format_file_size(1024) == "1.00 KB"
    assert format_file_size(1048576) == "1.01 MB"

    # Test invalid file size (negative)
    with pytest.raises(ValueError):
        format_file_size(-1)

    # Test invalid file size (zero)
    assert format_file_size(0) == "0 B"

def test_format_file_size_edge_cases():
    # Test very large file sizes
    assert format_file_size(2**64 - 1) == "2.00 GB"

    # Test very small file sizes
    assert format_file_size(1024 // 1024) == "0.01 MB"

def test_validate_model_name():
    # Test valid model names
    assert validate_model_name("model-name")
    assert validate_model_name("another-model-name")

    # Test invalid model names (too short)
    assert not validate_model_name("m")

    # Test invalid model names (too long)
    assert not validate_model_name("a" * 51)

def test_validate_model_name_edge_cases():
    # Test model names with special characters
    assert validate_model_name("model-name:example")
    assert validate_model_name("model-name-!example")

    # Test model names with invalid characters
    assert not validate_model_name("model-name@example")

def test_safe_filename():
    # Test valid filenames
    assert safe_filename("example.txt") == "example_txt"
    assert safe_filename("another-example.txt") == "another_example_txt"

    # Test filenames with spaces
    assert safe_filename("example with space.txt") == "example_with_space_txt"

    # Test filenames with special characters
    assert safe_filename("example!txt") == "example_txt"

def test_safe_filename_edge_cases():
    # Test filenames with invalid characters (e.g. newline)
    assert safe_filename("\nexample.txt") == "example_txt"

def test_safe_filename_invalid_characters():
    # Test filenames with invalid characters (e.g. tab, backslash)
    assert safe_filename("example\t.txt") == "example_txt"
    assert safe_filename("example\\txt") == "example_txt"

def test_safe_filename_empty_string():
    # Test empty string input
    assert safe_filename("") == ""
import pytest
from agent_swarm.utils.helpers import format_file_size, validate_model_name, safe_filename

def test_format_file_size_integration():
    # Test file size conversion with different units
    assert format_file_size(1024) == "1.00 KB"
    assert format_file_size(1048576) == "1.01 MB"

def test_validate_model_name_integration():
    # Test model name validation with different inputs
    assert validate_model_name("model-name")
    assert not validate_model_name("invalid-model-name")

def test_safe_filename_integration():
    # Test filename conversion with different inputs
    assert safe_filename("example.txt") == "example_txt"
    assert safe_filename("another-example.txt") == "another_example_txt"

def test_format_file_size_integration_edge_cases():
    # Test file size conversion with very large values
    assert format_file_size(2**64 - 1) == "2.00 GB"

def test_validate_model_name_integration_edge_cases():
    # Test model name validation with special characters
    assert validate_model_name("model-name:example")
    assert not validate_model_name("invalid-model-name")

def test_safe_filename_integration_edge_cases():
    # Test filename conversion with spaces and special characters
    assert safe_filename("example!txt") == "example_txt"
