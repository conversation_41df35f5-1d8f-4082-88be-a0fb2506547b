# 🧪 Agent Swarm Testing Framework

## 📋 Overview

Comprehensive testing framework for Agent Swarm with focus on **MCP tools testing**, **proper test organization**, and **Python best practices**.

## 🏗️ Test Structure

```
tests/
├── conftest.py                 # Shared fixtures and configuration
├── pytest.ini                 # Pytest configuration
├── test_runner.py             # Comprehensive test runner
├── README.md                  # This file
├── unit/                      # Unit tests
│   ├── test_core_features.py
│   ├── test_orchestrator.py
│   ├── test_priority_weighting.py
│   ├── test_query_chunking.py
│   ├── test_mcp_tools.py      # ⭐ MCP tools unit tests
│   └── test_mcp_integration.py # ⭐ MCP integration unit tests
├── integration/               # Integration tests
│   ├── test_advanced_features.py
│   ├── test_at_commands.py
│   ├── test_at_autocomplete.py
│   ├── test_context_enhancement.py
│   ├── test_double_ctrl_c.py
│   └── test_mcp_integration.py # ⭐ MCP end-to-end integration
└── e2e/                       # End-to-end tests
    └── test_fixes.py
```

## 🚀 Quick Start

### Run All Tests
```bash
make test
# or
python tests/test_runner.py all
```

### Run Specific Test Types
```bash
# Unit tests
make test-unit
python tests/test_runner.py unit

# Integration tests  
make test-integration
python tests/test_runner.py integration

# MCP-specific tests
make test-mcp
python tests/test_runner.py mcp

# End-to-end tests
make test-e2e
python tests/test_runner.py e2e
```

### Run with Coverage
```bash
make test-coverage
python tests/test_runner.py all --coverage
```

### Comprehensive Check
```bash
make check
python tests/test_runner.py --all-checks
```

## 🔧 MCP Tools Testing

### **Comprehensive MCP Test Coverage**

#### **Unit Tests (`tests/unit/test_mcp_tools.py`)**
- ✅ **FileSystemTool** - read_file, write_file, list_directory
- ✅ **DatabaseTool** - SQLite query execution and error handling
- ✅ **WebSearchTool** - Web search with mocked responses
- ✅ **CodeExecutionTool** - Python code execution and timeout handling
- ✅ **GitTool** - Git commands with mocked subprocess
- ✅ **Tool Integration** - Cross-tool workflows and consistent interfaces

#### **Integration Tests (`tests/unit/test_mcp_integration.py`)**
- ✅ **MCPClient** - Server connections, tool calls, metadata tracking
- ✅ **MCPToolRegistry** - Multiple clients, tool routing, conflict resolution
- ✅ **MCPEnabledAgent** - Agent integration with MCP tools
- ✅ **Concurrent Operations** - Multiple simultaneous tool calls
- ✅ **Error Handling** - Graceful failure and recovery

#### **End-to-End Tests (`tests/integration/test_mcp_integration.py`)**
- ✅ **Complete Workflows** - File operations, tool chaining, real scenarios
- ✅ **Performance Testing** - Tool call speed, concurrent load, memory usage
- ✅ **Real-World Scenarios** - Development workflows, data processing

### **MCP Test Examples**

```python
# Unit test example
@pytest.mark.mcp
@pytest.mark.asyncio
async def test_filesystem_tool():
    result = await FileSystemTool.execute("read_file", {"path": "test.txt"})
    assert result["success"] is True
    assert "content" in result

# Integration test example  
@pytest.mark.mcp
@pytest.mark.integration
async def test_mcp_workflow(mock_mcp_registry):
    # Write file
    write_result = await mock_mcp_registry.call_tool("write_file", {
        "path": "test.txt", "content": "Hello MCP!"
    })
    assert write_result.success is True
    
    # Read file back
    read_result = await mock_mcp_registry.call_tool("read_file", {
        "path": "test.txt"
    })
    assert "Hello MCP!" in read_result.result["content"]
```

## 🎯 Test Categories & Markers

### **Test Markers**
- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests  
- `@pytest.mark.e2e` - End-to-end tests
- `@pytest.mark.mcp` - MCP-related tests ⭐
- `@pytest.mark.slow` - Slow tests (>1 second)
- `@pytest.mark.requires_ollama` - Requires Ollama service
- `@pytest.mark.requires_api_keys` - Requires cloud API keys
- `@pytest.mark.requires_network` - Requires network access

### **Running Specific Markers**
```bash
# Run only MCP tests
pytest -m mcp

# Run fast tests only
pytest -m "not slow"

# Run unit tests excluding network-dependent ones
pytest -m "unit and not requires_network"
```

## 🔍 Test Runner Features

### **Comprehensive Test Runner (`tests/test_runner.py`)**

```bash
# Basic usage
python tests/test_runner.py [test_type] [options]

# Test types
python tests/test_runner.py unit          # Unit tests
python tests/test_runner.py integration  # Integration tests
python tests/test_runner.py e2e          # End-to-end tests
python tests/test_runner.py mcp          # MCP tests
python tests/test_runner.py all          # All tests

# Quality checks
python tests/test_runner.py quality      # Code quality (black, isort, flake8, mypy)
python tests/test_runner.py security     # Security checks (bandit, safety)
python tests/test_runner.py performance  # Performance tests

# Options
python tests/test_runner.py all -v       # Verbose output
python tests/test_runner.py all -c       # With coverage
python tests/test_runner.py --all-checks # Everything
python tests/test_runner.py --report     # Test structure report
```

### **Test Runner Output**
```
🚀 Agent Swarm Test Runner
==================================================
🔄 Unit Tests
   Command: python -m pytest tests/unit/ -v
✅ Unit Tests - PASSED

🔄 Integration Tests  
   Command: python -m pytest tests/integration/ -v
✅ Integration Tests - PASSED

🔄 MCP Tests
   Command: python -m pytest -m mcp -v
✅ MCP Tests - PASSED

==================================================
🎉 All checks passed!
```

## 📊 Coverage & Reporting

### **Coverage Configuration**
- **Source**: `src/agent_swarm`
- **HTML Report**: `htmlcov/index.html`
- **XML Report**: `coverage.xml`
- **Minimum Coverage**: 80% (configurable)

### **Coverage Commands**
```bash
# Generate coverage report
make test-coverage

# View HTML report
open htmlcov/index.html

# Generate test structure report
make test-report
```

## 🛠️ Fixtures & Utilities

### **Available Fixtures**
- `mock_llm` - Mock LLM for testing
- `mock_llm_router` - Mock LLM router
- `mock_mcp_client` - Mock MCP client ⭐
- `mock_mcp_registry` - Mock MCP registry ⭐
- `temp_directory` - Temporary directory
- `temp_file` - Temporary file
- `sample_project_structure` - Sample project for testing

### **MCP-Specific Fixtures**
```python
@pytest.fixture
async def mock_mcp_registry(mock_mcp_client):
    """Provide a mock MCP registry for testing."""
    registry = MCPToolRegistry()
    await registry.register_client("mock_client", mock_mcp_client)
    return registry

@pytest.fixture
def sample_project_structure(temp_directory):
    """Create a sample project structure for testing."""
    # Creates realistic project structure with files
    return project_root
```

## 🎪 Best Practices

### **Test Organization**
1. **Unit tests** - Test individual components in isolation
2. **Integration tests** - Test component interactions
3. **End-to-end tests** - Test complete user workflows
4. **MCP tests** - Comprehensive MCP tool testing ⭐

### **Test Naming**
```python
# Good test names
def test_filesystem_tool_read_file_success()
def test_mcp_client_server_connection()
def test_agent_with_mcp_tools_integration()

# Test class organization
class TestFileSystemTool:
    def test_read_file(self)
    def test_write_file(self)
    def test_list_directory(self)
```

### **Async Testing**
```python
@pytest.mark.asyncio
async def test_async_function():
    result = await some_async_function()
    assert result is not None
```

### **Mocking Best Practices**
```python
# Mock external dependencies
@patch('aiohttp.ClientSession.get')
async def test_web_search_mock(mock_get):
    mock_response = Mock()
    mock_response.json = AsyncMock(return_value={"results": []})
    mock_get.return_value.__aenter__.return_value = mock_response
    
    result = await WebSearchTool.execute("search_web", {"query": "test"})
    assert result["success"] is True
```

## 🚀 Next Steps

1. **Run the tests**: `make test`
2. **Check coverage**: `make test-coverage`
3. **Run comprehensive check**: `make check`
4. **Focus on MCP**: `make test-mcp`
5. **View reports**: `make test-report`

The testing framework is designed to be **comprehensive**, **fast**, and **reliable** with special focus on **MCP tools testing** following **Python best practices**! 🎯
