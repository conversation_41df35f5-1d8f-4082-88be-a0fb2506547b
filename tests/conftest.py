"""
Pytest configuration and shared fixtures.
"""

import asyncio
import os
import tempfile
from pathlib import Path
from typing import Any, As<PERSON><PERSON><PERSON>ator, Dict, List, Optional
from unittest.mock import AsyncMock, MagicMock, Mock

import pytest

from agent_swarm.backends import (
    AbstractLLM,
    LLMConfig,
    LLMResponse,
    LLMRouter,
    LLMTier,
    Message,
)

# Import MCP components for testing
try:
    from agent_swarm.mcp import (
        MCPClient,
        MCPToolRegistry,
        MCPTool,
        MCPToolCall,
        MCPToolResult,
        setup_default_mcp_tools,
    )
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def sample_message() -> Message:
    """Sample message for testing."""
    return Message(
        role="user",
        content="Write a Python function to add two numbers.",
        metadata={"test": True},
    )


@pytest.fixture
def sample_messages() -> list[Message]:
    """Sample message list for testing."""
    return [
        Message(role="system", content="You are a helpful coding assistant."),
        Message(role="user", content="Write a Python function to add two numbers."),
    ]


@pytest.fixture
def sample_llm_config() -> LLMConfig:
    """Sample LLM configuration for testing."""
    return LLMConfig(
        name="Test LLM",
        provider="test",
        model_id="test-model",
        tier=LLMTier.LOCAL_FAST,
        context_length=4096,
        cost_per_1m_tokens=0.0,
        recommended_for=["testing"],
        base_url="http://localhost:8000",
    )


@pytest.fixture
def sample_llm_response() -> LLMResponse:
    """Sample LLM response for testing."""
    return LLMResponse(
        content="def add_numbers(a, b):\n    return a + b",
        model="test-model",
        usage={"prompt_tokens": 20, "completion_tokens": 15, "total_tokens": 35},
        metadata={"test": True},
    )


class MockLLM(AbstractLLM):
    """Mock LLM implementation for testing."""

    def __init__(
        self, config: LLMConfig, available: bool = True, should_fail: bool = False
    ):
        super().__init__(config)
        self._available = available
        self._should_fail = should_fail
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the mock LLM."""
        if self._should_fail:
            raise RuntimeError("Mock initialization failed")
        self._initialized = True

    async def generate(
        self,
        messages: list[Message],
        temperature: float = 0.7,
        max_tokens: int | None = None,
        **kwargs: Any,
    ) -> LLMResponse:
        """Generate a mock response."""
        if self._should_fail:
            raise RuntimeError("Mock generation failed")

        return LLMResponse(
            content=f"Mock response for {len(messages)} messages",
            model=self.config.model_id,
            usage={"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15},
            metadata={"temperature": temperature, "max_tokens": max_tokens},
        )

    async def stream_generate(
        self,
        messages: list[Message],
        temperature: float = 0.7,
        max_tokens: int | None = None,
        **kwargs: Any,
    ) -> AsyncGenerator[str, None]:
        """Stream a mock response."""
        if self._should_fail:
            raise RuntimeError("Mock streaming failed")

        words = ["Mock", "streaming", "response"]
        for word in words:
            yield word + " "

    async def is_available(self) -> bool:
        """Check if mock LLM is available."""
        return self._available and self._initialized


@pytest.fixture
def mock_llm(sample_llm_config: LLMConfig) -> MockLLM:
    """Create a mock LLM for testing."""
    return MockLLM(sample_llm_config)


@pytest.fixture
def failing_mock_llm(sample_llm_config: LLMConfig) -> MockLLM:
    """Create a failing mock LLM for testing."""
    config = sample_llm_config.model_copy()
    config.name = "Failing Test LLM"
    config.model_id = "failing-test-model"
    return MockLLM(config, should_fail=True)


@pytest.fixture
def unavailable_mock_llm(sample_llm_config: LLMConfig) -> MockLLM:
    """Create an unavailable mock LLM for testing."""
    config = sample_llm_config.model_copy()
    config.name = "Unavailable Test LLM"
    config.model_id = "unavailable-test-model"
    return MockLLM(config, available=False)


@pytest.fixture
def llm_router() -> LLMRouter:
    """Create an LLM router for testing."""
    return LLMRouter()


@pytest.fixture
async def configured_router(llm_router: LLMRouter, mock_llm: MockLLM) -> LLMRouter:
    """Create a configured LLM router with mock LLMs."""
    await mock_llm.initialize()
    llm_router.register_llm("test", mock_llm)
    llm_router.set_fallback_chain(["test"])
    return llm_router


@pytest.fixture
def env_vars() -> Dict[str, str]:
    """Environment variables for testing."""
    return {
        "ANTHROPIC_API_KEY": "test-anthropic-key",
        "GOOGLE_API_KEY": "test-google-key",
        "OPENAI_API_KEY": "test-openai-key",
    }


@pytest.fixture(autouse=True)
def mock_env_vars(env_vars: Dict[str, str], monkeypatch: pytest.MonkeyPatch):
    """Mock environment variables for all tests."""
    for key, value in env_vars.items():
        monkeypatch.setenv(key, value)


# Markers for different test types
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "e2e: End-to-end tests")
    config.addinivalue_line("markers", "mcp: MCP-related tests")
    config.addinivalue_line("markers", "slow: Slow tests")
    config.addinivalue_line("markers", "requires_ollama: Tests that require Ollama service")
    config.addinivalue_line("markers", "requires_api_keys: Tests that require cloud API keys")
    config.addinivalue_line("markers", "requires_network: Tests that require network access")


# Skip tests based on availability
def pytest_collection_modifyitems(config, items):
    """Modify test collection based on available services."""

    # Check if Ollama is available
    ollama_available = False
    try:
        import asyncio

        import aiohttp

        async def check_ollama():
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        "http://localhost:11434/api/tags", timeout=2
                    ) as response:
                        return response.status == 200
            except:
                return False

        ollama_available = asyncio.run(check_ollama())
    except:
        pass

    # Check if API keys are available
    api_keys_available = all(
        os.getenv(key) for key in ["ANTHROPIC_API_KEY", "GOOGLE_API_KEY"]
    )

    # Apply skip markers
    skip_ollama = pytest.mark.skip(reason="Ollama service not available")
    skip_api_keys = pytest.mark.skip(reason="API keys not available")
    skip_mcp = pytest.mark.skip(reason="MCP components not available")

    for item in items:
        if "requires_ollama" in item.keywords and not ollama_available:
            item.add_marker(skip_ollama)
        if "requires_api_keys" in item.keywords and not api_keys_available:
            item.add_marker(skip_api_keys)
        if "mcp" in item.keywords and not MCP_AVAILABLE:
            item.add_marker(skip_mcp)


# MCP Testing Fixtures
class MockMCPClient:
    """Mock MCP client for testing."""

    def __init__(self):
        self.initialized = False
        self.servers = {}
        self.tools = {}
        self.call_history = []

    async def initialize(self):
        """Initialize mock client."""
        self.initialized = True

    async def connect_server(self, server_id: str, config: Dict[str, Any]):
        """Mock server connection."""
        self.servers[server_id] = {
            "config": config,
            "connected": True,
            "capabilities": ["tools", "resources"]
        }

        # Add mock tools based on server type
        if config.get("type") == "filesystem":
            self.tools.update({
                "read_file": {"server_id": server_id, "name": "read_file"},
                "write_file": {"server_id": server_id, "name": "write_file"},
                "list_directory": {"server_id": server_id, "name": "list_directory"}
            })
        elif config.get("type") == "web_search":
            self.tools.update({
                "search_web": {"server_id": server_id, "name": "search_web"}
            })

    async def disconnect_server(self, server_id: str):
        """Mock server disconnection."""
        if server_id in self.servers:
            self.servers[server_id]["connected"] = False

    def get_available_tools(self):
        """Get mock available tools."""
        if not MCP_AVAILABLE:
            return []

        return [
            MCPTool(
                name=tool["name"],
                description=f"Mock {tool['name']} tool",
                parameters={"type": "object"},
                server_id=tool["server_id"]
            )
            for tool in self.tools.values()
        ]

    async def call_tool(self, tool_call):
        """Mock tool call."""
        if not MCP_AVAILABLE:
            return Mock()

        self.call_history.append(tool_call)

        # Simulate tool execution
        if tool_call.tool_name == "write_file":
            return MCPToolResult(
                call_id=tool_call.call_id,
                success=True,
                result={"success": True, "path": tool_call.arguments["path"]},
                metadata={"server_id": "filesystem"}
            )
        elif tool_call.tool_name == "read_file":
            return MCPToolResult(
                call_id=tool_call.call_id,
                success=True,
                result={"content": "Mock file content", "path": tool_call.arguments["path"]},
                metadata={"server_id": "filesystem"}
            )
        elif tool_call.tool_name == "list_directory":
            return MCPToolResult(
                call_id=tool_call.call_id,
                success=True,
                result={
                    "entries": [
                        {"name": "file1.txt", "type": "file"},
                        {"name": "file2.py", "type": "file"},
                        {"name": "subdir", "type": "directory"}
                    ],
                    "path": tool_call.arguments["path"]
                },
                metadata={"server_id": "filesystem"}
            )
        elif tool_call.tool_name == "search_web":
            return MCPToolResult(
                call_id=tool_call.call_id,
                success=True,
                result={
                    "results": [
                        {"title": "Mock Result", "url": "https://example.com", "snippet": "Mock snippet"}
                    ]
                },
                metadata={"server_id": "web_search"}
            )
        else:
            return MCPToolResult(
                call_id=tool_call.call_id,
                success=False,
                error=f"Unknown tool: {tool_call.tool_name}"
            )


@pytest.fixture
def mock_mcp_client():
    """Provide a mock MCP client for testing."""
    return MockMCPClient()


@pytest.fixture
async def mock_mcp_registry(mock_mcp_client):
    """Provide a mock MCP registry for testing."""
    if not MCP_AVAILABLE:
        return Mock()

    registry = MCPToolRegistry()
    await registry.register_client("mock_client", mock_mcp_client)
    return registry


@pytest.fixture
def temp_directory():
    """Provide a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def temp_file():
    """Provide a temporary file for testing."""
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        f.write("Test file content")
        temp_path = f.name

    yield Path(temp_path)

    # Cleanup
    Path(temp_path).unlink(missing_ok=True)


@pytest.fixture
def sample_project_structure(temp_directory):
    """Create a sample project structure for testing."""
    project_root = temp_directory

    # Create directories
    (project_root / "src").mkdir()
    (project_root / "tests").mkdir()
    (project_root / "docs").mkdir()

    # Create files
    (project_root / "README.md").write_text("# Test Project\n\nA sample project for testing.")
    (project_root / "requirements.txt").write_text("pytest>=7.0.0\nrequests>=2.28.0\n")
    (project_root / "src" / "__init__.py").write_text("")
    (project_root / "src" / "main.py").write_text("""
def main():
    print("Hello, World!")
    return 0

if __name__ == "__main__":
    exit(main())
""".strip())
    (project_root / "tests" / "__init__.py").write_text("")
    (project_root / "tests" / "test_main.py").write_text("""
import pytest
from src.main import main

def test_main():
    assert main() == 0
""".strip())

    return project_root
