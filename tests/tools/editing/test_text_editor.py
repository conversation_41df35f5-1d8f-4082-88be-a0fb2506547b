"""
Comprehensive tests for the TextFileEditor.

Tests all functionality including safety features, encoding handling,
and various edit operations.
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import patch, mock_open

from agent_swarm.tools.editing.engines.text_editor import TextFileEditor
from agent_swarm.tools.editing.core.models import (
    EditOperation,
    EditOperationType,
    FileChangeStatus,
)


class TestTextFileEditor:
    """Test suite for TextFileEditor."""
    
    @pytest.fixture
    def editor(self):
        """Create a TextFileEditor instance for testing."""
        return TextFileEditor()
    
    @pytest.fixture
    def temp_file(self):
        """Create a temporary file for testing."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("line 1\nline 2\nline 3\n")
            temp_path = Path(f.name)
        
        yield temp_path
        
        # Cleanup
        if temp_path.exists():
            temp_path.unlink()
    
    @pytest.mark.asyncio
    async def test_read_file_success(self, editor, temp_file):
        """Test successful file reading."""
        content = await editor.read_file(temp_file)
        assert content == "line 1\nline 2\nline 3\n"
    
    @pytest.mark.asyncio
    async def test_read_file_not_found(self, editor):
        """Test reading non-existent file."""
        with pytest.raises(FileNotFoundError):
            await editor.read_file(Path("nonexistent.txt"))
    
    @pytest.mark.asyncio
    async def test_read_file_too_large(self, editor):
        """Test reading file that's too large."""
        editor.max_file_size = 10  # Very small limit
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("This content is longer than 10 bytes")
            large_file = Path(f.name)
        
        try:
            with pytest.raises(ValueError, match="File too large"):
                await editor.read_file(large_file)
        finally:
            large_file.unlink()
    
    @pytest.mark.asyncio
    async def test_write_file_success(self, editor):
        """Test successful file writing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = Path(temp_dir) / "test.txt"
            content = "Hello, World!"
            
            await editor.write_file(file_path, content)
            
            assert file_path.exists()
            assert file_path.read_text() == content
    
    @pytest.mark.asyncio
    async def test_write_file_creates_directories(self, editor):
        """Test that write_file creates parent directories."""
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = Path(temp_dir) / "subdir" / "test.txt"
            content = "Hello, World!"
            
            await editor.write_file(file_path, content)
            
            assert file_path.exists()
            assert file_path.read_text() == content
    
    @pytest.mark.asyncio
    async def test_apply_edit_replace_all(self, editor, temp_file):
        """Test replace all operation."""
        operation = EditOperation(
            operation_type=EditOperationType.REPLACE_ALL,
            file_path=temp_file,
            content="New content",
            description="Replace all content"
        )
        
        change = await editor.apply_edit(operation)
        
        assert change.file_path == temp_file
        assert change.original_content == "line 1\nline 2\nline 3\n"
        assert change.new_content == "New content"
        assert change.status == FileChangeStatus.PENDING
        assert change.description == "Replace all content"
    
    @pytest.mark.asyncio
    async def test_apply_edit_replace_lines(self, editor, temp_file):
        """Test replace lines operation."""
        operation = EditOperation(
            operation_type=EditOperationType.REPLACE_LINES,
            file_path=temp_file,
            content="new line 2\n",
            start_line=2,
            end_line=2,
            description="Replace line 2"
        )
        
        change = await editor.apply_edit(operation)
        
        expected_content = "line 1\nnew line 2\nline 3\n"
        assert change.new_content == expected_content
    
    @pytest.mark.asyncio
    async def test_apply_edit_insert_lines(self, editor, temp_file):
        """Test insert lines operation."""
        operation = EditOperation(
            operation_type=EditOperationType.INSERT_LINES,
            file_path=temp_file,
            content="inserted line\n",
            start_line=2,
            description="Insert line"
        )
        
        change = await editor.apply_edit(operation)
        
        expected_content = "line 1\ninserted line\nline 2\nline 3\n"
        assert change.new_content == expected_content
    
    @pytest.mark.asyncio
    async def test_apply_edit_delete_lines(self, editor, temp_file):
        """Test delete lines operation."""
        operation = EditOperation(
            operation_type=EditOperationType.DELETE_LINES,
            file_path=temp_file,
            start_line=2,
            end_line=2,
            content="",  # Not used for delete
            description="Delete line 2"
        )
        
        change = await editor.apply_edit(operation)
        
        expected_content = "line 1\nline 3\n"
        assert change.new_content == expected_content
    
    @pytest.mark.asyncio
    async def test_apply_edit_replace_text(self, editor, temp_file):
        """Test replace text operation."""
        operation = EditOperation(
            operation_type=EditOperationType.REPLACE_TEXT,
            file_path=temp_file,
            content="LINE",
            metadata={"search_text": "line"},
            description="Replace text"
        )
        
        change = await editor.apply_edit(operation)
        
        expected_content = "LINE 1\nLINE 2\nLINE 3\n"
        assert change.new_content == expected_content
    
    @pytest.mark.asyncio
    async def test_apply_edit_insert_text(self, editor, temp_file):
        """Test insert text operation."""
        operation = EditOperation(
            operation_type=EditOperationType.INSERT_TEXT,
            file_path=temp_file,
            content="INSERTED",
            start_pos=0,
            description="Insert text at beginning"
        )
        
        change = await editor.apply_edit(operation)
        
        expected_content = "INSERTEDline 1\nline 2\nline 3\n"
        assert change.new_content == expected_content
    
    @pytest.mark.asyncio
    async def test_apply_edit_delete_text(self, editor, temp_file):
        """Test delete text operation."""
        operation = EditOperation(
            operation_type=EditOperationType.DELETE_TEXT,
            file_path=temp_file,
            content="",  # Not used for delete
            start_pos=0,
            end_pos=7,  # Delete "line 1\n"
            description="Delete text"
        )
        
        change = await editor.apply_edit(operation)
        
        expected_content = "line 2\nline 3\n"
        assert change.new_content == expected_content
    
    @pytest.mark.asyncio
    async def test_validate_edit_valid_operation(self, editor, temp_file):
        """Test validation of valid operation."""
        operation = EditOperation(
            operation_type=EditOperationType.REPLACE_ALL,
            file_path=temp_file,
            content="New content"
        )
        
        is_valid = await editor.validate_edit(operation)
        assert is_valid is True
    
    @pytest.mark.asyncio
    async def test_validate_edit_invalid_line_numbers(self, editor, temp_file):
        """Test validation with invalid line numbers."""
        operation = EditOperation(
            operation_type=EditOperationType.REPLACE_LINES,
            file_path=temp_file,
            content="New content",
            start_line=10,  # Beyond file length
            end_line=15
        )
        
        is_valid = await editor.validate_edit(operation)
        assert is_valid is False
    
    @pytest.mark.asyncio
    async def test_validate_edit_start_after_end(self, editor, temp_file):
        """Test validation with start line after end line."""
        operation = EditOperation(
            operation_type=EditOperationType.REPLACE_LINES,
            file_path=temp_file,
            content="New content",
            start_line=3,
            end_line=1  # Start after end
        )
        
        is_valid = await editor.validate_edit(operation)
        assert is_valid is False
    
    def test_supports_file_type_by_extension(self, editor):
        """Test file type support by extension."""
        assert editor.supports_file_type(Path("test.txt"))
        assert editor.supports_file_type(Path("test.py"))
        assert editor.supports_file_type(Path("test.js"))
        assert editor.supports_file_type(Path("README.md"))
    
    def test_supports_file_type_unsupported_extension(self, editor):
        """Test unsupported file type."""
        # Limit supported extensions for this test
        editor.supported_extensions = {'.txt', '.py'}
        
        assert not editor.supports_file_type(Path("test.exe"))
        assert not editor.supports_file_type(Path("image.jpg"))
    
    @pytest.mark.asyncio
    async def test_apply_edit_new_file(self, editor):
        """Test applying edit to non-existent file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            new_file = Path(temp_dir) / "new_file.txt"
            
            operation = EditOperation(
                operation_type=EditOperationType.REPLACE_ALL,
                file_path=new_file,
                content="New file content",
                description="Create new file"
            )
            
            change = await editor.apply_edit(operation)
            
            assert change.original_content == ""
            assert change.new_content == "New file content"
            assert change.file_path == new_file
    
    @pytest.mark.asyncio
    async def test_apply_edit_unsupported_operation(self, editor, temp_file):
        """Test applying unsupported operation type."""
        # Create an operation with invalid type
        operation = EditOperation(
            operation_type="INVALID_TYPE",  # This will cause an error
            file_path=temp_file,
            content="content"
        )
        
        with pytest.raises(ValueError, match="Unsupported operation type"):
            await editor.apply_edit(operation)


class TestTextFileEditorSafety:
    """Test safety features of TextFileEditor."""
    
    @pytest.fixture
    def editor(self):
        return TextFileEditor()
    
    @pytest.mark.asyncio
    async def test_validate_edit_dangerous_path(self, editor):
        """Test validation rejects dangerous paths."""
        operation = EditOperation(
            operation_type=EditOperationType.REPLACE_ALL,
            file_path=Path("/etc/passwd"),
            content="malicious content"
        )
        
        is_valid = await editor.validate_edit(operation)
        assert is_valid is False
    
    @pytest.mark.asyncio
    async def test_read_file_binary_rejection(self, editor):
        """Test that binary files are rejected."""
        # Create a binary file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.bin') as f:
            f.write(b'\x00\x01\x02\x03\x04\x05')
            binary_file = Path(f.name)
        
        try:
            with pytest.raises(ValueError, match="Binary file not supported"):
                await editor.read_file(binary_file)
        finally:
            binary_file.unlink()


class TestTextFileEditorEncoding:
    """Test encoding handling in TextFileEditor."""
    
    @pytest.fixture
    def editor(self):
        return TextFileEditor()
    
    @pytest.mark.asyncio
    async def test_read_utf8_file(self, editor):
        """Test reading UTF-8 encoded file."""
        content = "Hello, 世界! 🌍"
        
        with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', delete=False, suffix='.txt') as f:
            f.write(content)
            utf8_file = Path(f.name)
        
        try:
            read_content = await editor.read_file(utf8_file)
            assert read_content == content
        finally:
            utf8_file.unlink()
    
    @pytest.mark.asyncio
    async def test_write_utf8_file(self, editor):
        """Test writing UTF-8 encoded file."""
        content = "Hello, 世界! 🌍"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = Path(temp_dir) / "utf8_test.txt"
            
            await editor.write_file(file_path, content)
            
            # Read back and verify
            read_content = file_path.read_text(encoding='utf-8')
            assert read_content == content
