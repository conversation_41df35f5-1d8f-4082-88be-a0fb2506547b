"""
Basic functionality tests to verify test infrastructure works.
"""

import pytest
import tempfile
from pathlib import Path


@pytest.mark.unit
class TestBasicFunctionality:
    """Test basic functionality to verify test infrastructure."""

    def test_python_basics(self):
        """Test basic Python functionality."""
        assert 1 + 1 == 2
        assert "hello" == "hello"
        assert [1, 2, 3] == [1, 2, 3]

    def test_file_operations(self):
        """Test basic file operations."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("test content")
            temp_path = f.name
        
        try:
            # Read file
            with open(temp_path, 'r') as f:
                content = f.read()
            
            assert content == "test content"
            
        finally:
            Path(temp_path).unlink()

    def test_pathlib_operations(self):
        """Test pathlib operations."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create file
            test_file = temp_path / "test.txt"
            test_file.write_text("Hello, World!")
            
            # Verify file exists
            assert test_file.exists()
            assert test_file.is_file()
            
            # Read content
            content = test_file.read_text()
            assert content == "Hello, World!"

    @pytest.mark.asyncio
    async def test_async_functionality(self):
        """Test async functionality."""
        import asyncio
        
        async def async_function():
            await asyncio.sleep(0.01)  # Very short sleep
            return "async result"
        
        result = await async_function()
        assert result == "async result"

    def test_imports_work(self):
        """Test that basic imports work."""
        import sys
        import os
        import json
        import subprocess
        
        assert sys is not None
        assert os is not None
        assert json is not None
        assert subprocess is not None

    def test_pytest_markers(self):
        """Test that pytest markers work."""
        # This test itself uses the @pytest.mark.unit marker
        # If it runs, markers are working
        assert True

    def test_fixtures_work(self, temp_directory):
        """Test that fixtures work."""
        assert temp_directory.exists()
        assert temp_directory.is_dir()
        
        # Create a file in the temp directory
        test_file = temp_directory / "fixture_test.txt"
        test_file.write_text("Fixtures work!")
        
        assert test_file.exists()
        assert test_file.read_text() == "Fixtures work!"


@pytest.mark.integration
class TestTestInfrastructure:
    """Test the test infrastructure itself."""

    def test_test_structure_exists(self):
        """Test that test structure exists."""
        tests_dir = Path("tests")
        assert tests_dir.exists()
        assert tests_dir.is_dir()
        
        # Check subdirectories
        assert (tests_dir / "unit").exists()
        assert (tests_dir / "integration").exists()
        assert (tests_dir / "e2e").exists()

    def test_conftest_exists(self):
        """Test that conftest.py exists."""
        conftest = Path("tests/conftest.py")
        assert conftest.exists()
        assert conftest.is_file()

    def test_pytest_ini_exists(self):
        """Test that pytest.ini exists."""
        pytest_ini = Path("pytest.ini")
        assert pytest_ini.exists()
        assert pytest_ini.is_file()

    def test_test_runner_exists(self):
        """Test that test runner exists."""
        test_runner = Path("tests/test_runner.py")
        assert test_runner.exists()
        assert test_runner.is_file()

    def test_makefile_has_test_commands(self):
        """Test that Makefile has test commands."""
        makefile = Path("Makefile")
        assert makefile.exists()
        
        content = makefile.read_text()
        assert "test:" in content
        assert "test-unit:" in content
        assert "test-integration:" in content
        assert "test-mcp:" in content


@pytest.mark.slow
class TestPerformance:
    """Test performance characteristics."""

    def test_fast_operation(self):
        """Test that fast operations are indeed fast."""
        import time
        
        start_time = time.time()
        
        # Simple operation
        result = sum(range(1000))
        
        end_time = time.time()
        duration = end_time - start_time
        
        assert result == 499500  # Correct sum
        assert duration < 0.1  # Should be very fast

    @pytest.mark.asyncio
    async def test_async_performance(self):
        """Test async operation performance."""
        import asyncio
        import time
        
        async def fast_async_op():
            await asyncio.sleep(0.001)  # 1ms
            return "done"
        
        start_time = time.time()
        result = await fast_async_op()
        end_time = time.time()
        
        duration = end_time - start_time
        
        assert result == "done"
        assert duration < 0.1  # Should complete quickly


if __name__ == "__main__":
    # Run basic tests if executed directly
    pytest.main([__file__, "-v"])
