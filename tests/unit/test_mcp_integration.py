"""
Comprehensive unit tests for MCP integration.
"""

import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from agent_swarm.mcp import (
    MCPClient,
    MCPToolRegistry,
    MCPEnabledAgent,
    MCPTool,
    MCPToolCall,
    MCPToolResult,
    MCPResource,
    setup_default_mcp_tools,
)
from agent_swarm.backends import LLMRouter
from ..conftest import MockLLM


@pytest.mark.unit
class TestMCPClient:
    """Test MCP client functionality."""

    async def test_client_initialization(self):
        """Test MCP client initialization."""
        client = MCPClient()
        assert not client._initialized

        await client.initialize()
        assert client._initialized

    async def test_server_connection(self):
        """Test connecting to MCP servers."""
        client = MCPClient()
        await client.initialize()

        await client.connect_server("test_server", {"type": "test"})

        assert "test_server" in client.servers
        assert client.servers["test_server"]["connected"] is True

    async def test_tool_discovery(self):
        """Test tool discovery from servers."""
        client = MCPClient()
        await client.initialize()

        # Connect filesystem server (has predefined tools)
        await client.connect_server("filesystem", {"type": "filesystem"})

        tools = client.get_available_tools()
        tool_names = [tool.name for tool in tools]

        assert "read_file" in tool_names
        assert "write_file" in tool_names

    async def test_tool_calling(self):
        """Test calling MCP tools."""
        client = MCPClient()
        await client.initialize()
        await client.connect_server("filesystem", {"type": "filesystem"})

        # Test file writing
        tool_call = MCPToolCall(
            tool_name="write_file",
            arguments={"path": "test_mcp.txt", "content": "Hello MCP!"}
        )

        result = await client.call_tool(tool_call)

        assert result.success is True
        assert "test_mcp.txt" in str(result.result)

        # Test file reading
        read_call = MCPToolCall(
            tool_name="read_file",
            arguments={"path": "test_mcp.txt"}
        )

        read_result = await client.call_tool(read_call)

        assert read_result.success is True
        assert "Hello MCP!" in read_result.result["content"]

    async def test_unknown_tool(self):
        """Test calling unknown tool."""
        client = MCPClient()
        await client.initialize()

        tool_call = MCPToolCall(
            tool_name="unknown_tool",
            arguments={}
        )

        result = await client.call_tool(tool_call)

        assert result.success is False
        assert "not found" in result.error


@pytest.mark.unit
class TestMCPToolRegistry:
    """Test MCP tool registry functionality."""

    async def test_registry_creation(self):
        """Test creating tool registry."""
        registry = MCPToolRegistry()

        assert len(registry.clients) == 0
        assert len(registry.global_tools) == 0

    async def test_client_registration(self):
        """Test registering MCP clients."""
        registry = MCPToolRegistry()
        client = MCPClient()
        await client.initialize()
        await client.connect_server("filesystem", {"type": "filesystem"})

        await registry.register_client("test_client", client)

        assert "test_client" in registry.clients
        assert len(registry.global_tools) > 0

    async def test_tool_calling_through_registry(self):
        """Test calling tools through registry."""
        registry = MCPToolRegistry()
        client = MCPClient()
        await client.initialize()
        await client.connect_server("filesystem", {"type": "filesystem"})

        await registry.register_client("fs_client", client)

        # Call tool through registry
        result = await registry.call_tool("write_file", {
            "path": "registry_test.txt",
            "content": "Registry test"
        })

        assert result.success is True

    async def test_setup_default_tools(self):
        """Test setting up default MCP tools."""
        registry = await setup_default_mcp_tools()

        tools = registry.get_all_tools()
        tool_names = [tool.name for tool in tools]

        assert "read_file" in tool_names
        assert "write_file" in tool_names
        assert "search_web" in tool_names


@pytest.mark.unit
class TestMCPEnabledAgent:
    """Test MCP-enabled agent functionality."""

    @pytest.fixture
    async def mcp_agent(self, mock_llm):
        """Create MCP-enabled agent for testing."""
        router = LLMRouter()
        await mock_llm.initialize()
        router.register_llm("test", mock_llm)
        router.set_fallback_chain(["test"])

        registry = await setup_default_mcp_tools()

        return MCPEnabledAgent("TestBot", "Test Agent", router, registry)

    def test_agent_creation(self, mcp_agent):
        """Test creating MCP-enabled agent."""
        assert mcp_agent.name == "TestBot"
        assert mcp_agent.role == "Test Agent"
        assert mcp_agent.mcp_registry is not None
        assert len(mcp_agent.tool_calls_history) == 0

    def test_tool_extraction(self, mcp_agent):
        """Test extracting tool calls from LLM response."""
        # Test valid tool call extraction
        response_with_tools = '''
        I need to use a tool to help with this task.
        {"tool_calls": [{"name": "read_file", "arguments": {"path": "test.txt"}}]}
        '''

        tool_calls = mcp_agent._extract_tool_calls(response_with_tools)

        assert len(tool_calls) == 1
        assert tool_calls[0]["name"] == "read_file"
        assert tool_calls[0]["arguments"]["path"] == "test.txt"

        # Test response without tool calls
        normal_response = "This is a normal response without tool calls."
        tool_calls = mcp_agent._extract_tool_calls(normal_response)

        assert len(tool_calls) == 0

    def test_tool_formatting(self, mcp_agent):
        """Test formatting tools for prompt."""
        tools = [
            {
                "function": {
                    "name": "test_tool",
                    "description": "A test tool",
                    "parameters": {"properties": {"param1": {}, "param2": {}}}
                }
            }
        ]

        formatted = mcp_agent._format_tools_for_prompt(tools)

        assert "test_tool" in formatted
        assert "A test tool" in formatted
        assert "param1, param2" in formatted

    def test_tool_usage_stats(self, mcp_agent):
        """Test tool usage statistics."""
        # Initially no usage
        stats = mcp_agent.get_tool_usage_stats()
        assert stats["total_calls"] == 0
        assert stats["success_rate"] == 0.0

        # Add some mock tool calls
        mcp_agent.tool_calls_history = [
            {"tool": "read_file", "success": True, "task_type": "coding"},
            {"tool": "write_file", "success": True, "task_type": "coding"},
            {"tool": "search_web", "success": False, "task_type": "research"}
        ]

        stats = mcp_agent.get_tool_usage_stats()
        assert stats["total_calls"] == 3
        assert stats["successful_calls"] == 2
        assert abs(stats["success_rate"] - 0.667) < 0.01
        assert "read_file" in stats["tools_used"]
        assert "write_file" in stats["tools_used"]
        assert "search_web" in stats["tools_used"]


@pytest.mark.unit
class TestMCPModels:
    """Test MCP data models."""

    def test_mcp_tool_model(self):
        """Test MCPTool model."""
        tool = MCPTool(
            name="test_tool",
            description="A test tool",
            parameters={"type": "object"},
            server_id="test_server"
        )

        assert tool.name == "test_tool"
        assert tool.description == "A test tool"
        assert tool.server_id == "test_server"

    def test_mcp_tool_call_model(self):
        """Test MCPToolCall model."""
        call = MCPToolCall(
            tool_name="read_file",
            arguments={"path": "test.txt"},
            call_id="call_123"
        )

        assert call.tool_name == "read_file"
        assert call.arguments["path"] == "test.txt"
        assert call.call_id == "call_123"

    def test_mcp_tool_result_model(self):
        """Test MCPToolResult model."""
        result = MCPToolResult(
            call_id="call_123",
            success=True,
            result={"content": "file content"},
            metadata={"server_id": "fs_server"}
        )

        assert result.call_id == "call_123"
        assert result.success is True
        assert result.result["content"] == "file content"
        assert result.metadata["server_id"] == "fs_server"

    def test_mcp_tool_result_error(self):
        """Test MCPToolResult error model."""
        result = MCPToolResult(
            call_id="call_456",
            success=False,
            error="File not found"
        )

        assert result.call_id == "call_456"
        assert result.success is False
        assert result.error == "File not found"
        assert result.result is None


@pytest.mark.unit
class TestMCPClientAdvanced:
    """Advanced tests for MCP client functionality."""

    @pytest.mark.asyncio
    async def test_client_initialization(self):
        """Test MCP client initialization."""
        client = MCPClient()

        # Should start uninitialized
        assert not client.initialized
        assert len(client.servers) == 0
        assert len(client.tools) == 0

        # Initialize
        await client.initialize()

        assert client.initialized

    @pytest.mark.asyncio
    async def test_multiple_server_connections(self):
        """Test connecting to multiple MCP servers."""
        client = MCPClient()
        await client.initialize()

        # Connect to multiple servers
        await client.connect_server("filesystem", {"type": "filesystem"})
        await client.connect_server("web_search", {"type": "web_search"})

        assert len(client.servers) == 2
        assert "filesystem" in client.servers
        assert "web_search" in client.servers

        # Check tools from both servers
        tools = client.get_available_tools()
        tool_names = [tool.name for tool in tools]

        assert "read_file" in tool_names  # From filesystem
        assert "search_web" in tool_names  # From web_search

    @pytest.mark.asyncio
    async def test_server_disconnection(self):
        """Test disconnecting from MCP servers."""
        client = MCPClient()
        await client.initialize()
        await client.connect_server("filesystem", {"type": "filesystem"})

        # Verify connection
        assert "filesystem" in client.servers

        # Disconnect
        await client.disconnect_server("filesystem")

        # Should be removed
        assert "filesystem" not in client.servers

    @pytest.mark.asyncio
    async def test_tool_call_with_metadata(self):
        """Test tool calling with metadata tracking."""
        client = MCPClient()
        await client.initialize()
        await client.connect_server("filesystem", {"type": "filesystem"})

        tool_call = MCPToolCall(
            tool_name="write_file",
            arguments={"path": "test_metadata.txt", "content": "Test content"},
            call_id="metadata_test_123"
        )

        result = await client.call_tool(tool_call)

        assert result.success is True
        assert result.call_id == "metadata_test_123"
        assert "server_id" in result.metadata
        assert result.metadata["server_id"] == "filesystem"

    @pytest.mark.asyncio
    async def test_concurrent_tool_calls(self):
        """Test concurrent tool calls."""
        import asyncio

        client = MCPClient()
        await client.initialize()
        await client.connect_server("filesystem", {"type": "filesystem"})

        # Create multiple tool calls
        calls = [
            MCPToolCall(
                tool_name="write_file",
                arguments={"path": f"concurrent_test_{i}.txt", "content": f"Content {i}"},
                call_id=f"concurrent_{i}"
            )
            for i in range(3)
        ]

        # Execute concurrently
        results = await asyncio.gather(*[client.call_tool(call) for call in calls])

        # All should succeed
        assert len(results) == 3
        for i, result in enumerate(results):
            assert result.success is True
            assert result.call_id == f"concurrent_{i}"


@pytest.mark.unit
class TestMCPToolRegistryAdvanced:
    """Advanced tests for MCP tool registry."""

    @pytest.mark.asyncio
    async def test_registry_with_multiple_clients(self):
        """Test registry with multiple clients."""
        registry = MCPToolRegistry()

        # Create multiple clients
        client1 = MCPClient()
        await client1.initialize()
        await client1.connect_server("filesystem", {"type": "filesystem"})

        client2 = MCPClient()
        await client2.initialize()
        await client2.connect_server("web_search", {"type": "web_search"})

        # Register both clients
        await registry.register_client("fs_client", client1)
        await registry.register_client("web_client", client2)

        assert len(registry.clients) == 2

        # Should have tools from both clients
        all_tools = registry.get_all_tools()
        tool_names = [tool.name for tool in all_tools]

        assert "read_file" in tool_names
        assert "search_web" in tool_names

    @pytest.mark.asyncio
    async def test_tool_name_conflicts(self):
        """Test handling of tool name conflicts."""
        registry = MCPToolRegistry()

        # Create two clients with potentially conflicting tools
        client1 = MCPClient()
        await client1.initialize()
        await client1.connect_server("filesystem1", {"type": "filesystem"})

        client2 = MCPClient()
        await client2.initialize()
        await client2.connect_server("filesystem2", {"type": "filesystem"})

        await registry.register_client("client1", client1)
        await registry.register_client("client2", client2)

        # Should handle conflicts gracefully
        all_tools = registry.get_all_tools()
        assert len(all_tools) > 0

    @pytest.mark.asyncio
    async def test_registry_tool_routing(self):
        """Test that registry routes tools to correct clients."""
        registry = MCPToolRegistry()

        client = MCPClient()
        await client.initialize()
        await client.connect_server("filesystem", {"type": "filesystem"})

        await registry.register_client("fs_client", client)

        # Call tool through registry
        result = await registry.call_tool("write_file", {
            "path": "registry_routing_test.txt",
            "content": "Routing test"
        })

        assert result.success is True
        assert "registry_routing_test.txt" in str(result.result)

    @pytest.mark.asyncio
    async def test_registry_error_handling(self):
        """Test registry error handling."""
        registry = MCPToolRegistry()

        # Try to call non-existent tool
        result = await registry.call_tool("nonexistent_tool", {})

        assert result.success is False
        assert "not found" in result.error

    @pytest.mark.asyncio
    async def test_registry_client_removal(self):
        """Test removing clients from registry."""
        registry = MCPToolRegistry()

        client = MCPClient()
        await client.initialize()
        await client.connect_server("filesystem", {"type": "filesystem"})

        await registry.register_client("test_client", client)

        # Verify registration
        assert "test_client" in registry.clients
        assert len(registry.global_tools) > 0

        # Remove client
        registry.unregister_client("test_client")

        # Should be removed
        assert "test_client" not in registry.clients
        # Tools should be updated
        remaining_tools = [tool for tool in registry.global_tools.values()
                          if tool.server_id != "filesystem"]
        assert len(remaining_tools) == 0 or len(remaining_tools) < len(registry.global_tools)

    def test_mcp_tool_result_model(self):
        """Test MCPToolResult model."""
        result = MCPToolResult(
            call_id="call_123",
            success=True,
            result={"content": "file content"},
            metadata={"server": "filesystem"}
        )

        assert result.call_id == "call_123"
        assert result.success is True
        assert result.result["content"] == "file content"
        assert result.metadata["server"] == "filesystem"

    def test_mcp_tool_result_error(self):
        """Test MCPToolResult with error."""
        result = MCPToolResult(
            success=False,
            error="File not found"
        )

        assert result.success is False
        assert result.error == "File not found"
        assert result.result is None


@pytest.mark.unit
class TestMCPIntegration:
    """Test overall MCP integration."""

    async def test_end_to_end_tool_flow(self):
        """Test complete tool execution flow."""
        # Set up registry
        registry = await setup_default_mcp_tools()

        # Test file operations
        write_result = await registry.call_tool("write_file", {
            "path": "integration_test.txt",
            "content": "Integration test content"
        })

        assert write_result.success is True

        read_result = await registry.call_tool("read_file", {
            "path": "integration_test.txt"
        })

        assert read_result.success is True
        assert "Integration test content" in read_result.result["content"]

        # Test web search
        search_result = await registry.call_tool("search_web", {
            "query": "test query",
            "num_results": 3
        })

        assert search_result.success is True
        assert len(search_result.result["results"]) == 3

    def test_llm_tool_format(self):
        """Test formatting tools for LLM consumption."""
        registry = MCPToolRegistry()

        # Add a mock tool
        tool = MCPTool(
            name="mock_tool",
            description="A mock tool for testing",
            parameters={
                "type": "object",
                "properties": {
                    "input": {"type": "string", "description": "Input parameter"}
                },
                "required": ["input"]
            },
            server_id="mock_server"
        )

        registry.global_tools["mock_tool"] = tool

        llm_tools = registry.get_tools_for_llm()

        assert len(llm_tools) == 1
        assert llm_tools[0]["type"] == "function"
        assert llm_tools[0]["function"]["name"] == "mock_tool"
        assert llm_tools[0]["function"]["description"] == "A mock tool for testing"
