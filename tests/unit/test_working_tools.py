"""
Working tests for Agent Swarm tools that match actual implementation.
"""

import pytest
import tempfile
import asyncio
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

from agent_swarm.tools import (
    LinuxCLITool, FileSystemTool, ProcessTool, NetworkTool,
    SystemInfoTool, DevelopmentTool
)


@pytest.mark.unit
class TestLinuxCLIToolWorking:
    """Test LinuxCLITool with actual interface."""

    @pytest.fixture
    def cli_tool(self):
        """Create LinuxCLITool instance."""
        return LinuxCLITool(timeout=10, safe_mode=True)

    @pytest.mark.asyncio
    async def test_execute_command_simple(self, cli_tool):
        """Test executing simple command."""
        with patch('asyncio.create_subprocess_shell') as mock_subprocess:
            # Mock process
            mock_process = Mock()
            mock_process.communicate = AsyncMock(return_value=(b"Hello World", b""))
            mock_process.returncode = 0
            mock_subprocess.return_value = mock_process

            result = await cli_tool.execute_command("echo 'Hello World'")

            assert result["success"] is True
            assert "Hello World" in result["stdout"]
            assert result["return_code"] == 0

    @pytest.mark.asyncio
    async def test_execute_command_with_error(self, cli_tool):
        """Test executing command that produces error."""
        with patch('asyncio.create_subprocess_shell') as mock_subprocess:
            mock_process = Mock()
            mock_process.communicate = AsyncMock(return_value=(b"", b"Command not found"))
            mock_process.returncode = 127
            mock_subprocess.return_value = mock_process

            result = await cli_tool.execute_command("nonexistent_command")

            assert result["success"] is False
            assert "Command not found" in result["stderr"]
            assert result["return_code"] == 127

    @pytest.mark.asyncio
    async def test_dangerous_command_blocked(self, cli_tool):
        """Test that dangerous commands are blocked in safe mode."""
        result = await cli_tool.execute_command("rm -rf /")

        assert result["success"] is False
        assert "Dangerous command blocked" in result["error"]
        assert result["return_code"] == -1

    def test_safe_mode_configuration(self):
        """Test safe mode configuration."""
        safe_tool = LinuxCLITool(safe_mode=True)
        unsafe_tool = LinuxCLITool(safe_mode=False)

        assert safe_tool.safe_mode is True
        assert unsafe_tool.safe_mode is False


@pytest.mark.unit
class TestFileSystemToolWorking:
    """Test FileSystemTool with actual interface."""

    @pytest.fixture
    def fs_tool(self):
        """Create FileSystemTool instance."""
        return FileSystemTool(safe_mode=True)

    @pytest.mark.asyncio
    async def test_find_files(self, fs_tool):
        """Test finding files."""
        with patch.object(fs_tool, 'execute_command') as mock_execute:
            mock_execute.return_value = {
                "success": True,
                "stdout": "./test1.py\n./test2.py\n",
                "stderr": "",
                "return_code": 0
            }

            result = await fs_tool.find_files("*.py", ".")

            assert result["success"] is True
            assert "./test1.py" in result["stdout"]
            assert "./test2.py" in result["stdout"]
            mock_execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_file_info_existing_file(self, fs_tool):
        """Test getting info for existing file."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("test content")
            temp_path = f.name

        try:
            result = await fs_tool.get_file_info(temp_path)

            assert result["success"] is True
            assert result["is_file"] is True
            assert result["is_directory"] is False
            assert "size" in result
            assert "modified" in result
            assert "permissions" in result

        finally:
            Path(temp_path).unlink()

    @pytest.mark.asyncio
    async def test_get_file_info_nonexistent(self, fs_tool):
        """Test getting info for non-existent file."""
        result = await fs_tool.get_file_info("/nonexistent/file.txt")

        assert result["success"] is False
        assert "does not exist" in result["error"]

    @pytest.mark.asyncio
    async def test_create_directory_structure(self, fs_tool):
        """Test creating directory structure."""
        with tempfile.TemporaryDirectory() as temp_dir:
            structure = {
                f"{temp_dir}/test_project": {
                    "src": {
                        "main.py": "print('hello')",
                        "__init__.py": ""
                    },
                    "tests": {},
                    "README.md": "# Test Project"
                }
            }

            result = await fs_tool.create_directory_structure(structure)

            assert result["success"] is True

            # Verify structure was created
            project_path = Path(temp_dir) / "test_project"
            assert project_path.exists()
            assert (project_path / "src").exists()
            assert (project_path / "src" / "main.py").exists()
            assert (project_path / "README.md").exists()

    @pytest.mark.asyncio
    async def test_grep_in_files(self, fs_tool):
        """Test searching for patterns in files."""
        with patch.object(fs_tool, 'execute_command') as mock_execute:
            mock_execute.return_value = {
                "success": True,
                "stdout": "./file1.py:5:def test_function():\n./file2.py:10:def test_method():\n",
                "stderr": "",
                "return_code": 0
            }

            result = await fs_tool.grep_in_files("def test", ".", "*.py")

            assert result["success"] is True
            assert "def test_function" in result["stdout"]
            assert "def test_method" in result["stdout"]


@pytest.mark.unit
class TestProcessToolWorking:
    """Test ProcessTool with actual interface."""

    @pytest.fixture
    def process_tool(self):
        """Create ProcessTool instance."""
        return ProcessTool()

    @pytest.mark.asyncio
    async def test_list_processes(self, process_tool):
        """Test listing processes."""
        with patch('psutil.process_iter') as mock_process_iter:
            # Mock process
            mock_proc = Mock()
            mock_proc.info = {
                'pid': 1234,
                'name': 'test_process',
                'cpu_percent': 5.0,
                'memory_percent': 2.5
            }
            mock_process_iter.return_value = [mock_proc]

            result = await process_tool.list_processes()

            assert result["success"] is True
            assert len(result["processes"]) == 1
            assert result["processes"][0]["name"] == "test_process"
            assert result["processes"][0]["pid"] == 1234

    @pytest.mark.asyncio
    async def test_list_processes_with_filter(self, process_tool):
        """Test listing processes with name filter."""
        with patch('psutil.process_iter') as mock_process_iter:
            mock_procs = [
                Mock(info={'pid': 1, 'name': 'python', 'cpu_percent': 1.0, 'memory_percent': 1.0}),
                Mock(info={'pid': 2, 'name': 'node', 'cpu_percent': 2.0, 'memory_percent': 2.0}),
                Mock(info={'pid': 3, 'name': 'python3', 'cpu_percent': 3.0, 'memory_percent': 3.0})
            ]
            mock_process_iter.return_value = mock_procs

            result = await process_tool.list_processes(filter_name="python")

            assert result["success"] is True
            assert len(result["processes"]) == 2  # python and python3
            process_names = [p["name"] for p in result["processes"]]
            assert "python" in process_names
            assert "python3" in process_names
            assert "node" not in process_names

    @pytest.mark.asyncio
    async def test_kill_process(self, process_tool):
        """Test killing a process."""
        with patch('psutil.Process') as mock_process_class:
            mock_proc = Mock()
            mock_proc.terminate = Mock()
            mock_process_class.return_value = mock_proc

            result = await process_tool.kill_process(1234)

            assert result["success"] is True
            assert "terminated" in result["message"]
            mock_proc.terminate.assert_called_once()

    @pytest.mark.asyncio
    async def test_kill_process_not_found(self, process_tool):
        """Test killing non-existent process."""
        with patch('psutil.Process') as mock_process_class:
            import psutil
            mock_process_class.side_effect = psutil.NoSuchProcess(1234)

            result = await process_tool.kill_process(1234)

            assert result["success"] is False
            assert "not found" in result["error"]


@pytest.mark.unit
class TestSystemInfoToolWorking:
    """Test SystemInfoTool with actual interface."""

    @pytest.fixture
    def system_tool(self):
        """Create SystemInfoTool instance."""
        return SystemInfoTool()

    @pytest.mark.asyncio
    async def test_get_system_info(self, system_tool):
        """Test getting system information."""
        result = await system_tool.get_system_info()

        assert result["success"] is True
        assert "platform" in result
        assert "system" in result
        assert "python_version" in result
        assert "cpu_count" in result
        assert "memory_total" in result

    @pytest.mark.asyncio
    async def test_check_dependencies(self, system_tool):
        """Test checking system dependencies."""
        with patch.object(system_tool, 'execute_command') as mock_execute:
            # Mock successful dependency check
            mock_execute.return_value = {
                "success": True,
                "stdout": "/usr/bin/python3\n",
                "stderr": "",
                "return_code": 0
            }

            result = await system_tool.check_dependencies(["python3"])

            assert result["success"] is True
            assert "dependencies" in result
            assert "python3" in result["dependencies"]
            assert result["dependencies"]["python3"]["installed"] is True
            assert "/usr/bin/python3" in result["dependencies"]["python3"]["path"]


@pytest.mark.unit
class TestDevelopmentToolWorking:
    """Test DevelopmentTool with actual interface."""

    @pytest.fixture
    def dev_tool(self):
        """Create DevelopmentTool instance."""
        return DevelopmentTool()

    @pytest.mark.asyncio
    async def test_create_project_structure_python(self, dev_tool):
        """Test creating Python project structure."""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch.object(dev_tool, 'execute_command'):
                # Change to temp directory for test
                original_cwd = Path.cwd()
                try:
                    import os
                    os.chdir(temp_dir)

                    result = await dev_tool.create_project_structure("test_project", "python")

                    assert result["success"] is True

                    # Verify project structure
                    project_path = Path("test_project")
                    assert project_path.exists()
                    assert (project_path / "src" / "test_project" / "__init__.py").exists()
                    assert (project_path / "src" / "test_project" / "main.py").exists()
                    assert (project_path / "tests" / "__init__.py").exists()
                    assert (project_path / "README.md").exists()

                finally:
                    os.chdir(original_cwd)

    @pytest.mark.asyncio
    async def test_create_project_structure_unknown_type(self, dev_tool):
        """Test creating project with unknown type."""
        result = await dev_tool.create_project_structure("test", "unknown")

        assert result["success"] is False
        assert "Unknown project type" in result["error"]

    @pytest.mark.asyncio
    async def test_run_tests(self, dev_tool):
        """Test running tests."""
        with patch.object(dev_tool, 'execute_command') as mock_execute:
            mock_execute.return_value = {
                "success": True,
                "stdout": "===== 5 passed in 0.12s =====",
                "stderr": "",
                "return_code": 0
            }

            result = await dev_tool.run_tests("pytest", ".")

            assert result["success"] is True
            assert "passed" in result["stdout"]

    @pytest.mark.asyncio
    async def test_format_code(self, dev_tool):
        """Test code formatting."""
        with patch.object(dev_tool, 'execute_command') as mock_execute:
            mock_execute.return_value = {
                "success": True,
                "stdout": "reformatted test.py",
                "stderr": "",
                "return_code": 0
            }

            result = await dev_tool.format_code("black", "test.py")

            assert result["success"] is True
            mock_execute.assert_called_once_with("black test.py")

    @pytest.mark.asyncio
    async def test_lint_code(self, dev_tool):
        """Test code linting."""
        with patch.object(dev_tool, 'execute_command') as mock_execute:
            mock_execute.return_value = {
                "success": True,
                "stdout": "test.py:1:1: E302 expected 2 blank lines",
                "stderr": "",
                "return_code": 1
            }

            result = await dev_tool.lint_code("flake8", "test.py")

            # Note: flake8 returns non-zero exit code when issues found
            assert "E302" in result["stdout"]
            mock_execute.assert_called_once_with("flake8 test.py")


@pytest.mark.integration
class TestToolsIntegrationWorking:
    """Integration tests for tools with actual interfaces."""

    @pytest.mark.asyncio
    async def test_filesystem_and_development_integration(self):
        """Test using filesystem and development tools together."""
        fs_tool = FileSystemTool()
        dev_tool = DevelopmentTool()

        with tempfile.TemporaryDirectory() as temp_dir:
            original_cwd = Path.cwd()
            try:
                import os
                os.chdir(temp_dir)

                # Create project structure
                result = await dev_tool.create_project_structure("integration_test", "python")
                assert result["success"] is True

                # Verify with filesystem tool
                info_result = await fs_tool.get_file_info("integration_test")
                assert info_result["success"] is True
                assert info_result["is_directory"] is True

                # Find Python files
                find_result = await fs_tool.find_files("*.py", "integration_test")
                assert find_result["success"] is True

            finally:
                os.chdir(original_cwd)

    @pytest.mark.asyncio
    async def test_error_handling_consistency(self):
        """Test that all tools handle errors consistently."""
        tools = [
            LinuxCLITool(),
            FileSystemTool(),
            ProcessTool(),
            NetworkTool(),
            SystemInfoTool(),
            DevelopmentTool()
        ]

        # All tools should have consistent error response format
        for tool in tools:
            # Test with a method that should exist
            if hasattr(tool, 'get_system_info'):
                result = await tool.get_system_info()
                assert isinstance(result, dict)
                assert "success" in result

            # Test file info with non-existent path (for FileSystemTool)
            if hasattr(tool, 'get_file_info'):
                result = await tool.get_file_info("/definitely/nonexistent/path")
                assert isinstance(result, dict)
                assert "success" in result
                assert result["success"] is False
                assert "error" in result


if __name__ == "__main__":
    # Run these working tests if executed directly
    pytest.main([__file__, "-v"])
