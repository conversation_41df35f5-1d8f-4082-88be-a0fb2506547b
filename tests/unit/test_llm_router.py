"""
Comprehensive unit tests for LLM Router functionality.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch

from agent_swarm.backends import (
    LLMRouter, LLMTier, LLMConfig, Message, LLMResponse, AbstractLLM
)


@pytest.mark.unit
class TestLLMRouter:
    """Test LLMRouter comprehensive functionality."""

    @pytest.fixture
    def router(self):
        """Create LLMRouter instance."""
        return LLMRouter()

    @pytest.fixture
    def mock_local_llm(self):
        """Create mock local LLM."""
        llm = Mock(spec=AbstractLLM)
        llm.generate = AsyncMock(return_value=LLMResponse(
            content="Local LLM response",
            model="local-model",
            usage={"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30},
            metadata={"tier": "local"}
        ))
        llm.config = LLMConfig(
            name="Local LLM",
            provider="ollama",
            model_id="local-model",
            tier=LLMTier.LOCAL_FAST,
            cost_per_1m_tokens=0.0
        )
        return llm

    @pytest.fixture
    def mock_cloud_llm(self):
        """Create mock cloud LLM."""
        llm = Mock(spec=AbstractLLM)
        llm.generate = AsyncMock(return_value=LLMResponse(
            content="Cloud LLM response",
            model="cloud-model",
            usage={"prompt_tokens": 15, "completion_tokens": 25, "total_tokens": 40},
            metadata={"tier": "cloud"}
        ))
        llm.config = LLMConfig(
            name="Cloud LLM",
            provider="anthropic",
            model_id="cloud-model",
            tier=LLMTier.CLOUD_PREMIUM,
            cost_per_1m_tokens=3.0
        )
        return llm

    def test_router_initialization(self, router):
        """Test router initialization."""
        assert len(router.llms) == 0
        assert len(router.task_routing) == 0
        assert len(router.fallback_chain) == 0
        assert router.default_llm is None

    def test_register_llm(self, router, mock_local_llm):
        """Test registering LLM."""
        router.register_llm("local", mock_local_llm)
        
        assert "local" in router.llms
        assert router.llms["local"] == mock_local_llm

    def test_register_multiple_llms(self, router, mock_local_llm, mock_cloud_llm):
        """Test registering multiple LLMs."""
        router.register_llm("local", mock_local_llm)
        router.register_llm("cloud", mock_cloud_llm)
        
        assert len(router.llms) == 2
        assert "local" in router.llms
        assert "cloud" in router.llms

    def test_set_default_llm(self, router, mock_local_llm):
        """Test setting default LLM."""
        router.register_llm("local", mock_local_llm)
        router.set_default_llm("local")
        
        assert router.default_llm == "local"

    def test_set_default_llm_not_registered(self, router):
        """Test setting default LLM that's not registered."""
        with pytest.raises(ValueError, match="LLM 'nonexistent' not registered"):
            router.set_default_llm("nonexistent")

    def test_set_task_routing(self, router):
        """Test setting task routing."""
        router.set_task_routing("coding", "local")
        router.set_task_routing("analysis", "cloud")
        
        assert router.task_routing["coding"] == "local"
        assert router.task_routing["analysis"] == "cloud"

    def test_set_fallback_chain(self, router):
        """Test setting fallback chain."""
        fallback_chain = ["local", "cloud", "backup"]
        router.set_fallback_chain(fallback_chain)
        
        assert router.fallback_chain == fallback_chain

    @pytest.mark.asyncio
    async def test_route_request_by_task(self, router, mock_local_llm, mock_cloud_llm):
        """Test routing request by task type."""
        router.register_llm("local", mock_local_llm)
        router.register_llm("cloud", mock_cloud_llm)
        router.set_task_routing("coding", "local")
        
        llm = await router.route_request(task_type="coding")
        
        assert llm == mock_local_llm

    @pytest.mark.asyncio
    async def test_route_request_by_tier(self, router, mock_local_llm, mock_cloud_llm):
        """Test routing request by tier preference."""
        router.register_llm("local", mock_local_llm)
        router.register_llm("cloud", mock_cloud_llm)
        
        # Prefer local tier
        llm = await router.route_request(tier_preference=LLMTier.LOCAL_FAST)
        assert llm == mock_local_llm
        
        # Prefer cloud tier
        llm = await router.route_request(tier_preference=LLMTier.CLOUD_PREMIUM)
        assert llm == mock_cloud_llm

    @pytest.mark.asyncio
    async def test_route_request_default(self, router, mock_local_llm):
        """Test routing request to default LLM."""
        router.register_llm("local", mock_local_llm)
        router.set_default_llm("local")
        
        llm = await router.route_request()
        
        assert llm == mock_local_llm

    @pytest.mark.asyncio
    async def test_route_request_no_match(self, router, mock_local_llm):
        """Test routing when no specific match found."""
        router.register_llm("local", mock_local_llm)
        
        # Should return first available LLM
        llm = await router.route_request(task_type="unknown")
        
        assert llm == mock_local_llm

    @pytest.mark.asyncio
    async def test_route_request_empty_router(self, router):
        """Test routing with empty router."""
        with pytest.raises(ValueError, match="No LLMs registered"):
            await router.route_request()

    @pytest.mark.asyncio
    async def test_generate_with_routing(self, router, mock_local_llm):
        """Test generate method with routing."""
        router.register_llm("local", mock_local_llm)
        router.set_default_llm("local")
        
        messages = [Message(role="user", content="Hello")]
        
        response = await router.generate(messages)
        
        assert isinstance(response, LLMResponse)
        assert response.content == "Local LLM response"
        mock_local_llm.generate.assert_called_once_with(messages)

    @pytest.mark.asyncio
    async def test_generate_with_task_routing(self, router, mock_local_llm, mock_cloud_llm):
        """Test generate method with task-specific routing."""
        router.register_llm("local", mock_local_llm)
        router.register_llm("cloud", mock_cloud_llm)
        router.set_task_routing("coding", "local")
        
        messages = [Message(role="user", content="Write a function")]
        
        response = await router.generate(messages, task_type="coding")
        
        assert response.content == "Local LLM response"
        mock_local_llm.generate.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_with_fallback(self, router, mock_local_llm, mock_cloud_llm):
        """Test generate method with fallback on failure."""
        # Make local LLM fail
        mock_local_llm.generate.side_effect = Exception("Local LLM failed")
        
        router.register_llm("local", mock_local_llm)
        router.register_llm("cloud", mock_cloud_llm)
        router.set_fallback_chain(["local", "cloud"])
        router.set_default_llm("local")
        
        messages = [Message(role="user", content="Hello")]
        
        response = await router.generate(messages)
        
        # Should fallback to cloud LLM
        assert response.content == "Cloud LLM response"
        mock_local_llm.generate.assert_called_once()
        mock_cloud_llm.generate.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_all_fallbacks_fail(self, router, mock_local_llm, mock_cloud_llm):
        """Test generate when all fallbacks fail."""
        # Make both LLMs fail
        mock_local_llm.generate.side_effect = Exception("Local failed")
        mock_cloud_llm.generate.side_effect = Exception("Cloud failed")
        
        router.register_llm("local", mock_local_llm)
        router.register_llm("cloud", mock_cloud_llm)
        router.set_fallback_chain(["local", "cloud"])
        router.set_default_llm("local")
        
        messages = [Message(role="user", content="Hello")]
        
        with pytest.raises(Exception, match="All LLMs in fallback chain failed"):
            await router.generate(messages)

    def test_get_available_llms(self, router, mock_local_llm, mock_cloud_llm):
        """Test getting available LLMs."""
        router.register_llm("local", mock_local_llm)
        router.register_llm("cloud", mock_cloud_llm)
        
        available = router.get_available_llms()
        
        assert len(available) == 2
        assert "local" in available
        assert "cloud" in available
        assert isinstance(available["local"], LLMConfig)
        assert isinstance(available["cloud"], LLMConfig)

    def test_estimate_cost(self, router, mock_local_llm, mock_cloud_llm):
        """Test cost estimation."""
        router.register_llm("local", mock_local_llm)
        router.register_llm("cloud", mock_cloud_llm)
        router.set_task_routing("coding", "local")
        router.set_task_routing("analysis", "cloud")
        
        # Local LLM should be free
        local_cost = router.estimate_cost("coding", 1000, 500)
        assert local_cost == 0.0
        
        # Cloud LLM should have cost
        cloud_cost = router.estimate_cost("analysis", 1000, 500)
        assert cloud_cost > 0.0

    def test_estimate_cost_no_routing(self, router, mock_local_llm):
        """Test cost estimation with no specific routing."""
        router.register_llm("local", mock_local_llm)
        router.set_default_llm("local")
        
        cost = router.estimate_cost("unknown_task", 1000, 500)
        assert cost == 0.0  # Local LLM is free

    def test_get_stats(self, router, mock_local_llm, mock_cloud_llm):
        """Test getting router statistics."""
        router.register_llm("local", mock_local_llm)
        router.register_llm("cloud", mock_cloud_llm)
        router.set_task_routing("coding", "local")
        router.set_fallback_chain(["local", "cloud"])
        
        stats = router.get_stats()
        
        assert isinstance(stats, dict)
        assert stats["total_llms"] == 2
        assert stats["task_routes"] == 1
        assert stats["fallback_chain_length"] == 2
        assert "registered_llms" in stats

    def test_remove_llm(self, router, mock_local_llm):
        """Test removing LLM."""
        router.register_llm("local", mock_local_llm)
        assert "local" in router.llms
        
        router.remove_llm("local")
        assert "local" not in router.llms

    def test_remove_llm_not_exists(self, router):
        """Test removing LLM that doesn't exist."""
        # Should not raise error
        router.remove_llm("nonexistent")

    def test_clear_routing(self, router):
        """Test clearing routing configuration."""
        router.set_task_routing("coding", "local")
        router.set_fallback_chain(["local", "cloud"])
        
        router.clear_routing()
        
        assert len(router.task_routing) == 0
        assert len(router.fallback_chain) == 0

    @pytest.mark.asyncio
    async def test_health_check(self, router, mock_local_llm, mock_cloud_llm):
        """Test health check of registered LLMs."""
        # Add health check method to mocks
        mock_local_llm.health_check = AsyncMock(return_value=True)
        mock_cloud_llm.health_check = AsyncMock(return_value=False)
        
        router.register_llm("local", mock_local_llm)
        router.register_llm("cloud", mock_cloud_llm)
        
        # Test if router has health check method
        if hasattr(router, 'health_check'):
            health = await router.health_check()
            assert isinstance(health, dict)
            assert "local" in health
            assert "cloud" in health

    @pytest.mark.asyncio
    async def test_concurrent_requests(self, router, mock_local_llm):
        """Test handling concurrent requests."""
        import asyncio
        
        router.register_llm("local", mock_local_llm)
        router.set_default_llm("local")
        
        messages = [Message(role="user", content="Hello")]
        
        # Create multiple concurrent requests
        tasks = [router.generate(messages) for _ in range(5)]
        responses = await asyncio.gather(*tasks)
        
        assert len(responses) == 5
        for response in responses:
            assert isinstance(response, LLMResponse)
            assert response.content == "Local LLM response"
        
        # Should have called generate 5 times
        assert mock_local_llm.generate.call_count == 5
