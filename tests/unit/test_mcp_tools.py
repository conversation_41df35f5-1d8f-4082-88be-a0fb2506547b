"""
Comprehensive unit tests for MCP tools.
"""

import pytest
import tempfile
import json
import sqlite3
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from agent_swarm.mcp.tools import (
    FileSystemTool,
    DatabaseTool,
    WebSearchTool,
    CodeExecutionTool,
    GitTool,
)


@pytest.mark.unit
class TestFileSystemTool:
    """Test FileSystemTool functionality."""

    def test_get_schema(self):
        """Test FileSystemTool schema."""
        schema = FileSystemTool.get_schema()
        
        assert "read_file" in schema
        assert "write_file" in schema
        assert "list_directory" in schema
        
        # Check schema structure
        read_schema = schema["read_file"]
        assert "description" in read_schema
        assert "parameters" in read_schema
        assert read_schema["parameters"]["type"] == "object"
        assert "path" in read_schema["parameters"]["properties"]

    @pytest.mark.asyncio
    async def test_read_file(self):
        """Test reading files."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("Hello, MCP!")
            temp_path = f.name
        
        try:
            result = await FileSystemTool.execute("read_file", {"path": temp_path})
            
            assert result["success"] is True
            assert result["content"] == "Hello, MCP!"
            assert result["path"] == temp_path
            
        finally:
            Path(temp_path).unlink()

    @pytest.mark.asyncio
    async def test_write_file(self):
        """Test writing files."""
        with tempfile.NamedTemporaryFile(delete=False, suffix='.txt') as f:
            temp_path = f.name
        
        try:
            result = await FileSystemTool.execute("write_file", {
                "path": temp_path,
                "content": "Hello from MCP!"
            })
            
            assert result["success"] is True
            assert result["path"] == temp_path
            assert result["bytes_written"] == len("Hello from MCP!")
            
            # Verify file was written
            with open(temp_path, 'r') as f:
                content = f.read()
            assert content == "Hello from MCP!"
            
        finally:
            Path(temp_path).unlink()

    @pytest.mark.asyncio
    async def test_list_directory(self):
        """Test listing directories."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create test files
            (temp_path / "file1.txt").write_text("content1")
            (temp_path / "file2.py").write_text("print('hello')")
            (temp_path / "subdir").mkdir()
            
            result = await FileSystemTool.execute("list_directory", {"path": str(temp_path)})
            
            assert result["success"] is True
            assert result["path"] == str(temp_path)
            assert len(result["entries"]) == 3
            
            # Check entries
            entry_names = [entry["name"] for entry in result["entries"]]
            assert "file1.txt" in entry_names
            assert "file2.py" in entry_names
            assert "subdir" in entry_names

    @pytest.mark.asyncio
    async def test_file_not_found(self):
        """Test handling of non-existent files."""
        with pytest.raises(Exception) as exc_info:
            await FileSystemTool.execute("read_file", {"path": "/nonexistent/file.txt"})
        
        assert "Failed to read file" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_invalid_tool(self):
        """Test handling of invalid tool names."""
        with pytest.raises(ValueError) as exc_info:
            await FileSystemTool.execute("invalid_tool", {"path": "test.txt"})
        
        assert "Unknown filesystem tool" in str(exc_info.value)


@pytest.mark.unit
class TestDatabaseTool:
    """Test DatabaseTool functionality."""

    def test_get_schema(self):
        """Test DatabaseTool schema."""
        schema = DatabaseTool.get_schema()
        
        assert "query_sqlite" in schema
        
        query_schema = schema["query_sqlite"]
        assert "description" in query_schema
        assert "parameters" in query_schema
        assert "database" in query_schema["parameters"]["properties"]
        assert "query" in query_schema["parameters"]["properties"]

    @pytest.mark.asyncio
    async def test_sqlite_query(self):
        """Test SQLite query execution."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            # Create test database
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    email TEXT UNIQUE
                )
            """)
            cursor.execute("INSERT INTO users (name, email) VALUES (?, ?)", 
                         ("John Doe", "<EMAIL>"))
            cursor.execute("INSERT INTO users (name, email) VALUES (?, ?)", 
                         ("Jane Smith", "<EMAIL>"))
            conn.commit()
            conn.close()
            
            # Test query
            result = await DatabaseTool.execute("query_sqlite", {
                "database": db_path,
                "query": "SELECT * FROM users WHERE name LIKE '%John%'"
            })
            
            assert result["success"] is True
            assert len(result["rows"]) == 1
            assert result["rows"][0]["name"] == "John Doe"
            assert result["rows"][0]["email"] == "<EMAIL>"
            
        finally:
            Path(db_path).unlink()

    @pytest.mark.asyncio
    async def test_sqlite_invalid_query(self):
        """Test handling of invalid SQL queries."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            # Create empty database
            conn = sqlite3.connect(db_path)
            conn.close()
            
            with pytest.raises(Exception) as exc_info:
                await DatabaseTool.execute("query_sqlite", {
                    "database": db_path,
                    "query": "INVALID SQL QUERY"
                })
            
            assert "Failed to execute query" in str(exc_info.value)
            
        finally:
            Path(db_path).unlink()


@pytest.mark.unit
class TestWebSearchTool:
    """Test WebSearchTool functionality."""

    def test_get_schema(self):
        """Test WebSearchTool schema."""
        schema = WebSearchTool.get_schema()
        
        assert "search_web" in schema
        
        search_schema = schema["search_web"]
        assert "description" in search_schema
        assert "parameters" in search_schema
        assert "query" in search_schema["parameters"]["properties"]
        assert "max_results" in search_schema["parameters"]["properties"]

    @pytest.mark.asyncio
    async def test_web_search_mock(self):
        """Test web search with mocked response."""
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Mock response
            mock_response = Mock()
            mock_response.json = AsyncMock(return_value={
                "items": [
                    {
                        "title": "Test Result 1",
                        "link": "https://example.com/1",
                        "snippet": "This is a test result"
                    },
                    {
                        "title": "Test Result 2", 
                        "link": "https://example.com/2",
                        "snippet": "Another test result"
                    }
                ]
            })
            mock_get.return_value.__aenter__.return_value = mock_response
            
            result = await WebSearchTool.execute("search_web", {
                "query": "test query",
                "max_results": 2
            })
            
            assert result["success"] is True
            assert len(result["results"]) == 2
            assert result["results"][0]["title"] == "Test Result 1"
            assert result["results"][1]["title"] == "Test Result 2"

    @pytest.mark.asyncio
    async def test_web_search_error(self):
        """Test web search error handling."""
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.side_effect = Exception("Network error")
            
            with pytest.raises(Exception) as exc_info:
                await WebSearchTool.execute("search_web", {
                    "query": "test query"
                })
            
            assert "Failed to search web" in str(exc_info.value)


@pytest.mark.unit
class TestCodeExecutionTool:
    """Test CodeExecutionTool functionality."""

    def test_get_schema(self):
        """Test CodeExecutionTool schema."""
        schema = CodeExecutionTool.get_schema()
        
        assert "execute_python" in schema
        
        exec_schema = schema["execute_python"]
        assert "description" in exec_schema
        assert "parameters" in exec_schema
        assert "code" in exec_schema["parameters"]["properties"]
        assert "timeout" in exec_schema["parameters"]["properties"]

    @pytest.mark.asyncio
    async def test_python_execution(self):
        """Test Python code execution."""
        code = """
result = 2 + 2
print(f"Result: {result}")
"""
        
        result = await CodeExecutionTool.execute("execute_python", {
            "code": code,
            "timeout": 5
        })
        
        assert result["success"] is True
        assert "Result: 4" in result["output"]
        assert result["exit_code"] == 0

    @pytest.mark.asyncio
    async def test_python_execution_error(self):
        """Test Python code execution with error."""
        code = """
# This will cause an error
undefined_variable + 1
"""
        
        result = await CodeExecutionTool.execute("execute_python", {
            "code": code,
            "timeout": 5
        })
        
        assert result["success"] is False
        assert "NameError" in result["error"]
        assert result["exit_code"] != 0

    @pytest.mark.asyncio
    async def test_python_execution_timeout(self):
        """Test Python code execution timeout."""
        code = """
import time
time.sleep(10)  # This will timeout
"""
        
        result = await CodeExecutionTool.execute("execute_python", {
            "code": code,
            "timeout": 1
        })
        
        assert result["success"] is False
        assert "timeout" in result["error"].lower()


@pytest.mark.unit
class TestGitTool:
    """Test GitTool functionality."""

    def test_get_schema(self):
        """Test GitTool schema."""
        schema = GitTool.get_schema()
        
        assert "git_status" in schema
        assert "git_log" in schema
        assert "git_diff" in schema
        
        status_schema = schema["git_status"]
        assert "description" in status_schema
        assert "parameters" in status_schema

    @pytest.mark.asyncio
    async def test_git_status_mock(self):
        """Test git status with mocked subprocess."""
        with patch('asyncio.create_subprocess_exec') as mock_subprocess:
            # Mock process
            mock_process = Mock()
            mock_process.communicate = AsyncMock(return_value=(
                b"On branch main\nnothing to commit, working tree clean",
                b""
            ))
            mock_process.returncode = 0
            mock_subprocess.return_value = mock_process
            
            result = await GitTool.execute("git_status", {
                "repository": "/test/repo"
            })
            
            assert result["success"] is True
            assert "working tree clean" in result["output"]

    @pytest.mark.asyncio
    async def test_git_log_mock(self):
        """Test git log with mocked subprocess."""
        with patch('asyncio.create_subprocess_exec') as mock_subprocess:
            mock_process = Mock()
            mock_process.communicate = AsyncMock(return_value=(
                b"commit abc123\nAuthor: Test User\nDate: 2024-01-01\n\nTest commit",
                b""
            ))
            mock_process.returncode = 0
            mock_subprocess.return_value = mock_process
            
            result = await GitTool.execute("git_log", {
                "repository": "/test/repo",
                "max_commits": 5
            })
            
            assert result["success"] is True
            assert "Test commit" in result["output"]

    @pytest.mark.asyncio
    async def test_git_error(self):
        """Test git command error handling."""
        with patch('asyncio.create_subprocess_exec') as mock_subprocess:
            mock_process = Mock()
            mock_process.communicate = AsyncMock(return_value=(
                b"",
                b"fatal: not a git repository"
            ))
            mock_process.returncode = 128
            mock_subprocess.return_value = mock_process
            
            with pytest.raises(Exception) as exc_info:
                await GitTool.execute("git_status", {
                    "repository": "/not/a/repo"
                })
            
            assert "Git command failed" in str(exc_info.value)


@pytest.mark.unit
class TestMCPToolsIntegration:
    """Test integration between different MCP tools."""

    @pytest.mark.asyncio
    async def test_file_and_git_integration(self):
        """Test using filesystem and git tools together."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create a test file
            test_file = temp_path / "test.py"
            await FileSystemTool.execute("write_file", {
                "path": str(test_file),
                "content": "print('Hello, Git!')"
            })
            
            # Verify file was created
            result = await FileSystemTool.execute("read_file", {
                "path": str(test_file)
            })
            
            assert result["success"] is True
            assert "Hello, Git!" in result["content"]

    @pytest.mark.asyncio
    async def test_database_and_filesystem_integration(self):
        """Test using database and filesystem tools together."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            db_file = temp_path / "test.db"
            
            # Create database using filesystem tool (write SQL script)
            sql_script = """
CREATE TABLE test_table (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL
);

INSERT INTO test_table (name) VALUES ('Test Entry');
"""
            
            await FileSystemTool.execute("write_file", {
                "path": str(temp_path / "schema.sql"),
                "content": sql_script
            })
            
            # Create database
            conn = sqlite3.connect(str(db_file))
            conn.executescript(sql_script)
            conn.close()
            
            # Query database using DatabaseTool
            result = await DatabaseTool.execute("query_sqlite", {
                "database": str(db_file),
                "query": "SELECT * FROM test_table"
            })
            
            assert result["success"] is True
            assert len(result["rows"]) == 1
            assert result["rows"][0]["name"] == "Test Entry"

    def test_all_tools_have_consistent_interface(self):
        """Test that all tools follow consistent interface patterns."""
        tools = [FileSystemTool, DatabaseTool, WebSearchTool, CodeExecutionTool, GitTool]
        
        for tool in tools:
            # All tools should have get_schema method
            assert hasattr(tool, 'get_schema'), f"{tool.__name__} missing get_schema"
            assert callable(tool.get_schema), f"{tool.__name__}.get_schema not callable"
            
            # All tools should have execute method
            assert hasattr(tool, 'execute'), f"{tool.__name__} missing execute"
            assert callable(tool.execute), f"{tool.__name__}.execute not callable"
            
            # Schema should be properly structured
            schema = tool.get_schema()
            assert isinstance(schema, dict), f"{tool.__name__} schema not dict"
            
            for tool_name, tool_schema in schema.items():
                assert "description" in tool_schema, f"{tool_name} missing description"
                assert "parameters" in tool_schema, f"{tool_name} missing parameters"
                assert isinstance(tool_schema["parameters"], dict), f"{tool_name} parameters not dict"
