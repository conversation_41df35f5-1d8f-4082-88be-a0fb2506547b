"""
Unit tests for the intelligent context engine.
"""

import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock

import pytest

from agent_swarm.context.context_engine import (
    ContextEngine,
    ContextResult,
    ProjectStructure,
    ContextProvider,
)


@pytest.fixture
def temp_project_dir():
    """Create a temporary project directory with sample files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        project_path = Path(temp_dir)

        # Create sample Python files
        (project_path / "main.py").write_text("""
def main():
    print("Hello, World!")

if __name__ == "__main__":
    main()
""")

        (project_path / "utils.py").write_text("""
def add_numbers(a, b):
    return a + b

def multiply_numbers(a, b):
    return a * b
""")

        # Create a subdirectory with more files
        src_dir = project_path / "src"
        src_dir.mkdir()
        (src_dir / "auth.py").write_text("""
class AuthManager:
    def __init__(self):
        self.users = {}

    def login(self, username, password):
        if username in self.users:
            return self.users[username] == password
        return False
""")

        # Create README
        (project_path / "README.md").write_text("""
# Test Project

This is a test project for context engine testing.

## Features
- Authentication system
- Utility functions
- Main application
""")

        yield project_path


@pytest.fixture
def mock_context_provider():
    """Mock context provider for testing."""
    provider = MagicMock(spec=ContextProvider)
    provider.get_context = AsyncMock(return_value=ContextResult(
        query="test",
        results=[{"content": "test content", "file_path": "test.py"}],
        total_found=1,
        retrieval_time=0.1,
        source="mock"
    ))
    provider.add_context = AsyncMock()
    return provider


@pytest.fixture
def context_engine():
    """Create context engine for testing."""
    engine = ContextEngine()
    return engine


@pytest.mark.unit
class TestContextEngine:
    """Test ContextEngine functionality."""

    def test_engine_initialization(self, context_engine):
        """Test context engine initialization."""
        assert context_engine.providers == {}
        assert context_engine.analyzer is not None
        assert context_engine.project_structure is None
        assert not context_engine._initialized

    async def test_initialize_without_project(self, context_engine):
        """Test context engine initialization without project."""
        await context_engine.initialize()
        assert context_engine._initialized
        assert context_engine.project_structure is None

    async def test_initialize_with_project(self, context_engine, temp_project_dir):
        """Test context engine initialization with project."""
        await context_engine.initialize(str(temp_project_dir))
        assert context_engine._initialized
        assert context_engine.project_structure is not None
        assert isinstance(context_engine.project_structure, ProjectStructure)

    def test_register_provider(self, context_engine, mock_context_provider):
        """Test registering a context provider."""
        context_engine.register_provider("test_provider", mock_context_provider)
        assert "test_provider" in context_engine.providers
        assert context_engine.providers["test_provider"] == mock_context_provider

    async def test_get_context(self, context_engine, mock_context_provider):
        """Test getting context from providers."""
        context_engine.register_provider("test_provider", mock_context_provider)

        results = await context_engine.get_context("test query")

        assert len(results) == 1
        assert isinstance(results[0], ContextResult)
        assert results[0].query == "test"
        mock_context_provider.get_context.assert_called_once()

    async def test_get_context_multiple_providers(self, context_engine):
        """Test getting context from multiple providers."""
        provider1 = MagicMock(spec=ContextProvider)
        provider1.get_context = AsyncMock(return_value=ContextResult(
            query="test", results=[], total_found=0, retrieval_time=0.1, source="provider1"
        ))

        provider2 = MagicMock(spec=ContextProvider)
        provider2.get_context = AsyncMock(return_value=ContextResult(
            query="test", results=[], total_found=0, retrieval_time=0.1, source="provider2"
        ))

        context_engine.register_provider("provider1", provider1)
        context_engine.register_provider("provider2", provider2)

        results = await context_engine.get_context("test query")

        assert len(results) == 2
        provider1.get_context.assert_called_once()
        provider2.get_context.assert_called_once()

    async def test_add_context(self, context_engine, mock_context_provider):
        """Test adding context to providers."""
        context_engine.register_provider("test_provider", mock_context_provider)

        content = "test content"
        metadata = {"source": "test.py"}

        await context_engine.add_context(content, metadata, "test_provider")

        mock_context_provider.add_context.assert_called_once_with(content, metadata)

    async def test_add_context_all_providers(self, context_engine):
        """Test adding context to all providers."""
        provider1 = MagicMock(spec=ContextProvider)
        provider1.add_context = AsyncMock()
        provider2 = MagicMock(spec=ContextProvider)
        provider2.add_context = AsyncMock()

        context_engine.register_provider("provider1", provider1)
        context_engine.register_provider("provider2", provider2)

        content = "test content"
        metadata = {"source": "test.py"}

        await context_engine.add_context(content, metadata)  # No specific provider

        provider1.add_context.assert_called_once_with(content, metadata)
        provider2.add_context.assert_called_once_with(content, metadata)

    def test_get_stats(self, context_engine):
        """Test getting context engine statistics."""
        stats = context_engine.get_stats()

        assert isinstance(stats, dict)
        assert "initialized" in stats
        assert "providers" in stats
        assert "provider_count" in stats
        assert stats["provider_count"] == 0  # No providers registered yet

    async def test_get_smart_context(self, context_engine, mock_context_provider):
        """Test getting smart context with intelligence."""
        context_engine.register_provider("test_provider", mock_context_provider)

        # Test without project structure (fallback mode)
        results = await context_engine.get_smart_context("test query")

        assert isinstance(results, list)
        # Results depend on implementation, but should be a list

    def test_determine_priority(self, context_engine):
        """Test priority determination."""
        # Test with no project structure
        priority = context_engine._determine_priority("test.py", "def test(): pass")
        assert priority is not None

    def test_determine_context_type(self, context_engine):
        """Test context type determination."""
        # Test different file types
        assert context_engine._determine_context_type("main.py", "def main(): pass") == "implementation"
        assert context_engine._determine_context_type("test_file.py", "def test(): pass") == "test"
        assert context_engine._determine_context_type("README.md", "# Title") == "documentation"
        assert context_engine._determine_context_type("config.json", "{}") == "config"

    def test_generate_summary(self, context_engine):
        """Test summary generation."""
        content = '"""This is a docstring."""\ndef function():\n    pass'
        summary = context_engine._generate_summary(content, "test.py")
        assert isinstance(summary, str)
        assert len(summary) > 0

    def test_extract_key_concepts(self, context_engine):
        """Test key concept extraction."""
        content = "class TestClass:\n    def method(self):\n        from module import something"
        concepts = context_engine._extract_key_concepts(content, "test.py")
        assert isinstance(concepts, list)
        # Should extract class names, function names, imports
        assert any("TestClass" in concept for concept in concepts)

    def test_find_related_files(self, context_engine):
        """Test finding related files."""
        # Test without project structure
        related = context_engine._find_related_files("test.py")
        assert isinstance(related, list)
        # Without project structure, should return empty list
        assert len(related) == 0


@pytest.mark.unit
class TestContextModels:
    """Test context engine data models."""

    def test_context_result_creation(self):
        """Test ContextResult model creation."""
        result = ContextResult(
            query="test query",
            results=[{"content": "test", "file_path": "test.py"}],
            total_found=1,
            retrieval_time=0.1,
            source="test"
        )

        assert result.query == "test query"
        assert len(result.results) == 1
        assert result.total_found == 1
        assert result.retrieval_time == 0.1
        assert result.source == "test"

    def test_project_structure_creation(self):
        """Test ProjectStructure model creation."""
        from agent_swarm.context.context_engine import ProjectType

        structure = ProjectStructure(
            project_type=ProjectType.PYTHON_PACKAGE,
            main_language="python",
            frameworks=["pytest"],
            entry_points=["main.py"],
            core_modules=["core.py"],
            config_files=["pyproject.toml"],
            documentation_files=["README.md"],
            test_directories=["tests/"],
            package_structure={"src": ["main.py"]},
            dependencies=["pytest"],
            confidence_score=0.9
        )

        assert structure.project_type == ProjectType.PYTHON_PACKAGE
        assert structure.main_language == "python"
        assert "pytest" in structure.frameworks
        assert structure.confidence_score == 0.9


@pytest.mark.integration
class TestContextEngineIntegration:
    """Integration tests for context engine."""

    async def test_full_workflow_with_project(self, temp_project_dir):
        """Test complete context engine workflow with project."""
        engine = ContextEngine()

        # Initialize with project
        await engine.initialize(str(temp_project_dir))
        assert engine._initialized
        assert engine.project_structure is not None

        # Get stats
        stats = engine.get_stats()
        assert stats["initialized"]

    async def test_workflow_with_providers(self, temp_project_dir):
        """Test workflow with context providers."""
        engine = ContextEngine()

        # Create mock provider
        provider = MagicMock(spec=ContextProvider)
        provider.get_context = AsyncMock(return_value=ContextResult(
            query="test", results=[], total_found=0, retrieval_time=0.1, source="test"
        ))
        provider.add_context = AsyncMock()

        # Register provider and initialize
        engine.register_provider("test_provider", provider)
        await engine.initialize(str(temp_project_dir))

        # Test context operations
        results = await engine.get_context("test query")
        assert len(results) == 1

        await engine.add_context("test content", {"source": "test"})
        provider.add_context.assert_called_once()
