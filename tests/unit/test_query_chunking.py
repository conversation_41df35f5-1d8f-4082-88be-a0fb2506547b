#!/usr/bin/env python3
"""
Test Query Chunking and Information Extraction functionality.
"""

import sys
import asyncio
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

async def test_query_analyzer_basic():
    """Test basic query analyzer functionality."""
    print("🧪 Testing Query Analyzer...")
    
    try:
        from agent_swarm.core.query_analyzer import QueryAnalyzer, QueryType, Priority
        
        analyzer = QueryAnalyzer()
        
        # Test basic analysis
        test_queries = [
            "How do we calculate the best agent?",
            "Edit this TypeScript file and update the types",
            "Create a proper React project with authentication",
            "Explain this function @main.py that imports from utils.helpers",
            "Search for async functions and then analyze the performance"
        ]
        
        print("   Testing query analysis...")
        for query in test_queries:
            extracted = analyzer.analyze_query(query)
            print(f"   '{query[:40]}...'")
            print(f"     Primary Action: {extracted.primary_action}")
            print(f"     Complexity: {extracted.complexity_score:.2f}")
            print(f"     Chunks: {len(extracted.chunks)}")
            print(f"     Technologies: {extracted.technologies}")
            print(f"     Files: {extracted.target_files + extracted.context_files}")
            print()
        
        print("✅ Query analyzer works!")
        return True
        
    except ImportError as e:
        print(f"❌ Query analyzer not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Query analyzer test failed: {e}")
        return False

async def test_query_chunking():
    """Test query chunking functionality."""
    print("\n🧪 Testing Query Chunking...")
    
    try:
        from agent_swarm.core.query_analyzer import QueryAnalyzer
        
        analyzer = QueryAnalyzer()
        
        # Test complex multi-part queries
        complex_queries = [
            "Create a React project and then add authentication and also setup TypeScript",
            "Edit the main.py file, update the imports from utils.helpers, and then run the tests",
            "Search for all async functions, analyze their performance, and create a report",
            "Explain @auth.py that uses session.py, then refactor it to use JWT tokens"
        ]
        
        for query in complex_queries:
            print(f"   Query: '{query}'")
            
            extracted = analyzer.analyze_query(query)
            
            print(f"     Chunks: {len(extracted.chunks)}")
            for chunk in extracted.chunks:
                print(f"       {chunk.chunk_id}: {chunk.text[:30]}... ({chunk.query_type.value}, {chunk.priority.value})")
            
            print(f"     Actions: {[chunk.actions for chunk in extracted.chunks]}")
            print(f"     Files: {[chunk.files for chunk in extracted.chunks]}")
            print()
        
        print("✅ Query chunking works!")
        return True
        
    except Exception as e:
        print(f"❌ Query chunking test failed: {e}")
        return False

async def test_information_extraction():
    """Test information extraction from queries."""
    print("\n🧪 Testing Information Extraction...")
    
    try:
        from agent_swarm.core.query_analyzer import QueryAnalyzer
        
        analyzer = QueryAnalyzer()
        
        # Test queries with rich information
        info_queries = [
            "Edit the TypeScript React component @src/components/Auth.tsx and update the interfaces",
            "Create a Python FastAPI project with PostgreSQL database and JWT authentication",
            "Analyze the authentication system that imports from utils.session and uses Redis",
            "Search for all React hooks and then refactor them to use TypeScript generics"
        ]
        
        for query in info_queries:
            print(f"   Query: '{query[:50]}...'")
            
            extracted = analyzer.analyze_query(query)
            
            print(f"     Primary Action: {extracted.primary_action}")
            print(f"     Technologies: {extracted.technologies}")
            print(f"     Target Files: {extracted.target_files}")
            print(f"     Context Files: {extracted.context_files}")
            print(f"     Dependencies: {extracted.dependencies}")
            print(f"     Complexity: {extracted.complexity_score:.2f}")
            print(f"     Estimated Steps: {extracted.estimated_steps}")
            print()
        
        print("✅ Information extraction works!")
        return True
        
    except Exception as e:
        print(f"❌ Information extraction test failed: {e}")
        return False

async def test_dependency_discovery():
    """Test dependency discovery patterns."""
    print("\n🧪 Testing Dependency Discovery...")
    
    try:
        from agent_swarm.core.query_analyzer import QueryAnalyzer
        
        analyzer = QueryAnalyzer()
        
        # Test queries with dependency patterns
        dependency_queries = [
            "Explain @main.py that imports from utils.helpers",
            "The auth.py file uses session_manager from utils.session",
            "This component depends on the UserInterface from types/user.ts",
            "Fix the bug in login.py that references the validate function from auth.utils"
        ]
        
        for query in dependency_queries:
            print(f"   Query: '{query}'")
            
            extracted = analyzer.analyze_query(query)
            
            print(f"     Dependencies found: {extracted.dependencies}")
            print(f"     Files mentioned: {extracted.target_files + extracted.context_files}")
            
            # Check if dependencies are properly extracted
            for chunk in extracted.chunks:
                if chunk.dependencies:
                    print(f"     Chunk '{chunk.chunk_id}' dependencies: {chunk.dependencies}")
            print()
        
        print("✅ Dependency discovery works!")
        return True
        
    except Exception as e:
        print(f"❌ Dependency discovery test failed: {e}")
        return False

async def test_complexity_assessment():
    """Test complexity assessment."""
    print("\n🧪 Testing Complexity Assessment...")
    
    try:
        from agent_swarm.core.query_analyzer import QueryAnalyzer
        
        analyzer = QueryAnalyzer()
        
        # Test queries with different complexity levels
        complexity_queries = [
            ("Simple query", "Show me the version"),
            ("Medium query", "Edit the main.py file"),
            ("Complex query", "Create a React TypeScript project with authentication"),
            ("Very complex", "Refactor the entire authentication system, update all TypeScript interfaces, create comprehensive tests, and deploy to production")
        ]
        
        for label, query in complexity_queries:
            extracted = analyzer.analyze_query(query)
            
            print(f"   {label}: '{query[:40]}...'")
            print(f"     Complexity Score: {extracted.complexity_score:.2f}")
            print(f"     Estimated Steps: {extracted.estimated_steps}")
            print(f"     Chunks: {len(extracted.chunks)}")
            print(f"     Technologies: {len(extracted.technologies)}")
            print()
        
        print("✅ Complexity assessment works!")
        return True
        
    except Exception as e:
        print(f"❌ Complexity assessment test failed: {e}")
        return False

async def test_orchestrator_integration():
    """Test integration with orchestrator."""
    print("\n🧪 Testing Orchestrator Integration...")
    
    try:
        from agent_swarm.core.orchestrator import MultiStepOrchestrator
        
        orchestrator = MultiStepOrchestrator()
        
        # Test that orchestrator uses query analyzer
        has_analyzer = hasattr(orchestrator, 'query_analyzer')
        print(f"   Orchestrator has query analyzer: {has_analyzer}")
        
        if has_analyzer:
            # Test enhanced analysis
            test_query = "How do we calculate the best agent and then create a report?"
            analysis = await orchestrator._analyze_request(test_query)
            
            print(f"   Enhanced analysis for: '{test_query}'")
            print(f"     Needs multi-step: {analysis['needs_multi_step']}")
            print(f"     Request type: {analysis['request_type']}")
            print(f"     Complexity: {analysis['complexity']}")
            print(f"     Estimated steps: {analysis['estimated_steps']}")
            
            if 'chunks' in analysis:
                print(f"     Chunks: {len(analysis['chunks'])}")
                for chunk in analysis['chunks']:
                    print(f"       {chunk['id']}: {chunk['type']} - {chunk['text'][:30]}...")
        
        print("✅ Orchestrator integration works!")
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator integration test failed: {e}")
        return False

async def test_real_world_scenarios():
    """Test real-world development scenarios."""
    print("\n🧪 Testing Real-World Scenarios...")
    
    try:
        from agent_swarm.core.query_analyzer import QueryAnalyzer
        
        analyzer = QueryAnalyzer()
        
        # Real-world development scenarios
        scenarios = [
            "There's a bug in the login system @src/auth/login.py that imports from utils.session, can you fix it?",
            "Create a new React component for user profiles, add TypeScript interfaces, and write unit tests",
            "Refactor the database layer to use async/await, update all the models, and migrate the existing data",
            "The API is slow, analyze @src/api/routes.py and @src/database/queries.py, then optimize the performance"
        ]
        
        for scenario in scenarios:
            print(f"   Scenario: '{scenario[:60]}...'")
            
            extracted = analyzer.analyze_query(scenario)
            
            print(f"     Analysis:")
            print(f"       Primary Action: {extracted.primary_action}")
            print(f"       Complexity: {extracted.complexity_score:.2f}")
            print(f"       Steps: {extracted.estimated_steps}")
            print(f"       Technologies: {extracted.technologies}")
            print(f"       Files: {len(extracted.target_files + extracted.context_files)}")
            print(f"       Dependencies: {len(extracted.dependencies)}")
            
            # Show chunk breakdown
            print(f"       Chunks ({len(extracted.chunks)}):")
            for chunk in extracted.chunks:
                print(f"         {chunk.chunk_id}: {chunk.query_type.value} - {chunk.text[:40]}...")
            print()
        
        print("✅ Real-world scenarios work!")
        return True
        
    except Exception as e:
        print(f"❌ Real-world scenarios test failed: {e}")
        return False

async def main():
    """Run all query chunking tests."""
    print("🚀 Testing Query Chunking & Information Extraction")
    print("=" * 70)
    
    tests = [
        test_query_analyzer_basic,
        test_query_chunking,
        test_information_extraction,
        test_dependency_discovery,
        test_complexity_assessment,
        test_orchestrator_integration,
        test_real_world_scenarios,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if await test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Query Chunking & Information Extraction fully functional!")
        print("\n💡 Revolutionary capabilities:")
        print("   ✅ Intelligent query chunking by semantic meaning")
        print("   ✅ Comprehensive information extraction")
        print("   ✅ Automatic dependency discovery")
        print("   ✅ Complexity assessment and step estimation")
        print("   ✅ Technology and file pattern recognition")
        print("   ✅ Priority assignment for query chunks")
        print("   ✅ Integration with multi-step orchestrator")
        print("\n🔮 Try these complex queries:")
        print("   • 'Create a React TypeScript project with auth and tests'")
        print("   • 'Fix the bug in @login.py that imports from utils.session'")
        print("   • 'Refactor the API, update types, and optimize performance'")
        print("   • 'Analyze @main.py dependencies and create documentation'")
        return True
    else:
        print(f"❌ {total - passed} features need work")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
