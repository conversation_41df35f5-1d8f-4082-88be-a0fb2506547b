#!/usr/bin/env python3
"""
Simple test for core revolutionary features.
"""

import sys
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_intent_detection():
    """Test intent detection."""
    print("🧪 Testing Intent Detection...")
    
    try:
        from agent_swarm.core.intent_detector import IntentDetector, IntentType
        
        detector = IntentDetector()
        
        # Test natural language search
        intent = detector.detect_intent("search for async functions")
        print(f"   Input: 'search for async functions'")
        print(f"   Intent: {intent.intent_type.value}")
        print(f"   Confidence: {intent.confidence:.1%}")
        print(f"   Agent: {intent.suggested_agent}")
        print(f"   Tools: {intent.suggested_tools}")
        
        assert intent.intent_type == IntentType.SEARCH
        assert intent.confidence > 0.5
        
        print("✅ Intent detection works!")
        return True
        
    except ImportError as e:
        print(f"❌ Intent detection not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Intent detection failed: {e}")
        return False

def test_agent_routing():
    """Test agent routing."""
    print("\n🧪 Testing Agent Routing...")
    
    try:
        from agent_swarm.core.agent_router import AgentRouter
        
        router = AgentRouter()
        
        # Test agent recommendations
        recommendations = router.get_agent_recommendations("search for functions")
        print(f"   Input: 'search for functions'")
        print(f"   Recommendations: {len(recommendations)} agents")
        
        if recommendations:
            best = recommendations[0]
            print(f"   Best agent: {best['agent']} ({best['score']:.1%})")
            print(f"   Reasoning: {best['reasoning']}")
        
        assert len(recommendations) > 0
        
        print("✅ Agent routing works!")
        return True
        
    except ImportError as e:
        print(f"❌ Agent routing not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Agent routing failed: {e}")
        return False

def test_shell_integration():
    """Test shell integration."""
    print("\n🧪 Testing Shell Integration...")
    
    try:
        from agent_swarm.cli.interactive_shell import InteractiveShell
        
        shell = InteractiveShell(verbose=False, auto_index=False)
        
        # Test attributes
        has_router = hasattr(shell, 'agent_router')
        has_show_info = hasattr(shell, 'show_agent_info')
        has_ctrl_c = hasattr(shell, '_interrupt_threshold')
        
        print(f"   Has agent router: {has_router}")
        print(f"   Has show agent info: {has_show_info}")
        print(f"   Has double Ctrl+C: {has_ctrl_c}")
        
        assert has_router
        assert has_show_info
        assert has_ctrl_c
        
        print("✅ Shell integration works!")
        return True
        
    except ImportError as e:
        print(f"❌ Shell integration not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Shell integration failed: {e}")
        return False

def test_natural_language_flow():
    """Test complete natural language flow."""
    print("\n🧪 Testing Natural Language Flow...")
    
    try:
        from agent_swarm.core.intent_detector import IntentDetector
        from agent_swarm.core.agent_router import AgentRouter
        
        # Complete flow
        user_input = "find all async functions"
        
        # 1. Detect intent
        detector = IntentDetector()
        intent = detector.detect_intent(user_input)
        
        # 2. Get recommendations
        router = AgentRouter()
        recommendations = router.get_agent_recommendations(user_input)
        
        print(f"   Input: '{user_input}'")
        print(f"   Intent: {intent.intent_type.value} ({intent.confidence:.1%})")
        print(f"   Recommended agent: {recommendations[0]['agent'] if recommendations else 'None'}")
        
        assert intent.confidence > 0.3
        assert len(recommendations) > 0
        
        print("✅ Natural language flow works!")
        return True
        
    except ImportError as e:
        print(f"❌ Natural language flow not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Natural language flow failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Core Revolutionary Features")
    print("=" * 50)
    
    tests = [
        test_intent_detection,
        test_agent_routing,
        test_shell_integration,
        test_natural_language_flow,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All core features working!")
        print("\n💡 Revolutionary Agent-Swarm features:")
        print("   ✅ Natural language intent detection")
        print("   ✅ Intelligent agent routing")
        print("   ✅ Agent visibility and recommendations")
        print("   ✅ Double Ctrl+C graceful quit")
        print("   ✅ Complete natural language workflow")
        return True
    else:
        print(f"❌ {total - passed} features need work")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
