"""
Comprehensive unit tests for UnifiedContext engine.
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

from agent_swarm.context.engine import UnifiedContext, create_unified_context
from agent_swarm.context.context_engine import QueryIntent, ResponseMode, ContextDepth


@pytest.mark.unit
class TestUnifiedContext:
    """Test UnifiedContext functionality."""

    @pytest.fixture
    def temp_project(self):
        """Create a temporary project for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_path = Path(temp_dir)
            
            # Create project structure
            (project_path / "src").mkdir()
            (project_path / "src" / "main.py").write_text("""
def main():
    print("Hello, World!")
    return 0

if __name__ == "__main__":
    exit(main())
""".strip())
            
            (project_path / "README.md").write_text("# Test Project\n\nA test project.")
            (project_path / "requirements.txt").write_text("pytest>=7.0.0\n")
            
            yield project_path

    @pytest.fixture
    def unified_context(self, temp_project):
        """Create UnifiedContext instance."""
        return UnifiedContext(project_path=temp_project)

    def test_unified_context_creation(self, temp_project):
        """Test creating UnifiedContext."""
        context = UnifiedContext(project_path=temp_project)
        
        assert context.project_path == temp_project
        assert not context.initialized
        assert context.smart_engine is not None
        assert context.rag_system is None
        assert context.dev_rag is None

    def test_unified_context_with_config(self, temp_project):
        """Test creating UnifiedContext with configuration."""
        config = {
            "rag_enabled": True,
            "dev_rag_enabled": True,
            "response_mode": ResponseMode.DETAILED,
            "context_depth": ContextDepth.DEEP
        }
        
        context = UnifiedContext(project_path=temp_project, config=config)
        
        assert context.config == config
        assert context.config["rag_enabled"] is True

    @pytest.mark.asyncio
    async def test_initialize(self, unified_context):
        """Test initializing UnifiedContext."""
        with patch.object(unified_context.smart_engine, 'initialize') as mock_init:
            mock_init.return_value = None
            
            await unified_context.initialize()
            
            assert unified_context.initialized
            mock_init.assert_called_once()

    @pytest.mark.asyncio
    async def test_initialize_with_rag(self, temp_project):
        """Test initializing with RAG enabled."""
        config = {"rag_enabled": True}
        context = UnifiedContext(project_path=temp_project, config=config)
        
        with patch('agent_swarm.context.engine.create_rag_system') as mock_create_rag:
            mock_rag = Mock()
            mock_rag.initialize = AsyncMock()
            mock_create_rag.return_value = mock_rag
            
            await context.initialize()
            
            assert context.initialized
            assert context.rag_system is not None
            mock_create_rag.assert_called_once()

    @pytest.mark.asyncio
    async def test_initialize_with_dev_rag(self, temp_project):
        """Test initializing with development RAG enabled."""
        config = {"dev_rag_enabled": True}
        context = UnifiedContext(project_path=temp_project, config=config)
        
        with patch('agent_swarm.context.engine.setup_project_rag') as mock_setup_rag:
            mock_dev_rag = Mock()
            mock_setup_rag.return_value = mock_dev_rag
            
            await context.initialize()
            
            assert context.initialized
            assert context.dev_rag is not None
            mock_setup_rag.assert_called_once_with(temp_project)

    @pytest.mark.asyncio
    async def test_get_context_not_initialized(self, unified_context):
        """Test getting context when not initialized."""
        with pytest.raises(RuntimeError, match="not initialized"):
            await unified_context.get_context("test query")

    @pytest.mark.asyncio
    async def test_get_context_smart_only(self, unified_context):
        """Test getting context with smart engine only."""
        # Mock smart engine
        mock_result = Mock()
        mock_result.results = ["Smart result"]
        mock_result.metadata = {"source": "smart"}
        
        unified_context.smart_engine.get_smart_context = AsyncMock(return_value=[mock_result])
        unified_context.initialized = True
        
        result = await unified_context.get_context("test query")
        
        assert result is not None
        assert "results" in result
        assert len(result["results"]) == 1
        assert result["results"][0]["content"] == "Smart result"
        assert result["results"][0]["source"] == "smart"

    @pytest.mark.asyncio
    async def test_get_context_with_rag(self, unified_context):
        """Test getting context with RAG system."""
        # Setup RAG system
        mock_rag = Mock()
        mock_rag.search = AsyncMock(return_value=Mock(
            chunks=[Mock(content="RAG result", metadata={"score": 0.9})]
        ))
        unified_context.rag_system = mock_rag
        
        # Mock smart engine
        unified_context.smart_engine.get_smart_context = AsyncMock(return_value=[])
        unified_context.initialized = True
        
        result = await unified_context.get_context("test query")
        
        assert result is not None
        assert "results" in result
        mock_rag.search.assert_called_once_with("test query")

    @pytest.mark.asyncio
    async def test_get_context_with_dev_rag(self, unified_context):
        """Test getting context with development RAG."""
        # Setup dev RAG
        mock_dev_rag = Mock()
        mock_dev_rag.get_context_for_task = AsyncMock(return_value=Mock(
            chunks=[Mock(content="Dev RAG result", metadata={"file": "main.py"})]
        ))
        unified_context.dev_rag = mock_dev_rag
        
        # Mock smart engine
        unified_context.smart_engine.get_smart_context = AsyncMock(return_value=[])
        unified_context.initialized = True
        
        result = await unified_context.get_context("test query")
        
        assert result is not None
        assert "results" in result
        mock_dev_rag.get_context_for_task.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_context_combined(self, unified_context):
        """Test getting context with all systems combined."""
        # Setup all systems
        mock_rag = Mock()
        mock_rag.search = AsyncMock(return_value=Mock(
            chunks=[Mock(content="RAG result", metadata={"score": 0.9})]
        ))
        unified_context.rag_system = mock_rag
        
        mock_dev_rag = Mock()
        mock_dev_rag.get_context_for_task = AsyncMock(return_value=Mock(
            chunks=[Mock(content="Dev RAG result", metadata={"file": "main.py"})]
        ))
        unified_context.dev_rag = mock_dev_rag
        
        mock_smart_result = Mock()
        mock_smart_result.results = ["Smart result"]
        mock_smart_result.metadata = {"source": "smart"}
        unified_context.smart_engine.get_smart_context = AsyncMock(return_value=[mock_smart_result])
        
        unified_context.initialized = True
        
        result = await unified_context.get_context("test query")
        
        assert result is not None
        assert "results" in result
        assert len(result["results"]) >= 3  # At least one from each system
        
        # Check that all systems were called
        mock_rag.search.assert_called_once()
        mock_dev_rag.get_context_for_task.assert_called_once()
        unified_context.smart_engine.get_smart_context.assert_called_once()

    @pytest.mark.asyncio
    async def test_search_code(self, unified_context):
        """Test searching code specifically."""
        mock_dev_rag = Mock()
        mock_dev_rag.search_code = AsyncMock(return_value=Mock(
            chunks=[Mock(content="Code result", metadata={"file": "main.py", "line": 5})]
        ))
        unified_context.dev_rag = mock_dev_rag
        unified_context.initialized = True
        
        result = await unified_context.search_code("function definition")
        
        assert result is not None
        assert "results" in result
        mock_dev_rag.search_code.assert_called_once_with("function definition")

    @pytest.mark.asyncio
    async def test_search_code_no_dev_rag(self, unified_context):
        """Test searching code without dev RAG."""
        unified_context.initialized = True
        
        result = await unified_context.search_code("function definition")
        
        assert result is not None
        assert result["results"] == []
        assert "dev_rag not available" in result["metadata"]["warning"]

    @pytest.mark.asyncio
    async def test_get_related_files(self, unified_context):
        """Test getting related files."""
        mock_dev_rag = Mock()
        mock_dev_rag.get_related_files = AsyncMock(return_value=Mock(
            chunks=[Mock(content="Related file", metadata={"file": "utils.py"})]
        ))
        unified_context.dev_rag = mock_dev_rag
        unified_context.initialized = True
        
        result = await unified_context.get_related_files("main.py")
        
        assert result is not None
        assert "results" in result
        mock_dev_rag.get_related_files.assert_called_once_with("main.py")

    def test_get_stats(self, unified_context):
        """Test getting statistics."""
        stats = unified_context.get_stats()
        
        assert isinstance(stats, dict)
        assert "initialized" in stats
        assert "project_path" in stats
        assert "rag_enabled" in stats
        assert "dev_rag_enabled" in stats
        assert stats["initialized"] is False

    def test_get_stats_initialized(self, unified_context):
        """Test getting statistics when initialized."""
        unified_context.initialized = True
        unified_context.rag_system = Mock()
        unified_context.dev_rag = Mock()
        
        stats = unified_context.get_stats()
        
        assert stats["initialized"] is True
        assert stats["rag_enabled"] is True
        assert stats["dev_rag_enabled"] is True


@pytest.mark.unit
class TestCreateUnifiedContext:
    """Test create_unified_context factory function."""

    @pytest.mark.asyncio
    async def test_create_unified_context(self, temp_directory):
        """Test creating unified context with factory function."""
        with patch('agent_swarm.context.engine.UnifiedContext') as mock_context_class:
            mock_context = Mock()
            mock_context.initialize = AsyncMock()
            mock_context_class.return_value = mock_context
            
            result = await create_unified_context(temp_directory)
            
            assert result == mock_context
            mock_context_class.assert_called_once_with(project_path=temp_directory, config=None)
            mock_context.initialize.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_unified_context_with_config(self, temp_directory):
        """Test creating unified context with configuration."""
        config = {"rag_enabled": True}
        
        with patch('agent_swarm.context.engine.UnifiedContext') as mock_context_class:
            mock_context = Mock()
            mock_context.initialize = AsyncMock()
            mock_context_class.return_value = mock_context
            
            result = await create_unified_context(temp_directory, config=config)
            
            assert result == mock_context
            mock_context_class.assert_called_once_with(project_path=temp_directory, config=config)
            mock_context.initialize.assert_called_once()


@pytest.mark.integration
class TestUnifiedContextIntegration:
    """Integration tests for UnifiedContext."""

    @pytest.mark.asyncio
    async def test_real_project_analysis(self, temp_directory):
        """Test with real project structure."""
        # Create realistic project
        project_path = temp_directory
        (project_path / "src").mkdir()
        (project_path / "src" / "main.py").write_text("""
import os
import sys

def main():
    \"\"\"Main function.\"\"\"
    print("Hello, World!")
    return 0

def helper_function(x, y):
    \"\"\"Helper function.\"\"\"
    return x + y

if __name__ == "__main__":
    exit(main())
""".strip())
        
        (project_path / "tests").mkdir()
        (project_path / "tests" / "test_main.py").write_text("""
import pytest
from src.main import main, helper_function

def test_main():
    assert main() == 0

def test_helper_function():
    assert helper_function(2, 3) == 5
""".strip())
        
        # Test unified context
        context = UnifiedContext(project_path=str(project_path))
        await context.initialize()
        
        # Test basic context retrieval
        result = await context.get_context("explain the main function")
        
        assert result is not None
        assert "results" in result
        assert context.initialized

    @pytest.mark.asyncio
    async def test_error_handling(self, temp_directory):
        """Test error handling in unified context."""
        # Test with invalid project path
        invalid_path = temp_directory / "nonexistent"
        
        context = UnifiedContext(project_path=str(invalid_path))
        
        # Should handle gracefully
        await context.initialize()
        assert context.initialized  # Should still initialize
