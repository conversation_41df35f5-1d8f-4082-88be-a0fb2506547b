"""
Unit tests for abstract LLM components.
"""

import pytest

from agent_swarm.backends import (
    LLMConfig,
    LLMResponse,
    LLMRouter,
    LLMTier,
    Message,
)
from ..conftest import MockLLM


@pytest.mark.unit
class TestMessage:
    """Test Message model."""

    def test_message_creation(self):
        """Test creating a message."""
        message = Message(role="user", content="Hello")
        assert message.role == "user"
        assert message.content == "Hello"
        assert message.metadata is None

    def test_message_with_metadata(self):
        """Test creating a message with metadata."""
        metadata = {"source": "test"}
        message = Message(role="system", content="You are helpful", metadata=metadata)
        assert message.metadata == metadata

    def test_message_validation(self):
        """Test message validation."""
        # Pydantic validates automatically, but empty strings are allowed
        # Let's test that required fields are actually required
        from pydantic import ValidationError

        with pytest.raises(ValidationError):
            Message()  # Missing required fields should fail


@pytest.mark.unit
class TestLLMConfig:
    """Test LLMConfig model."""

    def test_config_creation(self, sample_llm_config: LLMConfig):
        """Test creating an LLM config."""
        assert sample_llm_config.name == "Test LLM"
        assert sample_llm_config.provider == "test"
        assert sample_llm_config.tier == LLMTier.LOCAL_FAST
        assert sample_llm_config.cost_per_1m_tokens == 0.0

    def test_config_with_api_key(self):
        """Test config with API key."""
        config = LLMConfig(
            name="Cloud LLM",
            provider="anthropic",
            model_id="claude-3",
            tier=LLMTier.CLOUD_PREMIUM,
            context_length=100000,
            cost_per_1m_tokens=3.0,
            api_key_env="ANTHROPIC_API_KEY",
        )
        assert config.api_key_env == "ANTHROPIC_API_KEY"
        assert config.cost_per_1m_tokens == 3.0


@pytest.mark.unit
class TestLLMResponse:
    """Test LLMResponse model."""

    def test_response_creation(self, sample_llm_response: LLMResponse):
        """Test creating an LLM response."""
        assert "def add_numbers" in sample_llm_response.content
        assert sample_llm_response.model == "test-model"
        assert sample_llm_response.usage["total_tokens"] == 35

    def test_response_validation(self):
        """Test response validation."""
        # Test that required fields are actually required
        from pydantic import ValidationError

        with pytest.raises(ValidationError):
            LLMResponse()  # Missing required fields should fail


@pytest.mark.unit
class TestMockLLM:
    """Test MockLLM implementation."""

    async def test_mock_llm_initialization(self, mock_llm: MockLLM):
        """Test mock LLM initialization."""
        assert not await mock_llm.is_available()  # Not initialized yet

        await mock_llm.initialize()
        assert await mock_llm.is_available()

    async def test_mock_llm_generation(
        self, mock_llm: MockLLM, sample_messages: list[Message]
    ):
        """Test mock LLM generation."""
        await mock_llm.initialize()

        response = await mock_llm.generate(sample_messages, temperature=0.5)

        assert isinstance(response, LLMResponse)
        assert "Mock response for 2 messages" in response.content
        assert response.model == mock_llm.config.model_id
        assert response.metadata["temperature"] == 0.5

    async def test_mock_llm_streaming(
        self, mock_llm: MockLLM, sample_messages: list[Message]
    ):
        """Test mock LLM streaming."""
        await mock_llm.initialize()

        chunks = []
        async for chunk in mock_llm.stream_generate(sample_messages):
            chunks.append(chunk)

        assert len(chunks) == 3
        assert "Mock " in chunks[0]
        assert "streaming " in chunks[1]
        assert "response " in chunks[2]

    async def test_failing_mock_llm(self, failing_mock_llm: MockLLM):
        """Test failing mock LLM."""
        with pytest.raises(RuntimeError, match="Mock initialization failed"):
            await failing_mock_llm.initialize()

    async def test_unavailable_mock_llm(self, unavailable_mock_llm: MockLLM):
        """Test unavailable mock LLM."""
        await unavailable_mock_llm.initialize()
        assert not await unavailable_mock_llm.is_available()

    def test_cost_estimation(self, mock_llm: MockLLM):
        """Test cost estimation."""
        # Local model should have zero cost
        cost = mock_llm.get_cost_estimate(1000, 500)
        assert cost == 0.0

        # Cloud model should have cost
        mock_llm.config.cost_per_1m_tokens = 3.0
        cost = mock_llm.get_cost_estimate(1000, 500)
        assert (
            abs(cost - 0.0045) < 1e-10
        )  # (1500 / 1_000_000) * 3.0, account for float precision


@pytest.mark.unit
class TestLLMRouter:
    """Test LLMRouter functionality."""

    def test_router_creation(self, llm_router: LLMRouter):
        """Test creating an LLM router."""
        assert len(llm_router.llms) == 0
        assert len(llm_router.task_routing) == 0
        assert len(llm_router.fallback_chain) == 0

    async def test_register_llm(self, llm_router: LLMRouter, mock_llm: MockLLM):
        """Test registering an LLM."""
        llm_router.register_llm("test", mock_llm)
        assert "test" in llm_router.llms
        assert llm_router.llms["test"] == mock_llm

    def test_task_routing(self, llm_router: LLMRouter):
        """Test task routing configuration."""
        llm_router.set_task_routing("coding", "test-llm")
        assert llm_router.task_routing["coding"] == "test-llm"

    def test_fallback_chain(self, llm_router: LLMRouter):
        """Test fallback chain configuration."""
        chain = ["primary", "secondary", "tertiary"]
        llm_router.set_fallback_chain(chain)
        assert llm_router.fallback_chain == chain

    async def test_route_request(
        self, configured_router: LLMRouter, sample_messages: list[Message]
    ):
        """Test routing a request."""
        response = await configured_router.route_request(sample_messages)

        assert isinstance(response, LLMResponse)
        assert "Mock response" in response.content

    async def test_route_request_with_task_type(
        self, configured_router: LLMRouter, sample_messages: list[Message]
    ):
        """Test routing with specific task type."""
        configured_router.set_task_routing("coding", "test")

        response = await configured_router.route_request(
            sample_messages, task_type="coding"
        )

        assert isinstance(response, LLMResponse)

    async def test_route_request_with_tier_preference(
        self, configured_router: LLMRouter, sample_messages: list[Message]
    ):
        """Test routing with tier preference."""
        response = await configured_router.route_request(
            sample_messages, preferred_tier=LLMTier.LOCAL_FAST
        )

        assert isinstance(response, LLMResponse)

    async def test_route_request_no_llms(
        self, llm_router: LLMRouter, sample_messages: list[Message]
    ):
        """Test routing with no LLMs available."""
        with pytest.raises(ValueError, match="No LLMs available"):
            await llm_router.route_request(sample_messages)

    async def test_fallback_routing(
        self,
        llm_router: LLMRouter,
        mock_llm: MockLLM,
        failing_mock_llm: MockLLM,
        sample_messages: list[Message],
    ):
        """Test fallback routing when primary LLM fails."""
        # Register failing LLM as primary (don't initialize it, it will fail)
        llm_router.register_llm("failing", failing_mock_llm)

        # Register working LLM as fallback
        await mock_llm.initialize()
        llm_router.register_llm("working", mock_llm)

        # Set fallback chain
        llm_router.set_fallback_chain(["failing", "working"])
        llm_router.set_task_routing("test", "failing")

        # Should fallback to working LLM
        response = await llm_router.route_request(sample_messages, task_type="test")
        assert isinstance(response, LLMResponse)

    def test_get_available_llms(self, configured_router: LLMRouter):
        """Test getting available LLMs."""
        available = configured_router.get_available_llms()
        assert "test" in available
        assert isinstance(available["test"], LLMConfig)

    def test_estimate_cost(self, configured_router: LLMRouter):
        """Test cost estimation."""
        cost = configured_router.estimate_cost("general", 1000, 500)
        assert cost == 0.0  # Mock LLM has zero cost
