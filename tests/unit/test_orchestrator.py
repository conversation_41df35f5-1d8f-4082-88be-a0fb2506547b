#!/usr/bin/env python3
"""
Test Multi-Step Orchestrator functionality.
"""

import sys
import asyncio
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

async def test_orchestrator_basic():
    """Test basic orchestrator functionality."""
    print("🧪 Testing Multi-Step Orchestrator...")
    
    try:
        from agent_swarm.core.orchestrator import MultiStepOrchestrator, StepType, StepStatus
        
        orchestrator = MultiStepOrchestrator()
        
        # Test request analysis
        test_requests = [
            "How do we calculate the best agent?",
            "Edit this TypeScript file and update the types", 
            "Create a proper React project",
            "Explain this function @main.py",
            "Simple single step request"
        ]
        
        print("   Testing request analysis...")
        for request in test_requests:
            analysis = await orchestrator._analyze_request(request)
            print(f"   '{request[:30]}...' -> Multi-step: {analysis['needs_multi_step']}, Type: {analysis['request_type']}")
        
        print("✅ Request analysis works!")
        return True
        
    except ImportError as e:
        print(f"❌ Orchestrator not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Orchestrator test failed: {e}")
        return False

async def test_execution_plans():
    """Test execution plan generation."""
    print("\n🧪 Testing Execution Plans...")
    
    try:
        from agent_swarm.core.orchestrator import MultiStepOrchestrator
        
        orchestrator = MultiStepOrchestrator()
        
        # Test analysis plan
        analysis_request = "How do we calculate the best agent?"
        analysis = await orchestrator._analyze_request(analysis_request)
        
        if analysis["needs_multi_step"]:
            plan = await orchestrator._create_execution_plan(analysis_request, analysis)
            
            print(f"   Analysis plan created: {len(plan.steps)} steps")
            for step in plan.steps:
                print(f"     {step.step_id}: {step.description}")
        
        # Test TypeScript plan
        ts_request = "Edit this TypeScript file and update the types"
        ts_analysis = await orchestrator._analyze_request(ts_request)
        
        if ts_analysis["needs_multi_step"]:
            ts_plan = await orchestrator._create_execution_plan(ts_request, ts_analysis)
            
            print(f"   TypeScript plan created: {len(ts_plan.steps)} steps")
            for step in ts_plan.steps:
                print(f"     {step.step_id}: {step.description}")
        
        print("✅ Execution plans work!")
        return True
        
    except Exception as e:
        print(f"❌ Execution plans test failed: {e}")
        return False

async def test_multi_step_execution():
    """Test complete multi-step execution."""
    print("\n🧪 Testing Multi-Step Execution...")
    
    try:
        from agent_swarm.core.orchestrator import MultiStepOrchestrator
        
        orchestrator = MultiStepOrchestrator()
        
        # Test with a simple multi-step request
        request = "How do we calculate the best agent?"
        
        print(f"   Executing: '{request}'")
        result = await orchestrator.execute_multi_step(request)
        
        print(f"   Result type: {result['type']}")
        print(f"   Success: {result.get('success', 'N/A')}")
        print(f"   Steps completed: {result.get('steps_completed', 0)}/{result.get('total_steps', 0)}")
        
        if result.get("steps"):
            print("   Step details:")
            for step in result["steps"]:
                status = step.get("status", "unknown")
                print(f"     {step['step_id']}: {status} - {step['description']}")
        
        print("✅ Multi-step execution works!")
        return True
        
    except Exception as e:
        print(f"❌ Multi-step execution test failed: {e}")
        return False

async def test_dependency_discovery():
    """Test dependency discovery patterns."""
    print("\n🧪 Testing Dependency Discovery...")
    
    try:
        from agent_swarm.core.orchestrator import MultiStepOrchestrator
        
        orchestrator = MultiStepOrchestrator()
        
        # Test requests with dependency patterns
        dependency_requests = [
            "Explain this function @main.py that imports from example.module",
            "The auth.py file uses utils.helpers for validation",
            "This component depends on the types from interfaces.ts"
        ]
        
        for request in dependency_requests:
            analysis = await orchestrator._analyze_request(request)
            
            print(f"   Request: '{request[:50]}...'")
            print(f"     Multi-step needed: {analysis['needs_multi_step']}")
            print(f"     Mentioned files: {analysis['mentioned_files']}")
            print(f"     Request type: {analysis['request_type']}")
        
        print("✅ Dependency discovery works!")
        return True
        
    except Exception as e:
        print(f"❌ Dependency discovery test failed: {e}")
        return False

async def test_shell_integration():
    """Test integration with interactive shell."""
    print("\n🧪 Testing Shell Integration...")
    
    try:
        from agent_swarm.cli.interactive_shell import InteractiveShell
        
        shell = InteractiveShell(verbose=False, auto_index=False)
        
        # Test that orchestrator is initialized
        has_orchestrator = hasattr(shell, 'orchestrator') and shell.orchestrator is not None
        print(f"   Has orchestrator: {has_orchestrator}")
        
        if has_orchestrator:
            # Test orchestrator has shell reference
            has_shell_ref = shell.orchestrator.shell is shell
            print(f"   Orchestrator has shell reference: {has_shell_ref}")
            
            # Test display method exists
            has_display_method = hasattr(shell, '_display_multi_step_result')
            print(f"   Has display method: {has_display_method}")
        
        assert has_orchestrator, "Should have orchestrator"
        
        print("✅ Shell integration works!")
        return True
        
    except Exception as e:
        print(f"❌ Shell integration test failed: {e}")
        return False

async def test_step_types_and_status():
    """Test step types and status enums."""
    print("\n🧪 Testing Step Types and Status...")
    
    try:
        from agent_swarm.core.orchestrator import StepType, StepStatus, ExecutionStep
        
        # Test step types
        step_types = [StepType.THINK, StepType.SEARCH, StepType.ANALYZE, 
                     StepType.PLAN, StepType.EXECUTE, StepType.VALIDATE, StepType.REFLECT]
        
        print(f"   Step types available: {len(step_types)}")
        for step_type in step_types:
            print(f"     {step_type.name}: {step_type.value}")
        
        # Test step status
        statuses = [StepStatus.PENDING, StepStatus.RUNNING, StepStatus.COMPLETED, 
                   StepStatus.FAILED, StepStatus.SKIPPED]
        
        print(f"   Step statuses available: {len(statuses)}")
        for status in statuses:
            print(f"     {status.name}: {status.value}")
        
        # Test execution step creation
        step = ExecutionStep(
            step_id="test_step",
            step_type=StepType.SEARCH,
            description="Test step creation",
            action="search",
            parameters={"query": "test"}
        )
        
        print(f"   Created step: {step.step_id} - {step.description}")
        print(f"   Step type: {step.step_type.value}")
        print(f"   Step status: {step.status.value}")
        
        print("✅ Step types and status work!")
        return True
        
    except Exception as e:
        print(f"❌ Step types test failed: {e}")
        return False

async def main():
    """Run all orchestrator tests."""
    print("🚀 Testing Multi-Step Orchestrator System")
    print("=" * 60)
    
    tests = [
        test_orchestrator_basic,
        test_execution_plans,
        test_multi_step_execution,
        test_dependency_discovery,
        test_shell_integration,
        test_step_types_and_status,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if await test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Multi-Step Orchestrator fully functional!")
        print("\n💡 Revolutionary capabilities:")
        print("   ✅ Intelligent request analysis")
        print("   ✅ Multi-step execution planning")
        print("   ✅ Dependency discovery")
        print("   ✅ Context accumulation")
        print("   ✅ Error recovery")
        print("   ✅ Shell integration")
        print("\n🔮 Try these multi-step requests:")
        print("   • 'How do we calculate the best agent?'")
        print("   • 'Edit TypeScript file and update types'")
        print("   • 'Create a proper React project'")
        print("   • 'Explain @main.py that imports from utils'")
        return True
    else:
        print(f"❌ {total - passed} features need work")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
