"""
Comprehensive unit tests for Agent Swarm tools.
"""

import pytest
import tempfile
import subprocess
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

from agent_swarm.tools import (
    LinuxCLITool, FileSystemTool, ProcessTool, NetworkTool,
    SystemInfoTool, DevelopmentTool
)


@pytest.mark.unit
class TestLinuxCLITool:
    """Test LinuxCLITool functionality."""

    @pytest.fixture
    def cli_tool(self):
        """Create LinuxCLITool instance."""
        return LinuxCLITool()

    @pytest.mark.asyncio
    async def test_execute_simple_command(self, cli_tool):
        """Test executing simple command."""
        with patch('asyncio.create_subprocess_exec') as mock_subprocess:
            # Mock process
            mock_process = Mock()
            mock_process.communicate = AsyncMock(return_value=(b"Hello World", b""))
            mock_process.returncode = 0
            mock_subprocess.return_value = mock_process
            
            result = await cli_tool.execute("echo", ["Hello World"])
            
            assert result["success"] is True
            assert "Hello World" in result["output"]
            assert result["exit_code"] == 0

    @pytest.mark.asyncio
    async def test_execute_command_with_error(self, cli_tool):
        """Test executing command that produces error."""
        with patch('asyncio.create_subprocess_exec') as mock_subprocess:
            mock_process = Mock()
            mock_process.communicate = AsyncMock(return_value=(b"", b"Command not found"))
            mock_process.returncode = 127
            mock_subprocess.return_value = mock_process
            
            result = await cli_tool.execute("nonexistent_command", [])
            
            assert result["success"] is False
            assert "Command not found" in result["error"]
            assert result["exit_code"] == 127

    @pytest.mark.asyncio
    async def test_execute_command_timeout(self, cli_tool):
        """Test command execution timeout."""
        with patch('asyncio.create_subprocess_exec') as mock_subprocess:
            mock_process = Mock()
            mock_process.communicate = AsyncMock(side_effect=asyncio.TimeoutError())
            mock_subprocess.return_value = mock_process
            
            result = await cli_tool.execute("sleep", ["10"], timeout=1)
            
            assert result["success"] is False
            assert "timeout" in result["error"].lower()

    @pytest.mark.asyncio
    async def test_execute_with_working_directory(self, cli_tool):
        """Test executing command with specific working directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch('asyncio.create_subprocess_exec') as mock_subprocess:
                mock_process = Mock()
                mock_process.communicate = AsyncMock(return_value=(temp_dir.encode(), b""))
                mock_process.returncode = 0
                mock_subprocess.return_value = mock_process
                
                result = await cli_tool.execute("pwd", [], cwd=temp_dir)
                
                assert result["success"] is True
                mock_subprocess.assert_called_with(
                    "pwd", cwd=temp_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE
                )

    def test_get_schema(self, cli_tool):
        """Test getting tool schema."""
        schema = cli_tool.get_schema()
        
        assert isinstance(schema, dict)
        assert "execute_command" in schema
        assert "description" in schema["execute_command"]
        assert "parameters" in schema["execute_command"]


@pytest.mark.unit
class TestFileSystemTool:
    """Test FileSystemTool functionality."""

    @pytest.fixture
    def fs_tool(self):
        """Create FileSystemTool instance."""
        return FileSystemTool()

    @pytest.mark.asyncio
    async def test_read_file(self, fs_tool):
        """Test reading file."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("Test file content")
            temp_path = f.name
        
        try:
            result = await fs_tool.execute("read_file", {"path": temp_path})
            
            assert result["success"] is True
            assert result["content"] == "Test file content"
            assert result["path"] == temp_path
            
        finally:
            Path(temp_path).unlink()

    @pytest.mark.asyncio
    async def test_write_file(self, fs_tool):
        """Test writing file."""
        with tempfile.NamedTemporaryFile(delete=False) as f:
            temp_path = f.name
        
        try:
            content = "Hello, FileSystem!"
            result = await fs_tool.execute("write_file", {
                "path": temp_path,
                "content": content
            })
            
            assert result["success"] is True
            assert result["path"] == temp_path
            assert result["bytes_written"] == len(content)
            
            # Verify file was written
            with open(temp_path, 'r') as f:
                assert f.read() == content
                
        finally:
            Path(temp_path).unlink()

    @pytest.mark.asyncio
    async def test_list_directory(self, fs_tool):
        """Test listing directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create test files
            (temp_path / "file1.txt").write_text("content1")
            (temp_path / "file2.py").write_text("print('hello')")
            (temp_path / "subdir").mkdir()
            
            result = await fs_tool.execute("list_directory", {"path": str(temp_path)})
            
            assert result["success"] is True
            assert len(result["entries"]) == 3
            
            entry_names = [entry["name"] for entry in result["entries"]]
            assert "file1.txt" in entry_names
            assert "file2.py" in entry_names
            assert "subdir" in entry_names

    @pytest.mark.asyncio
    async def test_file_not_found(self, fs_tool):
        """Test handling non-existent file."""
        result = await fs_tool.execute("read_file", {"path": "/nonexistent/file.txt"})
        
        assert result["success"] is False
        assert "not found" in result["error"].lower()

    @pytest.mark.asyncio
    async def test_invalid_operation(self, fs_tool):
        """Test invalid operation."""
        with pytest.raises(ValueError, match="Unknown filesystem operation"):
            await fs_tool.execute("invalid_operation", {})

    def test_get_schema(self, fs_tool):
        """Test getting filesystem tool schema."""
        schema = fs_tool.get_schema()
        
        assert isinstance(schema, dict)
        assert "read_file" in schema
        assert "write_file" in schema
        assert "list_directory" in schema


@pytest.mark.unit
class TestProcessTool:
    """Test ProcessTool functionality."""

    @pytest.fixture
    def process_tool(self):
        """Create ProcessTool instance."""
        return ProcessTool()

    @pytest.mark.asyncio
    async def test_list_processes(self, process_tool):
        """Test listing processes."""
        with patch('psutil.process_iter') as mock_process_iter:
            # Mock process
            mock_proc = Mock()
            mock_proc.info = {
                'pid': 1234,
                'name': 'test_process',
                'cpu_percent': 5.0,
                'memory_percent': 2.5
            }
            mock_process_iter.return_value = [mock_proc]
            
            result = await process_tool.execute("list_processes", {})
            
            assert result["success"] is True
            assert len(result["processes"]) == 1
            assert result["processes"][0]["name"] == "test_process"

    @pytest.mark.asyncio
    async def test_get_process_info(self, process_tool):
        """Test getting process information."""
        with patch('psutil.Process') as mock_process_class:
            mock_proc = Mock()
            mock_proc.pid = 1234
            mock_proc.name.return_value = "test_process"
            mock_proc.cpu_percent.return_value = 5.0
            mock_proc.memory_percent.return_value = 2.5
            mock_process_class.return_value = mock_proc
            
            result = await process_tool.execute("get_process_info", {"pid": 1234})
            
            assert result["success"] is True
            assert result["process"]["name"] == "test_process"
            assert result["process"]["pid"] == 1234

    @pytest.mark.asyncio
    async def test_process_not_found(self, process_tool):
        """Test handling non-existent process."""
        with patch('psutil.Process') as mock_process_class:
            mock_process_class.side_effect = psutil.NoSuchProcess(1234)
            
            result = await process_tool.execute("get_process_info", {"pid": 1234})
            
            assert result["success"] is False
            assert "not found" in result["error"].lower()


@pytest.mark.unit
class TestNetworkTool:
    """Test NetworkTool functionality."""

    @pytest.fixture
    def network_tool(self):
        """Create NetworkTool instance."""
        return NetworkTool()

    @pytest.mark.asyncio
    async def test_ping(self, network_tool):
        """Test ping functionality."""
        with patch('asyncio.create_subprocess_exec') as mock_subprocess:
            mock_process = Mock()
            mock_process.communicate = AsyncMock(return_value=(
                b"PING google.com: 56 data bytes\n64 bytes from google.com: time=10ms",
                b""
            ))
            mock_process.returncode = 0
            mock_subprocess.return_value = mock_process
            
            result = await network_tool.execute("ping", {"host": "google.com"})
            
            assert result["success"] is True
            assert "google.com" in result["output"]

    @pytest.mark.asyncio
    async def test_get_network_info(self, network_tool):
        """Test getting network information."""
        with patch('psutil.net_if_addrs') as mock_net_if_addrs:
            mock_net_if_addrs.return_value = {
                'eth0': [Mock(address='*************', family=2)]
            }
            
            result = await network_tool.execute("get_network_info", {})
            
            assert result["success"] is True
            assert "interfaces" in result

    @pytest.mark.asyncio
    async def test_check_port(self, network_tool):
        """Test checking port availability."""
        with patch('socket.socket') as mock_socket:
            mock_sock = Mock()
            mock_sock.connect_ex.return_value = 0  # Port is open
            mock_socket.return_value.__enter__.return_value = mock_sock
            
            result = await network_tool.execute("check_port", {
                "host": "localhost",
                "port": 80
            })
            
            assert result["success"] is True
            assert result["port_open"] is True


@pytest.mark.unit
class TestSystemInfoTool:
    """Test SystemInfoTool functionality."""

    @pytest.fixture
    def system_tool(self):
        """Create SystemInfoTool instance."""
        return SystemInfoTool()

    @pytest.mark.asyncio
    async def test_get_system_info(self, system_tool):
        """Test getting system information."""
        with patch('platform.system') as mock_system, \
             patch('platform.release') as mock_release, \
             patch('psutil.cpu_count') as mock_cpu_count, \
             patch('psutil.virtual_memory') as mock_memory:
            
            mock_system.return_value = "Linux"
            mock_release.return_value = "5.4.0"
            mock_cpu_count.return_value = 4
            mock_memory.return_value = Mock(total=8589934592, available=4294967296)
            
            result = await system_tool.execute("get_system_info", {})
            
            assert result["success"] is True
            assert result["system"]["os"] == "Linux"
            assert result["system"]["cpu_count"] == 4

    @pytest.mark.asyncio
    async def test_get_disk_usage(self, system_tool):
        """Test getting disk usage."""
        with patch('psutil.disk_usage') as mock_disk_usage:
            mock_disk_usage.return_value = Mock(
                total=1000000000,
                used=500000000,
                free=500000000
            )
            
            result = await system_tool.execute("get_disk_usage", {"path": "/"})
            
            assert result["success"] is True
            assert result["disk"]["total"] == 1000000000
            assert result["disk"]["used"] == 500000000


@pytest.mark.unit
class TestDevelopmentTool:
    """Test DevelopmentTool functionality."""

    @pytest.fixture
    def dev_tool(self):
        """Create DevelopmentTool instance."""
        return DevelopmentTool()

    @pytest.mark.asyncio
    async def test_analyze_code(self, dev_tool):
        """Test code analysis."""
        code = """
def hello_world():
    print("Hello, World!")
    return "success"
"""
        
        result = await dev_tool.execute("analyze_code", {
            "code": code,
            "language": "python"
        })
        
        assert result["success"] is True
        assert "analysis" in result

    @pytest.mark.asyncio
    async def test_format_code(self, dev_tool):
        """Test code formatting."""
        code = "def hello():print('hello')"
        
        result = await dev_tool.execute("format_code", {
            "code": code,
            "language": "python"
        })
        
        assert result["success"] is True
        assert "formatted_code" in result

    @pytest.mark.asyncio
    async def test_run_tests(self, dev_tool):
        """Test running tests."""
        with tempfile.TemporaryDirectory() as temp_dir:
            test_file = Path(temp_dir) / "test_example.py"
            test_file.write_text("""
import unittest

class TestExample(unittest.TestCase):
    def test_simple(self):
        self.assertEqual(1 + 1, 2)

if __name__ == '__main__':
    unittest.main()
""")
            
            result = await dev_tool.execute("run_tests", {
                "test_path": str(test_file)
            })
            
            # Result depends on implementation
            assert "success" in result

    @pytest.mark.asyncio
    async def test_lint_code(self, dev_tool):
        """Test code linting."""
        code = """
def hello_world( ):
    print( "Hello, World!" )
    return"success"
"""
        
        result = await dev_tool.execute("lint_code", {
            "code": code,
            "language": "python"
        })
        
        assert result["success"] is True
        assert "issues" in result


@pytest.mark.integration
class TestToolsIntegration:
    """Integration tests for tools."""

    @pytest.mark.asyncio
    async def test_tool_chaining(self):
        """Test chaining multiple tools together."""
        fs_tool = FileSystemTool()
        cli_tool = LinuxCLITool()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            test_file = temp_path / "test.txt"
            
            # Write file using filesystem tool
            write_result = await fs_tool.execute("write_file", {
                "path": str(test_file),
                "content": "Hello from tools!"
            })
            assert write_result["success"] is True
            
            # Read file using CLI tool
            with patch('asyncio.create_subprocess_exec') as mock_subprocess:
                mock_process = Mock()
                mock_process.communicate = AsyncMock(return_value=(b"Hello from tools!", b""))
                mock_process.returncode = 0
                mock_subprocess.return_value = mock_process
                
                read_result = await cli_tool.execute("cat", [str(test_file)])
                assert read_result["success"] is True
                assert "Hello from tools!" in read_result["output"]

    @pytest.mark.asyncio
    async def test_error_propagation(self):
        """Test error propagation between tools."""
        fs_tool = FileSystemTool()
        
        # Try to read non-existent file
        result = await fs_tool.execute("read_file", {"path": "/nonexistent/file.txt"})
        
        assert result["success"] is False
        assert "error" in result

    def test_all_tools_have_schema(self):
        """Test that all tools have proper schema."""
        tools = [
            LinuxCLITool(),
            FileSystemTool(),
            ProcessTool(),
            NetworkTool(),
            SystemInfoTool(),
            DevelopmentTool()
        ]
        
        for tool in tools:
            schema = tool.get_schema()
            assert isinstance(schema, dict)
            assert len(schema) > 0
            
            # Each operation should have description and parameters
            for operation, details in schema.items():
                assert "description" in details
                assert "parameters" in details
