"""
Comprehensive tests for the MultiAgentConsensusStage.
Tests the revolutionary O(n³) consensus algorithm with virtual agents.
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from typing import List, Dict, Any

from src.agent_swarm.algorithms.algorithms.stages.multi_agent_consensus_stage import (
    MultiAgentConsensusStage,
    VirtualAgent
)
from src.agent_swarm.algorithms.core import AlgorithmContext
from src.agent_swarm.core.multi_agent_communication import (
    AgentRole,
    MultiAgentCommunicationHub
)


class TestVirtualAgent:
    """Test VirtualAgent functionality."""
    
    def test_virtual_agent_creation(self):
        """Test virtual agent creation with all parameters."""
        agent = VirtualAgent(
            id="test_agent",
            role=AgentRole.SPECIALIST,
            expertise=["technology", "algorithms"],
            personality_traits={"analytical": 0.9, "detail_oriented": 0.8},
            decision_bias=0.1,
            confidence_threshold=0.7
        )
        
        assert agent.id == "test_agent"
        assert agent.role == AgentRole.SPECIALIST
        assert agent.expertise == ["technology", "algorithms"]
        assert agent.personality_traits["analytical"] == 0.9
        assert agent.decision_bias == 0.1
        assert agent.confidence_threshold == 0.7
    
    def test_specialist_perspective_generation(self):
        """Test specialist agent perspective generation."""
        agent = VirtualAgent(
            id="tech_specialist",
            role=AgentRole.SPECIALIST,
            expertise=["technology", "software"],
            personality_traits={"analytical": 0.9},
            decision_bias=0.0,
            confidence_threshold=0.6
        )
        
        input_data = {
            "domain": "technology",
            "problem": "Choose between microservices and monolith"
        }
        context = {"urgency": "high"}
        
        perspective = agent.generate_perspective(input_data, context)
        
        assert perspective["agent_id"] == "tech_specialist"
        assert perspective["role"] == "specialist"
        assert "confidence" in perspective
        assert 0.0 <= perspective["confidence"] <= 1.0
        assert perspective["perspective"]["analysis_type"] == "technical_deep_dive"
        assert perspective["perspective"]["focus"] == "domain_expertise"
        assert "recommendations" in perspective["perspective"]
        assert "reasoning" in perspective
        assert "vote_weight" in perspective
    
    def test_critic_perspective_generation(self):
        """Test critic agent perspective generation."""
        agent = VirtualAgent(
            id="security_critic",
            role=AgentRole.CRITIC,
            expertise=["security", "risk"],
            personality_traits={"skeptical": 0.9},
            decision_bias=-0.3,
            confidence_threshold=0.8
        )
        
        input_data = {"problem": "Implement new feature"}
        context = {}
        
        perspective = agent.generate_perspective(input_data, context)
        
        assert perspective["role"] == "critic"
        assert perspective["perspective"]["analysis_type"] == "critical_evaluation"
        assert perspective["perspective"]["focus"] == "potential_issues"
        assert "concerns" in perspective["perspective"]
        assert "risk_assessment" in perspective["perspective"]
        assert len(perspective["perspective"]["concerns"]) >= 1
    
    def test_synthesizer_perspective_generation(self):
        """Test synthesizer agent perspective generation."""
        agent = VirtualAgent(
            id="main_synthesizer",
            role=AgentRole.SYNTHESIZER,
            expertise=["integration", "systems_thinking"],
            personality_traits={"balanced": 0.9},
            decision_bias=0.0,
            confidence_threshold=0.5
        )
        
        input_data = {"problem": "Complex system design"}
        context = {}
        
        perspective = agent.generate_perspective(input_data, context)
        
        assert perspective["role"] == "synthesizer"
        assert perspective["perspective"]["analysis_type"] == "holistic_synthesis"
        assert perspective["perspective"]["focus"] == "integration"
        assert perspective["perspective"]["synthesis_approach"] == "multi_perspective"
    
    def test_validator_perspective_generation(self):
        """Test validator agent perspective generation."""
        agent = VirtualAgent(
            id="quality_validator",
            role=AgentRole.VALIDATOR,
            expertise=["quality", "testing"],
            personality_traits={"meticulous": 0.9},
            decision_bias=-0.1,
            confidence_threshold=0.7
        )
        
        input_data = {"problem": "Code quality assessment"}
        context = {}
        
        perspective = agent.generate_perspective(input_data, context)
        
        assert perspective["role"] == "validator"
        assert perspective["perspective"]["analysis_type"] == "validation"
        assert perspective["perspective"]["focus"] == "correctness"
        assert "quality_metrics" in perspective["perspective"]
        
        quality_metrics = perspective["perspective"]["quality_metrics"]
        assert "completeness" in quality_metrics
        assert "accuracy" in quality_metrics
        assert "feasibility" in quality_metrics
        assert "clarity" in quality_metrics
    
    def test_vote_weight_calculation(self):
        """Test vote weight calculation based on role and confidence."""
        # Specialist should have higher weight
        specialist = VirtualAgent(
            id="specialist",
            role=AgentRole.SPECIALIST,
            expertise=["tech"],
            personality_traits={},
            decision_bias=0.0,
            confidence_threshold=0.5
        )
        
        # Observer should have lower weight
        observer = VirtualAgent(
            id="observer",
            role=AgentRole.OBSERVER,
            expertise=["general"],
            personality_traits={},
            decision_bias=0.0,
            confidence_threshold=0.5
        )
        
        specialist_perspective = specialist.generate_perspective("test", {})
        observer_perspective = observer.generate_perspective("test", {})
        
        # Specialist should have higher vote weight
        assert specialist_perspective["vote_weight"] > observer_perspective["vote_weight"]
    
    def test_expertise_confidence_boost(self):
        """Test confidence boost when agent expertise matches domain."""
        agent = VirtualAgent(
            id="tech_expert",
            role=AgentRole.SPECIALIST,
            expertise=["technology", "algorithms"],
            personality_traits={},
            decision_bias=0.0,
            confidence_threshold=0.5
        )
        
        # Input with matching domain
        matching_input = {"domain": "technology", "problem": "tech issue"}
        non_matching_input = {"domain": "business", "problem": "business issue"}
        
        matching_perspective = agent.generate_perspective(matching_input, {})
        non_matching_perspective = agent.generate_perspective(non_matching_input, {})
        
        # Should have higher confidence for matching domain
        # Note: This is probabilistic, so we test multiple times
        matching_confidences = []
        non_matching_confidences = []
        
        for _ in range(10):
            matching_confidences.append(
                agent.generate_perspective(matching_input, {})["confidence"]
            )
            non_matching_confidences.append(
                agent.generate_perspective(non_matching_input, {})["confidence"]
            )
        
        avg_matching = sum(matching_confidences) / len(matching_confidences)
        avg_non_matching = sum(non_matching_confidences) / len(non_matching_confidences)
        
        # On average, matching domain should have higher confidence
        assert avg_matching > avg_non_matching


class TestMultiAgentConsensusStage:
    """Test the main MultiAgentConsensusStage."""
    
    @pytest.fixture
    def consensus_stage(self):
        """Create consensus stage for testing."""
        return MultiAgentConsensusStage()
    
    @pytest.fixture
    def sample_context(self):
        """Create sample algorithm context."""
        return AlgorithmContext(
            request_id="test_request",
            user_id="test_user",
            session_id="test_session",
            metadata={}
        )
    
    def test_stage_initialization(self, consensus_stage):
        """Test consensus stage initialization."""
        stage = consensus_stage
        
        assert stage.stage_name == "multi_agent_consensus"
        assert len(stage.virtual_agents) == 6  # Should have 6 predefined agents
        assert stage.min_agents == 3
        assert stage.max_agents == 7
        assert stage.consensus_timeout == 30.0
        assert stage.min_confidence_threshold == 0.6
        
        # Check agent roles
        roles = [agent.role for agent in stage.virtual_agents]
        assert AgentRole.SPECIALIST in roles
        assert AgentRole.CRITIC in roles
        assert AgentRole.SYNTHESIZER in roles
        assert AgentRole.VALIDATOR in roles
        assert AgentRole.COORDINATOR in roles
    
    def test_virtual_agents_initialization(self, consensus_stage):
        """Test virtual agents are properly initialized."""
        stage = consensus_stage
        agents = stage.virtual_agents
        
        # Should have specific agents
        agent_ids = [agent.id for agent in agents]
        expected_ids = [
            "specialist_tech",
            "specialist_business", 
            "critic_security",
            "synthesizer_main",
            "validator_quality",
            "coordinator_main"
        ]
        
        for expected_id in expected_ids:
            assert expected_id in agent_ids
        
        # Check agent configurations
        tech_specialist = next(a for a in agents if a.id == "specialist_tech")
        assert tech_specialist.role == AgentRole.SPECIALIST
        assert "technology" in tech_specialist.expertise
        assert tech_specialist.personality_traits["analytical"] == 0.9
        
        security_critic = next(a for a in agents if a.id == "critic_security")
        assert security_critic.role == AgentRole.CRITIC
        assert "security" in security_critic.expertise
        assert security_critic.decision_bias == -0.3  # Pessimistic
    
    def test_task_domain_extraction(self, consensus_stage):
        """Test domain extraction from input tasks."""
        stage = consensus_stage
        
        # Test with explicit domain
        input_with_domain = {"domain": "technology", "problem": "test"}
        domains = stage._extract_task_domains(input_with_domain)
        assert "technology" in domains
        
        # Test with multiple domains
        input_with_domains = {"domains": ["technology", "business"], "problem": "test"}
        domains = stage._extract_task_domains(input_with_domains)
        assert "technology" in domains
        assert "business" in domains
        
        # Test with keyword detection
        tech_text = "We need to implement a new software algorithm for our system"
        domains = stage._extract_task_domains(tech_text)
        assert "technology" in domains
        
        business_text = "We need to analyze market strategy and customer revenue"
        domains = stage._extract_task_domains(business_text)
        assert "business" in domains
        
        # Test with no clear domain
        generic_text = "This is a general problem"
        domains = stage._extract_task_domains(generic_text)
        assert "general" in domains
    
    def test_agent_selection_for_task(self, consensus_stage):
        """Test agent selection based on task requirements."""
        stage = consensus_stage
        
        # Technology task
        tech_input = {
            "domain": "technology",
            "problem": "Choose software architecture"
        }
        selected_agents = stage._select_agents_for_task(tech_input, {})
        
        # Should select appropriate number of agents
        assert stage.min_agents <= len(selected_agents) <= stage.max_agents
        
        # Should include technology specialist
        agent_ids = [agent.id for agent in selected_agents]
        assert "specialist_tech" in agent_ids
        
        # Security task should include security critic
        security_input = {
            "domain": "security",
            "problem": "Assess security risks"
        }
        selected_agents = stage._select_agents_for_task(security_input, {})
        agent_ids = [agent.id for agent in selected_agents]
        assert "critic_security" in agent_ids
    
    def test_agent_relevance_calculation(self, consensus_stage):
        """Test agent relevance scoring for tasks."""
        stage = consensus_stage
        
        tech_specialist = next(a for a in stage.virtual_agents if a.id == "specialist_tech")
        business_specialist = next(a for a in stage.virtual_agents if a.id == "specialist_business")
        
        tech_domains = ["technology", "software"]
        business_domains = ["business", "strategy"]
        
        # Tech specialist should be more relevant for tech domains
        tech_relevance_for_tech = stage._calculate_agent_relevance(
            tech_specialist, tech_domains, {}
        )
        tech_relevance_for_business = stage._calculate_agent_relevance(
            tech_specialist, business_domains, {}
        )
        
        assert tech_relevance_for_tech > tech_relevance_for_business
        
        # Business specialist should be more relevant for business domains
        business_relevance_for_business = stage._calculate_agent_relevance(
            business_specialist, business_domains, {}
        )
        business_relevance_for_tech = stage._calculate_agent_relevance(
            business_specialist, tech_domains, {}
        )
        
        assert business_relevance_for_business > business_relevance_for_tech
    
    def test_conflict_detection(self, consensus_stage):
        """Test conflict detection between agent perspectives."""
        stage = consensus_stage
        
        # Create perspectives with high confidence variance (conflict)
        conflicting_perspectives = [
            {"agent_id": "agent_1", "role": "specialist", "confidence": 0.9},
            {"agent_id": "agent_2", "role": "critic", "confidence": 0.2},
            {"agent_id": "agent_3", "role": "validator", "confidence": 0.8}
        ]
        
        conflicts = stage._detect_conflicts(conflicting_perspectives)
        
        # Should detect confidence conflict
        assert len(conflicts) > 0
        confidence_conflicts = [c for c in conflicts if c["type"] == "confidence_conflict"]
        assert len(confidence_conflicts) > 0
        
        # Create perspectives with no conflict
        harmonious_perspectives = [
            {"agent_id": "agent_1", "role": "specialist", "confidence": 0.8},
            {"agent_id": "agent_2", "role": "critic", "confidence": 0.7},
            {"agent_id": "agent_3", "role": "validator", "confidence": 0.75}
        ]
        
        conflicts = stage._detect_conflicts(harmonious_perspectives)
        
        # Should detect fewer or no conflicts
        confidence_conflicts = [c for c in conflicts if c["type"] == "confidence_conflict"]
        assert len(confidence_conflicts) == 0
    
    def test_role_conflict_detection(self, consensus_stage):
        """Test detection of role-based conflicts."""
        stage = consensus_stage
        
        # Create perspectives with role conflict (critics vs specialists)
        perspectives = [
            {"agent_id": "specialist_1", "role": "specialist", "confidence": 0.9},
            {"agent_id": "specialist_2", "role": "specialist", "confidence": 0.85},
            {"agent_id": "critic_1", "role": "critic", "confidence": 0.3},
            {"agent_id": "critic_2", "role": "critic", "confidence": 0.4}
        ]
        
        conflicts = stage._detect_conflicts(perspectives)
        
        # Should detect role conflict
        role_conflicts = [c for c in conflicts if c["type"] == "role_conflict"]
        assert len(role_conflicts) > 0
        
        role_conflict = role_conflicts[0]
        assert "critics_confidence" in role_conflict
        assert "specialists_confidence" in role_conflict
        assert abs(role_conflict["critics_confidence"] - role_conflict["specialists_confidence"]) > 0.4
    
    @pytest.mark.asyncio
    async def test_conflict_resolution(self, consensus_stage):
        """Test conflict resolution mechanisms."""
        stage = consensus_stage
        
        # Create conflicting perspectives
        perspectives = [
            {"agent_id": "agent_1", "role": "specialist", "confidence": 0.9},
            {"agent_id": "agent_2", "role": "critic", "confidence": 0.2},
            {"agent_id": "agent_3", "role": "validator", "confidence": 0.8}
        ]
        
        conflicts = [
            {
                "type": "confidence_conflict",
                "severity": 0.35,
                "description": "High variance in confidence"
            }
        ]
        
        # Mock communication hub
        mock_hub = Mock()
        
        resolved_perspectives = await stage._resolve_conflicts(
            perspectives, conflicts, stage.virtual_agents, mock_hub
        )
        
        # Should have applied conflict resolution
        assert len(resolved_perspectives) == len(perspectives)
        
        # Confidence values should be adjusted (moved toward median)
        original_confidences = [p["confidence"] for p in perspectives]
        resolved_confidences = [p["confidence"] for p in resolved_perspectives]
        
        # Variance should be reduced
        import numpy as np
        original_variance = np.var(original_confidences)
        resolved_variance = np.var(resolved_confidences)
        assert resolved_variance <= original_variance
        
        # Should have conflict resolution metadata
        for perspective in resolved_perspectives:
            assert "conflict_resolution" in perspective
    
    @pytest.mark.asyncio
    async def test_consensus_building(self, consensus_stage):
        """Test consensus building from perspectives."""
        stage = consensus_stage
        
        # Create sample perspectives
        perspectives = [
            {
                "agent_id": "agent_1",
                "role": "specialist",
                "confidence": 0.8,
                "vote_weight": 1.2,
                "perspective": {
                    "recommendations": ["Use microservices", "Implement caching"],
                    "analysis_type": "technical"
                },
                "reasoning": ["Based on scalability needs", "Performance requirements"]
            },
            {
                "agent_id": "agent_2", 
                "role": "critic",
                "confidence": 0.6,
                "vote_weight": 0.9,
                "perspective": {
                    "concerns": ["Complexity overhead", "Maintenance burden"],
                    "analysis_type": "critical"
                },
                "reasoning": ["Risk assessment", "Long-term considerations"]
            }
        ]
        
        # Mock communication hub
        mock_hub = Mock()
        
        consensus_result = await stage._build_consensus(perspectives, mock_hub)
        
        assert consensus_result["consensus_reached"] is True
        assert "overall_confidence" in consensus_result
        assert "participating_agents" in consensus_result
        assert "agent_perspectives" in consensus_result
        assert "synthesized_recommendations" in consensus_result
        assert "identified_concerns" in consensus_result
        assert "collective_reasoning" in consensus_result
        assert "consensus_metrics" in consensus_result
        
        # Check synthesized results
        assert "Use microservices" in consensus_result["synthesized_recommendations"]
        assert "Complexity overhead" in consensus_result["identified_concerns"]
        assert len(consensus_result["collective_reasoning"]) > 0
        
        # Check consensus type
        assert consensus_result["consensus_type"] in ["strong_consensus", "moderate_consensus", "weak_consensus"]
    
    def test_triplet_consensus_validation(self, consensus_stage):
        """Test O(n³) triplet consensus validation."""
        stage = consensus_stage
        
        # Test strong consensus triplet
        strong_perspectives = [
            {"confidence": 0.8},
            {"confidence": 0.85},
            {"confidence": 0.9}
        ]
        
        consensus_strength = stage._validate_triplet_consensus(*strong_perspectives)
        assert consensus_strength > 0.7  # Should be strong
        
        # Test weak consensus triplet
        weak_perspectives = [
            {"confidence": 0.9},
            {"confidence": 0.2},
            {"confidence": 0.8}
        ]
        
        consensus_strength = stage._validate_triplet_consensus(*weak_perspectives)
        assert consensus_strength < 0.7  # Should be weak due to variance
    
    @pytest.mark.asyncio
    async def test_full_consensus_process(self, consensus_stage, sample_context):
        """Test the complete consensus process."""
        stage = consensus_stage
        
        # Mock the communication hub initialization
        with patch('src.agent_swarm.algorithms.algorithms.stages.multi_agent_consensus_stage.initialize_communication_hub') as mock_init:
            mock_hub = Mock(spec=MultiAgentCommunicationHub)
            mock_hub.register_agent = Mock()
            mock_init.return_value = mock_hub
            
            # Test input
            input_data = {
                "domain": "technology",
                "problem": "Should we migrate to microservices architecture?",
                "constraints": ["timeline", "team_size", "complexity"]
            }
            
            # Process the consensus
            result = await stage.process(input_data, sample_context)
            
            # Verify result structure
            assert result.success is True
            assert result.stage_name == "multi_agent_consensus"
            assert result.confidence > 0.0
            assert result.execution_time > 0.0
            
            # Verify output structure
            output = result.output
            assert output["stage"] == "multi_agent_consensus"
            assert "consensus_reached" in output
            assert "consensus_type" in output
            assert "overall_confidence" in output
            assert "participating_agents" in output
            assert "agent_contributions" in output
            assert "synthesized_result" in output
            assert "consensus_metrics" in output
            
            # Verify agent contributions
            contributions = output["agent_contributions"]
            assert len(contributions) >= stage.min_agents
            assert len(contributions) <= stage.max_agents
            
            for contribution in contributions:
                assert "agent_id" in contribution
                assert "role" in contribution
                assert "confidence" in contribution
                assert "perspective_summary" in contribution
                assert "vote_weight" in contribution
            
            # Verify synthesized result
            synthesized = output["synthesized_result"]
            assert "recommendations" in synthesized
            assert "concerns" in synthesized
            assert "reasoning_chain" in synthesized
            
            # Verify metadata
            metadata = result.metadata
            assert metadata["implemented"] is True
            assert metadata["revolutionary_features"] is True
            assert "agents_participated" in metadata
            assert "consensus_type" in metadata
            assert "ml_features_used" in metadata
    
    @pytest.mark.asyncio
    async def test_error_handling(self, consensus_stage, sample_context):
        """Test error handling in consensus process."""
        stage = consensus_stage
        
        # Test with invalid input that might cause errors
        invalid_input = None
        
        result = await stage.process(invalid_input, sample_context)
        
        # Should handle error gracefully
        assert result.success is False
        assert result.error is not None
        assert "Multi-agent consensus failed" in result.error
        assert result.confidence == 0.0
    
    @pytest.mark.asyncio
    async def test_consensus_history_tracking(self, consensus_stage, sample_context):
        """Test consensus history tracking."""
        stage = consensus_stage
        
        # Mock communication hub
        with patch('src.agent_swarm.algorithms.algorithms.stages.multi_agent_consensus_stage.initialize_communication_hub') as mock_init:
            mock_hub = Mock(spec=MultiAgentCommunicationHub)
            mock_hub.register_agent = Mock()
            mock_init.return_value = mock_hub
            
            initial_history_length = len(stage.consensus_history)
            
            # Process consensus
            input_data = {"problem": "Test consensus tracking"}
            await stage.process(input_data, sample_context)
            
            # Should have added to history
            assert len(stage.consensus_history) == initial_history_length + 1
            
            # Check history entry
            latest_entry = stage.consensus_history[-1]
            assert "timestamp" in latest_entry
            assert "input_summary" in latest_entry
            assert "consensus_reached" in latest_entry
            assert "confidence" in latest_entry
            assert "agents_used" in latest_entry


@pytest.mark.integration
class TestMultiAgentIntegration:
    """Integration tests for multi-agent consensus with real communication."""
    
    @pytest.mark.asyncio
    async def test_consensus_with_real_communication_hub(self):
        """Test consensus stage with actual communication hub."""
        from src.agent_swarm.core.multi_agent_communication import initialize_communication_hub
        
        # Initialize real communication hub
        hub = await initialize_communication_hub()
        
        try:
            stage = MultiAgentConsensusStage()
            context = AlgorithmContext(
                request_id="integration_test",
                user_id="test_user",
                session_id="test_session"
            )
            
            # Test complex decision scenario
            input_data = {
                "domain": "technology",
                "problem": "Evaluate the feasibility of implementing AI-powered code review system",
                "constraints": [
                    "Must integrate with existing CI/CD pipeline",
                    "Budget limit of $50k",
                    "Timeline of 3 months",
                    "Team has limited ML expertise"
                ],
                "requirements": [
                    "Reduce code review time by 40%",
                    "Maintain code quality standards",
                    "Support multiple programming languages"
                ]
            }
            
            # Process consensus
            result = await stage.process(input_data, context)
            
            # Verify comprehensive result
            assert result.success is True
            assert result.confidence > 0.0
            
            output = result.output
            assert output["consensus_reached"] is True
            assert len(output["agent_contributions"]) >= 3
            
            # Should have diverse perspectives
            roles = [contrib["role"] for contrib in output["agent_contributions"]]
            assert len(set(roles)) >= 3  # At least 3 different roles
            
            # Should have synthesized recommendations and concerns
            synthesized = output["synthesized_result"]
            assert len(synthesized["recommendations"]) > 0
            assert len(synthesized["concerns"]) > 0
            assert len(synthesized["reasoning_chain"]) > 0
            
            # Should have consensus metrics
            metrics = output["consensus_metrics"]
            assert "confidence_variance" in metrics
            assert "role_diversity" in metrics
            assert "expertise_coverage" in metrics
            
        finally:
            await hub.shutdown()
    
    @pytest.mark.asyncio
    async def test_multiple_consensus_rounds(self):
        """Test multiple consensus rounds with different scenarios."""
        from src.agent_swarm.core.multi_agent_communication import initialize_communication_hub
        
        hub = await initialize_communication_hub()
        
        try:
            stage = MultiAgentConsensusStage()
            context = AlgorithmContext(
                request_id="multi_round_test",
                user_id="test_user",
                session_id="test_session"
            )
            
            scenarios = [
                {
                    "domain": "security",
                    "problem": "Assess security implications of cloud migration",
                    "expected_critic_involvement": True
                },
                {
                    "domain": "business",
                    "problem": "Evaluate ROI of new product line",
                    "expected_business_specialist": True
                },
                {
                    "domain": "technology",
                    "problem": "Choose between React and Vue.js for frontend",
                    "expected_tech_specialist": True
                }
            ]
            
            results = []
            for scenario in scenarios:
                result = await stage.process(scenario, context)
                results.append(result)
                
                # All should succeed
                assert result.success is True
                
                # Should have appropriate agent involvement
                agent_roles = [contrib["role"] for contrib in result.output["agent_contributions"]]
                
                if scenario.get("expected_critic_involvement"):
                    assert "critic" in agent_roles
                if scenario.get("expected_business_specialist"):
                    # Business specialist should be involved
                    agent_ids = [contrib["agent_id"] for contrib in result.output["agent_contributions"]]
                    assert "specialist_business" in agent_ids
                if scenario.get("expected_tech_specialist"):
                    agent_ids = [contrib["agent_id"] for contrib in result.output["agent_contributions"]]
                    assert "specialist_tech" in agent_ids
            
            # Check consensus history
            assert len(stage.consensus_history) == len(scenarios)
            
            # All consensus attempts should be recorded
            for i, history_entry in enumerate(stage.consensus_history):
                assert "timestamp" in history_entry
                assert "consensus_reached" in history_entry
                assert history_entry["consensus_reached"] is True
                
        finally:
            await hub.shutdown()


if __name__ == "__main__":
    # Run tests with verbose output
    pytest.main([__file__, "-v", "--tb=short", "-m", "not integration"])