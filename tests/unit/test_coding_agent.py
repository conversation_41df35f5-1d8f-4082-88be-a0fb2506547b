"""
Unit tests for the CodingAgent.
"""

import tempfile
from pathlib import Path
from unittest.mock import Async<PERSON>ock, MagicMock, patch

import pytest

from agent_swarm.agents.coding_agent import CodingAgent
from agent_swarm.backends import <PERSON><PERSON><PERSON><PERSON>, LLMResponse, Message
from agent_swarm.context.dev_rag import DevelopmentRAG
from agent_swarm.mcp import MCPToolRegistry
from ..conftest import MockLLM


@pytest.fixture
def mock_dev_rag():
    """Mock development RAG system."""
    rag = MagicMock(spec=DevelopmentRAG)
    rag.get_context_for_task = AsyncMock(return_value=MagicMock(chunks=[]))
    rag.search_code = AsyncMock(return_value=MagicMock(chunks=[]))
    rag.get_related_files = AsyncMock(return_value=MagicMock(chunks=[]))
    return rag


@pytest.fixture
def mock_mcp_registry():
    """Mock MCP tool registry."""
    registry = MagicMock(spec=MCPToolRegistry)
    registry.call_tool = AsyncMock(return_value=MagicMock(success=True, result={"content": "test"}))
    return registry


@pytest.fixture
def temp_project_dir():
    """Create temporary project directory."""
    with tempfile.TemporaryDirectory() as temp_dir:
        project_path = Path(temp_dir)

        # Create sample files
        (project_path / "main.py").write_text("print('Hello, World!')")
        (project_path / "utils.py").write_text("def add(a, b): return a + b")

        yield project_path


@pytest.fixture
def coding_agent(configured_router, mock_dev_rag, mock_mcp_registry, temp_project_dir):
    """Create CodingAgent with mocked dependencies."""
    agent = CodingAgent(
        name="TestCoder",
        llm_router=configured_router,
        project_path=str(temp_project_dir),
        mcp_registry=mock_mcp_registry,
        dev_rag=mock_dev_rag
    )
    return agent


@pytest.mark.unit
class TestCodingAgent:
    """Test CodingAgent functionality."""

    def test_agent_initialization(self, coding_agent, temp_project_dir):
        """Test coding agent initialization."""
        assert coding_agent.name == "TestCoder"
        assert str(coding_agent.project_path) == str(temp_project_dir)
        assert coding_agent.dev_rag is not None
        assert coding_agent.mcp_registry is not None
        assert len(coding_agent.coding_sessions) == 0
        assert coding_agent.current_session is None
        assert coding_agent.cli_tool is not None
        assert coding_agent.fs_tool is not None
        assert coding_agent.dev_tool is not None

    async def test_start_coding_session(self, coding_agent):
        """Test starting a coding session."""
        task = "Implement user authentication"

        session = await coding_agent.start_coding_session(task)

        assert session["task"] == task
        assert "start_time" in session
        assert "steps" in session
        assert "files_created" in session
        assert "files_modified" in session
        assert "commands_executed" in session
        assert "context_used" in session

        assert coding_agent.current_session == session
        assert len(coding_agent.coding_sessions) == 1

    async def test_implement_feature(self, coding_agent, mock_mcp_registry):
        """Test feature implementation."""
        # Mock successful file writing
        mock_mcp_registry.call_tool.return_value = MagicMock(success=True)

        result = await coding_agent.implement_feature(
            feature_description="Create a calculator function",
            file_path="calculator.py",
            test_required=True
        )

        assert "success" in result
        assert "steps" in result
        assert result["task"] == "Implement feature: Create a calculator function"

        # Verify session was created
        assert coding_agent.current_session is not None
        assert len(coding_agent.coding_sessions) == 1

    async def test_implement_feature_with_tests(self, coding_agent, mock_mcp_registry, configured_router):
        """Test feature implementation with tests."""
        # Mock successful file operations
        mock_mcp_registry.call_tool.return_value = MagicMock(success=True)

        # Mock CLI tool for validation
        coding_agent.cli_tool.execute_command = AsyncMock(return_value={
            "success": True,
            "stdout": "",
            "stderr": "",
            "return_code": 0
        })

        # Mock Path.exists to return True for test files
        from unittest.mock import patch
        with patch('pathlib.Path.exists', return_value=True):
            # Mock LLM responses for all steps
            mock_response = LLMResponse(
                content="```python\ndef string_utility():\n    return 'utility'\n```",
                model="test-model",
                usage={"total_tokens": 50}
            )
            mock_response.metadata = {"llm_used": "test-model"}
            configured_router.route_request = AsyncMock(return_value=mock_response)

            result = await coding_agent.implement_feature(
                feature_description="Create a string utility",
                file_path="src/string_utils.py",
                test_required=True
            )

            assert result["success"]

            # Should have implementation and test steps
            steps = result["steps"]
            step_types = [step["step"] for step in steps]
            assert "implementation" in step_types
            assert "testing" in step_types

    async def test_fix_bug(self, coding_agent, mock_mcp_registry):
        """Test bug fixing functionality."""
        # Mock file reading and writing
        mock_mcp_registry.call_tool.side_effect = [
            MagicMock(success=True, result={"content": "def broken_function(): pass"}),  # read
            MagicMock(success=True)  # write
        ]

        result = await coding_agent.fix_bug(
            bug_description="Function returns None instead of result",
            file_path="utils.py",
            error_message="TypeError: NoneType object is not callable"
        )

        assert "success" in result
        assert "steps" in result
        assert result["task"] == "Fix bug: Function returns None instead of result"

        # Verify session was created
        assert coding_agent.current_session is not None

    async def test_get_coding_session_summary(self, coding_agent):
        """Test getting coding session summary."""
        # Create some mock sessions
        await coding_agent.start_coding_session("Task 1")
        coding_agent.current_session["success"] = True
        coding_agent.current_session["files_created"] = ["file1.py"]

        await coding_agent.start_coding_session("Task 2")
        coding_agent.current_session["success"] = False
        coding_agent.current_session["files_modified"] = ["file2.py"]

        summary = await coding_agent.get_coding_session_summary()

        assert summary["total_sessions"] == 2
        assert summary["successful_sessions"] == 1
        assert summary["success_rate"] == 0.5
        assert summary["files_created"] == 1
        assert summary["files_modified"] == 1
        assert len(summary["recent_sessions"]) == 2

    async def test_analyze_and_plan(self, coding_agent, configured_router):
        """Test analysis and planning step."""
        # Mock LLM response - metadata is optional
        mock_response = LLMResponse(
            content="Analysis: This feature requires...\nPlan: 1. Create function 2. Add tests",
            model="test-model",
            usage={"total_tokens": 50}
        )
        # Add metadata attribute manually since it's optional
        mock_response.metadata = {"llm_used": "test-model", "cost": 0.01}
        configured_router.route_request = AsyncMock(return_value=mock_response)

        plan = await coding_agent._analyze_and_plan(
            "Create authentication system",
            "auth.py"
        )

        assert "analysis" in plan
        assert "llm_used" in plan
        assert "This feature requires" in plan["analysis"]

    async def test_gather_implementation_context(self, coding_agent, mock_dev_rag):
        """Test gathering implementation context."""
        # Mock RAG responses
        mock_chunk = MagicMock()
        mock_chunk.content = "class AuthManager: pass"
        mock_chunk.metadata = {"file_path": "auth.py"}
        mock_chunk.score = 0.9

        mock_dev_rag.search_code.return_value = [mock_chunk]
        mock_dev_rag.get_related_files.return_value = []

        context = await coding_agent._gather_implementation_context(
            "authentication system",
            "auth.py"
        )

        assert "similar_implementations" in context
        assert "related_files" in context
        assert "relevant_patterns" in context
        assert len(context["similar_implementations"]) > 0

    async def test_implement_code(self, coding_agent, configured_router, mock_mcp_registry):
        """Test code implementation step."""
        # Start a session first
        await coding_agent.start_coding_session("Test implementation")

        # Mock LLM response with code
        mock_response = LLMResponse(
            content="```python\ndef authenticate(user, password):\n    return True\n```",
            model="test-model",
            usage={"total_tokens": 75}
        )
        mock_response.metadata = {"llm_used": "test-model"}
        configured_router.route_request = AsyncMock(return_value=mock_response)

        # Mock successful file writing
        mock_mcp_registry.call_tool.return_value = MagicMock(success=True)

        plan = {"analysis": "Create auth function"}
        context = {"similar_implementations": [], "related_files": []}

        result = await coding_agent._implement_code(
            "authentication function",
            "auth.py",
            plan,
            context
        )

        assert "code" in result
        assert "file_path" in result
        assert "file_written" in result
        assert "def authenticate" in result["code"]

    async def test_create_tests(self, coding_agent, configured_router, mock_mcp_registry):
        """Test test creation functionality."""
        # Start a session first
        await coding_agent.start_coding_session("Test creation")

        # Mock LLM response with test code
        mock_response = LLMResponse(
            content="```python\ndef test_authenticate():\n    assert authenticate('user', 'pass')\n```",
            model="test-model",
            usage={"total_tokens": 60}
        )
        mock_response.metadata = {"llm_used": "test-model"}
        configured_router.route_request = AsyncMock(return_value=mock_response)

        # Mock successful file writing
        mock_mcp_registry.call_tool.return_value = MagicMock(success=True)

        # Create implementation dict as expected by the method
        implementation = {
            "code": "def authenticate(user, password): return True",
            "file_path": "auth.py"
        }

        result = await coding_agent._create_tests(
            "authentication feature",
            "auth.py",
            implementation
        )

        assert "test_code" in result
        assert "test_file" in result
        assert "file_written" in result
        assert "test_authenticate" in result["test_code"]

    async def test_extract_code_from_response(self, coding_agent):
        """Test code extraction from LLM response."""
        # Test with code blocks
        response_with_blocks = """
        Here's the implementation:

        ```python
        def hello():
            print("Hello, World!")
        ```

        This function prints a greeting.
        """

        code = coding_agent._extract_code_from_response(response_with_blocks)
        assert "def hello():" in code
        assert "print(" in code

        # Test without code blocks
        response_without_blocks = "def simple(): return 42"
        code = coding_agent._extract_code_from_response(response_without_blocks)
        assert code == response_without_blocks

    async def test_generate_test_file_path(self, coding_agent):
        """Test test file path generation."""
        # Test with src/ directory
        test_path = coding_agent._generate_test_file_path("src/module/auth.py")
        assert "tests/test_auth.py" in test_path

        # Test without src/ directory
        test_path = coding_agent._generate_test_file_path("utils.py")
        assert "test_utils.py" in test_path

    async def test_analyze_bug(self, coding_agent, configured_router):
        """Test bug analysis functionality."""
        # Mock LLM response with metadata
        mock_response = LLMResponse(
            content="Root cause: Missing return statement\nFix: Add return value",
            model="test-model",
            usage={"total_tokens": 40}
        )
        mock_response.metadata = {"llm_used": "test-model"}
        configured_router.route_request = AsyncMock(return_value=mock_response)

        analysis = await coding_agent._analyze_bug(
            "Function returns None",
            "utils.py",
            "TypeError: NoneType",
            {"similar_bugs": []}
        )

        assert "analysis" in analysis
        assert "llm_used" in analysis
        assert "Root cause" in analysis["analysis"]

    async def test_implement_bug_fix(self, coding_agent, configured_router, mock_mcp_registry):
        """Test bug fix implementation."""
        # Start a session first
        await coding_agent.start_coding_session("Test bug fix")

        # Mock file reading
        mock_mcp_registry.call_tool.side_effect = [
            MagicMock(success=True, result={"content": "def broken(): pass"}),  # read
            MagicMock(success=True)  # write
        ]

        # Mock LLM response with fixed code
        mock_response = LLMResponse(
            content="def fixed(): return 'working'",
            model="test-model",
            usage={"total_tokens": 30}
        )
        mock_response.metadata = {"llm_used": "test-model"}
        configured_router.route_request = AsyncMock(return_value=mock_response)

        analysis = {"analysis": "Add return statement"}

        result = await coding_agent._implement_bug_fix(
            "Function returns None",
            "utils.py",
            analysis
        )

        assert "fixed_code" in result
        assert "file_written" in result
        assert "return 'working'" in result["fixed_code"]

    async def test_validate_bug_fix(self, coding_agent):
        """Test bug fix validation."""
        # Mock the CLI tool's execute_command method
        coding_agent.cli_tool.execute_command = AsyncMock(return_value={
            "success": True,
            "stdout": "",
            "stderr": "",
            "return_code": 0
        })

        result = await coding_agent._validate_bug_fix("utils.py", "Fix return value")

        assert "syntax_check" in result
        assert "success" in result
        assert result["syntax_check"]

    async def test_error_handling(self, coding_agent, configured_router):
        """Test error handling in coding agent."""
        # Mock LLM failure
        configured_router.route_request = AsyncMock(side_effect=Exception("LLM failed"))

        result = await coding_agent.implement_feature(
            "test feature",
            "test.py"
        )

        # Should handle error gracefully
        assert not result["success"]
        assert "error" in result or "steps" in result

    async def test_session_tracking(self, coding_agent):
        """Test session tracking functionality."""
        # Start multiple sessions
        session1 = await coding_agent.start_coding_session("Task 1")
        session1["success"] = True
        session1["files_created"] = ["file1.py"]

        session2 = await coding_agent.start_coding_session("Task 2")
        session2["success"] = False
        session2["files_modified"] = ["file2.py"]

        # Verify sessions are tracked
        assert len(coding_agent.coding_sessions) == 2
        assert coding_agent.current_session == session2

        # Verify session data
        assert coding_agent.coding_sessions[0]["task"] == "Task 1"
        assert coding_agent.coding_sessions[1]["task"] == "Task 2"


@pytest.mark.integration
class TestCodingAgentIntegration:
    """Integration tests for CodingAgent."""

    async def test_full_feature_implementation_workflow(self, temp_project_dir):
        """Test complete feature implementation workflow."""
        from agent_swarm import create_coding_agent

        # Create real coding agent (with mocked LLM)
        agent = await create_coding_agent(
            name="IntegrationTester",
            project_path=str(temp_project_dir),
            enable_rag=False  # Disable RAG for simpler test
        )

        # Mock the LLM router to return predictable responses
        mock_response = LLMResponse(
            content="def calculator(a, b, op):\n    if op == '+':\n        return a + b\n    return 0",
            model="test-model",
            usage={"total_tokens": 50}
        )
        mock_response.metadata = {"llm_used": "test-model", "cost": 0.01}
        agent.llm_router.route_request = AsyncMock(return_value=mock_response)

        # Mock MCP tool calls
        agent.mcp_registry.call_tool = AsyncMock(return_value=MagicMock(success=True))

        # Mock CLI tool for validation
        agent.cli_tool.execute_command = AsyncMock(return_value={
            "success": True,
            "stdout": "",
            "stderr": "",
            "return_code": 0
        })

        # Implement feature
        result = await agent.implement_feature(
            "Create a simple calculator function",
            "calculator.py",
            test_required=False
        )

        assert result["success"]
        assert len(result["steps"]) > 0

        # Verify session was created
        summary = await agent.get_coding_session_summary()
        assert summary["total_sessions"] == 1
