"""
Comprehensive unit tests for CLI Agent functionality.
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

from agent_swarm.agents.cli_agent import CLIAgent, create_cli_agent
from agent_swarm.backends import <PERSON><PERSON><PERSON><PERSON>, LLMResponse, Message
from agent_swarm.tools import LinuxCLITool, FileSystemTool


@pytest.mark.unit
class TestCLIAgent:
    """Test CLI Agent functionality."""

    @pytest.fixture
    def mock_llm_router(self):
        """Create mock LLM router."""
        router = Mock(spec=LLMRouter)
        router.generate = AsyncMock(return_value=LLMResponse(
            content="CLI agent response",
            model="test-model",
            usage={"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30},
            metadata={}
        ))
        return router

    @pytest.fixture
    def mock_cli_tool(self):
        """Create mock CLI tool."""
        tool = Mock(spec=LinuxCLITool)
        tool.execute = AsyncMock(return_value={
            "success": True,
            "output": "Command executed successfully",
            "exit_code": 0
        })
        return tool

    @pytest.fixture
    def mock_fs_tool(self):
        """Create mock filesystem tool."""
        tool = Mock(spec=FileSystemTool)
        tool.execute = AsyncMock(return_value={
            "success": True,
            "content": "File content",
            "path": "/test/file.txt"
        })
        return tool

    @pytest.fixture
    def cli_agent(self, mock_llm_router):
        """Create CLI agent instance."""
        return CLIAgent(
            name="TestCLIAgent",
            llm_router=mock_llm_router
        )

    def test_cli_agent_creation(self, mock_llm_router):
        """Test creating CLI agent."""
        agent = CLIAgent(
            name="TestAgent",
            llm_router=mock_llm_router
        )
        
        assert agent.name == "TestAgent"
        assert agent.llm_router == mock_llm_router
        assert agent.system_prompt == "Test prompt"
        assert len(agent.tools) > 0  # Should have default tools

    def test_cli_agent_with_custom_tools(self, mock_llm_router, mock_cli_tool):
        """Test creating CLI agent with custom tools."""
        tools = [mock_cli_tool]
        
        agent = CLIAgent(
            name="CustomAgent",
            llm_router=mock_llm_router,
            tools=tools
        )
        
        assert mock_cli_tool in agent.tools

    @pytest.mark.asyncio
    async def test_execute_command(self, cli_agent, mock_cli_tool):
        """Test executing CLI command."""
        cli_agent.tools = [mock_cli_tool]
        
        result = await cli_agent.execute_command("ls -la")
        
        assert result["success"] is True
        assert "output" in result
        mock_cli_tool.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_command_failure(self, cli_agent, mock_cli_tool):
        """Test executing CLI command that fails."""
        mock_cli_tool.execute.return_value = {
            "success": False,
            "error": "Command failed",
            "exit_code": 1
        }
        cli_agent.tools = [mock_cli_tool]
        
        result = await cli_agent.execute_command("invalid_command")
        
        assert result["success"] is False
        assert "error" in result

    @pytest.mark.asyncio
    async def test_read_file(self, cli_agent, mock_fs_tool):
        """Test reading file."""
        cli_agent.tools = [mock_fs_tool]
        
        result = await cli_agent.read_file("/test/file.txt")
        
        assert result["success"] is True
        assert "content" in result
        mock_fs_tool.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_write_file(self, cli_agent, mock_fs_tool):
        """Test writing file."""
        mock_fs_tool.execute.return_value = {
            "success": True,
            "path": "/test/output.txt",
            "bytes_written": 12
        }
        cli_agent.tools = [mock_fs_tool]
        
        result = await cli_agent.write_file("/test/output.txt", "Hello World!")
        
        assert result["success"] is True
        assert "path" in result
        mock_fs_tool.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_request(self, cli_agent):
        """Test processing user request."""
        request = "List files in current directory"
        
        response = await cli_agent.process_request(request)
        
        assert isinstance(response, LLMResponse)
        assert response.content == "CLI agent response"
        cli_agent.llm_router.generate.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_request_with_context(self, cli_agent):
        """Test processing request with context."""
        request = "Explain this file"
        context = {"file_content": "def hello(): print('Hello')"}
        
        response = await cli_agent.process_request(request, context=context)
        
        assert isinstance(response, LLMResponse)
        # Should have included context in the prompt
        call_args = cli_agent.llm_router.generate.call_args[0][0]
        assert any("file_content" in str(msg.content) for msg in call_args)

    @pytest.mark.asyncio
    async def test_get_system_info(self, cli_agent, mock_cli_tool):
        """Test getting system information."""
        mock_cli_tool.execute.return_value = {
            "success": True,
            "output": "Linux testhost 5.4.0",
            "exit_code": 0
        }
        cli_agent.tools = [mock_cli_tool]
        
        if hasattr(cli_agent, 'get_system_info'):
            info = await cli_agent.get_system_info()
            assert isinstance(info, dict)

    @pytest.mark.asyncio
    async def test_list_directory(self, cli_agent, mock_fs_tool):
        """Test listing directory contents."""
        mock_fs_tool.execute.return_value = {
            "success": True,
            "entries": [
                {"name": "file1.txt", "type": "file"},
                {"name": "subdir", "type": "directory"}
            ],
            "path": "/test"
        }
        cli_agent.tools = [mock_fs_tool]
        
        result = await cli_agent.list_directory("/test")
        
        assert result["success"] is True
        assert "entries" in result
        assert len(result["entries"]) == 2

    @pytest.mark.asyncio
    async def test_find_files(self, cli_agent, mock_cli_tool):
        """Test finding files."""
        mock_cli_tool.execute.return_value = {
            "success": True,
            "output": "/test/file1.py\n/test/file2.py",
            "exit_code": 0
        }
        cli_agent.tools = [mock_cli_tool]
        
        if hasattr(cli_agent, 'find_files'):
            result = await cli_agent.find_files("*.py", "/test")
            assert result["success"] is True

    @pytest.mark.asyncio
    async def test_error_handling(self, cli_agent, mock_cli_tool):
        """Test error handling in CLI agent."""
        mock_cli_tool.execute.side_effect = Exception("Tool failed")
        cli_agent.tools = [mock_cli_tool]
        
        result = await cli_agent.execute_command("test")
        
        # Should handle errors gracefully
        assert "error" in result or "success" in result

    def test_get_available_tools(self, cli_agent):
        """Test getting available tools."""
        tools = cli_agent.get_available_tools()
        
        assert isinstance(tools, list)
        assert len(tools) > 0

    def test_add_tool(self, cli_agent, mock_cli_tool):
        """Test adding tool to agent."""
        initial_count = len(cli_agent.tools)
        
        cli_agent.add_tool(mock_cli_tool)
        
        assert len(cli_agent.tools) == initial_count + 1
        assert mock_cli_tool in cli_agent.tools

    def test_remove_tool(self, cli_agent, mock_cli_tool):
        """Test removing tool from agent."""
        cli_agent.add_tool(mock_cli_tool)
        initial_count = len(cli_agent.tools)
        
        cli_agent.remove_tool(mock_cli_tool)
        
        assert len(cli_agent.tools) == initial_count - 1
        assert mock_cli_tool not in cli_agent.tools

    @pytest.mark.asyncio
    async def test_batch_commands(self, cli_agent, mock_cli_tool):
        """Test executing batch commands."""
        mock_cli_tool.execute.return_value = {
            "success": True,
            "output": "Command executed",
            "exit_code": 0
        }
        cli_agent.tools = [mock_cli_tool]
        
        commands = ["ls", "pwd", "whoami"]
        
        if hasattr(cli_agent, 'execute_batch'):
            results = await cli_agent.execute_batch(commands)
            assert len(results) == 3
            assert all(r["success"] for r in results)

    @pytest.mark.asyncio
    async def test_interactive_session(self, cli_agent):
        """Test interactive session handling."""
        if hasattr(cli_agent, 'start_interactive_session'):
            # Test that interactive session can be started
            session = await cli_agent.start_interactive_session()
            assert session is not None


@pytest.mark.unit
class TestCreateCLIAgent:
    """Test create_cli_agent factory function."""

    @pytest.mark.asyncio
    async def test_create_cli_agent_default(self):
        """Test creating CLI agent with defaults."""
        with patch('agent_swarm.agents.cli_agent.LLMRouter') as mock_router_class:
            mock_router = Mock()
            mock_router_class.return_value = mock_router
            
            agent = await create_cli_agent()
            
            assert isinstance(agent, CLIAgent)
            assert agent.name == "CLIAgent"

    @pytest.mark.asyncio
    async def test_create_cli_agent_custom(self):
        """Test creating CLI agent with custom parameters."""
        with patch('agent_swarm.agents.cli_agent.LLMRouter') as mock_router_class:
            mock_router = Mock()
            mock_router_class.return_value = mock_router
            
            agent = await create_cli_agent(
                name="CustomCLI",
                system_prompt="Custom prompt"
            )
            
            assert agent.name == "CustomCLI"
            assert "Custom prompt" in agent.system_prompt


@pytest.mark.integration
class TestCLIAgentIntegration:
    """Integration tests for CLI Agent."""

    @pytest.mark.asyncio
    async def test_real_file_operations(self):
        """Test with real file operations."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create mock router
            mock_router = Mock(spec=LLMRouter)
            mock_router.generate = AsyncMock(return_value=LLMResponse(
                content="File operations completed",
                model="test",
                usage={},
                metadata={}
            ))
            
            agent = CLIAgent(name="TestAgent", llm_router=mock_router)
            
            # Test file operations if tools are available
            test_file = temp_path / "test.txt"
            test_content = "Hello, CLI Agent!"
            
            # Write file
            if hasattr(agent, 'write_file'):
                result = await agent.write_file(str(test_file), test_content)
                if result.get("success"):
                    assert test_file.exists()
                    assert test_file.read_text() == test_content

    @pytest.mark.asyncio
    async def test_command_execution_workflow(self):
        """Test complete command execution workflow."""
        mock_router = Mock(spec=LLMRouter)
        mock_router.generate = AsyncMock(return_value=LLMResponse(
            content="Commands executed successfully",
            model="test",
            usage={},
            metadata={}
        ))
        
        agent = CLIAgent(name="WorkflowAgent", llm_router=mock_router)
        
        # Test workflow: request -> command execution -> response
        request = "Show me the current directory contents"
        
        response = await agent.process_request(request)
        
        assert isinstance(response, LLMResponse)
        assert response.content is not None

    @pytest.mark.asyncio
    async def test_error_recovery(self):
        """Test error recovery in CLI agent."""
        mock_router = Mock(spec=LLMRouter)
        mock_router.generate = AsyncMock(return_value=LLMResponse(
            content="Error handled gracefully",
            model="test",
            usage={},
            metadata={}
        ))
        
        agent = CLIAgent(name="ErrorAgent", llm_router=mock_router)
        
        # Test with invalid command
        if hasattr(agent, 'execute_command'):
            result = await agent.execute_command("invalid_command_xyz")
            # Should handle gracefully without crashing
            assert isinstance(result, dict)
