"""
Comprehensive tests for the revolutionary multi-agent communication system.
Tests all components: communication hub, consensus algorithms, virtual agents.
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch
from typing import List, Dict, Any

from src.agent_swarm.core.multi_agent_communication import (
    MultiAgentCommunicationHub,
    Message,
    MessageType,
    AgentRole,
    AgentProfile,
    ConsensusProposal,
    CommunicationTopology,
    CommunicationProtocol,
    AttentionMechanism,
    WeightedVotingConsensus,
    ByzantineFaultTolerantConsensus,
    get_communication_hub,
    initialize_communication_hub
)


class TestMessage:
    """Test Message class functionality."""
    
    def test_message_creation(self):
        """Test message creation with default values."""
        message = Message(
            sender_id="agent_1",
            receiver_id="agent_2",
            message_type=MessageType.QUERY,
            content="Test message"
        )
        
        assert message.sender_id == "agent_1"
        assert message.receiver_id == "agent_2"
        assert message.message_type == MessageType.QUERY
        assert message.content == "Test message"
        assert message.priority == 1
        assert not message.requires_response
        assert message.id is not None
        assert message.timestamp > 0
    
    def test_message_expiration(self):
        """Test message TTL and expiration."""
        # Message with short TTL
        message = Message(
            sender_id="agent_1",
            content="Test",
            ttl=0.1  # 0.1 seconds
        )
        
        assert not message.is_expired
        
        # Wait for expiration
        time.sleep(0.2)
        assert message.is_expired
    
    def test_message_age(self):
        """Test message age calculation."""
        message = Message(sender_id="agent_1", content="Test")
        
        # Age should be very small initially
        assert message.age < 0.1
        
        time.sleep(0.1)
        assert message.age >= 0.1


class TestAgentProfile:
    """Test AgentProfile functionality."""
    
    def test_agent_profile_creation(self):
        """Test agent profile creation."""
        profile = AgentProfile(
            id="test_agent",
            role=AgentRole.SPECIALIST,
            expertise_domains=["technology", "algorithms"]
        )
        
        assert profile.id == "test_agent"
        assert profile.role == AgentRole.SPECIALIST
        assert profile.expertise_domains == ["technology", "algorithms"]
        assert profile.trust_score == 1.0
        assert profile.is_active
        assert profile.average_performance == 0.5  # Default when no history
    
    def test_performance_tracking(self):
        """Test performance history tracking."""
        profile = AgentProfile(
            id="test_agent",
            role=AgentRole.SPECIALIST,
            expertise_domains=["test"]
        )
        
        # Add performance scores
        profile.update_performance(0.8)
        profile.update_performance(0.9)
        profile.update_performance(0.7)
        
        assert len(profile.performance_history) == 3
        assert profile.average_performance == 0.8  # (0.8 + 0.9 + 0.7) / 3
    
    def test_performance_history_limit(self):
        """Test performance history size limit."""
        profile = AgentProfile(
            id="test_agent",
            role=AgentRole.SPECIALIST,
            expertise_domains=["test"]
        )
        
        # Add more than 100 scores
        for i in range(150):
            profile.update_performance(0.5)
        
        # Should be limited to 100
        assert len(profile.performance_history) == 100


class TestCommunicationTopology:
    """Test communication topology management."""
    
    def test_mesh_topology(self):
        """Test mesh topology creation."""
        topology = CommunicationTopology(CommunicationProtocol.MESH)
        
        # Add agents
        agents = ["agent_1", "agent_2", "agent_3"]
        for agent in agents:
            topology.add_agent(agent)
        
        # In mesh topology, all agents should be connected
        for agent in agents:
            neighbors = topology.get_neighbors(agent)
            expected_neighbors = set(agents) - {agent}
            assert neighbors == expected_neighbors
    
    def test_ring_topology(self):
        """Test ring topology creation."""
        topology = CommunicationTopology(CommunicationProtocol.RING)
        
        # Add agents
        agents = ["agent_1", "agent_2", "agent_3"]
        for agent in agents:
            topology.add_agent(agent)
        
        # Each agent should have exactly 2 neighbors in ring
        for agent in agents:
            neighbors = topology.get_neighbors(agent)
            assert len(neighbors) == 2
    
    def test_star_topology(self):
        """Test star topology creation."""
        topology = CommunicationTopology(CommunicationProtocol.STAR)
        
        # Add agents
        agents = ["center", "agent_1", "agent_2", "agent_3"]
        for agent in agents:
            topology.add_agent(agent)
        
        # Center should be connected to all others
        center_neighbors = topology.get_neighbors("center")
        assert len(center_neighbors) == 3
        
        # Other agents should only be connected to center
        for agent in agents[1:]:
            neighbors = topology.get_neighbors(agent)
            assert neighbors == {"center"}
    
    def test_path_finding(self):
        """Test shortest path finding."""
        topology = CommunicationTopology(CommunicationProtocol.RING)
        
        agents = ["agent_1", "agent_2", "agent_3", "agent_4"]
        for agent in agents:
            topology.add_agent(agent)
        
        # Find path from agent_1 to agent_3
        path = topology.find_path("agent_1", "agent_3")
        assert len(path) >= 2  # Should find a path
        assert path[0] == "agent_1"
        assert path[-1] == "agent_3"


class TestAttentionMechanism:
    """Test attention mechanism for message routing."""
    
    def test_agent_embedding_update(self):
        """Test agent embedding updates."""
        attention = AttentionMechanism(embedding_dim=64)
        
        features = {
            'trust_score': 0.8,
            'performance': 0.9,
            'expertise_count': 3
        }
        
        attention.update_agent_embedding("agent_1", features)
        
        assert "agent_1" in attention.agent_embeddings
        assert attention.agent_embeddings["agent_1"].shape == (64,)
    
    def test_attention_score_computation(self):
        """Test attention score computation."""
        attention = AttentionMechanism(embedding_dim=64)
        
        # Update embeddings for test agents
        for i in range(3):
            features = {'score': i * 0.3}
            attention.update_agent_embedding(f"agent_{i}", features)
        
        # Create test message
        message = Message(sender_id="sender", content="test")
        candidates = ["agent_0", "agent_1", "agent_2"]
        
        scores = attention.compute_attention_scores(message, candidates)
        
        assert len(scores) == 3
        assert all(0 <= score <= 1 for score in scores.values())
        assert abs(sum(scores.values()) - 1.0) < 0.01  # Should sum to ~1 (softmax)


class TestConsensusAlgorithms:
    """Test consensus algorithms."""
    
    @pytest.fixture
    def sample_agents(self):
        """Create sample agents for testing."""
        return {
            "agent_1": AgentProfile("agent_1", AgentRole.SPECIALIST, ["tech"]),
            "agent_2": AgentProfile("agent_2", AgentRole.CRITIC, ["security"]),
            "agent_3": AgentProfile("agent_3", AgentRole.VALIDATOR, ["quality"])
        }
    
    @pytest.fixture
    def sample_proposal(self):
        """Create sample proposal for testing."""
        proposal = ConsensusProposal(
            proposer_id="agent_1",
            content="Test proposal"
        )
        proposal.votes = {
            "agent_1": 0.8,
            "agent_2": 0.6,
            "agent_3": 0.9
        }
        return proposal
    
    @pytest.mark.asyncio
    async def test_weighted_voting_consensus(self, sample_agents, sample_proposal):
        """Test weighted voting consensus algorithm."""
        algorithm = WeightedVotingConsensus(min_participation=0.5)
        
        result = await algorithm.reach_consensus(
            [sample_proposal], 
            sample_agents, 
            timeout=5.0
        )
        
        assert result is not None
        assert result.id == sample_proposal.id
    
    @pytest.mark.asyncio
    async def test_weighted_voting_insufficient_participation(self, sample_agents):
        """Test weighted voting with insufficient participation."""
        algorithm = WeightedVotingConsensus(min_participation=0.8)
        
        # Proposal with low participation
        proposal = ConsensusProposal(proposer_id="agent_1", content="Test")
        proposal.votes = {"agent_1": 0.8}  # Only 1 out of 3 agents
        
        result = await algorithm.reach_consensus([proposal], sample_agents)
        
        assert result is None  # Should fail due to low participation
    
    @pytest.mark.asyncio
    async def test_byzantine_fault_tolerant_consensus(self, sample_agents, sample_proposal):
        """Test Byzantine fault tolerant consensus."""
        algorithm = ByzantineFaultTolerantConsensus(fault_tolerance=0.33)
        
        # Ensure strong votes (> 0.7) for Byzantine consensus
        sample_proposal.votes = {
            "agent_1": 0.8,
            "agent_2": 0.9,
            "agent_3": 0.85
        }
        
        result = await algorithm.reach_consensus([sample_proposal], sample_agents)
        
        assert result is not None
        assert result.id == sample_proposal.id


class TestMultiAgentCommunicationHub:
    """Test the main communication hub."""
    
    @pytest.fixture
    async def communication_hub(self):
        """Create and initialize communication hub."""
        hub = MultiAgentCommunicationHub()
        await hub.initialize()
        yield hub
        await hub.shutdown()
    
    @pytest.mark.asyncio
    async def test_agent_registration(self, communication_hub):
        """Test agent registration and unregistration."""
        hub = communication_hub
        
        # Register agent
        hub.register_agent(
            "test_agent",
            AgentRole.SPECIALIST,
            ["technology", "algorithms"]
        )
        
        assert "test_agent" in hub.agents
        assert hub.agents["test_agent"].role == AgentRole.SPECIALIST
        assert "test_agent" in hub.topology.connections
        
        # Unregister agent
        hub.unregister_agent("test_agent")
        assert "test_agent" not in hub.agents
        assert "test_agent" not in hub.topology.connections
    
    @pytest.mark.asyncio
    async def test_message_sending(self, communication_hub):
        """Test message sending and processing."""
        hub = communication_hub
        
        # Register agents
        hub.register_agent("sender", AgentRole.COORDINATOR, ["general"])
        hub.register_agent("receiver", AgentRole.SPECIALIST, ["tech"])
        
        # Send message
        message = Message(
            sender_id="sender",
            receiver_id="receiver",
            message_type=MessageType.QUERY,
            content="Test query"
        )
        
        success = await hub.send_message(message)
        assert success
        assert hub.stats['messages_sent'] == 1
        
        # Wait for processing
        await asyncio.sleep(0.1)
        assert hub.stats['messages_received'] >= 1
    
    @pytest.mark.asyncio
    async def test_broadcast_message(self, communication_hub):
        """Test broadcast messaging."""
        hub = communication_hub
        
        # Register multiple agents
        for i in range(3):
            hub.register_agent(f"agent_{i}", AgentRole.SPECIALIST, ["tech"])
        
        # Broadcast message
        message_id = await hub.broadcast_message(
            "agent_0",
            "Broadcast test",
            MessageType.QUERY
        )
        
        assert message_id is not None
        assert hub.stats['messages_sent'] == 1
    
    @pytest.mark.asyncio
    async def test_expert_messaging(self, communication_hub):
        """Test sending messages to experts."""
        hub = communication_hub
        
        # Register agents with different expertise
        hub.register_agent("tech_expert", AgentRole.SPECIALIST, ["technology"])
        hub.register_agent("business_expert", AgentRole.SPECIALIST, ["business"])
        hub.register_agent("generalist", AgentRole.COORDINATOR, ["general"])
        
        # Send to technology experts
        message_ids = await hub.send_to_experts(
            "generalist",
            "Technical question",
            "technology",
            MessageType.QUERY
        )
        
        assert len(message_ids) == 1  # Only tech_expert should receive
        assert hub.stats['messages_sent'] == 1
    
    @pytest.mark.asyncio
    async def test_consensus_initiation(self, communication_hub):
        """Test consensus process initiation."""
        hub = communication_hub
        
        # Register agents
        for i in range(3):
            hub.register_agent(f"agent_{i}", AgentRole.SPECIALIST, ["tech"])
        
        # Initiate consensus
        proposal_id = await hub.initiate_consensus(
            "agent_0",
            "Should we implement feature X?",
            algorithm="weighted_voting",
            timeout=10.0
        )
        
        assert proposal_id is not None
        assert proposal_id in hub.pending_proposals
        assert hub.stats['messages_sent'] == 1  # Broadcast proposal
    
    @pytest.mark.asyncio
    async def test_voting_process(self, communication_hub):
        """Test voting on proposals."""
        hub = communication_hub
        
        # Register agents
        for i in range(3):
            hub.register_agent(f"agent_{i}", AgentRole.SPECIALIST, ["tech"])
        
        # Initiate consensus
        proposal_id = await hub.initiate_consensus(
            "agent_0",
            "Test proposal"
        )
        
        # Agents vote
        assert await hub.vote_on_proposal("agent_1", proposal_id, 0.8)
        assert await hub.vote_on_proposal("agent_2", proposal_id, 0.9)
        
        # Check votes recorded
        proposal = hub.pending_proposals[proposal_id]
        assert len(proposal.votes) == 2
        assert proposal.votes["agent_1"] == 0.8
        assert proposal.votes["agent_2"] == 0.9
    
    @pytest.mark.asyncio
    async def test_metrics_collection(self, communication_hub):
        """Test metrics collection."""
        hub = communication_hub
        
        # Register agent and send messages
        hub.register_agent("test_agent", AgentRole.SPECIALIST, ["tech"])
        
        message = Message(sender_id="test_agent", content="Test")
        await hub.send_message(message)
        
        # Get metrics
        metrics = hub.get_metrics()
        
        assert 'agents_registered' in metrics
        assert 'messages_sent' in metrics
        assert 'messages_received' in metrics
        assert metrics['agents_registered'] == 1
        assert metrics['messages_sent'] >= 1
    
    @pytest.mark.asyncio
    async def test_event_emission(self, communication_hub):
        """Test event emission during operations."""
        hub = communication_hub
        
        # Track events
        events_received = []
        
        def event_handler(data):
            events_received.append(data)
        
        hub.add_listener('agent.registered', event_handler)
        
        # Register agent (should emit event)
        hub.register_agent("test_agent", AgentRole.SPECIALIST, ["tech"])
        
        # Wait for event processing
        await asyncio.sleep(0.1)
        
        assert len(events_received) == 1
        assert events_received[0]['agent_id'] == "test_agent"


class TestGlobalFunctions:
    """Test global utility functions."""
    
    @pytest.mark.asyncio
    async def test_global_communication_hub(self):
        """Test global communication hub access."""
        # Get global hub
        hub1 = get_communication_hub()
        hub2 = get_communication_hub()
        
        # Should be same instance (singleton)
        assert hub1 is hub2
        
        # Initialize global hub
        initialized_hub = await initialize_communication_hub()
        assert initialized_hub is hub1
        
        # Cleanup
        await initialized_hub.shutdown()


@pytest.mark.integration
class TestIntegrationScenarios:
    """Integration tests for complex scenarios."""
    
    @pytest.mark.asyncio
    async def test_full_consensus_workflow(self):
        """Test complete consensus workflow."""
        hub = MultiAgentCommunicationHub()
        await hub.initialize()
        
        try:
            # Register diverse agents
            agents = [
                ("coordinator", AgentRole.COORDINATOR, ["leadership"]),
                ("tech_specialist", AgentRole.SPECIALIST, ["technology"]),
                ("security_critic", AgentRole.CRITIC, ["security"]),
                ("quality_validator", AgentRole.VALIDATOR, ["quality"])
            ]
            
            for agent_id, role, expertise in agents:
                hub.register_agent(agent_id, role, expertise)
            
            # Initiate consensus
            proposal_id = await hub.initiate_consensus(
                "coordinator",
                "Should we adopt new technology stack?",
                timeout=5.0
            )
            
            # All agents vote
            votes = [
                ("tech_specialist", 0.9),
                ("security_critic", 0.6),
                ("quality_validator", 0.8)
            ]
            
            for agent_id, vote in votes:
                await hub.vote_on_proposal(agent_id, proposal_id, vote)
            
            # Wait for consensus processing
            await asyncio.sleep(1.0)
            
            # Check if consensus was reached
            metrics = hub.get_metrics()
            assert metrics['consensus_reached'] >= 0  # Should have attempted consensus
            
        finally:
            await hub.shutdown()
    
    @pytest.mark.asyncio
    async def test_conflict_scenario(self):
        """Test scenario with conflicting agent opinions."""
        hub = MultiAgentCommunicationHub()
        await hub.initialize()
        
        try:
            # Register agents with opposing views
            hub.register_agent("optimist", AgentRole.SPECIALIST, ["tech"])
            hub.register_agent("pessimist", AgentRole.CRITIC, ["security"])
            hub.register_agent("mediator", AgentRole.MEDIATOR, ["general"])
            
            # Initiate consensus
            proposal_id = await hub.initiate_consensus(
                "mediator",
                "High-risk technical decision"
            )
            
            # Conflicting votes
            await hub.vote_on_proposal("optimist", proposal_id, 0.9)
            await hub.vote_on_proposal("pessimist", proposal_id, 0.2)
            await hub.vote_on_proposal("mediator", proposal_id, 0.6)
            
            # Wait for processing
            await asyncio.sleep(1.0)
            
            # Should handle conflict gracefully
            metrics = hub.get_metrics()
            assert metrics['messages_sent'] >= 1
            
        finally:
            await hub.shutdown()


if __name__ == "__main__":
    # Run tests with verbose output
    pytest.main([__file__, "-v", "--tb=short"])