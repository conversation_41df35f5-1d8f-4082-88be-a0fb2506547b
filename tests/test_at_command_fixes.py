"""
Unit and integration tests for @ command fixes.
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import Mock, patch

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm.cli.autocomplete import AdvancedCompleter
from agent_swarm.cli.context_manager import ContextManager


class TestAtCommandAutocomplete:
    """Test @ command autocomplete functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.project_path = Path(__file__).parent.parent
        self.completer = AdvancedCompleter()
        self.completer.project_path = self.project_path
    
    def test_at_command_detection(self):
        """Test @ command detection in various contexts."""
        test_cases = [
            # (line, text, expected)
            ("explain @README", "README", True),
            ("show me @src/", "src/", True),
            ("analyze @*.py", "*.py", True),
            ("@pyproject", "pyproject", True),
            ("regular text", "text", False),
            ("/edit file.py", "file.py", False),
            ("explain @", "", True),
            ("@", "", True),
        ]
        
        for line, text, expected in test_cases:
            result = self.completer._is_at_command_context(line, text)
            assert result == expected, f"Failed for '{line}' with text '{text}'"
    
    def test_at_command_completion(self):
        """Test @ command completion generation."""
        # Test README completion
        completions = self.completer._complete_at_command("explain @README", "README")
        
        assert len(completions) > 0, "Should find README completions"
        
        # Check that completions have @ prefix
        for comp in completions:
            assert comp.text.startswith("@"), f"Completion should start with @: {comp.text}"
            assert comp.type == "at_command", f"Should be at_command type: {comp.type}"
    
    def test_full_completion_integration(self):
        """Test full completion pipeline with @ commands."""
        test_cases = [
            "explain @README",
            "show me @src/",
            "@pyproject",
        ]
        
        for line in test_cases:
            text = line.split()[-1] if line.split() else line
            completions = self.completer.get_completions(line, text)
            
            # Should return at_command completions
            at_completions = [c for c in completions if c.type == "at_command"]
            assert len(at_completions) > 0, f"Should find @ completions for: {line}"
    
    @patch('readline.get_completer_delims')
    def test_readline_delimiters(self, mock_get_delims):
        """Test that @ is not in readline delimiters."""
        # Simulate the fixed delimiters
        mock_get_delims.return_value = ' \t\n`!#$%^&*()=+[{]}\\|;:\'",<>?'
        
        delims = mock_get_delims()
        assert '@' not in delims, "@ should not be in readline delimiters"
    
    def test_case_sensitive_completion(self):
        """Test case-sensitive file completion."""
        # Test that README (uppercase) finds README.md
        completions = self.completer._complete_at_command("@README", "README")
        
        readme_found = any("README.md" in comp.text for comp in completions)
        assert readme_found, "Should find README.md for @README"


class TestContextManager:
    """Test context manager @ command processing."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.project_path = Path(__file__).parent.parent
        self.context_manager = ContextManager(self.project_path)
    
    def test_at_command_parsing(self):
        """Test @ command parsing from text."""
        test_cases = [
            ("explain @README.md", (["README.md"], "explain")),
            ("show @src/ and @pyproject.toml", (["src/", "pyproject.toml"], "show and")),
            ("analyze @*.py files", (["*.py"], "analyze files")),
            ("no at commands here", ([], "no at commands here")),
        ]
        
        for text, (expected_commands, expected_cleaned) in test_cases:
            cleaned, commands = self.context_manager.parse_at_commands(text)
            
            assert commands == expected_commands, f"Commands mismatch for '{text}'"
            assert cleaned == expected_cleaned, f"Cleaned text mismatch for '{text}'"
    
    def test_file_context_processing(self):
        """Test processing @ commands for file context."""
        # Test with README.md (should exist)
        commands = ["README.md"]
        items = self.context_manager.process_at_commands(commands)
        
        assert len(items) == 1, "Should process one item"
        assert items[0].path.name == "README.md", "Should find README.md"
        assert items[0].error is None, "Should not have error for existing file"
    
    def test_missing_file_handling(self):
        """Test handling of missing files."""
        # Test with non-existent file
        commands = ["NonExistentFile.txt"]
        items = self.context_manager.process_at_commands(commands)
        
        assert len(items) == 1, "Should process one item"
        assert items[0].error is not None, "Should have error for missing file"
        assert "File not found" in items[0].error, "Should indicate file not found"
    
    def test_context_for_llm(self):
        """Test context generation for LLM."""
        # Add a file to context
        commands = ["README.md"]
        self.context_manager.process_at_commands(commands)
        
        # Get context for LLM
        llm_context = self.context_manager.get_context_for_llm()
        
        assert len(llm_context) > 0, "Should generate LLM context"
        assert "README.md" in llm_context, "Should include filename in context"
        assert "CONTEXT FILES" in llm_context, "Should have context header"


class TestShellIntegration:
    """Test shell integration with @ commands."""
    
    @patch('agent_swarm.cli.interactive_shell.AgentSwarmShell')
    def test_advanced_features_initialization(self, mock_shell):
        """Test that advanced features are properly initialized."""
        from agent_swarm.cli.interactive_shell import AgentSwarmShell
        
        # Create shell instance
        shell = AgentSwarmShell(verbose=True, auto_index=False)
        shell.project_path = Path(__file__).parent.parent
        
        # Initialize advanced features
        shell._initialize_advanced_features()
        
        # Check that components are initialized
        assert hasattr(shell, 'context_manager'), "Should have context_manager"
        assert hasattr(shell, 'autocompleter'), "Should have autocompleter"
        assert shell.context_manager is not None, "Context manager should be initialized"
        assert shell.autocompleter is not None, "Autocompleter should be initialized"
    
    def test_multi_step_orchestrator_disabled(self):
        """Test that multi-step orchestrator is disabled."""
        # Read the shell file and check for disabled condition
        shell_file = Path(__file__).parent.parent / "src" / "agent_swarm" / "cli" / "interactive_shell.py"
        content = shell_file.read_text()
        
        assert "if False and self.orchestrator:" in content, "Multi-step should be disabled"
    
    def test_input_method_uses_readline(self):
        """Test that shell uses regular input() for readline support."""
        shell_file = Path(__file__).parent.parent / "src" / "agent_swarm" / "cli" / "interactive_shell.py"
        content = shell_file.read_text()
        
        assert "user_input = input(prompt).strip()" in content, "Should use regular input()"


class TestContextWeighting:
    """Test context weighting and response quality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.project_path = Path(__file__).parent.parent
        self.context_manager = ContextManager(self.project_path)
    
    def test_empty_input_handling(self):
        """Test handling of empty input with @ commands."""
        # Test just "@README.md" without additional text
        cleaned, commands = self.context_manager.parse_at_commands("@README.md")
        
        assert cleaned == "", "Cleaned text should be empty for just @ command"
        assert commands == ["README.md"], "Should extract the @ command"
    
    def test_context_size_limits(self):
        """Test that context has reasonable size limits."""
        # Add a large file to context
        commands = ["README.md"]
        items = self.context_manager.process_at_commands(commands)
        
        if items and not items[0].error:
            llm_context = self.context_manager.get_context_for_llm()
            
            # Context should be reasonable size (not too large)
            assert len(llm_context) < 50000, "Context should not be too large"
            assert len(llm_context) > 100, "Context should have meaningful content"


@pytest.mark.integration
class TestEndToEndAtCommands:
    """End-to-end integration tests for @ commands."""
    
    def test_complete_at_command_workflow(self):
        """Test complete @ command workflow from input to context."""
        project_path = Path(__file__).parent.parent
        
        # 1. Initialize components
        completer = AdvancedCompleter()
        completer.project_path = project_path
        
        context_manager = ContextManager(project_path)
        
        # 2. Test autocomplete
        completions = completer.get_completions("explain @README", "README")
        assert len(completions) > 0, "Should get completions"
        
        # 3. Test context processing
        cleaned, commands = context_manager.parse_at_commands("explain @README.md")
        assert commands == ["README.md"], "Should parse @ command"
        assert cleaned == "explain", "Should clean text"
        
        # 4. Test context generation
        items = context_manager.process_at_commands(commands)
        assert len(items) > 0, "Should process items"
        
        llm_context = context_manager.get_context_for_llm()
        assert "README.md" in llm_context, "Should include file in context"
    
    def test_multiple_at_commands(self):
        """Test handling multiple @ commands in one input."""
        context_manager = ContextManager(Path(__file__).parent.parent)
        
        # Test multiple @ commands
        text = "compare @README.md and @pyproject.toml"
        cleaned, commands = context_manager.parse_at_commands(text)
        
        assert len(commands) == 2, "Should find two @ commands"
        assert "README.md" in commands, "Should find README.md"
        assert "pyproject.toml" in commands, "Should find pyproject.toml"
        assert cleaned == "compare and", "Should clean text properly"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
