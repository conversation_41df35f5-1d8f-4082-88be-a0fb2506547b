#!/usr/bin/env python3
"""
Test @ commands functionality in the shell.
"""

import sys
import asyncio
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

async def test_at_commands_integration():
    """Test @ commands integration with shell."""
    print("🧪 Testing @ Commands Integration...")
    
    try:
        from agent_swarm.cli.interactive_shell import InteractiveShell
        
        # Create shell with verbose mode
        shell = InteractiveShell(verbose=True, auto_index=False)
        
        # Set project path
        shell.project_path = Path("/home/<USER>/workspace/local-llm/agent-mode")
        
        # Initialize advanced features
        shell._initialize_advanced_features()
        
        # Check if context manager is available
        has_context_manager = hasattr(shell, 'context_manager') and shell.context_manager is not None
        print(f"   Context manager available: {has_context_manager}")
        
        if has_context_manager:
            print(f"   Context manager path: {shell.context_manager.project_path}")
            
            # Test @ command parsing
            test_inputs = [
                "explain @README.md",
                "show me @src/agent_swarm/__init__.py and @pyproject.toml",
                "analyze @*.py files in the project"
            ]
            
            for test_input in test_inputs:
                print(f"\\n   Testing: '{test_input}'")
                
                # Parse @ commands
                cleaned, commands = shell.context_manager.parse_at_commands(test_input)
                print(f"     Cleaned: '{cleaned}'")
                print(f"     Commands: {commands}")
                
                # Process @ commands
                if commands:
                    items = shell.context_manager.process_at_commands(commands)
                    print(f"     Processed {len(items)} items")
                    
                    # Get context for LLM
                    llm_context = shell.context_manager.get_context_for_llm()
                    print(f"     LLM context length: {len(llm_context)} characters")
                    
                    # Show first few lines of context
                    lines = llm_context.split('\\n')[:5]
                    print(f"     Context preview: {lines[0][:50]}...")
        
        print("✅ @ Commands integration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ @ Commands integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_shell_chat_with_at_commands():
    """Test shell chat handling with @ commands."""
    print("\\n🧪 Testing Shell Chat with @ Commands...")
    
    try:
        from agent_swarm.cli.interactive_shell import InteractiveShell
        
        # Create shell
        shell = InteractiveShell(verbose=True, auto_index=False)
        shell.project_path = Path("/home/<USER>/workspace/local-llm/agent-mode")
        
        # Initialize features
        shell._initialize_advanced_features()
        
        # Mock the agent to avoid actual LLM calls
        class MockAgent:
            def __init__(self):
                self.llm_router = None
            
            async def chat(self, prompt, **kwargs):
                return {
                    "response": f"Mock response for prompt: {prompt[:100]}...",
                    "usage": {"total_tokens": 100}
                }
        
        shell.agent = MockAgent()
        
        # Test chat with @ commands
        test_query = "explain @README.md and tell me about the project"
        
        print(f"   Testing chat with: '{test_query}'")
        
        # This should process @ commands and call the agent
        # We'll capture the processing by checking context manager state
        
        # Manually test the @ command processing part
        if shell.context_manager and '@' in test_query:
            cleaned_input, at_commands = shell.context_manager.parse_at_commands(test_query)
            print(f"     @ commands found: {at_commands}")
            print(f"     Cleaned input: '{cleaned_input}'")
            
            if at_commands:
                context_items = shell.context_manager.process_at_commands(at_commands)
                print(f"     Context items: {len(context_items)}")
                
                # Check if context is available for LLM
                llm_context = shell.context_manager.get_context_for_llm()
                has_readme_content = "README" in llm_context or "Agent Swarm" in llm_context
                print(f"     README content in context: {has_readme_content}")
        
        print("✅ Shell chat with @ commands test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Shell chat with @ commands test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_autocomplete_integration():
    """Test autocomplete integration."""
    print("\\n🧪 Testing Autocomplete Integration...")
    
    try:
        from agent_swarm.cli.interactive_shell import InteractiveShell
        
        shell = InteractiveShell(verbose=False, auto_index=False)
        shell.project_path = Path("/home/<USER>/workspace/local-llm/agent-mode")
        shell._initialize_advanced_features()
        
        # Check if autocompleter is available
        has_autocompleter = hasattr(shell, 'autocompleter') and shell.autocompleter is not None
        print(f"   Autocompleter available: {has_autocompleter}")
        
        if has_autocompleter:
            # Test path suggestions for @ commands
            suggestions = shell.autocompleter.get_path_suggestions("README", "@")
            print(f"   Path suggestions for 'README': {suggestions}")
            
            # Test file completion
            from agent_swarm.cli.autocomplete import CompletionSuggestion
            test_completions = shell.autocompleter._complete_file_path("README")
            print(f"   File completions for 'README': {len(test_completions)}")
        
        print("✅ Autocomplete integration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Autocomplete integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all @ commands tests."""
    print("🚀 Testing @ Commands System Integration")
    print("=" * 60)
    
    tests = [
        test_at_commands_integration,
        test_shell_chat_with_at_commands,
        test_autocomplete_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if await test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    print("\\n" + "=" * 60)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 @ Commands system fully integrated!")
        print("\\n💡 Features working:")
        print("   ✅ @ command parsing and processing")
        print("   ✅ File context extraction")
        print("   ✅ LLM context integration")
        print("   ✅ Shell integration")
        print("   ✅ Autocomplete support")
        print("\\n🔮 Try these @ commands:")
        print("   • 'explain @README.md'")
        print("   • 'analyze @src/agent_swarm/__init__.py'")
        print("   • 'review @*.py files'")
        print("   • 'show me @pyproject.toml and @requirements.txt'")
        return True
    else:
        print(f"❌ {total - passed} features need work")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
