#!/usr/bin/env python3
"""
Test context enhancement functionality.
"""

import sys
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_context_enhancement():
    """Test that context enhancement works."""
    print("🧪 Testing Context Enhancement...")

    try:
        from agent_swarm.cli.interactive_shell import InteractiveShell
        try:
            from agent_swarm.backends.base import Message
        except ImportError:
            from agent_swarm.backends import Message

        # Create shell
        shell = InteractiveShell(verbose=True, auto_index=False)

        # Simulate conversation history
        shell.conversation_history = [
            Message(role="user", content="find async def functions"),
            Message(role="assistant", content="Here are the async def functions found: search_code (async function) - located in docs/guides/RAG_DEVELOPMENT.md"),
        ]

        # Test context-aware query building
        user_input = "explain search_code"
        enhanced_query = shell._build_context_aware_query(user_input)

        print(f"   Original query: '{user_input}'")
        print(f"   Enhanced query: '{enhanced_query[:100]}...'")

        # Check that context was added
        assert "search_code" in enhanced_query.lower()
        assert "conversation context" in enhanced_query.lower() or "previous" in enhanced_query.lower()
        assert len(enhanced_query) > len(user_input)

        print("✅ Context enhancement works!")
        return True

    except ImportError as e:
        print(f"❌ Context enhancement not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Context enhancement failed: {e}")
        return False

def test_empty_history():
    """Test behavior with empty conversation history."""
    print("\n🧪 Testing Empty History...")

    try:
        from agent_swarm.cli.interactive_shell import InteractiveShell

        shell = InteractiveShell(verbose=False, auto_index=False)

        # Empty history
        shell.conversation_history = []

        user_input = "search for functions"
        enhanced_query = shell._build_context_aware_query(user_input)

        print(f"   Original query: '{user_input}'")
        print(f"   Enhanced query: '{enhanced_query}'")

        # Should return original query when no history
        assert enhanced_query == user_input

        print("✅ Empty history handling works!")
        return True

    except Exception as e:
        print(f"❌ Empty history test failed: {e}")
        return False

def test_agent_routing_with_context():
    """Test that agent routing works with enhanced context."""
    print("\n🧪 Testing Agent Routing with Context...")

    try:
        from agent_swarm.core.intent_detector import IntentDetector
        # Skip router test if not available
        try:
            from agent_swarm.core.agent_router import AgentRouter
        except ImportError:
            print("   ⚠️ AgentRouter not available, testing intent detection only")

        # Test with context-enhanced query
        original_query = "explain search_code"
        enhanced_query = """explain search_code

Conversation context: Previous question: find async def functions | Previous context: Here are the async def functions found... search_code (async function)"""

        detector = IntentDetector()

        # Test original query
        original_intent = detector.detect_intent(original_query)

        # Test enhanced query
        enhanced_intent = detector.detect_intent(enhanced_query)

        print(f"   Original: {original_intent.intent_type.value}")
        print(f"   Enhanced: {enhanced_intent.intent_type.value}")

        # Both should detect EXPLAIN intent
        assert original_intent.intent_type.value == "explain_code"
        assert enhanced_intent.intent_type.value == "explain_code"

        print("✅ Agent routing with context works!")
        return True

    except ImportError as e:
        print(f"❌ Agent routing test not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Agent routing test failed: {e}")
        return False

def test_context_extraction():
    """Test context extraction from conversation history."""
    print("\n🧪 Testing Context Extraction...")

    try:
        from agent_swarm.cli.interactive_shell import InteractiveShell
        try:
            from agent_swarm.backends.base import Message
        except ImportError:
            from agent_swarm.backends import Message

        shell = InteractiveShell(verbose=False, auto_index=False)

        # Rich conversation history
        shell.conversation_history = [
            Message(role="user", content="search for async functions"),
            Message(role="assistant", content="Found search_code function in main.py"),
            Message(role="user", content="show me the main.py file"),
            Message(role="assistant", content="Here's main.py content with async def search_code..."),
            Message(role="user", content="explain that function"),
        ]

        # Test context extraction
        enhanced_query = shell._build_context_aware_query("explain that function")

        print(f"   Query: 'explain that function'")
        print(f"   Context extracted: {len(enhanced_query) > 20}")

        # Should include context about search_code and main.py
        context_relevant = (
            "search_code" in enhanced_query.lower() or
            "main.py" in enhanced_query.lower() or
            "async" in enhanced_query.lower()
        )

        assert context_relevant, "Context should include relevant information"

        print("✅ Context extraction works!")
        return True

    except Exception as e:
        print(f"❌ Context extraction failed: {e}")
        return False

def main():
    """Run all context enhancement tests."""
    print("🚀 Testing Context Enhancement System")
    print("=" * 50)

    tests = [
        test_context_enhancement,
        test_empty_history,
        test_agent_routing_with_context,
        test_context_extraction,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed: {e}")

    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 Context enhancement system working!")
        print("\n💡 Key improvements:")
        print("   ✅ Chat history context preservation")
        print("   ✅ Entity reference resolution")
        print("   ✅ Enhanced query building")
        print("   ✅ Agent routing with context")
        print("\n🔮 Expected behavior:")
        print("   • 'find async functions' → finds search_code")
        print("   • 'explain search_code' → knows what search_code is")
        print("   • 'edit that file' → knows which file")
        return True
    else:
        print(f"❌ {total - passed} tests need work")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
