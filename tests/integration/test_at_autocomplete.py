#!/usr/bin/env python3
"""
Test @ command autocomplete functionality.
"""

import sys
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_at_command_detection():
    """Test @ command detection in autocomplete."""
    print("🧪 Testing @ Command Detection...")
    
    try:
        from agent_swarm.cli.autocomplete import AdvancedCompleter
        
        completer = AdvancedCompleter()
        completer.project_path = Path("/home/<USER>/workspace/local-llm/agent-mode")
        
        # Test cases for @ command detection
        test_cases = [
            ("explain @README", "README", True),
            ("show me @src/", "src/", True),
            ("analyze @*.py", "*.py", True),
            ("@pyproject", "pyproject", True),
            ("regular text", "text", False),
            ("/edit file.py", "file.py", False),
            ("explain @", "", True),
        ]
        
        for line, text, expected in test_cases:
            result = completer._is_at_command_context(line, text)
            status = "✅" if result == expected else "❌"
            print(f"   {status} '{line}' with text '{text}' -> {result} (expected {expected})")
        
        print("✅ @ Command detection test completed!")
        return True
        
    except Exception as e:
        print(f"❌ @ Command detection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_at_command_completion():
    """Test @ command completion functionality."""
    print("\n🧪 Testing @ Command Completion...")
    
    try:
        from agent_swarm.cli.autocomplete import AdvancedCompleter
        
        completer = AdvancedCompleter()
        completer.project_path = Path("/home/<USER>/workspace/local-llm/agent-mode")
        
        # Test @ command completion
        test_cases = [
            ("explain @README", "README"),
            ("show me @src/", "src/"),
            ("analyze @pyproject", "pyproject"),
            ("@", ""),
        ]
        
        for line, text in test_cases:
            print(f"\\n   Testing: '{line}' with text '{text}'")
            
            # Test if it's detected as @ command context
            is_at_context = completer._is_at_command_context(line, text)
            print(f"     @ context detected: {is_at_context}")
            
            if is_at_context:
                # Get @ command completions
                completions = completer._complete_at_command(line, text)
                print(f"     Completions found: {len(completions)}")
                
                # Show first few completions
                for i, comp in enumerate(completions[:5]):
                    print(f"       {i+1}. {comp.text} - {comp.description}")
                
                if len(completions) > 5:
                    print(f"       ... and {len(completions) - 5} more")
        
        print("\\n✅ @ Command completion test completed!")
        return True
        
    except Exception as e:
        print(f"❌ @ Command completion test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_autocomplete_integration():
    """Test full autocomplete integration with @ commands."""
    print("\\n🧪 Testing Full Autocomplete Integration...")
    
    try:
        from agent_swarm.cli.autocomplete import AdvancedCompleter
        
        completer = AdvancedCompleter()
        completer.project_path = Path("/home/<USER>/workspace/local-llm/agent-mode")
        
        # Test full completion pipeline
        test_cases = [
            "explain @README",
            "show me @src/agent_swarm/",
            "analyze @*.py",
            "@pyproject",
            "/edit README",  # Regular command completion
        ]
        
        for line in test_cases:
            print(f"\\n   Testing full completion for: '{line}'")
            
            # Simulate what readline would pass
            text = line.split()[-1] if line.split() else line
            
            # Get all completions
            completions = completer.get_completions(line, text)
            print(f"     Total completions: {len(completions)}")
            
            # Group by type
            by_type = {}
            for comp in completions:
                if comp.type not in by_type:
                    by_type[comp.type] = []
                by_type[comp.type].append(comp)
            
            # Show breakdown
            for comp_type, comps in by_type.items():
                print(f"     {comp_type}: {len(comps)} items")
                for comp in comps[:3]:  # Show first 3
                    print(f"       - {comp.text}")
                if len(comps) > 3:
                    print(f"       ... and {len(comps) - 3} more")
        
        print("\\n✅ Full autocomplete integration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Full autocomplete integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_readline_integration():
    """Test readline integration (if available)."""
    print("\\n🧪 Testing Readline Integration...")
    
    try:
        import readline
        from agent_swarm.cli.autocomplete import AdvancedCompleter
        
        completer = AdvancedCompleter()
        completer.project_path = Path("/home/<USER>/workspace/local-llm/agent-mode")
        
        # Check if readline is properly configured
        current_completer = readline.get_completer()
        print(f"   Current completer: {current_completer}")
        
        # Check delimiters (should not include @)
        delims = readline.get_completer_delims()
        has_at_delimiter = '@' in delims
        print(f"   @ in delimiters: {has_at_delimiter} (should be False)")
        
        # Test completion function
        test_text = "README"
        try:
            result = completer.complete(test_text, 0)
            print(f"   Complete function works: {result is not None}")
        except Exception as e:
            print(f"   Complete function error: {e}")
        
        status = "✅" if not has_at_delimiter else "❌"
        print(f"{status} Readline integration test completed!")
        return not has_at_delimiter
        
    except ImportError:
        print("   ⚠️  Readline not available (this is OK on some systems)")
        return True
    except Exception as e:
        print(f"❌ Readline integration test failed: {e}")
        return False

def test_real_world_scenarios():
    """Test real-world @ command scenarios."""
    print("\\n🧪 Testing Real-World Scenarios...")
    
    try:
        from agent_swarm.cli.autocomplete import AdvancedCompleter
        
        completer = AdvancedCompleter()
        completer.project_path = Path("/home/<USER>/workspace/local-llm/agent-mode")
        
        # Real-world scenarios
        scenarios = [
            "could you explain @README.md",
            "show me the code in @src/agent_swarm/__init__.py",
            "analyze @pyproject.toml and @requirements.txt",
            "what's in @src/ folder",
            "review all @*.py files",
        ]
        
        for scenario in scenarios:
            print(f"\\n   Scenario: '{scenario}'")
            
            # Find @ commands in the scenario
            import re
            at_matches = re.findall(r'@([^\\s]+)', scenario)
            print(f"     @ commands found: {at_matches}")
            
            # Test completion for each @ command
            for at_cmd in at_matches:
                # Simulate completing this @ command
                line = scenario
                text = f"@{at_cmd}"
                
                if completer._is_at_command_context(line, text):
                    completions = completer._complete_at_command(line, text)
                    print(f"     Completions for '@{at_cmd}': {len(completions)}")
                    
                    # Show relevant completions
                    relevant = [c for c in completions if at_cmd in c.text.lower()]
                    if relevant:
                        print(f"       Relevant: {relevant[0].text}")
        
        print("\\n✅ Real-world scenarios test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Real-world scenarios test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all @ command autocomplete tests."""
    print("🚀 Testing @ Command Autocomplete System")
    print("=" * 60)
    
    tests = [
        test_at_command_detection,
        test_at_command_completion,
        test_full_autocomplete_integration,
        test_readline_integration,
        test_real_world_scenarios,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    print("\\n" + "=" * 60)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 @ Command Autocomplete fully functional!")
        print("\\n💡 Features working:")
        print("   ✅ @ command detection in natural language")
        print("   ✅ File/folder path completion after @")
        print("   ✅ Integration with readline")
        print("   ✅ Rich completion display")
        print("   ✅ Real-world scenario support")
        print("\\n🔮 Try typing these and press TAB:")
        print("   • 'explain @README<TAB>'")
        print("   • 'show me @src/<TAB>'")
        print("   • 'analyze @*.py<TAB>'")
        print("   • '@pyproject<TAB>'")
        return True
    else:
        print(f"❌ {total - passed} features need work")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
