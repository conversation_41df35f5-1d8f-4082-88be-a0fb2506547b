"""
Integration tests for the professional editing system.

Tests the complete editing workflow including CLI integration,
AI tools, and storage components.
"""

import pytest
import tempfile
import asyncio
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch

from agent_swarm.tools.editing.factory import EditingSystemFactory, EditingSystemConfig
from agent_swarm.tools.editing.core.models import (
    EditOperation,
    EditOperationType,
    FileChangeStatus,
)
from agent_swarm.tools.editing_tools import EditingTools


class TestEditingSystemIntegration:
    """Integration tests for the complete editing system."""
    
    @pytest.fixture
    async def temp_project(self):
        """Create a temporary project directory with sample files."""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir)
            
            # Create sample files
            (project_root / "src").mkdir()
            (project_root / "src" / "main.py").write_text("""
def hello_world():
    print("Hello, World!")

if __name__ == "__main__":
    hello_world()
""".strip())
            
            (project_root / "config.yaml").write_text("""
app:
  name: test-app
  version: 1.0.0
  debug: true
""".strip())
            
            (project_root / "README.md").write_text("""
# Test Project

This is a test project for Agent Swarm.
""".strip())
            
            yield project_root
    
    @pytest.fixture
    async def editing_system(self, temp_project):
        """Create an editing system for testing."""
        config = EditingSystemConfig()
        config.storage_dir = temp_project / ".agent-swarm"
        config.create_backups = True
        config.ai_enabled = False  # Disable AI for integration tests
        
        system = EditingSystemFactory.create_default_system(config)
        await system.initialize()
        
        yield system
        
        # Cleanup
        await system.shutdown()
    
    @pytest.fixture
    async def editing_tools(self, temp_project):
        """Create editing tools for testing."""
        tools = EditingTools(temp_project)
        await tools.initialize()
        
        yield tools
    
    @pytest.mark.asyncio
    async def test_complete_file_editing_workflow(self, editing_system, temp_project):
        """Test complete file editing workflow."""
        file_path = temp_project / "src" / "main.py"
        
        # 1. Read original file
        original_content = await editing_system.file_editor.read_file(file_path)
        assert "Hello, World!" in original_content
        
        # 2. Create edit operation
        new_content = original_content.replace("Hello, World!", "Hello, Agent Swarm!")
        operation = EditOperation(
            operation_type=EditOperationType.REPLACE_ALL,
            file_path=file_path,
            content=new_content,
            description="Update greeting message"
        )
        
        # 3. Validate operation
        is_valid = await editing_system.validator.validate_safety(operation)
        assert is_valid
        
        # 4. Apply edit
        change = await editing_system.file_editor.apply_edit(operation)
        assert change.has_changes
        assert "Hello, Agent Swarm!" in change.new_content
        
        # 5. Generate diff
        diff_result = editing_system.diff_engine.generate_diff(
            change.original_content, change.new_content
        )
        assert diff_result.has_changes
        assert diff_result.lines_added > 0 or diff_result.lines_removed > 0
        
        # 6. Track change
        change_id = await editing_system.change_tracker.track_change(change)
        assert len(change_id) == 8  # 8-character ID
        
        # 7. Apply to filesystem
        await editing_system.file_editor.write_file(file_path, change.new_content)
        
        # 8. Verify file was updated
        updated_content = await editing_system.file_editor.read_file(file_path)
        assert "Hello, Agent Swarm!" in updated_content
        
        # 9. Retrieve change
        retrieved_change = await editing_system.change_tracker.get_change(change_id)
        assert retrieved_change is not None
        assert retrieved_change.change_id == change_id
        
        # 10. Revert change
        success = await editing_system.change_tracker.revert_change(change_id)
        assert success
        
        # 11. Verify revert
        reverted_content = await editing_system.file_editor.read_file(file_path)
        assert "Hello, World!" in reverted_content
    
    @pytest.mark.asyncio
    async def test_session_management_workflow(self, editing_system, temp_project):
        """Test session management workflow."""
        # 1. Start session
        session_id = await editing_system.session_manager.start_session(
            "test-session", "Test session for integration testing"
        )
        assert session_id is not None
        
        # 2. Make multiple changes
        changes = []
        files = [
            temp_project / "src" / "main.py",
            temp_project / "config.yaml",
            temp_project / "README.md"
        ]
        
        for i, file_path in enumerate(files):
            original_content = await editing_system.file_editor.read_file(file_path)
            new_content = f"# Modified {i+1}\n{original_content}"
            
            operation = EditOperation(
                operation_type=EditOperationType.REPLACE_ALL,
                file_path=file_path,
                content=new_content,
                description=f"Add header to {file_path.name}"
            )
            
            change = await editing_system.file_editor.apply_edit(operation)
            change_id = await editing_system.change_tracker.track_change(change)
            
            # Add to session
            await editing_system.session_manager.add_change_to_session(session_id, change)
            changes.append(change_id)
        
        # 3. List session changes
        session_changes = await editing_system.session_manager.get_session_changes(session_id)
        assert len(session_changes) == 3
        
        # 4. Get session info
        session = await editing_system.session_manager.get_session(session_id)
        assert session is not None
        assert session.name == "test-session"
        assert session.change_count == 3
        
        # 5. End session
        await editing_system.session_manager.end_session(session_id)
        
        # 6. Verify session status
        updated_session = await editing_system.session_manager.get_session(session_id)
        assert updated_session.status == "completed"
    
    @pytest.mark.asyncio
    async def test_backup_and_recovery_workflow(self, editing_system, temp_project):
        """Test backup and recovery workflow."""
        file_path = temp_project / "src" / "main.py"
        
        # 1. Create backup
        backup_id = await editing_system.backup_manager.create_backup(file_path)
        assert backup_id is not None
        
        # 2. Modify file
        original_content = await editing_system.file_editor.read_file(file_path)
        modified_content = "# This file was modified\n" + original_content
        await editing_system.file_editor.write_file(file_path, modified_content)
        
        # 3. Verify modification
        current_content = await editing_system.file_editor.read_file(file_path)
        assert "# This file was modified" in current_content
        
        # 4. Restore from backup
        success = await editing_system.backup_manager.restore_backup(backup_id)
        assert success
        
        # 5. Verify restoration
        restored_content = await editing_system.file_editor.read_file(file_path)
        assert restored_content == original_content
        assert "# This file was modified" not in restored_content
        
        # 6. List backups
        backups = await editing_system.backup_manager.list_backups(file_path)
        assert len(backups) >= 1
        assert any(b["backup_id"] == backup_id for b in backups)
    
    @pytest.mark.asyncio
    async def test_validation_workflow(self, editing_system, temp_project):
        """Test validation workflow."""
        file_path = temp_project / "src" / "main.py"
        
        # 1. Test valid Python code
        valid_code = """
def greet(name):
    return f"Hello, {name}!"

print(greet("World"))
"""
        
        operation = EditOperation(
            operation_type=EditOperationType.REPLACE_ALL,
            file_path=file_path,
            content=valid_code
        )
        
        validation_result = await editing_system.validator.validate_operation(operation)
        assert validation_result.is_valid
        assert len(validation_result.errors) == 0
        
        # 2. Test invalid Python code
        invalid_code = """
def greet(name:
    return f"Hello, {name}!"  # Missing closing parenthesis
"""
        
        operation.content = invalid_code
        validation_result = await editing_system.validator.validate_operation(operation)
        assert not validation_result.is_valid
        assert len(validation_result.errors) > 0
        
        # 3. Test dangerous file path
        dangerous_operation = EditOperation(
            operation_type=EditOperationType.REPLACE_ALL,
            file_path=Path("/etc/passwd"),
            content="malicious content"
        )
        
        validation_result = await editing_system.validator.validate_operation(dangerous_operation)
        assert not validation_result.is_valid
        assert any("safety" in error.lower() for error in validation_result.errors)


class TestEditingToolsIntegration:
    """Integration tests for the AI editing tools."""
    
    @pytest.mark.asyncio
    async def test_editing_tools_workflow(self, editing_tools, temp_project):
        """Test complete editing tools workflow."""
        file_path = "src/main.py"
        
        # 1. Read file
        result = await editing_tools.read_file(file_path)
        assert result["success"]
        assert "Hello, World!" in result["content"]
        
        # 2. Write file
        new_content = result["content"].replace("Hello, World!", "Hello, AI!")
        write_result = await editing_tools.write_file(
            file_path, new_content, "AI: Updated greeting"
        )
        assert write_result["success"]
        assert write_result["has_changes"]
        
        # 3. Verify change
        updated_result = await editing_tools.read_file(file_path)
        assert updated_result["success"]
        assert "Hello, AI!" in updated_result["content"]
        
        # 4. List changes
        changes_result = await editing_tools.list_changes()
        assert changes_result["success"]
        assert len(changes_result["changes"]) >= 1
        
        # 5. Get diff
        change_id = write_result["change_id"]
        diff_result = await editing_tools.get_diff(change_id)
        assert diff_result["success"]
        
        # 6. Revert change
        revert_result = await editing_tools.revert_change(change_id)
        assert revert_result["success"]
        
        # 7. Verify revert
        reverted_result = await editing_tools.read_file(file_path)
        assert reverted_result["success"]
        assert "Hello, World!" in reverted_result["content"]
    
    @pytest.mark.asyncio
    async def test_line_editing_workflow(self, editing_tools, temp_project):
        """Test line-specific editing workflow."""
        file_path = "src/main.py"
        
        # 1. Read original file
        original_result = await editing_tools.read_file(file_path)
        assert original_result["success"]
        
        # 2. Edit specific lines
        new_lines = '    print("Hello, Line Editor!")\n'
        result = await editing_tools.edit_lines(
            file_path, 2, 2, new_lines, "Update print statement"
        )
        assert result["success"]
        assert result["has_changes"]
        
        # 3. Verify line edit
        updated_result = await editing_tools.read_file(file_path)
        assert updated_result["success"]
        assert "Hello, Line Editor!" in updated_result["content"]
        
        # 4. Insert lines
        insert_result = await editing_tools.insert_lines(
            file_path, 1, "# This is a comment\n", "Add comment"
        )
        assert insert_result["success"]
        
        # 5. Verify insertion
        final_result = await editing_tools.read_file(file_path)
        assert final_result["success"]
        assert "# This is a comment" in final_result["content"]
    
    @pytest.mark.asyncio
    async def test_error_handling(self, editing_tools, temp_project):
        """Test error handling in editing tools."""
        # 1. Test reading non-existent file
        result = await editing_tools.read_file("nonexistent.py")
        assert not result["success"]
        assert "error" in result
        
        # 2. Test writing to invalid path
        result = await editing_tools.write_file(
            "/invalid/path/file.py", "content", "test"
        )
        assert not result["success"]
        assert "error" in result
        
        # 3. Test invalid change ID
        result = await editing_tools.get_diff("invalid_id")
        assert not result["success"]
        assert "error" in result
        
        # 4. Test reverting non-existent change
        result = await editing_tools.revert_change("invalid_id")
        assert not result["success"]


class TestCLIIntegration:
    """Integration tests for CLI commands."""
    
    @pytest.fixture
    async def mock_shell(self, temp_project):
        """Create a mock shell for testing."""
        from agent_swarm.cli.interactive_shell import InteractiveShell
        
        shell = InteractiveShell(verbose=True)
        shell.project_path = temp_project
        shell.agent = Mock()  # Mock agent
        
        yield shell
    
    @pytest.mark.asyncio
    async def test_edit_command_integration(self, mock_shell, temp_project):
        """Test edit command integration."""
        # This would require more complex mocking of user input
        # For now, test that the command can be imported and initialized
        
        try:
            from agent_swarm.cli.commands.professional_edit import ProfessionalEditCommands
            
            edit_commands = ProfessionalEditCommands(mock_shell)
            assert edit_commands is not None
            assert edit_commands.shell == mock_shell
            
        except ImportError:
            pytest.skip("Professional editing dependencies not available")
    
    @pytest.mark.asyncio
    async def test_command_routing(self, mock_shell):
        """Test that commands are properly routed."""
        # Test that edit commands exist in the shell
        assert 'edit' in mock_shell.commands
        assert 'smart-edit' in mock_shell.commands
        assert 'diff' in mock_shell.commands
        assert 'revert' in mock_shell.commands
        
        # Test that commands are callable
        assert callable(mock_shell.commands['edit'])
        assert callable(mock_shell.commands['smart-edit'])
        assert callable(mock_shell.commands['diff'])
        assert callable(mock_shell.commands['revert'])
