"""
Integration tests for the complete multi-agent consensus system.
Tests end-to-end functionality with real communication and consensus.
"""

import pytest
import asyncio
import time
import json
from pathlib import Path
from typing import Dict, Any, List

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    import sys
    sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from agent_swarm.algorithms.algorithms.stages.multi_agent_consensus_stage import (
    MultiAgentConsensusStage
)
from agent_swarm.algorithms.core import AlgorithmContext
from agent_swarm.core.multi_agent_communication import (
    initialize_communication_hub,
    MultiAgentCommunicationHub,
    AgentRole,
    MessageType
)


@pytest.mark.integration
class TestMultiAgentSystemIntegration:
    """Integration tests for the complete multi-agent system."""
    
    @pytest.fixture(scope="class")
    async def communication_hub(self):
        """Initialize communication hub for integration tests."""
        hub = await initialize_communication_hub()
        yield hub
        await hub.shutdown()
    
    @pytest.fixture
    def consensus_stage(self):
        """Create consensus stage for testing."""
        return MultiAgentConsensusStage()
    
    @pytest.fixture
    def algorithm_context(self):
        """Create algorithm context for testing."""
        return AlgorithmContext(
            request_id="integration_test",
            user_id="test_user",
            session_id="test_session"
        )
    
    @pytest.mark.asyncio
    async def test_technology_decision_consensus(self, consensus_stage, algorithm_context):
        """Test consensus on technology decision."""
        input_data = {
            "domain": "technology",
            "problem": "Should we migrate from monolith to microservices architecture?",
            "current_system": {
                "architecture": "monolithic",
                "team_size": 12,
                "deployment_frequency": "weekly",
                "scaling_issues": True
            },
            "migration_benefits": [
                "Independent service scaling",
                "Technology diversity",
                "Faster deployment cycles",
                "Team autonomy"
            ],
            "migration_challenges": [
                "Increased complexity",
                "Distributed system challenges",
                "Data consistency issues",
                "Operational overhead"
            ],
            "constraints": {
                "timeline": "6 months",
                "budget": "$500k",
                "team_experience": "limited microservices experience"
            }
        }
        
        result = await consensus_stage.process(input_data, algorithm_context)
        
        # Verify successful consensus
        assert result.success is True
        assert result.confidence > 0.0
        assert result.execution_time > 0.0
        
        # Verify output structure
        output = result.output
        assert output["consensus_reached"] is True
        assert output["participating_agents"] >= 3
        assert len(output["agent_contributions"]) >= 3
        
        # Verify agent diversity
        agent_roles = [contrib["role"] for contrib in output["agent_contributions"]]
        assert "specialist" in agent_roles  # Technology specialist should participate
        assert len(set(agent_roles)) >= 3  # At least 3 different roles
        
        # Verify synthesized results
        synthesized = output["synthesized_result"]
        assert len(synthesized["recommendations"]) > 0
        assert len(synthesized["concerns"]) > 0
        assert len(synthesized["reasoning_chain"]) > 0
        
        # Verify consensus quality
        assert output["overall_confidence"] > 0.5
        assert output["consensus_type"] in ["strong_consensus", "moderate_consensus", "weak_consensus"]
    
    @pytest.mark.asyncio
    async def test_business_strategy_consensus(self, consensus_stage, algorithm_context):
        """Test consensus on business strategy decision."""
        input_data = {
            "domain": "business",
            "problem": "Should we expand to the European market?",
            "market_analysis": {
                "market_size": "$5B addressable market",
                "growth_rate": "15% annually",
                "competition": "3 major players, fragmented market",
                "regulatory_complexity": "High (GDPR, local regulations)"
            },
            "financial_projections": {
                "investment_required": "$10M over 2 years",
                "break_even_timeline": "18 months",
                "projected_revenue": "$25M by year 3",
                "roi_estimate": "150% by year 5"
            },
            "strategic_considerations": [
                "Brand recognition in Europe",
                "Local partnership opportunities",
                "Currency exchange risks",
                "Cultural adaptation requirements"
            ],
            "operational_requirements": {
                "local_team": "30+ employees",
                "office_locations": "London, Berlin, Paris",
                "localization": "5 languages, local payment methods",
                "compliance": "GDPR, local business regulations"
            }
        }
        
        result = await consensus_stage.process(input_data, algorithm_context)
        
        # Verify successful consensus
        assert result.success is True
        assert result.confidence > 0.0
        
        # Verify business specialist participation
        output = result.output
        agent_ids = [contrib["agent_id"] for contrib in output["agent_contributions"]]
        assert "specialist_business" in agent_ids
        
        # Verify comprehensive analysis
        synthesized = output["synthesized_result"]
        assert len(synthesized["recommendations"]) > 0
        assert len(synthesized["concerns"]) > 0
        
        # Business decisions should have moderate to high confidence
        assert output["overall_confidence"] > 0.4
    
    @pytest.mark.asyncio
    async def test_security_risk_assessment(self, consensus_stage, algorithm_context):
        """Test consensus on security risk assessment."""
        input_data = {
            "domain": "security",
            "problem": "Assess security risks of cloud migration and recommend mitigation strategies",
            "current_infrastructure": {
                "deployment": "on-premise data centers",
                "security_model": "perimeter-based",
                "compliance": "SOC2, PCI DSS",
                "data_sensitivity": "high (customer PII, payment data)"
            },
            "cloud_migration_plan": {
                "target_provider": "AWS",
                "migration_timeline": "12 months",
                "workloads": ["web applications", "databases", "analytics"],
                "data_residency": "US and EU regions"
            },
            "identified_risks": [
                "Data breaches during migration",
                "Misconfigured cloud resources",
                "Insider threats with expanded access",
                "Compliance violations",
                "Vendor lock-in dependencies"
            ],
            "proposed_mitigations": [
                "Zero-trust architecture implementation",
                "End-to-end encryption",
                "Continuous security monitoring",
                "Regular penetration testing",
                "Staff security training"
            ]
        }
        
        result = await consensus_stage.process(input_data, algorithm_context)
        
        # Verify successful consensus
        assert result.success is True
        
        # Verify security critic participation
        output = result.output
        agent_ids = [contrib["agent_id"] for contrib in output["agent_contributions"]]
        assert "critic_security" in agent_ids
        
        # Security assessments should identify concerns
        synthesized = output["synthesized_result"]
        assert len(synthesized["concerns"]) > 0
        
        # Verify security-focused analysis
        security_contrib = next(
            contrib for contrib in output["agent_contributions"] 
            if contrib["agent_id"] == "critic_security"
        )
        assert security_contrib["confidence"] > 0.0
    
    @pytest.mark.asyncio
    async def test_complex_multi_domain_decision(self, consensus_stage, algorithm_context):
        """Test consensus on complex decision spanning multiple domains."""
        input_data = {
            "domains": ["technology", "business", "security"],
            "problem": "Should we implement AI-powered customer service automation?",
            "technical_requirements": {
                "ai_capabilities": "Natural language processing, sentiment analysis",
                "integration_complexity": "High - 15 existing systems",
                "infrastructure_needs": "GPU clusters, real-time processing",
                "data_requirements": "5 years customer interaction history"
            },
            "business_impact": {
                "cost_reduction": "$2M annually in support costs",
                "revenue_impact": "20% increase from improved customer satisfaction",
                "competitive_advantage": "Significant differentiation",
                "implementation_cost": "$5M over 18 months"
            },
            "security_considerations": {
                "data_privacy": "Customer conversation data processing",
                "ai_bias_risks": "Potential discriminatory responses",
                "model_security": "Protection against adversarial attacks",
                "compliance_impact": "GDPR, CCPA data processing requirements"
            },
            "stakeholder_perspectives": {
                "engineering": "Concerned about technical complexity",
                "product": "Excited about customer experience improvements",
                "security": "Worried about data privacy and AI bias",
                "finance": "Focused on ROI and implementation costs",
                "legal": "Concerned about compliance and liability"
            }
        }
        
        result = await consensus_stage.process(input_data, algorithm_context)
        
        # Verify successful consensus on complex decision
        assert result.success is True
        assert result.confidence > 0.0
        
        output = result.output
        
        # Should involve multiple agent types for multi-domain decision
        agent_roles = [contrib["role"] for contrib in output["agent_contributions"]]
        assert len(set(agent_roles)) >= 4  # Multiple roles for complex decision
        
        # Should include technology specialist and security critic
        agent_ids = [contrib["agent_id"] for contrib in output["agent_contributions"]]
        assert "specialist_tech" in agent_ids
        assert "critic_security" in agent_ids
        
        # Complex decisions should have comprehensive analysis
        synthesized = output["synthesized_result"]
        assert len(synthesized["recommendations"]) >= 2
        assert len(synthesized["concerns"]) >= 2
        assert len(synthesized["reasoning_chain"]) >= 3
        
        # Verify consensus metrics for complex decision
        metrics = output["consensus_metrics"]
        assert metrics["role_diversity"] >= 4
        assert metrics["expertise_coverage"] >= 5
    
    @pytest.mark.asyncio
    async def test_conflicting_perspectives_resolution(self, consensus_stage, algorithm_context):
        """Test consensus with deliberately conflicting agent perspectives."""
        input_data = {
            "domain": "technology",
            "problem": "Balance between security and usability in new authentication system",
            "security_requirements": {
                "multi_factor_authentication": "Mandatory for all users",
                "session_timeout": "15 minutes maximum",
                "password_complexity": "16+ characters with special requirements",
                "biometric_verification": "Required for sensitive operations"
            },
            "usability_requirements": {
                "single_sign_on": "Seamless across all applications",
                "minimal_friction": "No more than 2 authentication steps",
                "user_friendly": "Simple for non-technical users",
                "mobile_optimized": "Easy use on mobile devices"
            },
            "business_constraints": {
                "implementation_timeline": "3 months",
                "budget_limit": "$500k",
                "user_adoption": "Must maintain 95%+ adoption rate",
                "compliance": "Must meet SOC2 and ISO27001"
            },
            "conflict_areas": [
                "Security vs convenience trade-offs",
                "Implementation complexity vs timeline",
                "Cost vs comprehensive security",
                "User experience vs compliance requirements"
            ]
        }
        
        result = await consensus_stage.process(input_data, algorithm_context)
        
        # Should still reach consensus despite conflicts
        assert result.success is True
        
        output = result.output
        
        # Should involve security critic and other perspectives
        agent_ids = [contrib["agent_id"] for contrib in output["agent_contributions"]]
        assert "critic_security" in agent_ids
        
        # Conflict resolution should be evident in reasoning
        synthesized = output["synthesized_result"]
        reasoning = " ".join(synthesized["reasoning_chain"]).lower()
        
        # Should mention balancing or trade-offs
        assert any(word in reasoning for word in ["balance", "trade-off", "compromise", "consider"])
        
        # Should have both recommendations and concerns
        assert len(synthesized["recommendations"]) > 0
        assert len(synthesized["concerns"]) > 0
        
        # Confidence might be lower due to conflicts, but should still be reasonable
        assert output["overall_confidence"] > 0.3
    
    @pytest.mark.asyncio
    async def test_consensus_performance_metrics(self, consensus_stage, algorithm_context):
        """Test consensus performance and timing characteristics."""
        # Simple decision for baseline
        simple_input = {
            "domain": "technology",
            "problem": "Choose between Python and Java for new microservice",
            "criteria": ["development_speed", "performance", "team_expertise"]
        }
        
        # Complex decision for comparison
        complex_input = {
            "domain": "technology",
            "problem": "Design comprehensive enterprise architecture",
            "requirements": {
                "scalability": "Support 10M+ users",
                "reliability": "99.99% uptime",
                "security": "Enterprise-grade with compliance",
                "performance": "Sub-100ms response times",
                "maintainability": "Easy to modify and extend"
            },
            "constraints": {
                "budget": "$5M",
                "timeline": "18 months",
                "team_size": 50,
                "technology_constraints": ["existing Java ecosystem", "cloud-native"]
            },
            "architecture_components": [
                "API gateway and load balancing",
                "Microservices architecture",
                "Event-driven communication",
                "Data storage strategy",
                "Monitoring and observability",
                "Security and authentication",
                "Deployment and CI/CD"
            ]
        }
        
        # Test simple decision
        start_time = time.time()
        simple_result = await consensus_stage.process(simple_input, algorithm_context)
        simple_time = time.time() - start_time
        
        # Test complex decision
        start_time = time.time()
        complex_result = await consensus_stage.process(complex_input, algorithm_context)
        complex_time = time.time() - start_time
        
        # Both should succeed
        assert simple_result.success is True
        assert complex_result.success is True
        
        # Complex decision should take longer (but not excessively)
        assert complex_time > simple_time
        assert complex_time < simple_time * 10  # Reasonable upper bound
        
        # Complex decision should involve more agents
        simple_agents = simple_result.output["participating_agents"]
        complex_agents = complex_result.output["participating_agents"]
        assert complex_agents >= simple_agents
        
        # Complex decision should have more comprehensive output
        simple_recs = len(simple_result.output["synthesized_result"]["recommendations"])
        complex_recs = len(complex_result.output["synthesized_result"]["recommendations"])
        assert complex_recs >= simple_recs
    
    @pytest.mark.asyncio
    async def test_communication_hub_integration(self, communication_hub, consensus_stage, algorithm_context):
        """Test integration with communication hub."""
        # Get initial hub metrics
        initial_metrics = communication_hub.get_metrics()
        
        # Run consensus process
        input_data = {
            "domain": "technology",
            "problem": "Test communication hub integration",
            "details": "Verify that consensus stage properly integrates with communication hub"
        }
        
        result = await consensus_stage.process(input_data, algorithm_context)
        
        # Verify successful processing
        assert result.success is True
        
        # Get final hub metrics
        final_metrics = communication_hub.get_metrics()
        
        # Should have registered agents
        assert final_metrics["agents_registered"] >= initial_metrics["agents_registered"]
        
        # Communication hub should be active
        assert communication_hub._processing_task is not None
        assert not communication_hub._processing_task.done()
    
    @pytest.mark.asyncio
    async def test_multiple_concurrent_consensus(self, consensus_stage):
        """Test multiple concurrent consensus processes."""
        # Create multiple different scenarios
        scenarios = [
            {
                "domain": "technology",
                "problem": "Choose database technology",
                "options": ["PostgreSQL", "MongoDB", "Cassandra"]
            },
            {
                "domain": "business", 
                "problem": "Pricing strategy decision",
                "options": ["freemium", "subscription", "pay-per-use"]
            },
            {
                "domain": "security",
                "problem": "Authentication method selection",
                "options": ["OAuth2", "SAML", "custom_solution"]
            }
        ]
        
        # Create contexts for each scenario
        contexts = [
            AlgorithmContext(
                request_id=f"concurrent_test_{i}",
                user_id="test_user",
                session_id=f"session_{i}"
            )
            for i in range(len(scenarios))
        ]
        
        # Run all scenarios concurrently
        tasks = [
            consensus_stage.process(scenario, context)
            for scenario, context in zip(scenarios, contexts)
        ]
        
        results = await asyncio.gather(*tasks)
        
        # All should succeed
        for i, result in enumerate(results):
            assert result.success is True, f"Scenario {i} failed: {result.error}"
            assert result.confidence > 0.0
            assert result.output["consensus_reached"] is True
        
        # Each should have unique request IDs
        request_ids = [result.metadata.get("request_id") for result in results]
        assert len(set(request_ids)) == len(results)
    
    @pytest.mark.asyncio
    async def test_consensus_history_tracking(self, consensus_stage, algorithm_context):
        """Test that consensus history is properly tracked."""
        initial_history_length = len(consensus_stage.consensus_history)
        
        # Run multiple consensus processes
        scenarios = [
            {"domain": "technology", "problem": "Test scenario 1"},
            {"domain": "business", "problem": "Test scenario 2"},
            {"domain": "security", "problem": "Test scenario 3"}
        ]
        
        for scenario in scenarios:
            result = await consensus_stage.process(scenario, algorithm_context)
            assert result.success is True
        
        # History should have grown
        final_history_length = len(consensus_stage.consensus_history)
        assert final_history_length == initial_history_length + len(scenarios)
        
        # Each history entry should have required fields
        for entry in consensus_stage.consensus_history[-len(scenarios):]:
            assert "timestamp" in entry
            assert "input_summary" in entry
            assert "consensus_reached" in entry
            assert "confidence" in entry
            assert "agents_used" in entry
    
    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self, consensus_stage, algorithm_context):
        """Test error handling and system recovery."""
        # Test with invalid input
        invalid_inputs = [
            None,
            "",
            {"invalid": "structure"},
            {"domain": "unknown_domain", "problem": "test"}
        ]
        
        for invalid_input in invalid_inputs:
            result = await consensus_stage.process(invalid_input, algorithm_context)
            
            # Should handle errors gracefully
            if not result.success:
                assert result.error is not None
                assert result.confidence == 0.0
            # Some invalid inputs might still work due to robust handling
        
        # After errors, system should still work normally
        valid_input = {
            "domain": "technology",
            "problem": "Test recovery after errors"
        }
        
        result = await consensus_stage.process(valid_input, algorithm_context)
        assert result.success is True
        assert result.confidence > 0.0


if __name__ == "__main__":
    # Run integration tests
    pytest.main([__file__, "-v", "--tb=short"])