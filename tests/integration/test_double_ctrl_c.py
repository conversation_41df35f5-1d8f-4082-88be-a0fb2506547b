#!/usr/bin/env python3
"""
Simple test for double Ctrl+C functionality.
"""

import time
import sys
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_double_ctrl_c_logic():
    """Test the double Ctrl+C detection logic."""
    
    # Simulate the shell's interrupt handling logic
    last_interrupt_time = 0
    interrupt_threshold = 2.0  # 2 seconds
    
    # Test 1: Single Ctrl+C
    current_time = time.time()
    last_interrupt_time = current_time
    
    # Simulate second Ctrl+C after 3 seconds (should not trigger quit)
    time_after_3_seconds = current_time + 3.0
    is_double_interrupt = (time_after_3_seconds - last_interrupt_time) < interrupt_threshold
    
    print(f"Test 1 - Single Ctrl+C (3s gap): {not is_double_interrupt}")
    assert not is_double_interrupt, "Should not detect double Ctrl+C after 3 seconds"
    
    # Test 2: Double Ctrl+C within threshold
    current_time = time.time()
    last_interrupt_time = current_time
    
    # Simulate second Ctrl+C after 1 second (should trigger quit)
    time_after_1_second = current_time + 1.0
    is_double_interrupt = (time_after_1_second - last_interrupt_time) < interrupt_threshold
    
    print(f"Test 2 - Double Ctrl+C (1s gap): {is_double_interrupt}")
    assert is_double_interrupt, "Should detect double Ctrl+C within 1 second"
    
    # Test 3: Edge case - exactly at threshold
    current_time = time.time()
    last_interrupt_time = current_time
    
    # Simulate second Ctrl+C exactly at threshold
    time_at_threshold = current_time + interrupt_threshold
    is_double_interrupt = (time_at_threshold - last_interrupt_time) < interrupt_threshold
    
    print(f"Test 3 - At threshold (2s gap): {not is_double_interrupt}")
    assert not is_double_interrupt, "Should not detect double Ctrl+C exactly at threshold"
    
    print("✅ All double Ctrl+C logic tests passed!")


def test_shell_initialization():
    """Test that the shell can be imported and initialized."""
    
    try:
        from agent_swarm.cli.interactive_shell import InteractiveShell
        
        # Test shell creation
        shell = InteractiveShell(verbose=False, auto_index=False)
        
        # Test that double Ctrl+C attributes exist
        assert hasattr(shell, '_last_interrupt_time'), "Shell should have _last_interrupt_time attribute"
        assert hasattr(shell, '_interrupt_threshold'), "Shell should have _interrupt_threshold attribute"
        
        # Test default values
        assert shell._last_interrupt_time == 0, "Initial interrupt time should be 0"
        assert shell._interrupt_threshold == 2.0, "Interrupt threshold should be 2.0 seconds"
        
        print("✅ Shell initialization test passed!")
        
    except ImportError as e:
        print(f"⚠️  Shell import failed: {e}")
        print("💡 This is expected if dependencies are not installed")


def test_editing_tools_import():
    """Test that editing tools can be imported."""
    
    try:
        from agent_swarm.tools.editing_tools import EditingTools, get_editing_tools
        
        # Test that classes exist
        assert EditingTools is not None, "EditingTools class should exist"
        assert get_editing_tools is not None, "get_editing_tools function should exist"
        
        print("✅ Editing tools import test passed!")
        
    except ImportError as e:
        print(f"⚠️  Editing tools import failed: {e}")
        print("💡 This is expected if editing dependencies are not installed")


def test_professional_editing_import():
    """Test that professional editing system can be imported."""
    
    try:
        from agent_swarm.tools.editing.factory import EditingSystemFactory
        from agent_swarm.tools.editing.core.models import EditOperation, EditOperationType
        
        # Test that classes exist
        assert EditingSystemFactory is not None, "EditingSystemFactory should exist"
        assert EditOperation is not None, "EditOperation should exist"
        assert EditOperationType is not None, "EditOperationType should exist"
        
        print("✅ Professional editing import test passed!")
        
    except ImportError as e:
        print(f"⚠️  Professional editing import failed: {e}")
        print("💡 This is expected if editing dependencies are not installed")


def test_cli_commands_integration():
    """Test that CLI commands are properly integrated."""
    
    try:
        from agent_swarm.cli.interactive_shell import InteractiveShell
        
        shell = InteractiveShell(verbose=False, auto_index=False)
        
        # Test that editing commands exist
        expected_commands = ['edit', 'smart-edit', 'diff', 'revert']
        
        for cmd in expected_commands:
            assert cmd in shell.commands, f"Command '{cmd}' should be in shell.commands"
            assert callable(shell.commands[cmd]), f"Command '{cmd}' should be callable"
        
        print("✅ CLI commands integration test passed!")
        
    except ImportError as e:
        print(f"⚠️  CLI commands test failed: {e}")
        print("💡 This is expected if dependencies are not installed")


def main():
    """Run all tests."""
    print("🧪 Running Agent Swarm Tests")
    print("=" * 40)
    
    tests = [
        test_double_ctrl_c_logic,
        test_shell_initialization,
        test_editing_tools_import,
        test_professional_editing_import,
        test_cli_commands_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            print(f"\n🔧 Running {test.__name__}...")
            test()
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print(f"❌ {total - passed} tests failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
