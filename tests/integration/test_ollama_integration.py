"""
Integration tests for Ollama backend.
These tests require a running Ollama service.
"""

import pytest

from agent_swarm.backends import LL<PERSON>onfig, LLMTier, Message


@pytest.mark.integration
@pytest.mark.requires_ollama
class TestOllamaIntegration:
    """Integration tests for Ollama backend."""

    @pytest.fixture
    def ollama_config(self) -> LLMConfig:
        """Ollama configuration for testing."""
        return LLMConfig(
            name="Test Ollama Model",
            provider="ollama",
            model_id="qwen2.5-coder:7b",  # Assuming this model is available
            tier=LLMTier.LOCAL_FAST,
            context_length=32768,
            cost_per_1m_tokens=0.0,
            recommended_for=["testing"],
            base_url="http://localhost:11434",
        )

    @pytest.fixture
    async def ollama_llm(self, ollama_config: LLMConfig):
        """Create Ollama LLM instance."""
        from agent_swarm.backends.ollama import OllamaLLM

        llm = OllamaLLM(ollama_config)
        await llm.initialize()
        yield llm
        await llm.close()

    async def test_ollama_availability(self, ollama_llm):
        """Test if Ollama service is available."""
        available = await ollama_llm.is_available()
        assert available, "Ollama service should be available"

    async def test_ollama_generation(self, ollama_llm):
        """Test Ollama text generation."""
        messages = [
            Message(role="system", content="You are a helpful assistant."),
            Message(role="user", content="Say 'Hello, World!' in Python."),
        ]

        response = await ollama_llm.generate(messages, temperature=0.1, max_tokens=50)

        assert response.content
        assert response.model == ollama_llm.config.model_id
        assert response.usage["total_tokens"] > 0
        assert (
            "print" in response.content.lower() or "hello" in response.content.lower()
        )

    async def test_ollama_streaming(self, ollama_llm):
        """Test Ollama streaming generation."""
        messages = [
            Message(role="user", content="Count from 1 to 3."),
        ]

        chunks = []
        async for chunk in ollama_llm.stream_generate(
            messages, temperature=0.1, max_tokens=20
        ):
            chunks.append(chunk)

        assert len(chunks) > 0
        full_response = "".join(chunks)
        assert full_response.strip()

    async def test_ollama_error_handling(self, ollama_config: LLMConfig):
        """Test Ollama error handling with invalid model."""
        from agent_swarm.backends.ollama import OllamaLLM

        # Create config with non-existent model
        invalid_config = ollama_config.model_copy()
        invalid_config.model_id = "non-existent-model:latest"

        llm = OllamaLLM(invalid_config)

        # Should handle initialization gracefully
        try:
            await llm.initialize()
            # If it doesn't fail, the model might actually exist
            # In that case, test that it's not available
            if not await llm.is_available():
                pytest.skip("Model not available as expected")
        except Exception:
            # Expected behavior for non-existent model
            pass
        finally:
            await llm.close()


@pytest.mark.integration
@pytest.mark.requires_ollama
class TestOllamaRouter:
    """Integration tests for LLM router with Ollama."""

    @pytest.fixture
    async def ollama_router(self):
        """Create router with Ollama LLMs."""
        from agent_swarm.backends import LLMRouter
        from agent_swarm.backends.ollama import create_ollama_llm

        router = LLMRouter()

        # Try to create and register Ollama LLMs
        try:
            # Primary model
            primary_llm = create_ollama_llm("qwen2.5-coder:7b")
            await primary_llm.initialize()
            if await primary_llm.is_available():
                router.register_llm("primary", primary_llm)
        except Exception:
            pass

        try:
            # Fallback model (if different model available)
            fallback_llm = create_ollama_llm("deepseek-r1:7b")
            await fallback_llm.initialize()
            if await fallback_llm.is_available():
                router.register_llm("fallback", fallback_llm)
        except Exception:
            pass

        if not router.llms:
            pytest.skip("No Ollama models available for testing")

        # Set up routing
        router.set_fallback_chain(list(router.llms.keys()))

        yield router

        # Cleanup
        for llm in router.llms.values():
            await llm.close()

    async def test_router_with_ollama(self, ollama_router):
        """Test router with real Ollama models."""
        messages = [
            Message(role="user", content="Write a simple Python function."),
        ]

        response = await ollama_router.route_request(
            messages, task_type="coding", temperature=0.1
        )

        assert response.content
        assert response.model
        assert response.usage["total_tokens"] > 0

    async def test_router_task_routing(self, ollama_router):
        """Test task-specific routing."""
        # Set up task routing
        primary_llm_name = list(ollama_router.llms.keys())[0]
        ollama_router.set_task_routing("coding", primary_llm_name)

        messages = [
            Message(role="user", content="Explain Python variables."),
        ]

        response = await ollama_router.route_request(messages, task_type="coding")

        assert response.content
        assert (
            "python" in response.content.lower()
            or "variable" in response.content.lower()
        )

    async def test_router_cost_estimation(self, ollama_router):
        """Test cost estimation with local models."""
        cost = ollama_router.estimate_cost("coding", 1000, 500)
        assert cost == 0.0  # Local models should be free


@pytest.mark.integration
@pytest.mark.slow
@pytest.mark.requires_ollama
class TestOllamaPerformance:
    """Performance tests for Ollama backend."""

    async def test_concurrent_requests(self):
        """Test handling multiple concurrent requests."""
        import asyncio

        from agent_swarm.backends.ollama import create_ollama_llm

        llm = create_ollama_llm("qwen2.5-coder:7b")
        await llm.initialize()

        if not await llm.is_available():
            pytest.skip("Ollama model not available")

        async def generate_response(i: int):
            messages = [Message(role="user", content=f"Count to {i}.")]
            return await llm.generate(messages, temperature=0.1, max_tokens=20)

        # Run 3 concurrent requests
        tasks = [generate_response(i) for i in range(1, 4)]
        responses = await asyncio.gather(*tasks)

        assert len(responses) == 3
        for response in responses:
            assert response.content
            assert response.usage["total_tokens"] > 0

        await llm.close()

    async def test_large_context(self):
        """Test handling large context."""
        from agent_swarm.backends.ollama import create_ollama_llm

        llm = create_ollama_llm("qwen2.5-coder:7b")
        await llm.initialize()

        if not await llm.is_available():
            pytest.skip("Ollama model not available")

        # Create a large context message
        large_content = "This is a test. " * 100  # ~1500 characters
        messages = [
            Message(role="system", content="You are a helpful assistant."),
            Message(role="user", content=large_content + " Summarize this text."),
        ]

        response = await llm.generate(messages, temperature=0.1, max_tokens=100)

        assert response.content
        assert len(response.content) > 0

        await llm.close()
