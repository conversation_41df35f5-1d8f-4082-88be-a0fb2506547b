#!/usr/bin/env python3
"""
Integration tests for advanced CLI features.
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch


@pytest.mark.integration
class TestCLICommandsIndexer:
    """Test CLI commands indexer integration."""

    def test_cli_commands_indexer(self):
        """Test CLI commands indexer."""
        try:
            from agent_swarm.context.cli_commands_indexer import CLICommandsIndexer
            
            indexer = C<PERSON><PERSON>ommandsIndexer()
            
            # Test that it can create documentation
            docs = indexer.generate_commands_documentation()
            
            # Test command suggestions
            suggestions = indexer.get_command_suggestions("/ed")
            
            assert len(docs) > 1000, "Documentation should be comprehensive"
            assert "edit" in docs.lower(), "Should contain edit commands"
            assert len(suggestions) >= 0, "Should provide suggestions"
            
        except ImportError:
            pytest.skip("CLI commands indexer not available")


@pytest.mark.integration
class TestAutocomplete:
    """Test autocomplete system integration."""

    def test_autocomplete_system(self):
        """Test autocomplete system."""
        try:
            from agent_swarm.cli.autocomplete import AdvancedCompleter, CompletionSuggestion
            
            completer = AdvancedCompleter()
            
            # Test command completion
            completions = completer._complete_command("/ed")
            
            # Test file type detection
            test_file = Path("test.py")
            file_type = completer._get_file_type(test_file)
            
            # Test completion suggestion creation
            suggestion = CompletionSuggestion(
                text="/edit",
                description="Edit files with diff preview",
                type="command",
                priority=100
            )
            
            assert len(completions) >= 0, "Should return completions"
            assert file_type == "python", "Should detect Python files"
            assert suggestion.text == "/edit", "Should create suggestions correctly"
            
        except ImportError:
            pytest.skip("Autocomplete system not available")


@pytest.mark.integration
class TestContextManager:
    """Test @ commands context manager integration."""

    def test_context_manager(self):
        """Test @ commands context manager."""
        try:
            from agent_swarm.cli.context_manager import ContextManager, ContextItem
            
            # Create temporary test files
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Create test files
                (temp_path / "test.py").write_text("def hello():\n    print('Hello, World!')")
                (temp_path / "config.json").write_text('{"name": "test", "version": "1.0"}')
                (temp_path / "subdir").mkdir()
                (temp_path / "subdir" / "utils.py").write_text("def utility():\n    pass")
                
                # Test context manager
                context_manager = ContextManager(temp_path)
                
                # Test @ command parsing
                text = "Explain this code @test.py and @config.json"
                cleaned_text, at_commands = context_manager.parse_at_commands(text)
                
                # Test processing @ commands
                context_items = context_manager.process_at_commands(at_commands)
                
                # Test context summary
                summary = context_manager.get_context_summary()
                
                # Test LLM context
                llm_context = context_manager.get_context_for_llm()
                
                assert cleaned_text == "Explain this code and", "Should remove @ commands"
                assert len(at_commands) == 2, "Should find 2 @ commands"
                assert len(context_items) >= 2, "Should process context items"
                assert "test.py" in llm_context, "Should include file content"
                
        except ImportError:
            pytest.skip("Context manager not available")


@pytest.mark.integration
class TestShellIntegration:
    """Test shell integration of advanced features."""

    def test_shell_integration(self):
        """Test shell integration of advanced features."""
        try:
            from agent_swarm.cli.interactive_shell import InteractiveShell
            
            shell = InteractiveShell(verbose=False, auto_index=False)
            
            # Test that advanced features are initialized
            has_context_manager = hasattr(shell, 'context_manager')
            has_autocompleter = hasattr(shell, 'autocompleter')
            has_commands_indexer = hasattr(shell, 'commands_indexer')
            
            # Test initialization method exists
            has_init_method = hasattr(shell, '_initialize_advanced_features')
            
            assert has_context_manager, "Should have context manager"
            assert has_autocompleter, "Should have autocompleter"
            assert has_commands_indexer, "Should have commands indexer"
            assert has_init_method, "Should have initialization method"
            
        except ImportError:
            pytest.skip("Shell integration not available")


@pytest.mark.integration
class TestEndToEndWorkflow:
    """Test end-to-end workflow with all features."""

    def test_end_to_end_workflow(self):
        """Test end-to-end workflow with all features."""
        try:
            # Create a realistic test scenario
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Create project structure
                (temp_path / "src").mkdir()
                (temp_path / "src" / "main.py").write_text("""
def main():
    print("Hello, Agent Swarm!")
    return 0

if __name__ == "__main__":
    exit(main())
""".strip())
                
                (temp_path / "config.yaml").write_text("""
app:
  name: test-app
  version: 1.0.0
""".strip())
                
                # Test complete workflow
                from agent_swarm.cli.context_manager import ContextManager
                from agent_swarm.context.cli_commands_indexer import CLICommandsIndexer
                from agent_swarm.cli.autocomplete import AdvancedCompleter
                
                # Initialize components
                context_manager = ContextManager(temp_path)
                commands_indexer = CLICommandsIndexer()
                autocompleter = AdvancedCompleter()
                
                # Simulate user input with @ commands
                user_input = "Explain this code @src/main.py and suggest improvements"
                
                # Process @ commands
                cleaned_input, at_commands = context_manager.parse_at_commands(user_input)
                context_items = context_manager.process_at_commands(at_commands)
                
                # Get CLI commands context
                cli_docs = commands_indexer.generate_commands_documentation()
                
                # Get file context
                file_context = context_manager.get_context_for_llm()
                
                # Build enhanced prompt (like the shell would)
                enhanced_prompt = f"{cleaned_input}\n\n{file_context}\n\n=== CLI COMMANDS ===\n{cli_docs[:500]}..."
                
                assert len(at_commands) == 1, "Should find 1 @ command"
                assert len(context_items) >= 1, "Should process context items"
                assert "Hello, Agent Swarm!" in enhanced_prompt, "Should include file content"
                assert "edit" in enhanced_prompt.lower(), "Should include CLI commands"
                
        except ImportError:
            pytest.skip("End-to-end workflow components not available")


@pytest.mark.integration
class TestAdvancedFeaturesIntegration:
    """Test integration of all advanced features together."""

    def test_complete_integration(self):
        """Test that all advanced features work together."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create test project
            (temp_path / "main.py").write_text("print('Hello World')")
            (temp_path / "README.md").write_text("# Test Project")
            
            try:
                from agent_swarm.cli.context_manager import ContextManager
                from agent_swarm.context.cli_commands_indexer import CLICommandsIndexer
                from agent_swarm.cli.autocomplete import AdvancedCompleter
                
                # Initialize all components
                context_manager = ContextManager(temp_path)
                commands_indexer = CLICommandsIndexer()
                autocompleter = AdvancedCompleter()
                
                # Test that they all work together
                user_input = "Help me understand @main.py"
                cleaned_input, at_commands = context_manager.parse_at_commands(user_input)
                context_items = context_manager.process_at_commands(at_commands)
                cli_docs = commands_indexer.generate_commands_documentation()
                completions = autocompleter._complete_command("/help")
                
                # Verify integration
                assert len(at_commands) == 1
                assert len(context_items) >= 1
                assert len(cli_docs) > 0
                assert len(completions) >= 0
                
                # Test that context includes file content
                llm_context = context_manager.get_context_for_llm()
                assert "Hello World" in llm_context
                
            except ImportError:
                pytest.skip("Advanced features not available")

    def test_error_handling(self):
        """Test error handling in advanced features."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            try:
                from agent_swarm.cli.context_manager import ContextManager
                
                context_manager = ContextManager(temp_path)
                
                # Test with non-existent file
                user_input = "Check @nonexistent.py"
                cleaned_input, at_commands = context_manager.parse_at_commands(user_input)
                context_items = context_manager.process_at_commands(at_commands)
                
                # Should handle gracefully
                assert len(at_commands) == 1
                # Context items might be empty or contain error info
                assert isinstance(context_items, list)
                
            except ImportError:
                pytest.skip("Context manager not available")

    def test_performance(self):
        """Test performance of advanced features."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create many test files
            for i in range(10):
                (temp_path / f"file_{i}.py").write_text(f"# File {i}\nprint('File {i}')")
            
            try:
                from agent_swarm.cli.context_manager import ContextManager
                import time
                
                context_manager = ContextManager(temp_path)
                
                # Test processing multiple @ commands
                at_commands = [f"@file_{i}.py" for i in range(5)]
                
                start_time = time.time()
                context_items = context_manager.process_at_commands(at_commands)
                processing_time = time.time() - start_time
                
                # Should process reasonably quickly
                assert processing_time < 5.0, "Should process quickly"
                assert len(context_items) == 5, "Should process all files"
                
            except ImportError:
                pytest.skip("Context manager not available")
