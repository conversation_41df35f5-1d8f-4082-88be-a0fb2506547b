"""
Integration tests for complete agent workflows.
"""

import pytest
import tempfile
import asyncio
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

from agent_swarm.agents import CodingAgent, MultiLLMAgent, create_coding_agent
from agent_swarm.backends import <PERSON><PERSON><PERSON><PERSON>, LLMResponse, Message, LLMTier
from agent_swarm.context import UnifiedContext
from agent_swarm.mcp import MCPToolRegistry


@pytest.mark.integration
class TestCodingAgentWorkflows:
    """Test complete coding agent workflows."""

    @pytest.fixture
    def mock_llm_router(self):
        """Create comprehensive mock LLM router."""
        router = Mock(spec=LLMRouter)
        router.generate = AsyncMock(return_value=LLMResponse(
            content="I'll help you with that coding task. Here's my analysis...",
            model="test-model",
            usage={"prompt_tokens": 50, "completion_tokens": 100, "total_tokens": 150},
            metadata={"tier": "local"}
        ))
        router.route_request = AsyncMock(return_value=Mock())
        return router

    @pytest.fixture
    def sample_project(self):
        """Create a sample project structure."""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir)
            
            # Create project structure
            (project_root / "src").mkdir()
            (project_root / "src" / "__init__.py").write_text("")
            (project_root / "src" / "main.py").write_text("""
#!/usr/bin/env python3
\"\"\"
Main application module.
\"\"\"

def main():
    \"\"\"Main function.\"\"\"
    print("Hello, World!")
    return 0

def calculate_sum(a, b):
    \"\"\"Calculate sum of two numbers.\"\"\"
    return a + b

if __name__ == "__main__":
    exit(main())
""".strip())
            
            (project_root / "src" / "utils.py").write_text("""
\"\"\"
Utility functions.
\"\"\"

def format_output(text):
    \"\"\"Format output text.\"\"\"
    return f"[OUTPUT] {text}"

def validate_input(value):
    \"\"\"Validate input value.\"\"\"
    return isinstance(value, (int, float)) and value >= 0
""".strip())
            
            (project_root / "tests").mkdir()
            (project_root / "tests" / "__init__.py").write_text("")
            (project_root / "tests" / "test_main.py").write_text("""
import unittest
from src.main import main, calculate_sum

class TestMain(unittest.TestCase):
    def test_main(self):
        self.assertEqual(main(), 0)
    
    def test_calculate_sum(self):
        self.assertEqual(calculate_sum(2, 3), 5)
        self.assertEqual(calculate_sum(0, 0), 0)
        self.assertEqual(calculate_sum(-1, 1), 0)

if __name__ == '__main__':
    unittest.main()
""".strip())
            
            (project_root / "README.md").write_text("""
# Sample Project

A sample Python project for testing Agent Swarm.

## Features
- Main application module
- Utility functions
- Comprehensive tests

## Usage
```bash
python src/main.py
```
""".strip())
            
            (project_root / "requirements.txt").write_text("pytest>=7.0.0\nblack>=22.0.0\n")
            
            yield project_root

    @pytest.mark.asyncio
    async def test_coding_agent_creation_and_initialization(self, mock_llm_router, sample_project):
        """Test creating and initializing coding agent."""
        agent = CodingAgent(
            name="TestCodingAgent",
            llm_router=mock_llm_router,
            project_root=sample_project
        )
        
        assert agent.name == "TestCodingAgent"
        assert agent.llm_router == mock_llm_router
        assert agent.project_root == sample_project
        
        # Initialize agent
        await agent.initialize()
        
        # Should have context and tools available
        assert hasattr(agent, 'context')
        assert hasattr(agent, 'tools')

    @pytest.mark.asyncio
    async def test_code_analysis_workflow(self, mock_llm_router, sample_project):
        """Test complete code analysis workflow."""
        agent = CodingAgent(
            name="AnalysisAgent",
            llm_router=mock_llm_router,
            project_root=sample_project
        )
        
        await agent.initialize()
        
        # Test analyzing specific file
        analysis_request = "Analyze the main.py file and suggest improvements"
        
        response = await agent.process_request(analysis_request)
        
        assert isinstance(response, LLMResponse)
        assert response.content is not None
        
        # Should have called LLM with context about the file
        mock_llm_router.generate.assert_called()
        call_args = mock_llm_router.generate.call_args[0][0]
        
        # Should include file content in context
        assert any("main.py" in str(msg.content) for msg in call_args)

    @pytest.mark.asyncio
    async def test_code_generation_workflow(self, mock_llm_router, sample_project):
        """Test code generation workflow."""
        # Mock LLM to return code
        mock_llm_router.generate.return_value = LLMResponse(
            content="""I'll create a new utility function for you:

```python
def multiply_numbers(a, b):
    \"\"\"Multiply two numbers.\"\"\"
    return a * b
```

This function follows the same pattern as your existing calculate_sum function.""",
            model="test-model",
            usage={"prompt_tokens": 30, "completion_tokens": 80, "total_tokens": 110},
            metadata={}
        )
        
        agent = CodingAgent(
            name="GenerationAgent",
            llm_router=mock_llm_router,
            project_root=sample_project
        )
        
        await agent.initialize()
        
        # Request code generation
        generation_request = "Add a multiply_numbers function to utils.py"
        
        response = await agent.process_request(generation_request)
        
        assert isinstance(response, LLMResponse)
        assert "multiply_numbers" in response.content
        assert "```python" in response.content

    @pytest.mark.asyncio
    async def test_test_generation_workflow(self, mock_llm_router, sample_project):
        """Test test generation workflow."""
        mock_llm_router.generate.return_value = LLMResponse(
            content="""I'll create tests for the utils.py functions:

```python
import unittest
from src.utils import format_output, validate_input

class TestUtils(unittest.TestCase):
    def test_format_output(self):
        self.assertEqual(format_output("test"), "[OUTPUT] test")
        self.assertEqual(format_output(""), "[OUTPUT] ")
    
    def test_validate_input(self):
        self.assertTrue(validate_input(5))
        self.assertTrue(validate_input(0))
        self.assertFalse(validate_input(-1))
        self.assertFalse(validate_input("invalid"))

if __name__ == '__main__':
    unittest.main()
```""",
            model="test-model",
            usage={"prompt_tokens": 40, "completion_tokens": 120, "total_tokens": 160},
            metadata={}
        )
        
        agent = CodingAgent(
            name="TestAgent",
            llm_router=mock_llm_router,
            project_root=sample_project
        )
        
        await agent.initialize()
        
        # Request test generation
        test_request = "Generate tests for the utils.py module"
        
        response = await agent.process_request(test_request)
        
        assert isinstance(response, LLMResponse)
        assert "TestUtils" in response.content
        assert "unittest" in response.content

    @pytest.mark.asyncio
    async def test_debugging_workflow(self, mock_llm_router, sample_project):
        """Test debugging workflow."""
        # Create a file with a bug
        buggy_file = sample_project / "src" / "buggy.py"
        buggy_file.write_text("""
def divide_numbers(a, b):
    return a / b  # Bug: no zero division check

def process_list(items):
    result = []
    for i in range(len(items) + 1):  # Bug: off-by-one error
        result.append(items[i])
    return result
""".strip())
        
        mock_llm_router.generate.return_value = LLMResponse(
            content="""I found several issues in the code:

1. **Zero Division Error**: The `divide_numbers` function doesn't check for division by zero
2. **Index Error**: The `process_list` function has an off-by-one error

Here are the fixes:

```python
def divide_numbers(a, b):
    if b == 0:
        raise ValueError("Cannot divide by zero")
    return a / b

def process_list(items):
    result = []
    for i in range(len(items)):  # Fixed: removed +1
        result.append(items[i])
    return result
```""",
            model="test-model",
            usage={"prompt_tokens": 60, "completion_tokens": 150, "total_tokens": 210},
            metadata={}
        )
        
        agent = CodingAgent(
            name="DebugAgent",
            llm_router=mock_llm_router,
            project_root=sample_project
        )
        
        await agent.initialize()
        
        # Request debugging
        debug_request = "Find and fix bugs in src/buggy.py"
        
        response = await agent.process_request(debug_request)
        
        assert isinstance(response, LLMResponse)
        assert "Zero Division" in response.content or "division by zero" in response.content
        assert "off-by-one" in response.content or "Index Error" in response.content

    @pytest.mark.asyncio
    async def test_refactoring_workflow(self, mock_llm_router, sample_project):
        """Test code refactoring workflow."""
        mock_llm_router.generate.return_value = LLMResponse(
            content="""Here's a refactored version of the main.py file with improvements:

```python
#!/usr/bin/env python3
\"\"\"
Main application module with improved structure.
\"\"\"

import sys
from typing import Union

def main() -> int:
    \"\"\"Main function with improved error handling.\"\"\"
    try:
        print("Hello, World!")
        return 0
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        return 1

def calculate_sum(a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
    \"\"\"Calculate sum of two numbers with type hints.\"\"\"
    if not isinstance(a, (int, float)) or not isinstance(b, (int, float)):
        raise TypeError("Arguments must be numbers")
    return a + b

if __name__ == "__main__":
    sys.exit(main())
```

Improvements:
- Added type hints
- Better error handling
- Input validation
- More robust main function""",
            model="test-model",
            usage={"prompt_tokens": 80, "completion_tokens": 200, "total_tokens": 280},
            metadata={}
        )
        
        agent = CodingAgent(
            name="RefactorAgent",
            llm_router=mock_llm_router,
            project_root=sample_project
        )
        
        await agent.initialize()
        
        # Request refactoring
        refactor_request = "Refactor main.py to improve code quality and add type hints"
        
        response = await agent.process_request(refactor_request)
        
        assert isinstance(response, LLMResponse)
        assert "type hints" in response.content.lower()
        assert "Union[int, float]" in response.content

    @pytest.mark.asyncio
    async def test_project_overview_workflow(self, mock_llm_router, sample_project):
        """Test project overview and documentation workflow."""
        mock_llm_router.generate.return_value = LLMResponse(
            content="""# Project Analysis

## Structure Overview
- **src/**: Main source code directory
  - `main.py`: Entry point with main() and calculate_sum() functions
  - `utils.py`: Utility functions for formatting and validation
- **tests/**: Test directory
  - `test_main.py`: Unit tests for main module
- **README.md**: Project documentation
- **requirements.txt**: Dependencies

## Code Quality Assessment
- ✅ Good function documentation
- ✅ Clear module structure
- ✅ Unit tests present
- ⚠️ Could benefit from type hints
- ⚠️ Error handling could be improved

## Recommendations
1. Add type hints to all functions
2. Implement better error handling
3. Add more comprehensive tests
4. Consider adding logging
5. Add code formatting with Black""",
            model="test-model",
            usage={"prompt_tokens": 100, "completion_tokens": 250, "total_tokens": 350},
            metadata={}
        )
        
        agent = CodingAgent(
            name="OverviewAgent",
            llm_router=mock_llm_router,
            project_root=sample_project
        )
        
        await agent.initialize()
        
        # Request project overview
        overview_request = "Provide a comprehensive overview of this project"
        
        response = await agent.process_request(overview_request)
        
        assert isinstance(response, LLMResponse)
        assert "main.py" in response.content
        assert "utils.py" in response.content
        assert "Structure" in response.content or "structure" in response.content

    @pytest.mark.asyncio
    async def test_multi_step_workflow(self, mock_llm_router, sample_project):
        """Test multi-step development workflow."""
        agent = CodingAgent(
            name="MultiStepAgent",
            llm_router=mock_llm_router,
            project_root=sample_project
        )
        
        await agent.initialize()
        
        # Step 1: Analyze current code
        analysis_response = await agent.process_request("Analyze the current codebase")
        assert isinstance(analysis_response, LLMResponse)
        
        # Step 2: Generate new feature
        feature_response = await agent.process_request("Add a new feature to calculate product of numbers")
        assert isinstance(feature_response, LLMResponse)
        
        # Step 3: Generate tests
        test_response = await agent.process_request("Generate tests for the new feature")
        assert isinstance(test_response, LLMResponse)
        
        # All steps should have been processed
        assert mock_llm_router.generate.call_count == 3

    @pytest.mark.asyncio
    async def test_error_handling_in_workflow(self, mock_llm_router, sample_project):
        """Test error handling in agent workflows."""
        # Make LLM fail
        mock_llm_router.generate.side_effect = Exception("LLM service unavailable")
        
        agent = CodingAgent(
            name="ErrorAgent",
            llm_router=mock_llm_router,
            project_root=sample_project
        )
        
        await agent.initialize()
        
        # Should handle LLM errors gracefully
        with pytest.raises(Exception, match="LLM service unavailable"):
            await agent.process_request("Analyze this code")

    @pytest.mark.asyncio
    async def test_context_integration(self, mock_llm_router, sample_project):
        """Test integration with context system."""
        agent = CodingAgent(
            name="ContextAgent",
            llm_router=mock_llm_router,
            project_root=sample_project
        )
        
        await agent.initialize()
        
        # Test that context is properly integrated
        if hasattr(agent, 'context'):
            assert agent.context is not None
            
            # Test context retrieval
            if hasattr(agent.context, 'get_context'):
                context_result = await agent.context.get_context("main function")
                assert context_result is not None


@pytest.mark.integration
class TestMultiLLMAgentWorkflows:
    """Test MultiLLMAgent workflows."""

    @pytest.mark.asyncio
    async def test_multi_llm_routing(self):
        """Test routing between different LLMs."""
        # Create router with multiple LLMs
        router = LLMRouter()
        
        # Mock local LLM
        local_llm = Mock()
        local_llm.generate = AsyncMock(return_value=LLMResponse(
            content="Local LLM response",
            model="local-model",
            usage={},
            metadata={"tier": "local"}
        ))
        
        # Mock cloud LLM
        cloud_llm = Mock()
        cloud_llm.generate = AsyncMock(return_value=LLMResponse(
            content="Cloud LLM response",
            model="cloud-model",
            usage={},
            metadata={"tier": "cloud"}
        ))
        
        router.register_llm("local", local_llm)
        router.register_llm("cloud", cloud_llm)
        router.set_task_routing("simple", "local")
        router.set_task_routing("complex", "cloud")
        
        agent = MultiLLMAgent(
            name="MultiAgent",
            llm_router=router
        )
        
        # Test simple task routing
        simple_response = await agent.process_request("Simple task", task_type="simple")
        assert "Local LLM" in simple_response.content
        
        # Test complex task routing
        complex_response = await agent.process_request("Complex task", task_type="complex")
        assert "Cloud LLM" in complex_response.content

    @pytest.mark.asyncio
    async def test_fallback_mechanism(self):
        """Test LLM fallback mechanism."""
        router = LLMRouter()
        
        # Primary LLM that fails
        primary_llm = Mock()
        primary_llm.generate = AsyncMock(side_effect=Exception("Primary LLM failed"))
        
        # Fallback LLM that works
        fallback_llm = Mock()
        fallback_llm.generate = AsyncMock(return_value=LLMResponse(
            content="Fallback LLM response",
            model="fallback-model",
            usage={},
            metadata={}
        ))
        
        router.register_llm("primary", primary_llm)
        router.register_llm("fallback", fallback_llm)
        router.set_fallback_chain(["primary", "fallback"])
        router.set_default_llm("primary")
        
        agent = MultiLLMAgent(
            name="FallbackAgent",
            llm_router=router
        )
        
        # Should fallback to secondary LLM
        response = await agent.process_request("Test request")
        assert "Fallback LLM" in response.content
