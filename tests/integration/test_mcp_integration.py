"""
Integration tests for MCP (Model Context Protocol) functionality.
"""

import pytest
import tempfile
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

from agent_swarm.mcp import (
    MCPClient,
    MCPToolRegistry,
    MCPEnabledAgent,
    setup_default_mcp_tools,
    create_mcp_enabled_agent,
)
from agent_swarm.backends import <PERSON><PERSON><PERSON><PERSON>, LLMTier
from agent_swarm.mcp.tools import FileSystemTool, WebSearchTool, CodeExecutionTool


@pytest.mark.integration
class TestMCPEndToEnd:
    """End-to-end integration tests for MCP functionality."""

    @pytest.mark.asyncio
    async def test_complete_mcp_workflow(self):
        """Test complete MCP workflow from setup to tool execution."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create test files
            test_file = temp_path / "test_document.txt"
            test_file.write_text("This is a test document for MCP integration.")
            
            # Set up MCP registry with default tools
            registry = await setup_default_mcp_tools()
            
            # Test file operations
            write_result = await registry.call_tool("write_file", {
                "path": str(temp_path / "mcp_test.txt"),
                "content": "Hello from MCP integration test!"
            })
            
            assert write_result.success is True
            
            # Read the file back
            read_result = await registry.call_tool("read_file", {
                "path": str(temp_path / "mcp_test.txt")
            })
            
            assert read_result.success is True
            assert "Hello from MCP integration test!" in read_result.result["content"]
            
            # List directory
            list_result = await registry.call_tool("list_directory", {
                "path": str(temp_path)
            })
            
            assert list_result.success is True
            assert len(list_result.result["entries"]) >= 2  # test_document.txt and mcp_test.txt

    @pytest.mark.asyncio
    async def test_mcp_enabled_agent_integration(self):
        """Test MCP-enabled agent with tool calling."""
        # Create mock LLM router
        router = LLMRouter()
        
        # Create MCP-enabled agent
        agent = await create_mcp_enabled_agent(
            name="TestAgent",
            role="Test Assistant",
            llm_router=router
        )
        
        assert agent.mcp_registry is not None
        assert len(agent.mcp_registry.get_all_tools()) > 0
        
        # Test that agent has MCP tools available
        tools = agent.mcp_registry.get_all_tools()
        tool_names = [tool.name for tool in tools]
        
        assert "read_file" in tool_names
        assert "write_file" in tool_names
        assert "search_web" in tool_names

    @pytest.mark.asyncio
    async def test_concurrent_mcp_operations(self):
        """Test concurrent MCP tool operations."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            registry = await setup_default_mcp_tools()
            
            # Create multiple concurrent file operations
            tasks = []
            for i in range(5):
                task = registry.call_tool("write_file", {
                    "path": str(temp_path / f"concurrent_file_{i}.txt"),
                    "content": f"Concurrent content {i}"
                })
                tasks.append(task)
            
            # Execute all tasks concurrently
            results = await asyncio.gather(*tasks)
            
            # All should succeed
            assert len(results) == 5
            for result in results:
                assert result.success is True
            
            # Verify all files were created
            list_result = await registry.call_tool("list_directory", {
                "path": str(temp_path)
            })
            
            assert list_result.success is True
            assert len(list_result.result["entries"]) == 5

    @pytest.mark.asyncio
    async def test_mcp_error_recovery(self):
        """Test MCP error handling and recovery."""
        registry = await setup_default_mcp_tools()
        
        # Test with invalid file path
        result = await registry.call_tool("read_file", {
            "path": "/nonexistent/invalid/path.txt"
        })
        
        assert result.success is False
        assert "Failed to read file" in result.error
        
        # Test with invalid tool
        result = await registry.call_tool("nonexistent_tool", {})
        
        assert result.success is False
        assert "not found" in result.error

    @pytest.mark.asyncio
    async def test_mcp_tool_chaining(self):
        """Test chaining multiple MCP tool calls."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            registry = await setup_default_mcp_tools()
            
            # Step 1: Write a file
            write_result = await registry.call_tool("write_file", {
                "path": str(temp_path / "chain_test.txt"),
                "content": "Initial content"
            })
            assert write_result.success is True
            
            # Step 2: Read the file
            read_result = await registry.call_tool("read_file", {
                "path": str(temp_path / "chain_test.txt")
            })
            assert read_result.success is True
            content = read_result.result["content"]
            
            # Step 3: Modify and write back
            modified_content = content + "\nModified content"
            write_result2 = await registry.call_tool("write_file", {
                "path": str(temp_path / "chain_test.txt"),
                "content": modified_content
            })
            assert write_result2.success is True
            
            # Step 4: Verify modification
            read_result2 = await registry.call_tool("read_file", {
                "path": str(temp_path / "chain_test.txt")
            })
            assert read_result2.success is True
            assert "Modified content" in read_result2.result["content"]


@pytest.mark.integration
class TestMCPServerIntegration:
    """Test MCP server management integration."""

    @pytest.mark.asyncio
    async def test_multiple_server_management(self):
        """Test managing multiple MCP servers."""
        from agent_swarm.mcp.server import MCPServerManager
        
        manager = MCPServerManager()
        
        # Start multiple servers
        await manager.start_server("filesystem", {"type": "filesystem", "root": "/tmp"})
        await manager.start_server("web_search", {"type": "web_search", "api_key": "test"})
        
        # Check server status
        assert len(manager.list_servers()) == 2
        assert manager.get_server_status("filesystem")["status"] == "running"
        assert manager.get_server_status("web_search")["status"] == "running"
        
        # Stop one server
        await manager.stop_server("filesystem")
        assert manager.get_server_status("filesystem")["status"] == "stopped"
        
        # Restart server
        await manager.restart_server("filesystem")
        assert manager.get_server_status("filesystem")["status"] == "running"

    @pytest.mark.asyncio
    async def test_server_client_integration(self):
        """Test integration between MCP servers and clients."""
        from agent_swarm.mcp.server import MCPServerManager
        
        # Start server
        manager = MCPServerManager()
        await manager.start_server("test_server", {"type": "filesystem"})
        
        # Connect client to server
        client = MCPClient()
        await client.initialize()
        await client.connect_server("test_server", {"type": "filesystem"})
        
        # Verify connection
        assert "test_server" in client.servers
        assert client.servers["test_server"]["connected"] is True
        
        # Test tool availability
        tools = client.get_available_tools()
        assert len(tools) > 0


@pytest.mark.integration
class TestMCPPerformance:
    """Test MCP performance characteristics."""

    @pytest.mark.asyncio
    async def test_tool_call_performance(self):
        """Test performance of MCP tool calls."""
        import time
        
        registry = await setup_default_mcp_tools()
        
        # Measure single tool call performance
        start_time = time.time()
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("Performance test content")
            temp_path = f.name
        
        try:
            result = await registry.call_tool("read_file", {"path": temp_path})
            end_time = time.time()
            
            assert result.success is True
            call_duration = end_time - start_time
            
            # Should complete reasonably quickly (adjust threshold as needed)
            assert call_duration < 1.0, f"Tool call took {call_duration:.2f}s, expected < 1.0s"
            
        finally:
            Path(temp_path).unlink()

    @pytest.mark.asyncio
    async def test_concurrent_performance(self):
        """Test performance under concurrent load."""
        import time
        
        registry = await setup_default_mcp_tools()
        
        # Create multiple concurrent operations
        num_operations = 10
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            start_time = time.time()
            
            # Create concurrent write operations
            tasks = [
                registry.call_tool("write_file", {
                    "path": str(temp_path / f"perf_test_{i}.txt"),
                    "content": f"Performance test content {i}"
                })
                for i in range(num_operations)
            ]
            
            results = await asyncio.gather(*tasks)
            end_time = time.time()
            
            # All should succeed
            assert all(result.success for result in results)
            
            total_duration = end_time - start_time
            avg_duration = total_duration / num_operations
            
            # Should handle concurrent operations efficiently
            assert avg_duration < 0.5, f"Average operation took {avg_duration:.2f}s, expected < 0.5s"

    @pytest.mark.asyncio
    async def test_memory_usage(self):
        """Test memory usage of MCP operations."""
        import gc
        import sys
        
        registry = await setup_default_mcp_tools()
        
        # Get initial memory usage
        gc.collect()
        initial_objects = len(gc.get_objects())
        
        # Perform many operations
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            for i in range(100):
                result = await registry.call_tool("write_file", {
                    "path": str(temp_path / f"memory_test_{i}.txt"),
                    "content": f"Memory test content {i}"
                })
                assert result.success is True
        
        # Check memory usage after operations
        gc.collect()
        final_objects = len(gc.get_objects())
        
        # Memory usage should not grow excessively
        object_growth = final_objects - initial_objects
        assert object_growth < 1000, f"Memory grew by {object_growth} objects, expected < 1000"


@pytest.mark.integration
class TestMCPRealWorldScenarios:
    """Test MCP in real-world usage scenarios."""

    @pytest.mark.asyncio
    async def test_development_workflow(self):
        """Test MCP in a typical development workflow."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            registry = await setup_default_mcp_tools()
            
            # Scenario: Create a Python project structure
            
            # 1. Create main.py
            main_content = '''#!/usr/bin/env python3
"""
Main application file.
"""

def main():
    print("Hello, World!")
    return 0

if __name__ == "__main__":
    exit(main())
'''
            
            result = await registry.call_tool("write_file", {
                "path": str(temp_path / "main.py"),
                "content": main_content
            })
            assert result.success is True
            
            # 2. Create requirements.txt
            requirements = "pytest>=7.0.0\nrequests>=2.28.0\n"
            result = await registry.call_tool("write_file", {
                "path": str(temp_path / "requirements.txt"),
                "content": requirements
            })
            assert result.success is True
            
            # 3. Create README.md
            readme = "# My Project\n\nA sample Python project.\n"
            result = await registry.call_tool("write_file", {
                "path": str(temp_path / "README.md"),
                "content": readme
            })
            assert result.success is True
            
            # 4. Verify project structure
            list_result = await registry.call_tool("list_directory", {
                "path": str(temp_path)
            })
            assert list_result.success is True
            
            files = [entry["name"] for entry in list_result.result["entries"]]
            assert "main.py" in files
            assert "requirements.txt" in files
            assert "README.md" in files
            
            # 5. Read and verify file contents
            main_result = await registry.call_tool("read_file", {
                "path": str(temp_path / "main.py")
            })
            assert main_result.success is True
            assert "Hello, World!" in main_result.result["content"]

    @pytest.mark.asyncio
    async def test_data_processing_workflow(self):
        """Test MCP in a data processing workflow."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            registry = await setup_default_mcp_tools()
            
            # Scenario: Process CSV data
            
            # 1. Create sample CSV data
            csv_data = """name,age,city
John Doe,30,New York
Jane Smith,25,Los Angeles
Bob Johnson,35,Chicago
"""
            
            result = await registry.call_tool("write_file", {
                "path": str(temp_path / "data.csv"),
                "content": csv_data
            })
            assert result.success is True
            
            # 2. Read and process the data (simulated)
            read_result = await registry.call_tool("read_file", {
                "path": str(temp_path / "data.csv")
            })
            assert read_result.success is True
            
            # 3. Create processed output
            processed_data = "# Processed Data\n\nTotal records: 3\nAverage age: 30\n"
            result = await registry.call_tool("write_file", {
                "path": str(temp_path / "processed_data.md"),
                "content": processed_data
            })
            assert result.success is True
            
            # 4. Verify processing results
            verify_result = await registry.call_tool("read_file", {
                "path": str(temp_path / "processed_data.md")
            })
            assert verify_result.success is True
            assert "Total records: 3" in verify_result.result["content"]
