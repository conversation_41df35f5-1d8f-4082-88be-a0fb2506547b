[tool:pytest]
# Pytest configuration for Agent Swarm

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --color=yes
    --disable-warnings

# Asyncio configuration
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    mcp: MCP-related tests
    slow: Slow tests (may take more than 1 second)
    requires_ollama: Tests that require Ollama service
    requires_api_keys: Tests that require cloud API keys
    requires_network: Tests that require network access

# Test filtering
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:urllib3.*

# Coverage settings (when using --cov)
# These are used by pytest-cov plugin
[coverage:run]
source = src/agent_swarm
omit =
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*
    */.venv/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = true
precision = 2
skip_covered = false

[coverage:html]
directory = htmlcov

[coverage:xml]
output = coverage.xml
