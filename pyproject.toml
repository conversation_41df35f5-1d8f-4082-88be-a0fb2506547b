[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "agent-swarm"
dynamic = ["version"]
description = "Multi-LLM Agent Swarm Development Framework"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Agent Swarm Contributors"},
]
keywords = ["ai", "agents", "llm", "swarm", "multi-agent"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.10"
dependencies = [
    "aiohttp>=3.9.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "rich>=13.0.0",
    "typer>=0.9.0",
    "psutil>=5.9.0",
    "pyyaml>=6.0.0",
    # Critical dependencies added from code review
    "tiktoken>=0.5.0",
    "gitpython>=3.1.0",
    "scikit-learn>=1.0.0",
    "aiofiles>=23.0.0",
]

[project.optional-dependencies]
cloud = [
    "anthropic>=0.25.0",
    "google-generativeai>=0.5.0",
    "openai>=1.0.0",
]
autogen = [
    "autogen-agentchat>=0.4.0",
]
mcp = [
    "mcp>=1.0.0",
    "httpx>=0.24.0",
]
cli = [
    "psutil>=5.9.0",
]
rag = [
    "chromadb>=0.4.0",
    "sentence-transformers>=2.2.0",
    "numpy>=1.24.0",
]
editing = [
    "unidiff>=0.7.0",
    "chardet>=5.0.0",
    "watchdog>=3.0.0",
    "tree-sitter>=0.20.0",
    "tree-sitter-python>=0.20.0",
    "tree-sitter-javascript>=0.20.0",
    "tree-sitter-typescript>=0.20.0",
]
all = [
    "anthropic>=0.25.0",
    "google-generativeai>=0.5.0",
    "openai>=1.0.0",
    "autogen-agentchat>=0.4.0",
    "mcp>=1.0.0",
    "httpx>=0.24.0",
    "psutil>=5.9.0",
    "chromadb>=0.4.0",
    "sentence-transformers>=2.2.0",
    "numpy>=1.24.0",
    "unidiff>=0.7.0",
    "chardet>=5.0.0",
    "watchdog>=3.0.0",
    "tree-sitter>=0.20.0",
    "tree-sitter-python>=0.20.0",
    "tree-sitter-javascript>=0.20.0",
    "tree-sitter-typescript>=0.20.0",
]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.0.0",
    "httpx>=0.24.0",  # For testing HTTP clients
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.0.0",
    "mkdocstrings[python]>=0.22.0",
]

[project.urls]
Homepage = "https://github.com/your-username/agent-swarm"
Documentation = "https://agent-swarm.readthedocs.io"
Repository = "https://github.com/your-username/agent-swarm"
Issues = "https://github.com/your-username/agent-swarm/issues"

[project.scripts]
agent-swarm = "agent_swarm.cli.interactive_shell:cli_main"
agent-swarm-shell = "agent_swarm.cli.interactive_shell:cli_main"

[tool.hatch.version]
path = "src/agent_swarm/_version.py"

[tool.hatch.build.targets.wheel]
packages = ["src/agent_swarm"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/tests",
    "/examples",
    "/docs",
    "/README.md",
    "/LICENSE",
]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["agent_swarm"]

# mypy configuration
[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "anthropic.*",
    "google.generativeai.*",
    "ollama.*",
]
ignore_missing_imports = true

# pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=agent_swarm",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow tests",
    "requires_ollama: Tests that require Ollama service",
    "requires_api_keys: Tests that require cloud API keys",
]

# Coverage configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/examples/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
