# Agent Swarm .gitattributes
# Git attributes for proper handling of different file types

# ============================================================================
# Text Files - Ensure consistent line endings
# ============================================================================

# Source code
*.py text eol=lf
*.pyx text eol=lf
*.pyi text eol=lf
*.pxd text eol=lf

# Configuration files
*.toml text eol=lf
*.yaml text eol=lf
*.yml text eol=lf
*.json text eol=lf
*.ini text eol=lf
*.cfg text eol=lf
*.conf text eol=lf

# Documentation
*.md text eol=lf
*.rst text eol=lf
*.txt text eol=lf

# Web files
*.html text eol=lf
*.css text eol=lf
*.js text eol=lf
*.ts text eol=lf

# Shell scripts
*.sh text eol=lf
*.bash text eol=lf
*.zsh text eol=lf
*.fish text eol=lf

# Batch files (Windows)
*.bat text eol=crlf
*.cmd text eol=crlf
*.ps1 text eol=crlf

# Makefiles
Makefile text eol=lf
*.mk text eol=lf

# Docker
Dockerfile text eol=lf
*.dockerfile text eol=lf
docker-compose*.yml text eol=lf

# CI/CD
.github/**/* text eol=lf
.gitlab-ci.yml text eol=lf
*.jenkinsfile text eol=lf

# ============================================================================
# Binary Files - Treat as binary
# ============================================================================

# Images
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg binary
*.webp binary

# Audio/Video
*.mp3 binary
*.mp4 binary
*.wav binary
*.avi binary
*.mov binary

# Archives
*.zip binary
*.tar binary
*.gz binary
*.bz2 binary
*.xz binary
*.7z binary
*.rar binary

# Documents
*.pdf binary
*.doc binary
*.docx binary
*.xls binary
*.xlsx binary
*.ppt binary
*.pptx binary

# ============================================================================
# AI/ML Model Files - Binary and Large File Handling
# ============================================================================

# Model files
*.pkl binary
*.pickle binary
*.joblib binary
*.h5 binary
*.hdf5 binary
*.pb binary
*.pth binary
*.pt binary
*.ckpt binary
*.safetensors binary
*.gguf binary
*.bin binary

# Data files
*.npy binary
*.npz binary
*.parquet binary
*.feather binary
*.arrow binary

# ============================================================================
# Language-specific attributes
# ============================================================================

# Python
*.py linguist-language=Python
*.pyi linguist-language=Python
*.pyx linguist-language=Python

# Jupyter Notebooks
*.ipynb linguist-language=Jupyter-Notebook

# ============================================================================
# Git LFS (Large File Storage) - for large model files
# ============================================================================

# Uncomment these lines if you want to use Git LFS for large files
# *.pkl filter=lfs diff=lfs merge=lfs -text
# *.pth filter=lfs diff=lfs merge=lfs -text
# *.h5 filter=lfs diff=lfs merge=lfs -text
# *.gguf filter=lfs diff=lfs merge=lfs -text
# *.bin filter=lfs diff=lfs merge=lfs -text

# ============================================================================
# Diff and Merge Settings
# ============================================================================

# Don't diff binary files
*.pkl -diff
*.pickle -diff
*.pth -diff
*.h5 -diff
*.bin -diff
*.gguf -diff

# Use specific diff drivers for certain files
*.json diff=json
*.yaml diff=yaml
*.yml diff=yaml

# ============================================================================
# Export-ignore (files to exclude from git archive)
# ============================================================================

# Development files
.gitignore export-ignore
.gitattributes export-ignore
.pre-commit-config.yaml export-ignore
tests/ export-ignore
docs/ export-ignore
scripts/dev_setup.py export-ignore
scripts/check_status.py export-ignore

# ============================================================================
# Linguist overrides (for GitHub language detection)
# ============================================================================

# Mark certain directories as documentation
docs/* linguist-documentation
examples/* linguist-documentation

# Mark test files
tests/* linguist-detectable=false

# Mark generated files
src/agent_swarm/_version.py linguist-generated=true
