# Include base requirements
-r requirements.txt

# Development dependencies
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
httpx>=0.24.0

# Code quality
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0
pre-commit>=3.0.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.0.0
mkdocstrings[python]>=0.22.0

# Optional cloud providers for testing
anthropic>=0.25.0
google-generativeai>=0.5.0
openai>=1.0.0
