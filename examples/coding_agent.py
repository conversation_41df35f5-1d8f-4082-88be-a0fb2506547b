#!/usr/bin/env python3
"""
🤖 Coding Agent - Real Development Workflows

Demonstrates Agent Swarm's coding capabilities with real-world scenarios:
- Feature implementation with context awareness
- Intelligent bug fixing and debugging
- Code review and optimization
- Multi-step development workflows

Prerequisites:
- Python 3.8+
- Ollama with coding models (qwen2.5-coder:7b recommended)
- Run: pip install -e .

Usage:
    python examples/coding_agent.py
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm.agents.coding_agent import create_coding_agent
from agent_swarm import setup_logging


async def demo_feature_implementation():
    """Demonstrate intelligent feature implementation."""
    print("🚀 Feature Implementation Demo")
    print("=" * 50)

    # Create coding agent with full capabilities
    project_path = str(Path(__file__).parent.parent)
    
    print(f"🔧 Creating coding agent for project: {Path(project_path).name}")
    coding_agent = await create_coding_agent(
        name="FeatureDev",
        project_path=project_path,
        enable_rag=True  # Enable codebase context
    )

    print("✅ Coding agent ready with:")
    print("   • Multi-LLM routing (local + cloud)")
    print("   • RAG context from codebase")
    print("   • MCP tool integration")
    print("   • CLI development tools")

    # Feature to implement
    feature_description = """
    Create a simple task manager module with:
    - Task class with id, title, description, status
    - TaskManager class to add, remove, list, and update tasks
    - Save/load tasks to/from JSON file
    - Include proper error handling and type hints
    """

    target_file = "examples/task_manager.py"

    print(f"\n📝 Implementing feature:")
    print(f"   Description: Task manager with persistence")
    print(f"   Target file: {target_file}")
    print(f"   Requirements: Type hints, error handling, JSON persistence")

    # Implement the feature
    print("\n⚡ Starting implementation...")
    session = await coding_agent.implement_feature(
        feature_description=feature_description,
        file_path=target_file,
        test_required=True
    )

    # Show results
    print(f"\n📊 Implementation Results:")
    print(f"   Success: {'✅' if session['success'] else '❌'}")
    print(f"   Files created: {len(session.get('files_created', []))}")
    print(f"   Steps completed: {len(session.get('steps', []))}")

    # Show implementation steps
    print(f"\n🔍 Implementation Steps:")
    for i, step in enumerate(session.get('steps', []), 1):
        step_name = step['step'].replace('_', ' ').title()
        status = "✅" if step.get('success', False) else "❌"
        print(f"   {i}. {step_name} {status}")

    return session


async def demo_bug_fixing():
    """Demonstrate intelligent bug fixing."""
    print("\n🐛 Bug Fixing Demo")
    print("=" * 50)

    project_path = str(Path(__file__).parent.parent)
    
    coding_agent = await create_coding_agent(
        name="BugFixer",
        project_path=project_path,
        enable_rag=True
    )

    # Create a buggy file for demonstration
    buggy_code = '''
def calculate_average(numbers):
    """Calculate the average of a list of numbers."""
    total = sum(numbers)
    return total / len(numbers)  # Bug: Division by zero if empty list

def process_scores(scores):
    """Process a list of test scores."""
    if not scores:
        return {"message": "No scores provided"}
    
    # Bug: This will crash if scores is empty due to calculate_average
    avg = calculate_average(scores)
    return {
        "average": avg,
        "count": len(scores),
        "grade": "A" if avg >= 90 else "B" if avg >= 80 else "C"
    }
'''

    buggy_file = "examples/buggy_scores.py"

    # Write buggy code
    print(f"📝 Creating buggy file: {buggy_file}")
    write_result = await coding_agent.mcp_registry.call_tool("write_file", {
        "path": buggy_file,
        "content": buggy_code
    })

    if not write_result.success:
        print("❌ Failed to create buggy file")
        return

    # Define the bug
    bug_description = "Division by zero error when processing empty score list"
    error_message = "ZeroDivisionError: division by zero"

    print(f"\n🔍 Bug to fix:")
    print(f"   Description: {bug_description}")
    print(f"   Error: {error_message}")
    print(f"   File: {buggy_file}")

    # Fix the bug
    print("\n⚡ Starting bug fix...")
    fix_session = await coding_agent.fix_bug(
        bug_description=bug_description,
        file_path=buggy_file,
        error_message=error_message
    )

    # Show results
    print(f"\n📊 Bug Fix Results:")
    print(f"   Success: {'✅' if fix_session['success'] else '❌'}")
    print(f"   Files modified: {len(fix_session.get('files_modified', []))}")

    return fix_session


async def demo_code_review():
    """Demonstrate code review capabilities."""
    print("\n📋 Code Review Demo")
    print("=" * 50)

    project_path = str(Path(__file__).parent.parent)
    
    coding_agent = await create_coding_agent(
        name="CodeReviewer",
        project_path=project_path,
        enable_rag=True
    )

    # Code to review
    review_code = '''
def user_login(username, password):
    # TODO: Add proper validation
    if username == "admin" and password == "password123":
        return True
    return False

def get_user_data(user_id):
    # Direct database query - potential SQL injection
    query = f"SELECT * FROM users WHERE id = {user_id}"
    # ... database execution code ...
    return query

class UserManager:
    def __init__(self):
        self.users = []
    
    def add_user(self, user):
        self.users.append(user)  # No validation
    
    def find_user(self, username):
        for user in self.users:
            if user.username == username:
                return user
        return None  # Could use more efficient lookup
'''

    review_file = "examples/code_to_review.py"

    # Write code to review
    print(f"📝 Creating code for review: {review_file}")
    await coding_agent.mcp_registry.call_tool("write_file", {
        "path": review_file,
        "content": review_code
    })

    # Perform code review
    print("\n⚡ Starting code review...")
    review_session = await coding_agent.review_code(
        file_path=review_file,
        focus_areas=["security", "performance", "best_practices"]
    )

    # Show review results
    print(f"\n📊 Code Review Results:")
    print(f"   Issues found: {len(review_session.get('issues', []))}")
    print(f"   Suggestions: {len(review_session.get('suggestions', []))}")

    # Show top issues
    issues = review_session.get('issues', [])
    if issues:
        print(f"\n🚨 Top Issues Found:")
        for i, issue in enumerate(issues[:3], 1):
            severity = issue.get('severity', 'medium')
            print(f"   {i}. [{severity.upper()}] {issue.get('description', 'Unknown issue')}")

    return review_session


async def demo_development_workflow():
    """Demonstrate complete development workflow."""
    print("\n🌊 Development Workflow Demo")
    print("=" * 50)

    project_path = str(Path(__file__).parent.parent)
    
    coding_agent = await create_coding_agent(
        name="WorkflowBot",
        project_path=project_path,
        enable_rag=True
    )

    print("📋 Simulating complete development workflow:")
    print("   1. 📝 Feature planning and analysis")
    print("   2. 🔍 Codebase context gathering")
    print("   3. 💻 Implementation with tests")
    print("   4. 🧪 Validation and testing")
    print("   5. 📋 Code review and optimization")

    # Workflow: Build a simple web scraper
    feature_spec = """
    Create a web scraper utility with:
    - Function to fetch webpage content
    - Function to extract links from HTML
    - Function to save results to file
    - Proper error handling for network issues
    - Rate limiting to be respectful
    """

    print(f"\n⚡ Executing workflow...")
    
    # Step 1-4: Feature implementation
    implementation = await coding_agent.implement_feature(
        feature_description=feature_spec,
        file_path="examples/web_scraper.py",
        test_required=True
    )

    # Step 5: Code review
    if implementation['success']:
        review = await coding_agent.review_code(
            file_path="examples/web_scraper.py",
            focus_areas=["security", "performance", "error_handling"]
        )
    else:
        review = {"success": False}

    # Workflow summary
    print(f"\n📊 Workflow Summary:")
    print(f"   Implementation: {'✅' if implementation['success'] else '❌'}")
    print(f"   Code review: {'✅' if review['success'] else '❌'}")
    print(f"   Files created: {len(implementation.get('files_created', []))}")
    
    if review.get('issues'):
        print(f"   Review issues: {len(review['issues'])}")

    return {"implementation": implementation, "review": review}


async def show_agent_capabilities():
    """Show comprehensive agent capabilities."""
    print("\n🎯 Agent Capabilities Overview")
    print("=" * 50)

    project_path = str(Path(__file__).parent.parent)
    
    coding_agent = await create_coding_agent(
        name="CapabilityDemo",
        project_path=project_path,
        enable_rag=True
    )

    print("🔍 Analyzing agent capabilities...")

    # Tool integration
    mcp_tools = coding_agent.mcp_registry.get_all_tools()
    print(f"\n🛠️  Tool Integration:")
    print(f"   MCP tools: {len(mcp_tools)}")
    print(f"   CLI tools: ✅")
    print(f"   RAG context: {'✅' if coding_agent.dev_rag else '❌'}")

    # RAG capabilities
    if coding_agent.dev_rag:
        rag_stats = await coding_agent.dev_rag.get_project_stats()
        print(f"\n🧠 RAG Capabilities:")
        print(f"   Files indexed: {rag_stats['total_files_indexed']}")
        print(f"   Languages: {list(rag_stats.get('languages', {}).keys())[:5]}")

    # LLM routing
    router_stats = coding_agent.llm_router.get_stats()
    print(f"\n🤖 LLM Routing:")
    print(f"   Available models: {len(router_stats.get('registered_llms', []))}")

    print(f"\n✨ Key Features:")
    print("   • Context-aware code generation")
    print("   • Intelligent bug detection and fixing")
    print("   • Automated testing and validation")
    print("   • Code review with security analysis")
    print("   • Multi-step workflow orchestration")
    print("   • Cost-optimized model selection")


def show_next_steps():
    """Show what users can do next."""
    print(f"\n🎯 What's Next?")
    print("=" * 20)
    print("🚀 You've seen Agent Swarm's coding power! Here's how to use it:")
    print()
    print("🛠️  Integration Options:")
    print("   • IDE plugins (VS Code, PyCharm)")
    print("   • CI/CD pipeline integration")
    print("   • Git hooks for automated review")
    print("   • Custom development workflows")
    print()
    print("📚 More Examples:")
    print("   python examples/interactive_shell.py  # Interactive development")
    print("   python examples/context_engine.py     # Smart project analysis")
    print("   python examples/mcp_integration.py    # Advanced tool integration")
    print()
    print("🔧 Customization:")
    print("   • Configure models for your use case")
    print("   • Add custom tools and integrations")
    print("   • Train on your codebase patterns")
    print("   • Scale with cloud vector databases")


async def main():
    """Main demo function."""
    print("🤖 Agent Swarm Coding Agent Demo")
    print("🎯 Real Development Workflows")
    print()

    setup_logging(level="INFO")

    try:
        # Run all demos
        await demo_feature_implementation()
        await demo_bug_fixing()
        await demo_code_review()
        await demo_development_workflow()
        await show_agent_capabilities()
        
        show_next_steps()

        print("\n🎉 Coding Agent Demo Complete!")
        print("\n💡 Ready to revolutionize your development workflow!")

    except KeyboardInterrupt:
        print("\n👋 Demo interrupted. Thanks for exploring!")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Install dependencies: pip install -e .")
        print("2. Set up coding models: ollama pull qwen2.5-coder:7b")
        print("3. Run setup: agent-swarm setup")


if __name__ == "__main__":
    asyncio.run(main())
