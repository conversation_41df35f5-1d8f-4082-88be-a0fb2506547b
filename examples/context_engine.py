#!/usr/bin/env python3
"""
🧠 Context Engine - Smart Project Understanding

Demonstrates Agent Swarm's intelligent context capabilities:
- Project structure analysis and understanding
- RAG-powered code search and retrieval
- Intent detection and smart context assembly
- Multi-speed response modes for different needs

Prerequisites:
- Python 3.8+
- Project with code files to analyze
- Run: pip install -e .

Usage:
    python examples/context_engine.py
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm.context import UnifiedContext, QueryIntent, ResponseMode, ContextDepth
from agent_swarm import setup_logging


async def demo_project_analysis():
    """Demonstrate intelligent project analysis."""
    print("🧠 Project Analysis Demo")
    print("=" * 50)
    
    project_path = str(Path(__file__).parent.parent)
    
    print(f"📁 Analyzing project: {Path(project_path).name}")
    print("🔧 Initializing unified context engine...")
    
    # Create unified context engine
    context = UnifiedContext(project_path)
    await context.initialize(
        enable_rag=True,
        enable_dev_features=True,
        auto_index=True  # Automatically index the project
    )
    
    print("✅ Context engine initialized")
    
    # Get project analysis
    project_analysis = await context.get_project_analysis()
    
    if project_analysis:
        print(f"\n📊 Project Analysis Results:")
        print(f"   Project Type: {project_analysis.project_type.value}")
        print(f"   Main Language: {project_analysis.main_language}")
        print(f"   Frameworks: {', '.join(project_analysis.frameworks[:5])}")
        print(f"   Core Modules: {len(project_analysis.core_modules)}")
        print(f"   Entry Points: {', '.join(project_analysis.entry_points[:3])}")
        print(f"   Confidence Score: {project_analysis.confidence_score:.2f}")
        
        if project_analysis.dependencies:
            print(f"   Dependencies: {len(project_analysis.dependencies)} detected")
    
    # Get system statistics
    stats = await context.get_stats()
    print(f"\n🔧 Context System Stats:")
    print(f"   Components active: {len([c for c in stats['components'].values() if c])}")
    
    if stats.get('indexing'):
        indexing_stats = stats['indexing']
        print(f"   Files indexed: {indexing_stats['total_files_indexed']}")
        print(f"   Languages: {list(indexing_stats.get('languages', {}).keys())[:5]}")
    
    return context


async def demo_smart_context_retrieval(context):
    """Demonstrate smart context retrieval with different intents."""
    print("\n🎯 Smart Context Retrieval Demo")
    print("=" * 50)
    
    # Test queries with different intents
    test_queries = [
        {
            "query": "How does the agent system work?",
            "intent": QueryIntent.UNDERSTAND,
            "mode": ResponseMode.DETAILED,
            "depth": ContextDepth.MEDIUM
        },
        {
            "query": "Find authentication code",
            "intent": QueryIntent.FEATURE,
            "mode": ResponseMode.FAST,
            "depth": ContextDepth.SHALLOW
        },
        {
            "query": "Debug import error in context module",
            "intent": QueryIntent.DEBUG,
            "mode": ResponseMode.COMPREHENSIVE,
            "depth": ContextDepth.DEEP
        },
        {
            "query": "Show me the CLI implementation",
            "intent": QueryIntent.GENERAL,
            "mode": ResponseMode.DETAILED,
            "depth": ContextDepth.MEDIUM
        }
    ]
    
    for i, test in enumerate(test_queries, 1):
        print(f"\n🔍 Query {i}: {test['query']}")
        print(f"   Intent: {test['intent'].value}")
        print(f"   Mode: {test['mode'].value}")
        print(f"   Depth: {test['depth'].value}")
        
        # Get smart context
        results = await context.get_context(
            query=test['query'],
            intent=test['intent'],
            response_mode=test['mode'],
            context_depth=test['depth'],
            max_results=3
        )
        
        print(f"   Results: {len(results)} context items found")
        
        # Show top results
        for j, result in enumerate(results[:2], 1):
            print(f"     {j}. {result.file_path}")
            print(f"        Relevance: {result.relevance_score:.2f}")
            print(f"        Priority: {result.priority.value}")
            print(f"        Type: {result.context_type}")
            if result.summary:
                print(f"        Summary: {result.summary[:80]}...")


async def demo_code_search(context):
    """Demonstrate development-aware code search."""
    print("\n💻 Code Search Demo")
    print("=" * 50)
    
    # Test different types of code searches
    search_tests = [
        {
            "query": "async def",
            "language": "python",
            "description": "Find async functions"
        },
        {
            "query": "class.*Agent",
            "language": "python", 
            "description": "Find Agent classes"
        },
        {
            "query": "import.*context",
            "language": "python",
            "description": "Find context imports"
        },
        {
            "query": "def.*test",
            "language": "python",
            "description": "Find test functions"
        }
    ]
    
    for i, test in enumerate(search_tests, 1):
        print(f"\n🔍 Search {i}: {test['description']}")
        print(f"   Query: {test['query']}")
        print(f"   Language: {test['language']}")
        
        # Search code
        results = await context.search_code(
            query=test['query'],
            language=test['language'],
            max_results=5
        )
        
        print(f"   Found: {len(results)} matches")
        
        # Show top matches
        for j, result in enumerate(results[:3], 1):
            file_path = result.metadata.get('file_path', 'unknown')
            print(f"     {j}. {file_path}")
            if result.content:
                # Show first line of content
                first_line = result.content.split('\n')[0].strip()
                print(f"        {first_line[:60]}...")


async def demo_response_modes(context):
    """Demonstrate different response modes and their performance."""
    print("\n⚡ Response Modes Demo")
    print("=" * 50)
    
    query = "Explain the context engine architecture"
    
    modes = [
        (ResponseMode.FAST, ContextDepth.SHALLOW, "Quick overview"),
        (ResponseMode.DETAILED, ContextDepth.MEDIUM, "Balanced analysis"),
        (ResponseMode.COMPREHENSIVE, ContextDepth.DEEP, "Deep dive")
    ]
    
    for mode, depth, description in modes:
        print(f"\n🎚️  Mode: {mode.value.upper()} ({description})")
        print(f"   Depth: {depth.value}")
        
        import time
        start_time = time.time()
        
        # Get context with different modes
        results = await context.get_context(
            query=query,
            intent=QueryIntent.UNDERSTAND,
            response_mode=mode,
            context_depth=depth,
            max_results=5 if mode == ResponseMode.COMPREHENSIVE else 3
        )
        
        end_time = time.time()
        
        print(f"   Results: {len(results)} items")
        print(f"   Time: {end_time - start_time:.2f}s")
        
        # Show result quality indicators
        if results:
            avg_relevance = sum(r.relevance_score for r in results) / len(results)
            high_priority = sum(1 for r in results if r.priority.value in ['high', 'critical'])
            print(f"   Avg relevance: {avg_relevance:.2f}")
            print(f"   High priority: {high_priority}/{len(results)}")


async def demo_context_features(context):
    """Demonstrate advanced context features."""
    print("\n✨ Advanced Features Demo")
    print("=" * 50)
    
    # Test related files functionality
    print("🔗 Related Files Analysis:")
    test_file = "src/agent_swarm/context/engine.py"
    
    try:
        related = await context.get_related_files(test_file)
        print(f"   File: {test_file}")
        print(f"   Related files: {len(related)}")
        
        for i, rel_file in enumerate(related[:3], 1):
            file_path = rel_file.metadata.get('file_path', 'unknown')
            print(f"     {i}. {file_path}")
    
    except Exception as e:
        print(f"   ⚠️  Related files not available: {e}")
    
    # Test code explanation
    print(f"\n💡 Code Explanation:")
    code_snippet = '''
async def get_context(self, query: str, intent: QueryIntent):
    """Get intelligent context for a query."""
    results = await self.smart_engine.get_smart_context(
        query=query, intent=intent
    )
    return results
'''
    
    try:
        explanation = await context.explain_code(code_snippet)
        print(f"   Code snippet: async def get_context...")
        print(f"   Explanation: {explanation[:100]}...")
    except Exception as e:
        print(f"   ⚠️  Code explanation not available: {e}")


def show_next_steps():
    """Show what users can do next."""
    print(f"\n🎯 What's Next?")
    print("=" * 20)
    print("🧠 You've seen the context engine power! Here's how to use it:")
    print()
    print("🔧 Integration Options:")
    print("   • Use in your own applications")
    print("   • Integrate with IDEs and editors")
    print("   • Build custom RAG applications")
    print("   • Create project-aware chatbots")
    print()
    print("📚 More Examples:")
    print("   python examples/quick_start.py       # Basic Agent Swarm usage")
    print("   python examples/coding_agent.py      # Development workflows")
    print("   python examples/interactive_shell.py # Interactive development")
    print()
    print("⚙️  Customization:")
    print("   • Configure response modes for your needs")
    print("   • Add custom context providers")
    print("   • Tune relevance scoring")
    print("   • Scale with cloud vector databases")


async def main():
    """Main demo function."""
    print("🧠 Agent Swarm Context Engine Demo")
    print("🎯 Smart Project Understanding")
    print()

    setup_logging(level="INFO")

    try:
        # Initialize and analyze project
        context = await demo_project_analysis()
        
        # Demonstrate core features
        await demo_smart_context_retrieval(context)
        await demo_code_search(context)
        await demo_response_modes(context)
        await demo_context_features(context)
        
        show_next_steps()

        print("\n🎉 Context Engine Demo Complete!")
        print("\n💡 Ready to build context-aware applications!")

    except KeyboardInterrupt:
        print("\n👋 Demo interrupted. Thanks for exploring!")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Install dependencies: pip install -e .")
        print("2. Ensure project has code files to analyze")
        print("3. Run setup: agent-swarm setup")


if __name__ == "__main__":
    asyncio.run(main())
