#!/usr/bin/env python3
"""
🔗 MCP Integration - Model Context Protocol

Demonstrates Agent Swarm's MCP (Model Context Protocol) integration:
- Standardized tool calling across different LLMs
- Secure tool execution with proper boundaries
- Agent collaboration through shared tools
- Custom tool development and integration

Prerequisites:
- Python 3.8+
- MCP Python SDK: pip install mcp
- Run: pip install -e .

Usage:
    python examples/mcp_integration.py
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm import setup_logging


async def demo_mcp_overview():
    """Demonstrate MCP overview and capabilities."""
    print("🔗 MCP Integration Overview")
    print("=" * 50)
    print("Model Context Protocol (MCP) provides:")
    print("   🛠️  Standardized tool interface for all LLMs")
    print("   🔒 Secure tool execution with proper boundaries")
    print("   🔄 Composable tools that work with any model")
    print("   🌐 Rich ecosystem of MCP-compatible tools")
    print("   🤝 Easy agent collaboration through shared tools")
    print()
    
    print("🎯 Agent Swarm MCP Features:")
    print("   • Automatic tool discovery and registration")
    print("   • Type-safe tool calling with validation")
    print("   • Error handling and retry mechanisms")
    print("   • Tool usage analytics and monitoring")
    print("   • Custom tool development framework")
    print()


async def demo_mcp_tools():
    """Demonstrate available MCP tools."""
    print("🛠️  Available MCP Tools Demo")
    print("=" * 50)
    
    try:
        from agent_swarm.mcp import setup_default_mcp_tools
        
        print("🔧 Setting up default MCP tools...")
        mcp_registry = await setup_default_mcp_tools()
        
        tools = mcp_registry.get_all_tools()
        print(f"✅ {len(tools)} MCP tools available:")
        
        # Group tools by category
        tool_categories = {
            "File Operations": ["read_file", "write_file", "list_files"],
            "Web & Network": ["search_web", "fetch_url", "ping_host"],
            "System": ["execute_command", "get_system_info"],
            "Development": ["run_code", "analyze_code", "format_code"]
        }
        
        for category, tool_names in tool_categories.items():
            available_tools = [t for t in tools if t.name in tool_names]
            if available_tools:
                print(f"\n📁 {category}:")
                for tool in available_tools:
                    print(f"   • {tool.name}: {tool.description}")
        
        return mcp_registry
        
    except ImportError:
        print("⚠️  MCP not available - install with: pip install mcp")
        return None
    except Exception as e:
        print(f"❌ MCP setup failed: {e}")
        return None


async def demo_tool_calling(mcp_registry):
    """Demonstrate direct tool calling."""
    if not mcp_registry:
        print("\n⚠️  Skipping tool calling demo - MCP not available")
        return
    
    print("\n🎯 Tool Calling Demo")
    print("=" * 50)
    
    # Demo file operations
    print("📁 File Operations:")
    
    try:
        # Create a test file
        print("   Creating test file...")
        result = await mcp_registry.call_tool("write_file", {
            "path": "examples/mcp_test.txt",
            "content": "Hello from MCP! This file was created using the Model Context Protocol."
        })
        
        if result.success:
            print("   ✅ File created successfully")
            
            # Read the file back
            print("   Reading file back...")
            read_result = await mcp_registry.call_tool("read_file", {
                "path": "examples/mcp_test.txt"
            })
            
            if read_result.success:
                content = read_result.result[:50] + "..." if len(read_result.result) > 50 else read_result.result
                print(f"   ✅ File content: {content}")
            else:
                print(f"   ❌ Read failed: {read_result.error}")
        else:
            print(f"   ❌ Write failed: {result.error}")
    
    except Exception as e:
        print(f"   ❌ File operations failed: {e}")
    
    # Demo web search (if available)
    print("\n🌐 Web Search:")
    try:
        print("   Searching for 'Model Context Protocol'...")
        search_result = await mcp_registry.call_tool("search_web", {
            "query": "Model Context Protocol MCP",
            "num_results": 3
        })
        
        if search_result.success:
            results = search_result.result.get('results', [])
            print(f"   ✅ Found {len(results)} results:")
            for i, result in enumerate(results[:2], 1):
                print(f"     {i}. {result.get('title', 'No title')}")
                print(f"        {result.get('url', 'No URL')}")
        else:
            print(f"   ❌ Search failed: {search_result.error}")
    
    except Exception as e:
        print(f"   ⚠️  Web search not available: {e}")


async def demo_agent_integration():
    """Demonstrate MCP integration with agents."""
    print("\n🤖 Agent Integration Demo")
    print("=" * 50)
    
    try:
        from agent_swarm.mcp import MCPEnabledAgent
        from agent_swarm import LLMRouter
        
        # Create router and MCP registry
        router = LLMRouter()
        mcp_registry = await demo_mcp_tools()  # Reuse from previous demo
        
        if not mcp_registry:
            print("⚠️  Skipping agent demo - MCP not available")
            return
        
        # Create MCP-enabled agent
        agent = MCPEnabledAgent(
            name="MCPBot",
            role="MCP Demonstration Assistant",
            llm_router=router,
            mcp_registry=mcp_registry
        )
        
        print(f"✅ Created MCP-enabled agent: {agent.name}")
        print(f"   Role: {agent.role}")
        print(f"   Available tools: {len(agent.mcp_registry.get_all_tools())}")
        
        # Show agent capabilities
        print(f"\n🎯 Agent Capabilities:")
        print("   • Automatic tool selection based on task")
        print("   • Multi-step workflows with tool chaining")
        print("   • Error handling and recovery")
        print("   • Tool usage optimization")
        
        # Simulate agent tasks
        demo_tasks = [
            "Create a summary of the project structure",
            "Search for information about MCP and save it to a file",
            "Analyze the codebase and generate a report"
        ]
        
        print(f"\n📋 Example Tasks Agent Could Handle:")
        for i, task in enumerate(demo_tasks, 1):
            print(f"   {i}. {task}")
            print(f"      → Agent would analyze task and select appropriate tools")
        
        # Show tool usage stats
        stats = agent.get_tool_usage_stats()
        print(f"\n📊 Tool Usage Statistics:")
        print(f"   Total calls: {stats.get('total_calls', 0)}")
        print(f"   Success rate: {stats.get('success_rate', 0):.1%}")
        print(f"   Most used tools: {', '.join(stats.get('top_tools', [])[:3])}")
        
    except ImportError:
        print("⚠️  Agent integration not available - check dependencies")
    except Exception as e:
        print(f"❌ Agent integration demo failed: {e}")


async def demo_custom_tools():
    """Demonstrate custom tool development."""
    print("\n🔧 Custom Tool Development Demo")
    print("=" * 50)
    
    print("📋 Creating Custom MCP Tools:")
    print()
    
    # Example custom tool definitions
    custom_tools = [
        {
            "name": "analyze_project",
            "description": "Analyze project structure and generate insights",
            "parameters": {
                "project_path": "string",
                "include_tests": "boolean",
                "output_format": "string"
            },
            "use_case": "Project analysis and documentation"
        },
        {
            "name": "optimize_code",
            "description": "Analyze code and suggest performance optimizations",
            "parameters": {
                "file_path": "string",
                "language": "string",
                "optimization_level": "string"
            },
            "use_case": "Code optimization and refactoring"
        },
        {
            "name": "deploy_service",
            "description": "Deploy application to cloud platform",
            "parameters": {
                "service_name": "string",
                "platform": "string",
                "environment": "string"
            },
            "use_case": "DevOps automation"
        }
    ]
    
    print("🛠️  Example Custom Tools:")
    for tool in custom_tools:
        print(f"\n📦 {tool['name']}:")
        print(f"   Description: {tool['description']}")
        print(f"   Parameters: {', '.join(tool['parameters'].keys())}")
        print(f"   Use case: {tool['use_case']}")
    
    print(f"\n💡 Tool Development Process:")
    print("1. 📝 Define tool schema with parameters and types")
    print("2. 🔧 Implement tool execution logic")
    print("3. 🧪 Test tool with various inputs")
    print("4. 📋 Register tool with MCP server")
    print("5. 🤖 Make available to agents")
    print("6. 📊 Monitor usage and performance")
    
    print(f"\n📚 Development Resources:")
    print("   • MCP Specification: https://spec.modelcontextprotocol.io/")
    print("   • Python SDK: https://github.com/modelcontextprotocol/python-sdk")
    print("   • Tool Examples: https://github.com/modelcontextprotocol/servers")
    print("   • Agent Swarm Integration: src/agent_swarm/mcp/")


def show_next_steps():
    """Show what users can do next."""
    print(f"\n🎯 What's Next?")
    print("=" * 20)
    print("🔗 You've seen MCP integration power! Here's how to use it:")
    print()
    print("🛠️  Setup & Configuration:")
    print("   pip install mcp                     # Install MCP SDK")
    print("   agent-swarm setup                   # Configure environment")
    print("   # Set up MCP servers for your tools")
    print()
    print("🔧 Development:")
    print("   • Create custom tools for your domain")
    print("   • Integrate with existing systems")
    print("   • Build agent workflows with tools")
    print("   • Deploy MCP servers in production")
    print()
    print("📚 More Examples:")
    print("   python examples/quick_start.py      # Basic Agent Swarm usage")
    print("   python examples/coding_agent.py     # Development workflows")
    print("   python examples/interactive_shell.py # Interactive development")
    print()
    print("🌐 Community:")
    print("   • MCP Tool Registry: Discover existing tools")
    print("   • Agent Swarm Discord: Get help and share tools")
    print("   • Contribute tools back to the community")


async def main():
    """Main demo function."""
    print("🔗 Agent Swarm MCP Integration Demo")
    print("🎯 Model Context Protocol for Standardized Tools")
    print()

    setup_logging(level="INFO")

    try:
        # Run all demos
        await demo_mcp_overview()
        mcp_registry = await demo_mcp_tools()
        await demo_tool_calling(mcp_registry)
        await demo_agent_integration()
        await demo_custom_tools()
        
        show_next_steps()

        print("\n🎉 MCP Integration Demo Complete!")
        print("\n💡 Ready to build tool-powered agents!")

    except KeyboardInterrupt:
        print("\n👋 Demo interrupted. Thanks for exploring MCP!")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Install MCP SDK: pip install mcp")
        print("2. Install dependencies: pip install -e .")
        print("3. Run setup: agent-swarm setup")


if __name__ == "__main__":
    asyncio.run(main())
