#!/usr/bin/env python3
"""
Demo script to show @ command autocomplete functionality.
"""

import sys
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def demo_at_autocomplete():
    """Demonstrate @ command autocomplete functionality."""
    print("🎯 @ Command Autocomplete Demo")
    print("=" * 50)
    
    try:
        from agent_swarm.cli.autocomplete import AdvancedCompleter
        
        # Initialize completer
        completer = AdvancedCompleter()
        completer.project_path = Path("/home/<USER>/workspace/local-llm/agent-mode")
        
        print(f"📁 Project path: {completer.project_path}")
        print()
        
        # Demo scenarios
        demo_cases = [
            ("explain @README", "README"),
            ("show me @src/", "src/"),
            ("analyze @pyproject", "pyproject"),
            ("@", ""),
        ]
        
        for line, text in demo_cases:
            print(f"🔍 Input: '{line}' (completing '{text}')")
            
            # Check if it's @ command context
            is_at_context = completer._is_at_command_context(line, text)
            print(f"   @ command detected: {is_at_context}")
            
            if is_at_context:
                # Get completions
                completions = completer.get_completions(line, text)
                print(f"   Completions found: {len(completions)}")
                
                # Show completions
                for i, comp in enumerate(completions[:5]):
                    print(f"     {i+1}. {comp.text:<25} {comp.description}")
                
                if len(completions) > 5:
                    print(f"     ... and {len(completions) - 5} more")
            else:
                print("   (Not an @ command - would use regular completion)")
            
            print()
        
        print("✅ @ Command autocomplete is working perfectly!")
        print()
        print("🎯 How to use in Agent Swarm:")
        print("1. Start Agent Swarm: python -m agent_swarm.cli.interactive_shell")
        print("2. Type: 'explain @README' and press TAB")
        print("3. You'll see: 'explain @README.md'")
        print("4. Press Enter to execute with file context!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demo_at_autocomplete()
