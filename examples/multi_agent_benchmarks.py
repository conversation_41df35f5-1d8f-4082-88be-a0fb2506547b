#!/usr/bin/env python3
"""
Multi-Agent System Performance Benchmarks

This module provides comprehensive benchmarks to evaluate the performance,
scalability, and quality of the revolutionary multi-agent consensus system.
"""

import asyncio
import time
import statistics
import json
from pathlib import Path
from typing import Dict, Any, List, Tuple
from dataclasses import dataclass, asdict
import matplotlib.pyplot as plt
import numpy as np

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    import sys
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm.algorithms.algorithms.stages.multi_agent_consensus_stage import (
    MultiAgentConsensusStage
)
from agent_swarm.algorithms.core import AlgorithmContext
from agent_swarm.core.multi_agent_communication import initialize_communication_hub


@dataclass
class BenchmarkResult:
    """Result of a benchmark test."""
    test_name: str
    execution_time: float
    consensus_reached: bool
    confidence: float
    participating_agents: int
    memory_usage_mb: float
    cpu_usage_percent: float
    message_count: int
    consensus_type: str
    complexity_score: float


@dataclass
class ScalabilityResult:
    """Result of scalability testing."""
    agent_count: int
    avg_execution_time: float
    success_rate: float
    avg_confidence: float
    theoretical_complexity: float
    actual_complexity: float


class MultiAgentBenchmarks:
    """Comprehensive benchmarking suite for multi-agent system."""
    
    def __init__(self):
        self.consensus_stage = MultiAgentConsensusStage()
        self.communication_hub = None
        self.benchmark_results = []
        
    async def initialize(self):
        """Initialize benchmarking environment."""
        self.communication_hub = await initialize_communication_hub()
        
    async def cleanup(self):
        """Cleanup benchmarking resources."""
        if self.communication_hub:
            await self.communication_hub.shutdown()
    
    def _get_system_metrics(self) -> Tuple[float, float]:
        """Get current system resource usage."""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            cpu_percent = process.cpu_percent()
            return memory_mb, cpu_percent
        except ImportError:
            return 0.0, 0.0
    
    def _calculate_complexity_score(self, input_data: Dict[str, Any]) -> float:
        """Calculate complexity score for input data."""
        score = 0.0
        
        # Text complexity
        if isinstance(input_data, dict):
            text_content = str(input_data)
            score += len(text_content) / 1000  # Length factor
            
            # Nested structure complexity
            def count_nested_levels(obj, level=0):
                if isinstance(obj, dict):
                    return max([count_nested_levels(v, level + 1) for v in obj.values()] + [level])
                elif isinstance(obj, list):
                    return max([count_nested_levels(item, level + 1) for item in obj] + [level])
                return level
            
            score += count_nested_levels(input_data) * 0.2
            
            # Domain complexity
            domains = input_data.get('domain', '')
            if 'technology' in domains:
                score += 0.3
            if 'security' in domains:
                score += 0.4
            if 'business' in domains:
                score += 0.2
        
        return min(score, 5.0)  # Cap at 5.0
    
    async def _run_single_benchmark(
        self, 
        test_name: str, 
        input_data: Dict[str, Any],
        iterations: int = 1
    ) -> BenchmarkResult:
        """Run a single benchmark test."""
        execution_times = []
        consensus_results = []
        confidence_scores = []
        agent_counts = []
        message_counts = []
        consensus_types = []
        
        for i in range(iterations):
            # Reset communication hub stats
            if self.communication_hub:
                self.communication_hub.reset_metrics()
            
            # Measure system resources before
            memory_before, cpu_before = self._get_system_metrics()
            
            # Create context
            context = AlgorithmContext(
                request_id=f"benchmark_{test_name}_{i}",
                user_id="benchmark_user",
                session_id="benchmark_session"
            )
            
            # Run consensus
            start_time = time.time()
            result = await self.consensus_stage.process(input_data, context)
            execution_time = time.time() - start_time
            
            # Measure system resources after
            memory_after, cpu_after = self._get_system_metrics()
            
            # Collect metrics
            execution_times.append(execution_time)
            consensus_results.append(result.success and result.output.get('consensus_reached', False))
            confidence_scores.append(result.confidence if result.success else 0.0)
            
            if result.success:
                agent_counts.append(result.output.get('participating_agents', 0))
                consensus_types.append(result.output.get('consensus_type', 'unknown'))
            else:
                agent_counts.append(0)
                consensus_types.append('failed')
            
            # Get message count from communication hub
            if self.communication_hub:
                hub_metrics = self.communication_hub.get_metrics()
                message_counts.append(hub_metrics.get('messages_sent', 0))
            else:
                message_counts.append(0)
        
        # Calculate averages
        avg_execution_time = statistics.mean(execution_times)
        consensus_rate = sum(consensus_results) / len(consensus_results)
        avg_confidence = statistics.mean(confidence_scores) if confidence_scores else 0.0
        avg_agents = statistics.mean(agent_counts) if agent_counts else 0.0
        avg_messages = statistics.mean(message_counts) if message_counts else 0.0
        
        # Most common consensus type
        if consensus_types:
            most_common_type = max(set(consensus_types), key=consensus_types.count)
        else:
            most_common_type = 'unknown'
        
        return BenchmarkResult(
            test_name=test_name,
            execution_time=avg_execution_time,
            consensus_reached=consensus_rate > 0.5,
            confidence=avg_confidence,
            participating_agents=int(avg_agents),
            memory_usage_mb=memory_after - memory_before,
            cpu_usage_percent=cpu_after - cpu_before,
            message_count=int(avg_messages),
            consensus_type=most_common_type,
            complexity_score=self._calculate_complexity_score(input_data)
        )
    
    async def benchmark_simple_decisions(self) -> List[BenchmarkResult]:
        """Benchmark simple decision scenarios."""
        simple_scenarios = [
            {
                "name": "simple_tech_choice",
                "input": {
                    "domain": "technology",
                    "problem": "Choose between Python and Java for new service",
                    "options": ["Python", "Java"],
                    "criteria": ["development_speed", "performance", "team_expertise"]
                }
            },
            {
                "name": "simple_business_decision",
                "input": {
                    "domain": "business",
                    "problem": "Should we increase marketing budget by 20%?",
                    "current_budget": "$100k",
                    "expected_roi": "150%"
                }
            },
            {
                "name": "simple_security_policy",
                "input": {
                    "domain": "security",
                    "problem": "Implement mandatory 2FA for all employees?",
                    "security_benefit": "High",
                    "user_impact": "Medium"
                }
            }
        ]
        
        results = []
        for scenario in simple_scenarios:
            result = await self._run_single_benchmark(
                scenario["name"], 
                scenario["input"], 
                iterations=5
            )
            results.append(result)
            
        return results
    
    async def benchmark_complex_decisions(self) -> List[BenchmarkResult]:
        """Benchmark complex decision scenarios."""
        complex_scenarios = [
            {
                "name": "complex_architecture_decision",
                "input": {
                    "domain": "technology",
                    "problem": "Design microservices architecture for enterprise platform",
                    "requirements": {
                        "scalability": "Handle 1M+ users",
                        "reliability": "99.99% uptime",
                        "security": "Enterprise-grade",
                        "performance": "<100ms response time"
                    },
                    "constraints": {
                        "budget": "$2M",
                        "timeline": "12 months",
                        "team_size": 25,
                        "technology_stack": ["Java", "Python", "Node.js"]
                    },
                    "considerations": [
                        "Service decomposition strategy",
                        "Data consistency patterns",
                        "Inter-service communication",
                        "Deployment and monitoring",
                        "Security and compliance"
                    ]
                }
            },
            {
                "name": "complex_market_strategy",
                "input": {
                    "domain": "business",
                    "problem": "Multi-market expansion strategy for B2B SaaS",
                    "target_markets": {
                        "europe": {
                            "market_size": "$5B",
                            "competition": "High",
                            "regulatory_complexity": "High",
                            "investment_required": "$10M"
                        },
                        "asia_pacific": {
                            "market_size": "$8B",
                            "competition": "Medium",
                            "regulatory_complexity": "Medium",
                            "investment_required": "$15M"
                        },
                        "latin_america": {
                            "market_size": "$2B",
                            "competition": "Low",
                            "regulatory_complexity": "Low",
                            "investment_required": "$5M"
                        }
                    },
                    "strategic_factors": [
                        "Market entry timing",
                        "Localization requirements",
                        "Partnership strategies",
                        "Competitive positioning",
                        "Resource allocation"
                    ]
                }
            },
            {
                "name": "complex_security_framework",
                "input": {
                    "domain": "security",
                    "problem": "Implement zero-trust security architecture",
                    "current_state": {
                        "network_security": "Perimeter-based",
                        "identity_management": "Basic AD",
                        "device_management": "Limited",
                        "data_protection": "Basic encryption"
                    },
                    "zero_trust_components": {
                        "identity_verification": "Multi-factor authentication",
                        "device_security": "Endpoint detection and response",
                        "network_segmentation": "Micro-segmentation",
                        "data_protection": "End-to-end encryption",
                        "monitoring": "Continuous security monitoring"
                    },
                    "implementation_phases": [
                        "Identity and access management",
                        "Network segmentation",
                        "Endpoint security",
                        "Data protection",
                        "Monitoring and analytics"
                    ]
                }
            }
        ]
        
        results = []
        for scenario in complex_scenarios:
            result = await self._run_single_benchmark(
                scenario["name"], 
                scenario["input"], 
                iterations=3
            )
            results.append(result)
            
        return results
    
    async def benchmark_scalability(self) -> List[ScalabilityResult]:
        """Benchmark system scalability with varying complexity."""
        # Test with different numbers of virtual agents
        # Note: This simulates scalability by varying input complexity
        
        scalability_tests = [
            {
                "complexity_level": 1,
                "agent_simulation": 3,
                "input": {
                    "domain": "technology",
                    "problem": "Simple binary choice",
                    "options": ["A", "B"]
                }
            },
            {
                "complexity_level": 2,
                "agent_simulation": 4,
                "input": {
                    "domain": "business",
                    "problem": "Medium complexity decision with multiple factors",
                    "factors": ["cost", "time", "quality", "risk"],
                    "options": ["option1", "option2", "option3"]
                }
            },
            {
                "complexity_level": 3,
                "agent_simulation": 5,
                "input": {
                    "domain": "technology",
                    "problem": "Complex multi-dimensional decision",
                    "dimensions": {
                        "technical": ["scalability", "maintainability", "performance"],
                        "business": ["cost", "time_to_market", "competitive_advantage"],
                        "operational": ["team_skills", "infrastructure", "support"]
                    },
                    "constraints": ["budget", "timeline", "resources", "compliance"]
                }
            },
            {
                "complexity_level": 4,
                "agent_simulation": 6,
                "input": {
                    "domain": "business",
                    "problem": "Highly complex strategic decision with multiple stakeholders",
                    "stakeholders": {
                        "engineering": {"priorities": ["technical_excellence", "maintainability"]},
                        "product": {"priorities": ["user_experience", "feature_completeness"]},
                        "business": {"priorities": ["revenue", "market_share"]},
                        "operations": {"priorities": ["reliability", "scalability"]},
                        "security": {"priorities": ["data_protection", "compliance"]},
                        "finance": {"priorities": ["cost_optimization", "roi"]}
                    },
                    "decision_matrix": {
                        "criteria": ["impact", "effort", "risk", "strategic_value"],
                        "options": ["option_a", "option_b", "option_c", "option_d"],
                        "constraints": ["budget", "timeline", "resources", "regulations"]
                    }
                }
            }
        ]
        
        results = []
        for test in scalability_tests:
            execution_times = []
            success_rates = []
            confidence_scores = []
            
            # Run multiple iterations for statistical significance
            for _ in range(5):
                result = await self._run_single_benchmark(
                    f"scalability_level_{test['complexity_level']}", 
                    test["input"],
                    iterations=1
                )
                
                execution_times.append(result.execution_time)
                success_rates.append(1.0 if result.consensus_reached else 0.0)
                confidence_scores.append(result.confidence)
            
            # Calculate theoretical O(n³) complexity
            n = test["agent_simulation"]
            theoretical_complexity = n ** 3
            
            # Calculate actual complexity based on execution time
            base_time = execution_times[0] if execution_times else 1.0
            actual_complexity = statistics.mean(execution_times) / base_time if base_time > 0 else 1.0
            
            scalability_result = ScalabilityResult(
                agent_count=n,
                avg_execution_time=statistics.mean(execution_times),
                success_rate=statistics.mean(success_rates),
                avg_confidence=statistics.mean(confidence_scores),
                theoretical_complexity=theoretical_complexity,
                actual_complexity=actual_complexity
            )
            
            results.append(scalability_result)
        
        return results
    
    async def benchmark_consensus_quality(self) -> List[BenchmarkResult]:
        """Benchmark consensus quality with conflicting scenarios."""
        conflict_scenarios = [
            {
                "name": "high_agreement_scenario",
                "input": {
                    "domain": "technology",
                    "problem": "Adopt widely accepted best practice",
                    "context": "Industry standard with proven benefits",
                    "expected_consensus": "strong"
                }
            },
            {
                "name": "moderate_disagreement_scenario",
                "input": {
                    "domain": "business",
                    "problem": "Balance between innovation and stability",
                    "trade_offs": {
                        "innovation": "High potential, high risk",
                        "stability": "Lower potential, lower risk"
                    },
                    "expected_consensus": "moderate"
                }
            },
            {
                "name": "high_conflict_scenario",
                "input": {
                    "domain": "security",
                    "problem": "Security vs usability trade-off",
                    "security_perspective": "Maximum protection required",
                    "usability_perspective": "User experience priority",
                    "business_perspective": "Cost-effectiveness focus",
                    "expected_consensus": "weak"
                }
            }
        ]
        
        results = []
        for scenario in conflict_scenarios:
            result = await self._run_single_benchmark(
                scenario["name"], 
                scenario["input"], 
                iterations=3
            )
            results.append(result)
            
        return results
    
    async def run_comprehensive_benchmarks(self) -> Dict[str, Any]:
        """Run all benchmark suites and compile results."""
        print("🚀 Starting Comprehensive Multi-Agent Benchmarks...")
        
        # Run all benchmark suites
        simple_results = await self.benchmark_simple_decisions()
        print("✅ Simple decisions benchmark completed")
        
        complex_results = await self.benchmark_complex_decisions()
        print("✅ Complex decisions benchmark completed")
        
        scalability_results = await self.benchmark_scalability()
        print("✅ Scalability benchmark completed")
        
        quality_results = await self.benchmark_consensus_quality()
        print("✅ Consensus quality benchmark completed")
        
        # Compile comprehensive report
        all_results = simple_results + complex_results + quality_results
        
        report = {
            "summary": {
                "total_tests": len(all_results),
                "successful_consensus": sum(1 for r in all_results if r.consensus_reached),
                "average_execution_time": statistics.mean([r.execution_time for r in all_results]),
                "average_confidence": statistics.mean([r.confidence for r in all_results if r.consensus_reached]),
                "average_complexity": statistics.mean([r.complexity_score for r in all_results])
            },
            "performance_by_category": {
                "simple_decisions": {
                    "avg_time": statistics.mean([r.execution_time for r in simple_results]),
                    "success_rate": sum(1 for r in simple_results if r.consensus_reached) / len(simple_results),
                    "avg_confidence": statistics.mean([r.confidence for r in simple_results if r.consensus_reached])
                },
                "complex_decisions": {
                    "avg_time": statistics.mean([r.execution_time for r in complex_results]),
                    "success_rate": sum(1 for r in complex_results if r.consensus_reached) / len(complex_results),
                    "avg_confidence": statistics.mean([r.confidence for r in complex_results if r.consensus_reached])
                },
                "quality_scenarios": {
                    "avg_time": statistics.mean([r.execution_time for r in quality_results]),
                    "success_rate": sum(1 for r in quality_results if r.consensus_reached) / len(quality_results),
                    "avg_confidence": statistics.mean([r.confidence for r in quality_results if r.consensus_reached])
                }
            },
            "scalability_analysis": [asdict(r) for r in scalability_results],
            "detailed_results": [asdict(r) for r in all_results]
        }
        
        return report
    
    def generate_visualizations(self, report: Dict[str, Any]):
        """Generate performance visualization charts."""
        try:
            # Execution time by complexity
            results = [BenchmarkResult(**r) for r in report["detailed_results"]]
            
            plt.figure(figsize=(15, 10))
            
            # Plot 1: Execution Time vs Complexity
            plt.subplot(2, 3, 1)
            complexities = [r.complexity_score for r in results]
            times = [r.execution_time for r in results]
            plt.scatter(complexities, times, alpha=0.7)
            plt.xlabel('Complexity Score')
            plt.ylabel('Execution Time (s)')
            plt.title('Execution Time vs Complexity')
            
            # Plot 2: Confidence Distribution
            plt.subplot(2, 3, 2)
            confidences = [r.confidence for r in results if r.consensus_reached]
            plt.hist(confidences, bins=10, alpha=0.7, edgecolor='black')
            plt.xlabel('Confidence Score')
            plt.ylabel('Frequency')
            plt.title('Consensus Confidence Distribution')
            
            # Plot 3: Success Rate by Category
            plt.subplot(2, 3, 3)
            categories = ['Simple', 'Complex', 'Quality']
            success_rates = [
                report["performance_by_category"]["simple_decisions"]["success_rate"],
                report["performance_by_category"]["complex_decisions"]["success_rate"],
                report["performance_by_category"]["quality_scenarios"]["success_rate"]
            ]
            plt.bar(categories, success_rates, alpha=0.7)
            plt.ylabel('Success Rate')
            plt.title('Success Rate by Category')
            plt.ylim(0, 1)
            
            # Plot 4: Scalability Analysis
            plt.subplot(2, 3, 4)
            scalability_data = report["scalability_analysis"]
            agent_counts = [s["agent_count"] for s in scalability_data]
            exec_times = [s["avg_execution_time"] for s in scalability_data]
            theoretical = [s["theoretical_complexity"] for s in scalability_data]
            
            plt.plot(agent_counts, exec_times, 'o-', label='Actual Performance')
            plt.plot(agent_counts, np.array(theoretical) * exec_times[0] / theoretical[0], 's--', label='O(n³) Theoretical')
            plt.xlabel('Agent Count (Complexity Level)')
            plt.ylabel('Execution Time (s)')
            plt.title('Scalability Analysis')
            plt.legend()
            
            # Plot 5: Message Count vs Agents
            plt.subplot(2, 3, 5)
            agent_counts_detailed = [r.participating_agents for r in results]
            message_counts = [r.message_count for r in results]
            plt.scatter(agent_counts_detailed, message_counts, alpha=0.7)
            plt.xlabel('Participating Agents')
            plt.ylabel('Message Count')
            plt.title('Communication Overhead')
            
            # Plot 6: Consensus Type Distribution
            plt.subplot(2, 3, 6)
            consensus_types = [r.consensus_type for r in results if r.consensus_reached]
            type_counts = {}
            for ct in consensus_types:
                type_counts[ct] = type_counts.get(ct, 0) + 1
            
            plt.pie(type_counts.values(), labels=type_counts.keys(), autopct='%1.1f%%')
            plt.title('Consensus Type Distribution')
            
            plt.tight_layout()
            plt.savefig('multi_agent_benchmarks.png', dpi=300, bbox_inches='tight')
            plt.show()
            
            print("📊 Visualization saved as 'multi_agent_benchmarks.png'")
            
        except ImportError:
            print("⚠️ Matplotlib not available. Skipping visualizations.")
        except Exception as e:
            print(f"⚠️ Error generating visualizations: {e}")


async def main():
    """Run comprehensive benchmarks."""
    benchmarks = MultiAgentBenchmarks()
    
    try:
        await benchmarks.initialize()
        
        # Run benchmarks
        report = await benchmarks.run_comprehensive_benchmarks()
        
        # Print summary
        print("\n📊 Benchmark Results Summary:")
        print(f"Total Tests: {report['summary']['total_tests']}")
        print(f"Success Rate: {report['summary']['successful_consensus'] / report['summary']['total_tests']:.2%}")
        print(f"Average Execution Time: {report['summary']['average_execution_time']:.3f}s")
        print(f"Average Confidence: {report['summary']['average_confidence']:.2%}")
        print(f"Average Complexity: {report['summary']['average_complexity']:.2f}")
        
        # Save detailed report
        with open("multi_agent_benchmarks_report.json", "w") as f:
            json.dump(report, f, indent=2, default=str)
        
        print("\n✅ Detailed report saved to 'multi_agent_benchmarks_report.json'")
        
        # Generate visualizations
        benchmarks.generate_visualizations(report)
        
    finally:
        await benchmarks.cleanup()


if __name__ == "__main__":
    asyncio.run(main())