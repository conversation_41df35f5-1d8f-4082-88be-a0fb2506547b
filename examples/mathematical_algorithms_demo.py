#!/usr/bin/env python3
"""
Mathematical Algorithm Framework Demo.

This example demonstrates the comprehensive mathematical algorithm framework
with OpenHands-inspired architecture patterns.

Features demonstrated:
- Algorithm engine creation and configuration
- Event-driven execution (Action → Observation → Result)
- Performance monitoring and metrics
- Algorithm pipelines and stages
- Configuration management
- Error handling and validation

Run this demo to see the framework in action!
"""

import asyncio
import json
from pathlib import Path

# Add the src directory to the path for imports
import sys
# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm.algorithms import (
    # Core framework
    AlgorithmEngine,
    AlgorithmConfig,
    AlgorithmParameters,
    ParameterDefinition,
    ParameterType,
    ParameterConstraint,
    
    # Event system
    ExecuteAlgorithmAction,
    BenchmarkAction,
    ValidateAction,
    
    # Built-in algorithms
    ConfidenceThresholder,
    PatternMatcher,
    
    # Algorithm stages
    ExplicitCommandStage,
    
    # Pipeline system
    AlgorithmPipeline,
    
    # Registry
    AlgorithmRegistry,
    
    # Utilities
    create_algorithm_engine,
    create_parameter_definition,
    calculate_confidence,
    normalize_scores,
)


async def demo_confidence_thresholder():
    """Demonstrate the ConfidenceThresholder algorithm."""
    print("\n" + "="*60)
    print("🎯 CONFIDENCE THRESHOLDER DEMO")
    print("="*60)
    
    # Create algorithm configuration
    config = AlgorithmConfig(algorithm_name="confidence_thresholder")
    
    # Add parameter definitions
    threshold_param = create_parameter_definition(
        name="threshold",
        param_type="float",
        default_value=0.6,
        description="Confidence threshold",
        min_value=0.0,
        max_value=1.0,
    )
    config.parameters.add_parameter(threshold_param)
    
    strategy_param = create_parameter_definition(
        name="strategy",
        param_type="string",
        default_value="sigmoid",
        description="Thresholding strategy",
        allowed_values=["binary", "soft", "adaptive", "sigmoid"],
    )
    config.parameters.add_parameter(strategy_param)
    
    # Create engine
    engine = AlgorithmEngine(config)
    
    # Register the algorithm
    engine.registry.register_algorithm(ConfidenceThresholder)
    
    # Test data
    confidence_scores = [0.1, 0.3, 0.5, 0.7, 0.8, 0.9, 0.95]
    
    print(f"📊 Input confidence scores: {confidence_scores}")
    print(f"🎛️  Threshold: {config.parameters.get_parameter('threshold')}")
    print(f"🔧 Strategy: {config.parameters.get_parameter('strategy')}")
    
    # Create and execute action
    action = ExecuteAlgorithmAction(
        algorithm_name="confidence_thresholder",
        input_data=confidence_scores,
    )
    
    print("\n🚀 Executing algorithm...")
    observation = await engine.execute_action(action)
    
    if observation.success:
        print("✅ Algorithm executed successfully!")
        print(f"⏱️  Execution time: {observation.execution_time:.4f}s")
        print(f"🎯 Confidence: {observation.confidence:.2f}")
        
        if hasattr(observation, 'output_data'):
            output = observation.output_data
            print(f"📤 Output: {output}")
            
            if isinstance(output, dict) and 'thresholded_values' in output:
                print(f"🔢 Thresholded values: {output['thresholded_values']}")
    else:
        print("❌ Algorithm execution failed!")
        print(f"💥 Error: {observation.error}")


async def demo_pattern_matcher():
    """Demonstrate the PatternMatcher algorithm."""
    print("\n" + "="*60)
    print("🔍 PATTERN MATCHER DEMO")
    print("="*60)
    
    # Create engine with simple configuration
    engine = create_algorithm_engine(
        algorithm_name="pattern_matcher",
        parameters={
            "strategy": "hybrid",
            "similarity_threshold": 0.5,
            "max_matches": 5,
        }
    )
    
    # Register the algorithm
    engine.registry.register_algorithm(PatternMatcher)
    
    # Test data
    test_text = "Hello world! This is a pattern matching algorithm test example."
    test_patterns = ["hello", "world", "pattern", "algorithm", "test", "example", "demo"]
    
    input_data = {
        'text': test_text,
        'patterns': test_patterns,
    }
    
    print(f"📝 Input text: {test_text}")
    print(f"🎯 Patterns: {test_patterns}")
    
    # Create and execute action
    action = ExecuteAlgorithmAction(
        algorithm_name="pattern_matcher",
        input_data=input_data,
    )
    
    print("\n🚀 Executing pattern matching...")
    observation = await engine.execute_action(action)
    
    if observation.success:
        print("✅ Pattern matching successful!")
        print(f"⏱️  Execution time: {observation.execution_time:.4f}s")
        print(f"🎯 Confidence: {observation.confidence:.2f}")
        
        if hasattr(observation, 'output_data'):
            output = observation.output_data
            print(f"📊 Total matches found: {output.get('total_matches', 0)}")
            
            matches = output.get('matches', [])
            for i, match in enumerate(matches[:3]):  # Show first 3 matches
                print(f"  {i+1}. Pattern: '{match['pattern']}' | "
                      f"Similarity: {match['similarity']:.2f} | "
                      f"Type: {match['match_type']}")
    else:
        print("❌ Pattern matching failed!")
        print(f"💥 Error: {observation.error}")


async def demo_explicit_command_stage():
    """Demonstrate the ExplicitCommandStage."""
    print("\n" + "="*60)
    print("⚡ EXPLICIT COMMAND STAGE DEMO")
    print("="*60)
    
    # Create stage
    stage = ExplicitCommandStage()
    
    # Create context
    config = AlgorithmConfig(algorithm_name="explicit_command_test")
    from agent_swarm.algorithms.core.registry import AlgorithmContext
    context = AlgorithmContext(config)
    
    # Test commands
    test_commands = [
        "/help",
        "/config show",
        "/set model gpt-4",
        "/unknown_command",
        "not a command",
        "/",
    ]
    
    print("🧪 Testing various commands:")
    
    for command in test_commands:
        print(f"\n📝 Testing: '{command}'")
        
        try:
            result = await stage.process(command, context)
            
            if result.success:
                output = result.output
                is_explicit = output.get('is_explicit_command', False)
                recognized = output.get('command_recognized', False)
                valid = output.get('command_valid', False)
                
                print(f"  ✅ Processed successfully")
                print(f"  🎯 Confidence: {result.confidence:.2f}")
                print(f"  📊 Explicit command: {is_explicit}")
                
                if is_explicit:
                    print(f"  🔍 Recognized: {recognized}")
                    print(f"  ✔️  Valid: {valid}")
                    
                    if recognized and valid:
                        command_name = output.get('command_name')
                        command_args = output.get('command_args', [])
                        print(f"  📋 Command: {command_name}")
                        print(f"  📝 Args: {command_args}")
            else:
                print(f"  ❌ Processing failed: {result.error}")
                
        except Exception as e:
            print(f"  💥 Exception: {e}")


async def demo_algorithm_pipeline():
    """Demonstrate algorithm pipeline with multiple stages."""
    print("\n" + "="*60)
    print("🔗 ALGORITHM PIPELINE DEMO")
    print("="*60)
    
    # Create pipeline
    pipeline = AlgorithmPipeline("intent_processing_pipeline")
    
    # Add stages
    explicit_stage = ExplicitCommandStage()
    pipeline.add_stage(explicit_stage)
    
    print("🏗️  Created pipeline with stages:")
    for stage_name in pipeline.execution_order:
        stage = pipeline.stages[stage_name]
        print(f"  📦 {stage_name} - {stage.metadata.complexity}")
    
    # Test input
    test_input = "/help config"
    
    print(f"\n📝 Pipeline input: '{test_input}'")
    
    # Create context
    config = AlgorithmConfig(algorithm_name="pipeline_test")
    from agent_swarm.algorithms.core.registry import AlgorithmContext
    context = AlgorithmContext(config)
    
    # Execute pipeline
    print("\n🚀 Executing pipeline...")
    result = await pipeline.execute(test_input, context)
    
    if result.success:
        print("✅ Pipeline executed successfully!")
        print(f"⏱️  Total execution time: {result.execution_time:.4f}s")
        print(f"🎯 Overall confidence: {result.confidence:.2f}")
        print(f"📊 Stages executed: {result.metadata.get('stages_executed', 0)}")
        
        # Show stage results
        stage_results = result.metadata.get('stage_results', {})
        for stage_name, stage_result in stage_results.items():
            print(f"\n  📦 Stage: {stage_name}")
            print(f"    ✅ Success: {stage_result.get('success', False)}")
            print(f"    🎯 Confidence: {stage_result.get('confidence', 0):.2f}")
            print(f"    ⏱️  Time: {stage_result.get('execution_time', 0):.4f}s")
    else:
        print("❌ Pipeline execution failed!")
        print(f"💥 Error: {result.error}")


async def demo_performance_monitoring():
    """Demonstrate performance monitoring and metrics."""
    print("\n" + "="*60)
    print("📊 PERFORMANCE MONITORING DEMO")
    print("="*60)
    
    # Create engine with metrics enabled
    config = AlgorithmConfig(
        algorithm_name="performance_test",
        enable_metrics=True,
        metrics_interval=0.1,
    )
    
    engine = AlgorithmEngine(config)
    engine.registry.register_algorithm(ConfidenceThresholder)
    
    print("🔧 Running multiple algorithm executions for metrics...")
    
    # Run multiple executions
    test_data = [
        [0.1, 0.5, 0.9],
        [0.2, 0.6, 0.8],
        [0.3, 0.7, 0.95],
        [0.4, 0.8, 0.85],
    ]
    
    for i, data in enumerate(test_data):
        print(f"  🔄 Execution {i+1}/4...")
        
        action = ExecuteAlgorithmAction(
            algorithm_name="confidence_thresholder",
            input_data=data,
        )
        
        observation = await engine.execute_action(action)
        
        if observation.success:
            print(f"    ✅ Success - Time: {observation.execution_time:.4f}s")
        else:
            print(f"    ❌ Failed - {observation.error}")
    
    # Show metrics
    print("\n📈 Performance Metrics:")
    metrics = engine.context.metrics
    if hasattr(metrics, 'get_summary'):
        summary = metrics.get_summary()
        print(f"  📊 Algorithm: {summary.get('algorithm_name', 'N/A')}")
        print(f"  🔢 Executions: {summary.get('execution_count', 0)}")
        
        perf = summary.get('performance', {})
        print(f"  ⏱️  Avg time: {perf.get('avg_execution_time', 0):.4f}s")
        print(f"  🎯 Avg confidence: {perf.get('avg_confidence', 0):.2f}")
        print(f"  ❌ Error rate: {perf.get('error_rate', 0):.1f}%")


async def demo_utility_functions():
    """Demonstrate utility functions."""
    print("\n" + "="*60)
    print("🛠️  UTILITY FUNCTIONS DEMO")
    print("="*60)
    
    # Test confidence calculation
    scores = [0.3, 0.7, 0.9]
    confidences = [calculate_confidence(score, threshold=0.5) for score in scores]
    print(f"📊 Confidence calculation:")
    print(f"  Input scores: {scores}")
    print(f"  Confidences: {[f'{c:.2f}' for c in confidences]}")
    
    # Test score normalization
    raw_scores = [10, 25, 30, 15, 40]
    normalized = normalize_scores(raw_scores, method="min_max")
    print(f"\n📏 Score normalization (min-max):")
    print(f"  Raw scores: {raw_scores}")
    print(f"  Normalized: {[f'{s:.2f}' for s in normalized]}")
    
    # Test z-score normalization
    z_normalized = normalize_scores(raw_scores, method="z_score")
    print(f"\n📐 Z-score normalization:")
    print(f"  Raw scores: {raw_scores}")
    print(f"  Z-scores: {[f'{s:.2f}' for s in z_normalized]}")


async def main():
    """Run all demos."""
    print("🚀 MATHEMATICAL ALGORITHM FRAMEWORK DEMO")
    print("🔬 Demonstrating OpenHands-inspired architecture")
    print("⚡ Event-driven • 🔧 Configurable • 📊 Observable • 🧩 Extensible")
    
    try:
        # Run all demos
        await demo_confidence_thresholder()
        await demo_pattern_matcher()
        await demo_explicit_command_stage()
        await demo_algorithm_pipeline()
        await demo_performance_monitoring()
        await demo_utility_functions()
        
        print("\n" + "="*60)
        print("🎉 ALL DEMOS COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("✨ The mathematical algorithm framework is ready for use!")
        print("📚 Check the documentation for more advanced features.")
        print("🔧 Extend the framework by implementing custom algorithms.")
        
    except Exception as e:
        print(f"\n💥 Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
