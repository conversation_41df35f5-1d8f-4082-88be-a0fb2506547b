#!/usr/bin/env python3
"""
🐚 Interactive Shell - Development Environment

Experience Agent Swarm's interactive development environment:
- Context-aware conversations with your codebase
- Integrated CLI tools for development workflows
- Real-time project analysis and assistance
- Multi-model routing for optimal responses

Prerequisites:
- Python 3.8+
- Ollama with models (llama3.2:3b recommended)
- Run: pip install -e .

Usage:
    python examples/interactive_shell.py

    Or use the CLI:
    agent-swarm shell --project /path/to/project
"""

import asyncio
import sys
import logging
from pathlib import Path

# Enable debug logging for context system
logging.getLogger('context').setLevel(logging.DEBUG)
logging.getLogger('agent_swarm.context').setLevel(logging.DEBUG)

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm.cli.interactive_shell import AgentSwarmShell
from agent_swarm import setup_logging


async def demo_shell_features():
    """Demonstrate key shell features before starting interactive mode."""
    print("🐚 Agent Swarm Interactive Shell Demo")
    print("=" * 50)
    print("Welcome to the Agent Swarm interactive development environment!")
    print()

    print("✨ Key Features:")
    print("   🧠 Context-aware conversations with your codebase")
    print("   🛠️  Integrated CLI tools (find, grep, ping, system info)")
    print("   📊 Real-time project analysis and statistics")
    print("   🤖 Multi-model routing (local + cloud)")
    print("   🔍 Smart intent detection and response optimization")
    print("   📁 File system operations and project management")
    print()

    print("🎯 What You Can Do:")
    print("   💬 Ask questions about your codebase")
    print("   🔧 Get help with coding problems")
    print("   📝 Generate and review code")
    print("   🐛 Debug issues with context")
    print("   📊 Analyze project structure")
    print("   🌐 Test network connectivity")
    print("   💻 Check system resources")
    print()

    print("📚 Available Commands:")
    print("   /help                    - Show all commands")
    print("   /context                 - Show context engine status")
    print("   /analyze                 - Analyze current project")
    print("   /find <pattern>          - Find files matching pattern")
    print("   /grep <pattern>          - Search for pattern in files")
    print("   /ping <host>             - Test network connectivity")
    print("   /system                  - Show system information")
    print("   /projectinfo             - Show project details")
    print("   /services                - Check local development services")
    print()

    print("💡 Example Conversations:")
    print('   "Explain the main architecture of this project"')
    print('   "Help me fix this ImportError"')
    print('   "Generate a REST API for user management"')
    print('   "Review this function for security issues"')
    print('   "What testing frameworks are used here?"')
    print()


async def demo_context_awareness():
    """Demonstrate context-aware features."""
    print("🧠 Context Awareness Demo")
    print("=" * 30)

    project_path = str(Path(__file__).parent.parent)

    # Create shell instance to show capabilities
    shell = AgentSwarmShell(verbose=True)

    print(f"📁 Project: {Path(project_path).name}")
    print("🔧 Initializing context engine...")

    # Initialize the shell (this sets up context)
    await shell.initialize(project_path)

    print("✅ Context engine initialized")

    # Show context capabilities
    if shell.context_engine:
        stats = await shell.context_engine.get_stats()
        components = stats.get('components', {})
        active_components = [name for name, active in components.items() if active]

        print(f"🎯 Active components: {len(active_components)}")
        for component in active_components:
            print(f"   • {component.replace('_', ' ').title()}")

        # Show project analysis
        project_analysis = await shell.context_engine.get_project_analysis()
        if project_analysis:
            print(f"\n📊 Project Analysis:")
            print(f"   Type: {project_analysis.project_type.value}")
            print(f"   Language: {project_analysis.main_language}")
            print(f"   Frameworks: {len(project_analysis.frameworks)} detected")
            print(f"   Confidence: {project_analysis.confidence_score:.2f}")

    print(f"\n💡 The shell now understands your project structure!")
    print(f"   Ask questions and get context-aware responses.")


async def demo_cli_integration():
    """Demonstrate CLI tool integration."""
    print("\n🛠️  CLI Tools Integration Demo")
    print("=" * 30)

    print("The shell integrates powerful CLI tools:")
    print()

    print("📁 File System Tools:")
    print("   /find *.py               - Find all Python files")
    print("   /grep 'async def'        - Search for async functions")
    print("   /info src/               - Get directory information")
    print()

    print("🌐 Network Tools:")
    print("   /ping google.com         - Test internet connectivity")
    print("   /services                - Check local development services")
    print("   /port localhost 8080     - Check if port is open")
    print()

    print("💻 System Tools:")
    print("   /system                  - Show system information")
    print("   /processes python        - List Python processes")
    print()

    print("🔧 Development Tools:")
    print("   /projectinfo             - Analyze project structure")
    print("   /analyze                 - Deep project analysis")
    print()

    print("💡 All tools work seamlessly with AI conversations!")


async def start_interactive_mode():
    """Start the interactive shell."""
    print("\n🚀 Starting Interactive Mode")
    print("=" * 30)

    project_path = str(Path(__file__).parent.parent)

    print("🎯 Tips for the interactive session:")
    print("   • Type /help to see all commands")
    print("   • Ask questions about the codebase")
    print("   • Use /context to check system status")
    print("   • Try /find and /grep for file operations")
    print("   • Type /exit when you're done")
    print()

    print("🐚 Launching Agent Swarm Shell...")
    print("=" * 50)

    # Create and run the interactive shell
    shell = AgentSwarmShell(verbose=False, auto_index=None)  # Ask user about indexing

    try:
        # Initialize with project path
        await shell.initialize(project_path)
        await shell.run()
    except KeyboardInterrupt:
        print("\n👋 Shell session ended. Thanks for exploring!")


async def main():
    """Main demo function."""
    setup_logging(level="INFO")

    try:
        # Show features and capabilities
        await demo_shell_features()
        await demo_context_awareness()
        await demo_cli_integration()

        # Ask user if they want to try interactive mode
        print("\n🎯 Ready to try the interactive shell?")
        print("   Press Enter to start interactive mode")
        print("   Or Ctrl+C to exit")

        try:
            input()
            await start_interactive_mode()
        except KeyboardInterrupt:
            print("\n👋 Demo ended. Thanks for exploring!")

    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Install dependencies: pip install -e .")
        print("2. Set up Ollama: ollama pull llama3.2:3b")
        print("3. Run setup: agent-swarm setup")


if __name__ == "__main__":
    asyncio.run(main())
