# 🚀 Agent Swarm Examples

Welcome to Agent Swarm examples! This directory contains **5 focused demonstrations** that showcase the full power of the Agent Swarm framework.

## 📋 Quick Start Guide

### **Prerequisites**
- Python 3.8+
- Ollama installed (recommended): https://ollama.ai/
- Run: `pip install -e .`

### **Setup**
```bash
# Install Agent Swarm
pip install -e .

# Set up environment (installs <PERSON><PERSON><PERSON>, pulls models, configures everything)
agent-swarm setup

# Test your setup
agent-swarm test
```

## 🎯 Examples Overview

### **1. 🚀 Quick Start** (`quick_start.py`)
**Your first experience with Agent Swarm**

```bash
python examples/quick_start.py
```

**What you'll see:**
- Basic agent creation and usage
- Local LLM integration (Ollama)
- Simple coding tasks
- Cost-effective model routing
- Multi-tier LLM selection

**Perfect for:** First-time users, understanding core concepts

---

### **2. 🤖 Coding Agent** (`coding_agent.py`)
**Real development workflows and intelligent coding assistance**

```bash
python examples/coding_agent.py
```

**What you'll see:**
- Feature implementation with context awareness
- Intelligent bug fixing and debugging
- Code review and optimization
- Multi-step development workflows
- RAG-powered code understanding

**Perfect for:** Developers, understanding coding capabilities

---

### **3. 🐚 Interactive Shell** (`interactive_shell.py`)
**Interactive development environment**

```bash
python examples/interactive_shell.py
```

**What you'll see:**
- Context-aware conversations with your codebase
- Integrated CLI tools (find, grep, ping, system info)
- Real-time project analysis
- Multi-model routing for optimal responses
- Development command integration

**Perfect for:** Interactive development, exploring capabilities

---

### **4. 🧠 Context Engine** (`context_engine.py`)
**Smart project understanding and RAG capabilities**

```bash
python examples/context_engine.py
```

**What you'll see:**
- Project structure analysis and understanding
- RAG-powered code search and retrieval
- Intent detection and smart context assembly
- Multi-speed response modes (fast/detailed/comprehensive)
- Intelligent context depth control

**Perfect for:** Understanding RAG capabilities, project analysis

---

### **5. 🔗 MCP Integration** (`mcp_integration.py`)
**Model Context Protocol for standardized tool calling**

```bash
python examples/mcp_integration.py
```

**What you'll see:**
- Standardized tool calling across different LLMs
- Secure tool execution with proper boundaries
- Agent collaboration through shared tools
- Custom tool development and integration
- Tool ecosystem and community

**Perfect for:** Advanced users, tool integration, extensibility

## 🎯 Choosing the Right Example

### **New to Agent Swarm?**
Start with **Quick Start** → **Interactive Shell** → **Coding Agent**

### **Want to understand specific features?**
- **Multi-LLM routing:** Quick Start
- **Development workflows:** Coding Agent  
- **Interactive usage:** Interactive Shell
- **RAG and context:** Context Engine
- **Tool integration:** MCP Integration

### **Building applications?**
- **Simple automation:** Quick Start + Coding Agent
- **Interactive tools:** Interactive Shell + Context Engine
- **Complex workflows:** All examples, focus on MCP Integration

## 🛠️ Troubleshooting

### **Common Issues**

**"No LLMs available"**
```bash
# Install and set up Ollama
agent-swarm setup

# Or manually:
# 1. Install Ollama: https://ollama.ai/
# 2. Pull a model: ollama pull llama3.2:3b
# 3. Test: ollama run llama3.2:3b "Hello"
```

**"Import errors"**
```bash
# Install in development mode
pip install -e .

# Or install dependencies
pip install -r requirements.txt
```

**"Context engine not working"**
```bash
# Ensure you're in a project directory with code files
cd /path/to/your/project
python examples/context_engine.py
```

**"MCP tools not available"**
```bash
# Install MCP SDK
pip install mcp

# Check MCP integration
python -c "from agent_swarm.mcp import setup_default_mcp_tools; print('MCP OK')"
```

### **Getting Help**

1. **Check logs:** `agent-swarm logs`
2. **Test setup:** `agent-swarm test`
3. **Verbose mode:** Add `--verbose` to any command
4. **Documentation:** See `docs/` directory
5. **Issues:** https://github.com/your-repo/agent-swarm/issues

## 🎯 Next Steps

### **After running examples:**

1. **Integrate with your workflow:**
   ```bash
   agent-swarm shell --project /path/to/your/project
   ```

2. **Customize for your needs:**
   - Configure models in `config.yaml`
   - Add custom tools and integrations
   - Set up cloud LLM API keys

3. **Build applications:**
   - Use Agent Swarm as a library
   - Create custom agents for your domain
   - Integrate with existing systems

4. **Join the community:**
   - Share your use cases
   - Contribute tools and examples
   - Help improve the framework

## 📚 Additional Resources

- **📖 Main README:** `../README.md` - Complete feature overview
- **🐚 Shell Guide:** `../docs/INTERACTIVE_SHELL.md` - Detailed shell documentation
- **🔗 MCP Guide:** `../docs/MCP_INTEGRATION_GUIDE.md` - MCP integration details
- **🧠 RAG Guide:** `../docs/RAG_DEVELOPMENT_GUIDE.md` - RAG development guide

## 🎉 Happy Coding!

Agent Swarm is designed to make AI-powered development accessible, powerful, and fun. These examples are just the beginning - the real magic happens when you start building with it!

**Questions? Ideas? Contributions?** We'd love to hear from you! 🚀
