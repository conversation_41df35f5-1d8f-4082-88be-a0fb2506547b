#!/usr/bin/env python3
"""
🚀 Agent Swarm Quick Start

Your first experience with Agent Swarm - the multi-LLM development framework.
This example demonstrates:
- Basic agent creation and usage
- Local LLM integration (Ollama)
- Simple coding tasks
- Cost-effective model routing

Prerequisites:
- Python 3.8+
- Ollama installed (optional, but recommended)
- Run: pip install -e .

Usage:
    python examples/quick_start.py
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm import LLMRouter, LLMTier, Message, setup_logging
from agent_swarm.utils import load_config


async def quick_start_demo():
    """Quick start demonstration of Agent Swarm."""
    print("🚀 Agent Swarm Quick Start")
    print("=" * 50)
    print("Welcome to Agent Swarm - the multi-LLM development framework!")
    print("This demo will show you the basics in just a few minutes.\n")

    # Setup logging
    setup_logging(level="INFO")

    # Load configuration
    config = load_config()
    print(f"📋 Configuration loaded")
    print(f"   Default model: {config.default_local_model}")
    print(f"   Fallback model: {config.fallback_local_model}")

    # Create router - the heart of Agent Swarm
    router = LLMRouter()
    print(f"\n🎯 LLM Router created - this manages all your models")

    # Try to set up local LLMs (free and fast!)
    local_available = await setup_local_llms(router, config)
    
    # Try to set up cloud LLMs (premium quality)
    cloud_available = await setup_cloud_llms(router, config)

    if not local_available and not cloud_available:
        print("\n❌ No LLMs available. Let's fix this:")
        print("   1. Install Ollama: https://ollama.ai/")
        print("   2. Pull a model: ollama pull llama3.2:3b")
        print("   3. Or run: agent-swarm setup")
        return

    # Set up intelligent routing
    router.set_fallback_chain(list(router.llms.keys()))
    print(f"\n🔄 Smart routing configured with {len(router.llms)} models")

    # Demo 1: Simple coding task
    await demo_simple_coding(router)
    
    # Demo 2: Intelligent model selection
    await demo_smart_routing(router)
    
    # Demo 3: Cost optimization
    await demo_cost_optimization(router)

    # Show what's next
    show_next_steps()


async def setup_local_llms(router, config):
    """Set up local LLMs (free and fast)."""
    print(f"\n🏠 Setting up local LLMs (free and fast)...")
    local_available = False

    try:
        from agent_swarm.backends.ollama import create_ollama_llm

        # Primary local model
        print(f"   🔧 Configuring {config.default_local_model}...")
        primary_llm = create_ollama_llm(config.default_local_model)
        await primary_llm.initialize()

        if await primary_llm.is_available():
            router.register_llm("local_primary", primary_llm)
            local_available = True
            print(f"   ✅ {config.default_local_model} ready")
        else:
            print(f"   ⚠️  {config.default_local_model} not available")

        # Fallback model
        if config.fallback_local_model != config.default_local_model:
            print(f"   🔧 Configuring {config.fallback_local_model}...")
            fallback_llm = create_ollama_llm(config.fallback_local_model)
            await fallback_llm.initialize()

            if await fallback_llm.is_available():
                router.register_llm("local_fallback", fallback_llm)
                print(f"   ✅ {config.fallback_local_model} ready")

    except ImportError:
        print("   ⚠️  Ollama backend not available")
    except Exception as e:
        print(f"   ⚠️  Local LLM setup failed: {e}")

    return local_available


async def setup_cloud_llms(router, config):
    """Set up cloud LLMs (premium quality)."""
    print(f"\n☁️  Setting up cloud LLMs (premium quality)...")
    cloud_available = False

    if config.anthropic_api_key:
        try:
            from agent_swarm.backends.cloud import create_claude_llm

            claude = create_claude_llm()
            await claude.initialize()
            router.register_llm("claude", claude)
            cloud_available = True
            print("   ✅ Claude 3.5 Sonnet ready")
        except Exception as e:
            print(f"   ⚠️  Claude setup failed: {e}")
    else:
        print("   ℹ️  No Anthropic API key found (optional)")

    return cloud_available


async def demo_simple_coding(router):
    """Demonstrate simple coding task."""
    print(f"\n💻 Demo 1: Simple Coding Task")
    print("-" * 30)

    task = "Write a Python function to calculate the factorial of a number"
    print(f"Task: {task}")

    messages = [
        Message(role="system", content="You are a helpful coding assistant. Be concise and include example usage."),
        Message(role="user", content=task),
    ]

    try:
        response = await router.route_request(
            messages=messages,
            task_type="simple_coding",
            preferred_tier=LLMTier.LOCAL_FAST,
            max_tokens=300,
        )

        print(f"\n🤖 Response from {response.model}:")
        print(f"{response.content}")
        print(f"\n💰 Estimated cost: ${router.estimate_cost('simple_coding', response.usage['prompt_tokens'], response.usage['completion_tokens']):.4f}")

    except Exception as e:
        print(f"❌ Task failed: {e}")


async def demo_smart_routing(router):
    """Demonstrate intelligent model selection."""
    print(f"\n🧠 Demo 2: Smart Model Routing")
    print("-" * 30)

    tasks = [
        ("Explain what a Python list is", "simple", LLMTier.LOCAL_FAST),
        ("Design a database schema for a blog", "complex", LLMTier.LOCAL_QUALITY),
        ("Write a comprehensive API documentation", "documentation", LLMTier.CLOUD_PREMIUM),
    ]

    for task, task_type, preferred_tier in tasks:
        print(f"\nTask: {task}")
        print(f"Type: {task_type} → Preferred tier: {preferred_tier.value}")

        messages = [
            Message(role="system", content="You are a helpful assistant. Be concise."),
            Message(role="user", content=task),
        ]

        try:
            response = await router.route_request(
                messages=messages,
                task_type=task_type,
                preferred_tier=preferred_tier,
                max_tokens=150,
            )

            print(f"🤖 Routed to: {response.model}")
            print(f"📝 Response: {response.content[:100]}...")

        except Exception as e:
            print(f"❌ Failed: {e}")


async def demo_cost_optimization(router):
    """Demonstrate cost optimization."""
    print(f"\n💰 Demo 3: Cost Optimization")
    print("-" * 30)

    print("Agent Swarm automatically optimizes costs by:")
    print("• Using free local models for simple tasks (80% of work)")
    print("• Reserving premium models for complex tasks (20% of work)")
    print("• Automatic fallbacks if preferred model unavailable")
    print("• Real-time cost estimation and tracking")

    # Show available models and their tiers
    available_llms = router.get_available_llms()
    print(f"\n📊 Your available models:")
    for name, llm_config in available_llms.items():
        cost_per_1k = router.estimate_cost("general", 1000, 1000) * 1000
        print(f"• {llm_config.name} ({llm_config.tier.value}) - ~${cost_per_1k:.3f}/1K tokens")


def show_next_steps():
    """Show what users can do next."""
    print(f"\n🎯 What's Next?")
    print("=" * 20)
    print("✨ You've seen the basics! Here's what to explore next:")
    print()
    print("📚 More Examples:")
    print("   python examples/coding_agent.py      # Real development workflows")
    print("   python examples/interactive_shell.py # Interactive development")
    print("   python examples/context_engine.py    # Smart project analysis")
    print()
    print("🛠️  Setup & Configuration:")
    print("   agent-swarm setup                    # Complete environment setup")
    print("   agent-swarm test                     # Test your configuration")
    print("   agent-swarm shell                    # Start interactive shell")
    print()
    print("📖 Documentation:")
    print("   README.md                            # Complete feature overview")
    print("   docs/INTERACTIVE_SHELL.md           # Shell guide")
    print("   docs/MCP_INTEGRATION_GUIDE.md       # MCP integration")
    print()
    print("🚀 Happy coding with Agent Swarm!")


def main():
    """Main entry point."""
    try:
        asyncio.run(quick_start_demo())
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted. Thanks for trying Agent Swarm!")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Install dependencies: pip install -e .")
        print("2. Set up Ollama: https://ollama.ai/")
        print("3. Pull a model: ollama pull llama3.2:3b")
        print("4. Run setup: agent-swarm setup")


if __name__ == "__main__":
    main()
