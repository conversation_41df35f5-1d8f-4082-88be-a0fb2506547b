#!/usr/bin/env python3
"""
Intent Filtering Triangle Demo - Revolutionary Multi-Dimensional Intent Filtering.

This demo showcases the IntentFilteringTriangle algorithm, a breakthrough in
intent understanding that processes user intents through three fundamental dimensions:

1. **Clarity Dimension** - How clear and explicit the intent is
2. **Complexity Dimension** - How complex the intent processing requirements are  
3. **Context Dimension** - How much contextual information is needed

The algorithm demonstrates:
- Multi-dimensional intent space mapping (3D coordinates)
- Holistic intelligence with symbiotic dimension interactions
- Flow state analysis for optimal processing paths
- Advanced filtering strategies with mathematical optimization
- Real-time intent space visualization and analytics

This represents the next evolution in AI intent understanding.
"""

import asyncio
import json
from pathlib import Path
import time

# Add the src directory to the path for imports
import sys
# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm.algorithms import (
    # Core framework
    AlgorithmEngine,
    AlgorithmConfig,
    ExecuteAlgorithmAction,
    
    # The revolutionary algorithm
    IntentFilteringTriangle,
    
    # Supporting algorithms
    AdaptiveIntentProcessor,
    ConfidenceThresholder,
    
    # Utilities
    create_algorithm_engine,
    create_parameter_definition,
)


async def demo_basic_intent_filtering():
    """Demonstrate basic intent filtering triangle functionality."""
    print("\n" + "="*70)
    print("🔺 INTENT FILTERING TRIANGLE - BASIC DEMO")
    print("="*70)
    
    # Create engine with holistic configuration
    engine = create_algorithm_engine(
        algorithm_name="intent_filtering_triangle",
        parameters={
            "filtering_strategy": "holistic",
            "enable_symbiotic_intelligence": True,
            "enable_flow_state_analysis": True,
            "visualization_detail_level": "detailed",
        }
    )
    
    # Register the algorithm
    engine.registry.register_algorithm(IntentFilteringTriangle)
    
    # Test various intent types across the 3D space
    test_intents = [
        "/help config",                           # High clarity, low complexity, low context
        "create a Python function to sort data", # Medium clarity, medium complexity, low context
        "I need help with this thing",           # Low clarity, low complexity, high context
        "optimize the machine learning algorithm for better performance", # Medium clarity, high complexity, medium context
        "what should I do about this situation?", # Low clarity, medium complexity, high context
    ]
    
    print("🧪 Testing various intents across the 3D intent space:")
    
    for i, intent in enumerate(test_intents, 1):
        print(f"\n📝 Test {i}: '{intent}'")
        
        # Create action
        action = ExecuteAlgorithmAction(
            algorithm_name="intent_filtering_triangle",
            input_data=intent,
        )
        
        # Execute with timing
        start_time = time.time()
        observation = await engine.execute_action(action)
        execution_time = time.time() - start_time
        
        if observation.success:
            output = observation.output_data
            
            print(f"  ✅ Success | Time: {execution_time:.4f}s")
            
            # Show 3D coordinates
            coords = output.get('intent_coordinates', {})
            print(f"  📍 3D Coordinates:")
            print(f"    Clarity: {coords.get('clarity', 0):.2f}")
            print(f"    Complexity: {coords.get('complexity', 0):.2f}")
            print(f"    Context: {coords.get('context', 0):.2f}")
            
            # Show holistic score
            holistic = output.get('holistic_score', {})
            print(f"  🎯 Holistic Score: {holistic.get('overall_score', 0):.2f}")
            print(f"  🔮 Confidence: {holistic.get('overall_confidence', 0):.2f}")
            
            # Show flow state
            flow_state = output.get('flow_state_analysis', {})
            print(f"  🌊 Flow State: {flow_state.get('flow_state', 'unknown')}")
            
            # Show intent classification
            classification = output.get('dimensional_analysis', {}).get('intent_classification', {})
            print(f"  🏷️  Intent Type: {classification.get('intent_type', 'unknown')}")
            
        else:
            print(f"  ❌ Failed: {observation.error}")


async def demo_filtering_strategies():
    """Demonstrate different filtering strategies."""
    print("\n" + "="*70)
    print("🎛️ FILTERING STRATEGIES COMPARISON")
    print("="*70)
    
    strategies = ["holistic", "dimensional", "geometric", "harmonic", "adaptive"]
    test_intent = "analyze customer data to improve marketing campaigns"
    
    print(f"📝 Testing intent: '{test_intent}'")
    print("🔬 Comparing filtering strategies:")
    
    results = {}
    
    for strategy in strategies:
        print(f"\n🧪 Testing {strategy} strategy...")
        
        # Create engine for this strategy
        engine = create_algorithm_engine(
            algorithm_name="intent_filtering_triangle",
            parameters={
                "filtering_strategy": strategy,
                "enable_symbiotic_intelligence": True,
                "enable_flow_state_analysis": True,
            }
        )
        
        engine.registry.register_algorithm(IntentFilteringTriangle)
        
        # Execute
        action = ExecuteAlgorithmAction(
            algorithm_name="intent_filtering_triangle",
            input_data=test_intent,
        )
        
        start_time = time.time()
        observation = await engine.execute_action(action)
        execution_time = time.time() - start_time
        
        if observation.success:
            output = observation.output_data
            holistic = output.get('holistic_score', {})
            flow_state = output.get('flow_state_analysis', {})
            
            results[strategy] = {
                'execution_time': execution_time,
                'overall_score': holistic.get('overall_score', 0),
                'confidence': holistic.get('overall_confidence', 0),
                'flow_state': flow_state.get('flow_state', 'unknown'),
                'strategy_used': holistic.get('strategy_used', strategy),
            }
            
            print(f"  ✅ Score: {results[strategy]['overall_score']:.3f} | "
                  f"Confidence: {results[strategy]['confidence']:.3f} | "
                  f"Flow: {results[strategy]['flow_state']}")
        else:
            print(f"  ❌ Failed: {observation.error}")
            results[strategy] = {'error': observation.error}
    
    # Compare results
    print(f"\n📊 Strategy Comparison Summary:")
    print(f"{'Strategy':<12} {'Score':<8} {'Confidence':<12} {'Flow State':<12} {'Time (s)'}")
    print("-" * 65)
    
    for strategy, result in results.items():
        if 'error' not in result:
            score_str = f"{result['overall_score']:.3f}"
            conf_str = f"{result['confidence']:.3f}"
            flow_str = result['flow_state'][:11]
            time_str = f"{result['execution_time']:.4f}"
            
            print(f"{strategy:<12} {score_str:<8} {conf_str:<12} {flow_str:<12} {time_str}")
        else:
            print(f"{strategy:<12} {'ERROR':<8} {'-':<12} {'-':<12} {'-'}")


async def demo_symbiotic_intelligence():
    """Demonstrate symbiotic intelligence between dimensions."""
    print("\n" + "="*70)
    print("🧠 SYMBIOTIC INTELLIGENCE DEMONSTRATION")
    print("="*70)
    
    # Test with and without symbiotic intelligence
    test_intent = "fix this bug in the code"  # Ambiguous but simple
    
    print(f"📝 Testing intent: '{test_intent}'")
    print("🔬 Comparing with and without symbiotic intelligence:")
    
    configurations = [
        {"enable_symbiotic_intelligence": False, "name": "Without Symbiotic"},
        {"enable_symbiotic_intelligence": True, "symbiotic_strength": 0.1, "name": "Low Symbiotic"},
        {"enable_symbiotic_intelligence": True, "symbiotic_strength": 0.2, "name": "High Symbiotic"},
    ]
    
    for config in configurations:
        print(f"\n🧪 Testing {config['name']}...")
        
        engine = create_algorithm_engine(
            algorithm_name="intent_filtering_triangle",
            parameters=config
        )
        
        engine.registry.register_algorithm(IntentFilteringTriangle)
        
        action = ExecuteAlgorithmAction(
            algorithm_name="intent_filtering_triangle",
            input_data=test_intent,
        )
        
        observation = await engine.execute_action(action)
        
        if observation.success:
            output = observation.output_data
            
            # Original coordinates
            coords = output.get('intent_coordinates', {})
            print(f"  📍 Original Coordinates:")
            print(f"    Clarity: {coords.get('clarity', 0):.3f}")
            print(f"    Complexity: {coords.get('complexity', 0):.3f}")
            print(f"    Context: {coords.get('context', 0):.3f}")
            
            # Symbiotic analysis if available
            symbiotic = output.get('symbiotic_analysis', {})
            if symbiotic and 'adjusted_scores' in symbiotic:
                adjusted = symbiotic['adjusted_scores']
                print(f"  🔄 Adjusted Coordinates:")
                print(f"    Clarity: {adjusted.get('clarity', 0):.3f}")
                print(f"    Complexity: {adjusted.get('complexity', 0):.3f}")
                print(f"    Context: {adjusted.get('context', 0):.3f}")
                
                # Show adjustments
                adjustments = symbiotic.get('symbiotic_adjustments', {})
                for adj_type, value in adjustments.items():
                    if abs(value) > 0.001:
                        print(f"    {adj_type}: {value:+.3f}")
            
            # Final holistic score
            holistic = output.get('holistic_score', {})
            print(f"  🎯 Final Score: {holistic.get('overall_score', 0):.3f}")
            print(f"  🔮 Confidence: {holistic.get('overall_confidence', 0):.3f}")
        else:
            print(f"  ❌ Failed: {observation.error}")


async def demo_flow_state_analysis():
    """Demonstrate flow state analysis for optimal processing."""
    print("\n" + "="*70)
    print("🌊 FLOW STATE ANALYSIS DEMONSTRATION")
    print("="*70)
    
    # Test intents designed to trigger different flow states
    flow_test_intents = [
        ("/help", "Optimal Flow"),
        ("create a simple function", "Good Flow"),
        ("help me understand this concept", "Acceptable Flow"),
        ("I need to solve this complex problem but I'm not sure how", "Challenging Flow"),
        ("what?", "Difficult Flow"),
    ]
    
    print("🧪 Testing intents designed for different flow states:")
    
    engine = create_algorithm_engine(
        algorithm_name="intent_filtering_triangle",
        parameters={
            "enable_flow_state_analysis": True,
            "filtering_strategy": "holistic",
        }
    )
    
    engine.registry.register_algorithm(IntentFilteringTriangle)
    
    for intent, expected_flow in flow_test_intents:
        print(f"\n📝 Testing: '{intent}' (Expected: {expected_flow})")
        
        action = ExecuteAlgorithmAction(
            algorithm_name="intent_filtering_triangle",
            input_data=intent,
        )
        
        observation = await engine.execute_action(action)
        
        if observation.success:
            output = observation.output_data
            
            flow_analysis = output.get('flow_state_analysis', {})
            flow_state = flow_analysis.get('flow_state', 'unknown')
            flow_score = flow_analysis.get('flow_score', 0)
            flow_confidence = flow_analysis.get('flow_confidence', 0)
            
            print(f"  🌊 Flow State: {flow_state}")
            print(f"  📊 Flow Score: {flow_score:.3f}")
            print(f"  🔮 Flow Confidence: {flow_confidence:.3f}")
            print(f"  📝 Description: {flow_analysis.get('flow_description', 'N/A')}")
            
            # Show recommendations
            recommendations = flow_analysis.get('recommendations', [])
            if recommendations:
                print(f"  💡 Recommendations:")
                for rec in recommendations[:3]:  # Show first 3
                    print(f"    • {rec}")
            
            # Show flow metrics
            metrics = flow_analysis.get('flow_metrics', {})
            if metrics:
                print(f"  📈 Flow Metrics:")
                print(f"    Efficiency: {metrics.get('flow_efficiency', 0):.3f}")
                print(f"    Readiness: {metrics.get('processing_readiness', 0):.3f}")
                print(f"    Stability: {metrics.get('intent_stability', 0):.3f}")
        else:
            print(f"  ❌ Failed: {observation.error}")


async def demo_intent_space_visualization():
    """Demonstrate intent space visualization capabilities."""
    print("\n" + "="*70)
    print("📊 INTENT SPACE VISUALIZATION DEMONSTRATION")
    print("="*70)
    
    # Test different visualization detail levels
    detail_levels = ["minimal", "standard", "detailed", "comprehensive"]
    test_intent = "create a machine learning model to predict customer behavior"
    
    print(f"📝 Testing intent: '{test_intent}'")
    print("🔬 Comparing visualization detail levels:")
    
    for detail_level in detail_levels:
        print(f"\n🧪 Testing {detail_level} visualization...")
        
        engine = create_algorithm_engine(
            algorithm_name="intent_filtering_triangle",
            parameters={
                "visualization_detail_level": detail_level,
                "filtering_strategy": "holistic",
                "enable_symbiotic_intelligence": True,
            }
        )
        
        engine.registry.register_algorithm(IntentFilteringTriangle)
        
        action = ExecuteAlgorithmAction(
            algorithm_name="intent_filtering_triangle",
            input_data=test_intent,
        )
        
        observation = await engine.execute_action(action)
        
        if observation.success:
            output = observation.output_data
            visualization = output.get('visualization_data', {})
            
            print(f"  📊 Visualization Components:")
            for component, data in visualization.items():
                if isinstance(data, dict):
                    print(f"    • {component}: {len(data)} elements")
                elif isinstance(data, list):
                    print(f"    • {component}: {len(data)} items")
                else:
                    print(f"    • {component}: {type(data).__name__}")
            
            # Show intent space region if available
            if 'intent_space_region' in visualization:
                region = visualization['intent_space_region']
                print(f"  🗺️  Intent Space Region: {region}")
            
            # Show processing path if available
            if 'processing_path_visualization' in visualization:
                path_viz = visualization['processing_path_visualization']
                stages = path_viz.get('recommended_stages', [])
                print(f"  🛤️  Recommended Processing Path: {' → '.join(stages)}")
        else:
            print(f"  ❌ Failed: {observation.error}")


async def demo_filtering_analytics():
    """Demonstrate filtering analytics and learning capabilities."""
    print("\n" + "="*70)
    print("📈 FILTERING ANALYTICS DEMONSTRATION")
    print("="*70)
    
    # Create engine and process multiple intents to build analytics
    engine = create_algorithm_engine(
        algorithm_name="intent_filtering_triangle",
        parameters={
            "filtering_strategy": "adaptive",
            "enable_symbiotic_intelligence": True,
        }
    )
    
    engine.registry.register_algorithm(IntentFilteringTriangle)
    
    # Get algorithm instance for analytics
    algorithm = engine.registry.get_algorithm("intent_filtering_triangle")
    
    # Process a variety of intents
    analytics_intents = [
        "/help",
        "/config show",
        "create a function",
        "analyze this data",
        "what should I do?",
        "optimize the algorithm",
        "explain machine learning",
        "debug this code",
        "I need help with something",
        "build a web application",
    ]
    
    print("🔄 Processing multiple intents for analytics...")
    
    for i, intent in enumerate(analytics_intents, 1):
        print(f"  Processing {i}/10: '{intent[:30]}{'...' if len(intent) > 30 else ''}'")
        
        action = ExecuteAlgorithmAction(
            algorithm_name="intent_filtering_triangle",
            input_data=intent,
        )
        
        observation = await engine.execute_action(action)
        
        # Brief pause to simulate real usage
        await asyncio.sleep(0.1)
    
    # Show analytics
    print(f"\n📈 Filtering Analytics:")
    
    if hasattr(algorithm, 'get_filtering_analytics'):
        analytics = algorithm.get_filtering_analytics()
        
        print(f"  📊 Total Processed: {analytics.get('total_processed', 0)}")
        print(f"  🎯 Average Score: {analytics.get('average_score', 0):.3f}")
        print(f"  🔮 Average Confidence: {analytics.get('average_confidence', 0):.3f}")
        
        # Recommendation distribution
        rec_dist = analytics.get('recommendation_distribution', {})
        if rec_dist:
            print(f"\n  📋 Recommendation Distribution:")
            for rec, count in rec_dist.items():
                print(f"    {rec}: {count} times")
        
        # Priority distribution
        pri_dist = analytics.get('priority_distribution', {})
        if pri_dist:
            print(f"\n  ⚡ Priority Distribution:")
            for pri, count in pri_dist.items():
                print(f"    {pri}: {count} times")
        
        # Recent trends
        trends = analytics.get('recent_trends', {})
        if trends and 'message' not in trends:
            print(f"\n  📈 Recent Trends:")
            print(f"    Score Trend: {trends.get('score_trend', 'unknown')}")
            print(f"    Confidence Trend: {trends.get('confidence_trend', 'unknown')}")


async def main():
    """Run all intent filtering triangle demos."""
    print("🔺 INTENT FILTERING TRIANGLE - COMPREHENSIVE DEMO")
    print("🚀 Revolutionary Multi-Dimensional Intent Filtering")
    print("🧠 Holistic Intelligence | 🌊 Flow State Analysis | 📊 3D Intent Space")
    
    try:
        # Run all demos
        await demo_basic_intent_filtering()
        await demo_filtering_strategies()
        await demo_symbiotic_intelligence()
        await demo_flow_state_analysis()
        await demo_intent_space_visualization()
        await demo_filtering_analytics()
        
        print("\n" + "="*70)
        print("🎉 INTENT FILTERING TRIANGLE DEMO COMPLETED!")
        print("="*70)
        print("✨ The IntentFilteringTriangle demonstrates revolutionary capabilities:")
        print("  🔺 Multi-dimensional intent space mapping (Clarity × Complexity × Context)")
        print("  🧠 Symbiotic intelligence with dimension interactions")
        print("  🌊 Flow state analysis for optimal processing paths")
        print("  🎛️ Multiple filtering strategies with mathematical optimization")
        print("  📊 Real-time intent space visualization and analytics")
        print("  📈 Learning and adaptation from processing history")
        print("\n🚀 Ready to revolutionize AI intent understanding!")
        
    except Exception as e:
        print(f"\n💥 Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
