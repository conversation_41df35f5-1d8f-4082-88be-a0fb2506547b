#!/usr/bin/env python3
"""
Thinking-Enabled LLM Demo - Latest Ollama Features Integration.

This demo showcases the integration of the latest Ollama features with our
mathematical intent processing algorithms:

- Thinking capabilities (enable/disable based on complexity)
- Structured outputs with JSON schema validation
- <PERSON><PERSON> calling with streaming support
- Adaptive model selection based on intent analysis
- Performance optimization with thinking time management

Features demonstrated:
- DeepSeek-R1 and Qwen3 thinking models
- Adaptive thinking based on mathematical complexity analysis
- Fast vs quality mode selection
- Real-time performance monitoring
- Integration with Enhanced Intent Processor

This represents the cutting edge of local AI model interaction.
"""

import asyncio
import sys
import json
from pathlib import Path

# Add the src directory to the path for imports
# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm.backends.enhanced_llm_backend import (
    EnhancedLLMBackend,
    EnhancedLLMRequest,
    ThinkingConfig,
    ThinkingMode,
)
from agent_swarm.backends.abstract import Message


async def demo_thinking_modes():
    """Demonstrate different thinking modes with Ollama models."""
    print("\n" + "="*70)
    print("🧠 OLLAMA THINKING MODES DEMO")
    print("="*70)
    
    # Initialize enhanced backend
    backend = EnhancedLLMBackend()
    await backend.initialize()
    
    # Test different thinking modes
    thinking_tests = [
        {
            'name': 'Disabled Thinking (Fast Response)',
            'content': 'What is the capital of France?',
            'mode': ThinkingMode.DISABLED,
            'expected': 'Fast, direct answer without thinking process'
        },
        {
            'name': 'Enabled Thinking (Quality Response)',
            'content': 'Explain the mathematical proof of the Pythagorean theorem',
            'mode': ThinkingMode.ENABLED,
            'expected': 'Detailed thinking process with step-by-step reasoning'
        },
        {
            'name': 'Adaptive Thinking (Complexity-Based)',
            'content': 'Design a fault-tolerant distributed database system',
            'mode': ThinkingMode.ADAPTIVE,
            'expected': 'Thinking enabled due to high complexity'
        },
        {
            'name': 'Complex Only Mode',
            'content': 'Hello, how are you?',
            'mode': ThinkingMode.COMPLEX_ONLY,
            'expected': 'No thinking for simple greeting'
        },
    ]
    
    for i, test in enumerate(thinking_tests, 1):
        print(f"\n📝 Test {i}: {test['name']}")
        print(f"   Content: {test['content']}")
        print(f"   Expected: {test['expected']}")
        print("-" * 60)
        
        request = EnhancedLLMRequest(
            messages=[Message(role="user", content=test['content'])],
            model_id="",  # Auto-select best model
            thinking_config=ThinkingConfig(mode=test['mode']),
            priority="normal",
            temperature=0.3,
        )
        
        print(f"🔄 Processing with {test['mode'].value} mode...")
        response = await backend.generate(request)
        
        print(f"✅ Model: {response.model}")
        print(f"🧠 Thinking: {'✅ Enabled' if response.thinking_enabled else '❌ Disabled'}")
        print(f"⏱️  Response Time: {response.response_time:.3f}s")
        
        if response.thinking:
            print(f"💭 Thinking Time: {response.thinking_time:.3f}s")
            print(f"🔍 Thinking Preview: {response.thinking[:150]}...")
        
        print(f"📝 Response Preview: {response.content[:200]}...")
        
        # Show intent analysis if available
        if response.metadata and 'intent_analysis' in response.metadata:
            analysis = response.metadata['intent_analysis']
            complexity = analysis.get('complexity_score', 0)
            flow_state = analysis.get('flow_state', 'unknown')
            print(f"📊 Complexity: {complexity:.2f} | Flow: {flow_state}")


async def demo_model_capabilities():
    """Demonstrate different model capabilities."""
    print("\n" + "="*70)
    print("🤖 MODEL CAPABILITIES DEMO")
    print("="*70)
    
    backend = EnhancedLLMBackend()
    await backend.initialize()
    
    # Show available models and their capabilities
    models = backend.get_available_models()
    
    print("📋 Available Models and Capabilities:")
    for model_id, model_info in models.items():
        print(f"\n🤖 {model_id}:")
        print(f"   Name: {model_info['config']['name']}")
        print(f"   Tier: {model_info['config']['tier']}")
        print(f"   Capabilities:")
        
        capabilities = model_info['capabilities']
        for capability in capabilities:
            icon = {
                'thinking': '🧠',
                'structured_output': '📋',
                'tool_calling': '🔧',
                'streaming': '🌊',
                'multimodal': '🖼️'
            }.get(capability, '⚙️')
            print(f"     {icon} {capability}")
        
        print(f"   Recommended for: {', '.join(model_info['config']['recommended_for'])}")


async def demo_adaptive_selection():
    """Demonstrate adaptive model selection based on intent complexity."""
    print("\n" + "="*70)
    print("🎯 ADAPTIVE MODEL SELECTION DEMO")
    print("="*70)
    
    backend = EnhancedLLMBackend()
    await backend.initialize()
    
    # Test requests with different complexity levels
    selection_tests = [
        ("Simple greeting", "Hi there!", "Should select fast model"),
        ("Basic question", "What is Python?", "Should select balanced model"),
        ("Complex analysis", "Analyze the time complexity of quicksort vs mergesort", "Should select thinking model"),
        ("Very complex task", "Design a distributed consensus algorithm for blockchain", "Should select best thinking model"),
    ]
    
    print("🔄 Testing adaptive model selection...")
    
    for name, content, expectation in selection_tests:
        print(f"\n📝 {name}:")
        print(f"   Content: {content}")
        print(f"   Expectation: {expectation}")
        
        request = EnhancedLLMRequest(
            messages=[Message(role="user", content=content)],
            model_id="",  # Let system choose
            thinking_config=ThinkingConfig(mode=ThinkingMode.ADAPTIVE),
            priority="normal",
        )
        
        response = await backend.generate(request)
        
        print(f"   ✅ Selected: {response.model}")
        print(f"   🧠 Thinking: {'✅' if response.thinking_enabled else '❌'}")
        print(f"   ⏱️  Time: {response.response_time:.3f}s")
        
        if response.metadata and 'intent_analysis' in response.metadata:
            analysis = response.metadata['intent_analysis']
            complexity = analysis.get('complexity_score', 0)
            print(f"   📊 Complexity Score: {complexity:.2f}")


async def demo_performance_optimization():
    """Demonstrate performance optimization with thinking management."""
    print("\n" + "="*70)
    print("⚡ PERFORMANCE OPTIMIZATION DEMO")
    print("="*70)
    
    backend = EnhancedLLMBackend()
    await backend.initialize()
    
    # Test performance with different configurations
    performance_tests = [
        ("Fast Mode", "fast", ThinkingMode.DISABLED),
        ("Quality Mode", "quality", ThinkingMode.ENABLED),
        ("Adaptive Mode", "normal", ThinkingMode.ADAPTIVE),
    ]
    
    test_content = "Explain the concept of machine learning and provide a simple example."
    
    print(f"🔄 Testing performance with: '{test_content}'")
    
    results = []
    
    for mode_name, priority, thinking_mode in performance_tests:
        print(f"\n🧪 Testing {mode_name}...")
        
        request = EnhancedLLMRequest(
            messages=[Message(role="user", content=test_content)],
            model_id="",
            thinking_config=ThinkingConfig(mode=thinking_mode),
            priority=priority,
        )
        
        response = await backend.generate(request)
        
        result = {
            'mode': mode_name,
            'model': response.model,
            'thinking_enabled': response.thinking_enabled,
            'response_time': response.response_time,
            'thinking_time': response.thinking_time or 0,
            'content_length': len(response.content),
        }
        
        results.append(result)
        
        print(f"   🤖 Model: {result['model']}")
        print(f"   🧠 Thinking: {'✅' if result['thinking_enabled'] else '❌'}")
        print(f"   ⏱️  Total Time: {result['response_time']:.3f}s")
        if result['thinking_time'] > 0:
            print(f"   💭 Thinking Time: {result['thinking_time']:.3f}s")
        print(f"   📝 Response Length: {result['content_length']} chars")
    
    # Performance comparison
    print(f"\n📊 Performance Comparison:")
    print(f"{'Mode':<15} {'Model':<20} {'Thinking':<10} {'Time':<8} {'Length':<8}")
    print("-" * 70)
    
    for result in results:
        thinking_str = "✅" if result['thinking_enabled'] else "❌"
        print(f"{result['mode']:<15} {result['model']:<20} {thinking_str:<10} "
              f"{result['response_time']:.3f}s   {result['content_length']:<8}")


async def demo_real_world_scenarios():
    """Demonstrate real-world usage scenarios."""
    print("\n" + "="*70)
    print("🌍 REAL-WORLD SCENARIOS DEMO")
    print("="*70)
    
    backend = EnhancedLLMBackend()
    await backend.initialize()
    
    # Real-world scenarios with different requirements
    scenarios = [
        {
            'name': 'Code Review',
            'content': 'Review this Python function for potential issues: def factorial(n): return 1 if n <= 1 else n * factorial(n-1)',
            'priority': 'quality',
            'thinking_mode': ThinkingMode.ENABLED,
        },
        {
            'name': 'Quick Help',
            'content': 'How do I install a Python package?',
            'priority': 'fast',
            'thinking_mode': ThinkingMode.DISABLED,
        },
        {
            'name': 'Complex Problem Solving',
            'content': 'Design a caching strategy for a high-traffic web application with multiple data sources',
            'priority': 'quality',
            'thinking_mode': ThinkingMode.ENABLED,
        },
        {
            'name': 'Adaptive Learning',
            'content': 'Explain the differences between supervised and unsupervised learning',
            'priority': 'normal',
            'thinking_mode': ThinkingMode.ADAPTIVE,
        },
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📝 Scenario {i}: {scenario['name']}")
        print(f"   Content: {scenario['content'][:80]}...")
        print(f"   Priority: {scenario['priority']}")
        print(f"   Thinking: {scenario['thinking_mode'].value}")
        
        request = EnhancedLLMRequest(
            messages=[Message(role="user", content=scenario['content'])],
            model_id="",
            thinking_config=ThinkingConfig(mode=scenario['thinking_mode']),
            priority=scenario['priority'],
        )
        
        response = await backend.generate(request)
        
        print(f"   ✅ Model: {response.model}")
        print(f"   🧠 Thinking: {'✅' if response.thinking_enabled else '❌'}")
        print(f"   ⏱️  Time: {response.response_time:.3f}s")
        print(f"   📝 Response: {response.content[:100]}...")


async def main():
    """Run all thinking-enabled LLM demos."""
    print("🚀 THINKING-ENABLED LLM WITH LATEST OLLAMA FEATURES")
    print("🧠 Revolutionary Local AI with Mathematical Intent Processing")
    print("🔺 Adaptive Thinking Based on Complexity Analysis")
    
    try:
        # Run all demos
        await demo_thinking_modes()
        await demo_model_capabilities()
        await demo_adaptive_selection()
        await demo_performance_optimization()
        await demo_real_world_scenarios()
        
        print("\n" + "="*70)
        print("🎉 THINKING-ENABLED LLM DEMO COMPLETED!")
        print("="*70)
        print("✨ Revolutionary capabilities demonstrated:")
        print("  🧠 Adaptive thinking based on mathematical complexity analysis")
        print("  🎯 Intelligent model selection for optimal performance")
        print("  ⚡ Performance optimization with thinking time management")
        print("  🤖 Latest Ollama features (DeepSeek-R1, Qwen3, structured outputs)")
        print("  📊 Real-time analytics and performance monitoring")
        print("  🔺 Integration with Enhanced Intent Processor")
        print("\n🚀 Ready for production use with local AI models!")
        
        print("\n💡 Try it yourself:")
        print("  python examples/thinking_enabled_llm_demo.py")
        print("  # Or integrate into your Agent Swarm workflow")
        
    except Exception as e:
        print(f"\n💥 Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
