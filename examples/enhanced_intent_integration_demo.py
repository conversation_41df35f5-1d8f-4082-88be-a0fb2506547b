#!/usr/bin/env python3
"""
Enhanced Intent Integration Demo - Revolutionary Intent Processing in Agent Swarm.

This demo showcases the complete integration of mathematical intent processing
algorithms into Agent Swarm's interactive shell, demonstrating:

- AdaptiveIntentProcessor integration for 5-stage processing
- IntentFilteringTriangle for 3D intent space analysis
- Enhanced confidence scoring and processing recommendations
- Real-time intent analytics and optimization
- Seamless integration with existing intent detection

This represents the next evolution in AI-human interaction.
"""

import asyncio
import sys
from pathlib import Path

# Add the src directory to the path for imports
# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm.core.enhanced_intent_processor import EnhancedIntentProcessor


async def demo_enhanced_intent_processor():
    """Demonstrate the enhanced intent processor capabilities."""
    print("\n" + "="*70)
    print("🔺 ENHANCED INTENT PROCESSOR - INTEGRATION DEMO")
    print("="*70)
    
    # Initialize the enhanced intent processor
    processor = EnhancedIntentProcessor(
        enable_adaptive=True,
        enable_filtering=True,
        verbose=True
    )
    
    print("🔄 Initializing enhanced intent processor...")
    await processor.initialize()
    
    # Test various intent types
    test_scenarios = [
        {
            'name': 'Explicit Command',
            'intent': '/help config',
            'expected': 'High clarity, low complexity, early stopping'
        },
        {
            'name': 'Clear Development Request',
            'intent': 'create a Python function to sort a list',
            'expected': 'Medium clarity, medium complexity, good flow'
        },
        {
            'name': 'Ambiguous Request',
            'intent': 'I need help with this thing',
            'expected': 'Low clarity, high context need, challenging flow'
        },
        {
            'name': 'Complex Technical Task',
            'intent': 'implement a machine learning algorithm to predict customer churn using ensemble methods',
            'expected': 'High complexity, technical domain, full pipeline'
        },
        {
            'name': 'Vague Question',
            'intent': 'what should I do?',
            'expected': 'Very low clarity, high context need, difficult flow'
        },
    ]
    
    print("🧪 Testing enhanced intent processing across various scenarios:")
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📝 Scenario {i}: {scenario['name']}")
        print(f"   Intent: '{scenario['intent']}'")
        print(f"   Expected: {scenario['expected']}")
        print("   " + "-" * 50)
        
        # Process the intent
        enhanced_intent = await processor.process_intent(
            scenario['intent'],
            context={'scenario': scenario['name']}
        )
        
        # Display results
        print(f"   ✅ Processing Time: {enhanced_intent.processing_time:.4f}s")
        print(f"   🎯 Enhanced Confidence: {enhanced_intent.processing_confidence:.2f}")
        print(f"   🌊 Flow State: {enhanced_intent.flow_state}")
        
        # Show 3D coordinates if available
        if enhanced_intent.intent_coordinates:
            coords = enhanced_intent.intent_coordinates
            print(f"   📍 3D Coordinates: ({coords['clarity']:.2f}, {coords['complexity']:.2f}, {coords['context']:.2f})")
        
        # Show algorithms used
        if enhanced_intent.algorithms_used:
            algorithms = ", ".join(enhanced_intent.algorithms_used)
            print(f"   🧠 Algorithms: {algorithms}")
        
        # Show processing recommendations
        if enhanced_intent.processing_recommendations:
            rec = enhanced_intent.processing_recommendations
            print(f"   ⚡ Processing: {rec.get('primary_recommendation', 'standard')}")
            print(f"   🎯 Priority: {rec.get('processing_priority', 'normal')}")
        
        # Show optimization suggestions
        if enhanced_intent.optimization_suggestions:
            suggestions = enhanced_intent.optimization_suggestions[:2]  # Show top 2
            print(f"   💡 Suggestions: {'; '.join(suggestions)}")
        
        print(f"   💭 Analysis: {enhanced_intent.enhanced_reasoning}")


async def demo_intent_analytics():
    """Demonstrate intent processing analytics."""
    print("\n" + "="*70)
    print("📊 INTENT PROCESSING ANALYTICS DEMO")
    print("="*70)
    
    # Initialize processor
    processor = EnhancedIntentProcessor(
        enable_adaptive=True,
        enable_filtering=True,
        verbose=False  # Reduce verbosity for analytics demo
    )
    
    await processor.initialize()
    
    # Process multiple intents to build analytics
    analytics_intents = [
        "/help",
        "/config show",
        "create a function",
        "analyze this data",
        "what should I do?",
        "optimize the algorithm",
        "explain machine learning",
        "/exit",
        "build a web application",
        "debug this code",
        "I need help with something complex",
        "implement a REST API",
        "fix this bug",
        "what?",
        "create a dashboard for metrics",
    ]
    
    print("🔄 Processing multiple intents for analytics...")
    
    for i, intent in enumerate(analytics_intents, 1):
        print(f"  Processing {i}/15: '{intent[:30]}{'...' if len(intent) > 30 else ''}'")
        
        enhanced_intent = await processor.process_intent(intent)
        
        # Brief pause to simulate real usage
        await asyncio.sleep(0.05)
    
    # Show comprehensive analytics
    print(f"\n📈 Processing Analytics:")
    analytics = processor.get_processing_analytics()
    
    print(f"  📊 Total Processed: {analytics['total_processed']}")
    print(f"  🎯 Confidence Improvement Rate: {analytics['confidence_improvement_rate']:.1%}")
    print(f"  ⏱️  Average Processing Time: {analytics['average_processing_time']:.4f}s")
    print(f"  🔮 Recent Average Confidence: {analytics['recent_average_confidence']:.2f}")
    
    # Algorithm usage
    print(f"\n🧠 Algorithm Usage:")
    for algorithm, count in analytics['algorithm_usage'].items():
        rate = count / analytics['total_processed']
        print(f"    {algorithm}: {count} times ({rate:.1%})")
    
    # Flow state distribution
    if analytics['flow_state_distribution']:
        print(f"\n🌊 Flow State Distribution:")
        total_flow = sum(analytics['flow_state_distribution'].values())
        for state, count in analytics['flow_state_distribution'].items():
            percentage = count / total_flow
            print(f"    {state}: {count} times ({percentage:.1%})")
    
    print(f"\n⚡ Performance Metrics:")
    print(f"    Adaptive Usage Rate: {analytics['adaptive_usage_rate']:.1%}")
    print(f"    Filtering Usage Rate: {analytics['filtering_usage_rate']:.1%}")


async def demo_intent_explanations():
    """Demonstrate comprehensive intent explanations."""
    print("\n" + "="*70)
    print("💭 INTENT EXPLANATION DEMO")
    print("="*70)
    
    processor = EnhancedIntentProcessor(
        enable_adaptive=True,
        enable_filtering=True,
        verbose=False
    )
    
    await processor.initialize()
    
    # Test intents with detailed explanations
    explanation_intents = [
        "create a machine learning model to predict sales",
        "I'm having trouble with authentication",
        "/help debugging",
    ]
    
    for intent in explanation_intents:
        print(f"\n📝 Intent: '{intent}'")
        print("=" * 60)
        
        enhanced_intent = await processor.process_intent(intent)
        
        # Get comprehensive explanation
        explanation = processor.get_intent_explanation(enhanced_intent)
        print(explanation)


async def demo_comparison_with_traditional():
    """Compare enhanced processing with traditional intent detection."""
    print("\n" + "="*70)
    print("⚖️  ENHANCED vs TRADITIONAL COMPARISON")
    print("="*70)
    
    from agent_swarm.core.intent_detector import IntentDetector
    
    # Initialize both processors
    traditional_detector = IntentDetector()
    enhanced_processor = EnhancedIntentProcessor(
        enable_adaptive=True,
        enable_filtering=True,
        verbose=False
    )
    
    await enhanced_processor.initialize()
    
    # Test intents for comparison
    comparison_intents = [
        "create a function",
        "I need help with something",
        "analyze the performance data",
        "what should I do about this bug?",
    ]
    
    for intent in comparison_intents:
        print(f"\n📝 Intent: '{intent}'")
        print("-" * 50)
        
        # Traditional processing
        traditional_intent = traditional_detector.detect_intent(intent)
        print(f"🔧 Traditional:")
        print(f"    Type: {traditional_intent.intent_type.value}")
        print(f"    Confidence: {traditional_intent.confidence:.2f}")
        print(f"    Reasoning: {traditional_intent.reasoning}")
        
        # Enhanced processing
        enhanced_intent = await enhanced_processor.process_intent(intent)
        print(f"🔺 Enhanced:")
        print(f"    Enhanced Confidence: {enhanced_intent.processing_confidence:.2f}")
        print(f"    Flow State: {enhanced_intent.flow_state}")
        if enhanced_intent.intent_coordinates:
            coords = enhanced_intent.intent_coordinates
            print(f"    3D Coordinates: ({coords['clarity']:.2f}, {coords['complexity']:.2f}, {coords['context']:.2f})")
        print(f"    Algorithms: {', '.join(enhanced_intent.algorithms_used)}")
        
        # Show improvement
        confidence_improvement = enhanced_intent.processing_confidence - traditional_intent.confidence
        improvement_indicator = "📈" if confidence_improvement > 0 else "📉" if confidence_improvement < 0 else "➡️"
        print(f"    {improvement_indicator} Confidence Change: {confidence_improvement:+.2f}")


async def demo_real_world_scenarios():
    """Demonstrate real-world usage scenarios."""
    print("\n" + "="*70)
    print("🌍 REAL-WORLD SCENARIOS DEMO")
    print("="*70)
    
    processor = EnhancedIntentProcessor(
        enable_adaptive=True,
        enable_filtering=True,
        verbose=False
    )
    
    await processor.initialize()
    
    # Real-world scenarios
    scenarios = [
        {
            'category': 'Software Development',
            'intents': [
                "create a REST API for user management",
                "debug the authentication middleware",
                "optimize database queries for better performance",
            ]
        },
        {
            'category': 'Data Analysis',
            'intents': [
                "analyze customer churn patterns",
                "create a dashboard for sales metrics",
                "identify outliers in the dataset",
            ]
        },
        {
            'category': 'User Support',
            'intents': [
                "help me understand this error message",
                "how do I reset my password?",
                "troubleshoot connection issues",
            ]
        },
    ]
    
    for scenario in scenarios:
        print(f"\n🎭 Scenario: {scenario['category']}")
        print("-" * 40)
        
        scenario_stats = {
            'total_time': 0,
            'avg_confidence': 0,
            'flow_states': [],
        }
        
        for intent in scenario['intents']:
            enhanced_intent = await processor.process_intent(intent)
            
            scenario_stats['total_time'] += enhanced_intent.processing_time
            scenario_stats['avg_confidence'] += enhanced_intent.processing_confidence
            scenario_stats['flow_states'].append(enhanced_intent.flow_state)
            
            print(f"  📝 '{intent[:40]}{'...' if len(intent) > 40 else ''}'")
            print(f"    ⏱️  {enhanced_intent.processing_time:.3f}s | "
                  f"🎯 {enhanced_intent.processing_confidence:.2f} | "
                  f"🌊 {enhanced_intent.flow_state}")
        
        # Scenario summary
        num_intents = len(scenario['intents'])
        print(f"\n  📊 Scenario Summary:")
        print(f"    ⏱️  Total time: {scenario_stats['total_time']:.3f}s")
        print(f"    🎯 Avg confidence: {scenario_stats['avg_confidence'] / num_intents:.2f}")
        
        # Flow state distribution
        flow_dist = {}
        for state in scenario_stats['flow_states']:
            flow_dist[state] = flow_dist.get(state, 0) + 1
        print(f"    🌊 Flow states: {dict(flow_dist)}")


async def main():
    """Run all enhanced intent integration demos."""
    print("🔺 ENHANCED INTENT INTEGRATION - COMPREHENSIVE DEMO")
    print("🚀 Revolutionary Intent Processing in Agent Swarm")
    print("🧠 AdaptiveIntentProcessor + IntentFilteringTriangle Integration")
    
    try:
        # Run all demos
        await demo_enhanced_intent_processor()
        await demo_intent_analytics()
        await demo_intent_explanations()
        await demo_comparison_with_traditional()
        await demo_real_world_scenarios()
        
        print("\n" + "="*70)
        print("🎉 ENHANCED INTENT INTEGRATION DEMO COMPLETED!")
        print("="*70)
        print("✨ The Enhanced Intent Processor demonstrates revolutionary capabilities:")
        print("  🔺 Seamless integration of mathematical algorithms")
        print("  🧠 AdaptiveIntentProcessor: 5-stage processing with early stopping")
        print("  🌊 IntentFilteringTriangle: 3D intent space analysis")
        print("  📊 Real-time analytics and performance monitoring")
        print("  🎯 Enhanced confidence scoring and processing recommendations")
        print("  💡 Intelligent optimization suggestions")
        print("\n🚀 Ready for production deployment in Agent Swarm!")
        print("\n💡 Try it in the interactive shell:")
        print("  agent-swarm")
        print("  /intent-test")
        print("  /intent-analytics")
        
    except Exception as e:
        print(f"\n💥 Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
