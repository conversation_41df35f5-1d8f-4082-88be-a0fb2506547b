#!/usr/bin/env python3
"""
Adaptive Intent Processor Demo - Revolutionary Intent Processing.

This demo showcases the AdaptiveIntentProcessor, the crown jewel of the
mathematical algorithm framework. It demonstrates:

- 5-stage adaptive processing pipeline (O(1) to O(n³))
- Early stopping optimization with confidence thresholding
- Mathematical optimization and performance monitoring
- Sophisticated intent understanding and classification
- Real-world intent processing scenarios

The AdaptiveIntentProcessor represents a breakthrough in AI intent understanding,
combining mathematical rigor with practical intelligence.
"""

import asyncio
import json
from pathlib import Path
import time

# Add the src directory to the path for imports
import sys
# Add src to path for development
try:
    import agent_swarm
except ImportError:
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm.algorithms import (
    # Core framework
    AlgorithmEngine,
    AlgorithmConfig,
    ExecuteAlgorithmAction,
    
    # The star of the show
    AdaptiveIntentProcessor,
    
    # Supporting algorithms
    ConfidenceThresholder,
    PatternMatcher,
    
    # Utilities
    create_algorithm_engine,
    create_parameter_definition,
)


async def demo_basic_intent_processing():
    """Demonstrate basic adaptive intent processing."""
    print("\n" + "="*70)
    print("🧠 ADAPTIVE INTENT PROCESSOR - BASIC DEMO")
    print("="*70)
    
    # Create engine with adaptive configuration
    engine = create_algorithm_engine(
        algorithm_name="adaptive_intent_processor",
        parameters={
            "enable_early_stopping": True,
            "confidence_threshold": 0.85,
            "processing_strategy": "adaptive",
            "max_processing_stages": 5,
            "enable_learning": True,
        }
    )
    
    # Register the algorithm
    engine.registry.register_algorithm(AdaptiveIntentProcessor)
    
    # Test various intent types
    test_intents = [
        "/help config",                    # Explicit command - should stop at stage 1
        "create a Python function",       # Clear request - should stop at stage 2-3
        "I need help with something",     # Vague request - should go deeper
        "analyze the performance data",   # Complex analysis - may need full pipeline
        "what do you think about this?", # Ambiguous - likely full pipeline
    ]
    
    print("🧪 Testing various intent types with adaptive processing:")
    
    for i, intent in enumerate(test_intents, 1):
        print(f"\n📝 Test {i}: '{intent}'")
        
        # Create action
        action = ExecuteAlgorithmAction(
            algorithm_name="adaptive_intent_processor",
            input_data=intent,
        )
        
        # Execute with timing
        start_time = time.time()
        observation = await engine.execute_action(action)
        execution_time = time.time() - start_time
        
        if observation.success:
            output = observation.output_data
            
            print(f"  ✅ Success | Time: {execution_time:.4f}s")
            print(f"  🎯 Confidence: {output.get('final_confidence', 0):.2f}")
            print(f"  📊 Stages executed: {output.get('stages_executed', 0)}")
            print(f"  ⚡ Early stopped: {output.get('early_stopped', False)}")
            print(f"  🏁 Final stage: {output.get('final_stage', 'unknown')}")
            
            # Show confidence progression
            progression = output.get('confidence_progression', [])
            if progression:
                print(f"  📈 Confidence progression:")
                for stage_info in progression:
                    stage = stage_info['stage']
                    conf = stage_info['confidence']
                    print(f"    {stage}: {conf:.2f}")
        else:
            print(f"  ❌ Failed: {observation.error}")


async def demo_processing_strategies():
    """Demonstrate different processing strategies."""
    print("\n" + "="*70)
    print("🎛️ PROCESSING STRATEGIES COMPARISON")
    print("="*70)
    
    strategies = ["adaptive", "sequential", "confidence_driven", "parallel_hybrid"]
    test_intent = "create a machine learning model to analyze customer data"
    
    print(f"📝 Testing intent: '{test_intent}'")
    print("🔬 Comparing processing strategies:")
    
    results = {}
    
    for strategy in strategies:
        print(f"\n🧪 Testing {strategy} strategy...")
        
        # Create engine for this strategy
        engine = create_algorithm_engine(
            algorithm_name="adaptive_intent_processor",
            parameters={
                "processing_strategy": strategy,
                "confidence_threshold": 0.8,
                "enable_early_stopping": True,
            }
        )
        
        engine.registry.register_algorithm(AdaptiveIntentProcessor)
        
        # Execute
        action = ExecuteAlgorithmAction(
            algorithm_name="adaptive_intent_processor",
            input_data=test_intent,
        )
        
        start_time = time.time()
        observation = await engine.execute_action(action)
        execution_time = time.time() - start_time
        
        if observation.success:
            output = observation.output_data
            results[strategy] = {
                'execution_time': execution_time,
                'confidence': output.get('final_confidence', 0),
                'stages_executed': output.get('stages_executed', 0),
                'early_stopped': output.get('early_stopped', False),
            }
            
            print(f"  ✅ Time: {execution_time:.4f}s | "
                  f"Confidence: {results[strategy]['confidence']:.2f} | "
                  f"Stages: {results[strategy]['stages_executed']}")
        else:
            print(f"  ❌ Failed: {observation.error}")
            results[strategy] = {'error': observation.error}
    
    # Compare results
    print(f"\n📊 Strategy Comparison Summary:")
    print(f"{'Strategy':<20} {'Time (s)':<10} {'Confidence':<12} {'Stages':<8} {'Early Stop'}")
    print("-" * 65)
    
    for strategy, result in results.items():
        if 'error' not in result:
            time_str = f"{result['execution_time']:.4f}"
            conf_str = f"{result['confidence']:.2f}"
            stages_str = str(result['stages_executed'])
            early_str = "Yes" if result['early_stopped'] else "No"
            
            print(f"{strategy:<20} {time_str:<10} {conf_str:<12} {stages_str:<8} {early_str}")
        else:
            print(f"{strategy:<20} {'ERROR':<10} {'-':<12} {'-':<8} {'-'}")


async def demo_confidence_optimization():
    """Demonstrate confidence threshold optimization."""
    print("\n" + "="*70)
    print("🎯 CONFIDENCE THRESHOLD OPTIMIZATION")
    print("="*70)
    
    # Test different confidence thresholds
    thresholds = [0.6, 0.7, 0.8, 0.9, 0.95]
    test_intent = "help me understand how to optimize database queries"
    
    print(f"📝 Testing intent: '{test_intent}'")
    print("🔧 Optimizing confidence thresholds:")
    
    optimization_results = []
    
    for threshold in thresholds:
        print(f"\n🎯 Testing threshold: {threshold}")
        
        engine = create_algorithm_engine(
            algorithm_name="adaptive_intent_processor",
            parameters={
                "confidence_threshold": threshold,
                "enable_early_stopping": True,
                "processing_strategy": "adaptive",
            }
        )
        
        engine.registry.register_algorithm(AdaptiveIntentProcessor)
        
        action = ExecuteAlgorithmAction(
            algorithm_name="adaptive_intent_processor",
            input_data=test_intent,
        )
        
        start_time = time.time()
        observation = await engine.execute_action(action)
        execution_time = time.time() - start_time
        
        if observation.success:
            output = observation.output_data
            
            result = {
                'threshold': threshold,
                'execution_time': execution_time,
                'final_confidence': output.get('final_confidence', 0),
                'stages_executed': output.get('stages_executed', 0),
                'early_stopped': output.get('early_stopped', False),
                'efficiency_score': output.get('stages_executed', 5) / 5.0,  # Lower is better
            }
            
            optimization_results.append(result)
            
            print(f"  ⏱️  Time: {execution_time:.4f}s")
            print(f"  🎯 Final confidence: {result['final_confidence']:.2f}")
            print(f"  📊 Stages: {result['stages_executed']}")
            print(f"  ⚡ Early stopped: {result['early_stopped']}")
        else:
            print(f"  ❌ Failed: {observation.error}")
    
    # Find optimal threshold
    if optimization_results:
        print(f"\n📈 Optimization Results:")
        print(f"{'Threshold':<12} {'Time (s)':<10} {'Confidence':<12} {'Stages':<8} {'Efficiency'}")
        print("-" * 60)
        
        for result in optimization_results:
            time_str = f"{result['execution_time']:.4f}"
            conf_str = f"{result['final_confidence']:.2f}"
            stages_str = str(result['stages_executed'])
            eff_str = f"{1 - result['efficiency_score']:.2f}"  # Higher is better
            
            print(f"{result['threshold']:<12} {time_str:<10} {conf_str:<12} {stages_str:<8} {eff_str}")
        
        # Find best balance of speed and confidence
        best_result = max(optimization_results, 
                         key=lambda r: r['final_confidence'] * (1 - r['efficiency_score']))
        
        print(f"\n🏆 Optimal threshold: {best_result['threshold']} "
              f"(Confidence: {best_result['final_confidence']:.2f}, "
              f"Stages: {best_result['stages_executed']})")


async def demo_performance_monitoring():
    """Demonstrate performance monitoring and statistics."""
    print("\n" + "="*70)
    print("📊 PERFORMANCE MONITORING & STATISTICS")
    print("="*70)
    
    # Create engine with monitoring enabled
    config = AlgorithmConfig(
        algorithm_name="adaptive_intent_processor",
        enable_metrics=True,
        metrics_interval=0.1,
    )
    
    engine = AlgorithmEngine(config)
    engine.registry.register_algorithm(AdaptiveIntentProcessor)
    
    # Get the algorithm instance to access statistics
    algorithm = engine.registry.get_algorithm("adaptive_intent_processor")
    
    # Run multiple processing sessions
    test_intents = [
        "/help",
        "/config show",
        "create a function",
        "analyze this data",
        "what should I do?",
        "optimize the algorithm",
        "explain machine learning",
        "/exit",
        "build a web application",
        "debug this code",
    ]
    
    print("🔄 Running multiple processing sessions for statistics...")
    
    for i, intent in enumerate(test_intents, 1):
        print(f"  Processing {i}/10: '{intent[:30]}{'...' if len(intent) > 30 else ''}'")
        
        action = ExecuteAlgorithmAction(
            algorithm_name="adaptive_intent_processor",
            input_data=intent,
        )
        
        observation = await engine.execute_action(action)
        
        # Brief pause to simulate real usage
        await asyncio.sleep(0.1)
    
    # Show statistics
    print(f"\n📈 Processing Statistics:")
    
    if hasattr(algorithm, 'get_processing_stats'):
        stats = algorithm.get_processing_stats()
        
        print(f"  📊 Total processed: {stats.get('total_processed', 0)}")
        print(f"  ⚡ Early stops: {stats.get('early_stops', 0)}")
        print(f"  🔄 Full pipeline: {stats.get('full_pipeline', 0)}")
        print(f"  📈 Early stop rate: {stats.get('early_stop_rate', 0):.1%}")
        
        # Stage usage statistics
        stage_usage = stats.get('stage_usage', {})
        if stage_usage:
            print(f"\n  🎭 Stage Usage:")
            for stage, count in stage_usage.items():
                print(f"    {stage}: {count} times")
    
    # Show engine metrics
    print(f"\n⚙️  Engine Performance:")
    if hasattr(engine.context, 'metrics'):
        metrics = engine.context.metrics
        if hasattr(metrics, 'get_summary'):
            summary = metrics.get_summary()
            perf = summary.get('performance', {})
            
            print(f"  ⏱️  Avg execution time: {perf.get('avg_execution_time', 0):.4f}s")
            print(f"  🎯 Avg confidence: {perf.get('avg_confidence', 0):.2f}")
            print(f"  ❌ Error rate: {perf.get('error_rate', 0):.1f}%")


async def demo_real_world_scenarios():
    """Demonstrate real-world intent processing scenarios."""
    print("\n" + "="*70)
    print("🌍 REAL-WORLD INTENT PROCESSING SCENARIOS")
    print("="*70)
    
    # Create production-like configuration
    engine = create_algorithm_engine(
        algorithm_name="adaptive_intent_processor",
        parameters={
            "enable_early_stopping": True,
            "confidence_threshold": 0.82,
            "processing_strategy": "adaptive",
            "enable_learning": True,
            "context_weight": 0.3,
        }
    )
    
    engine.registry.register_algorithm(AdaptiveIntentProcessor)
    
    # Real-world scenarios
    scenarios = [
        {
            'name': 'Software Development',
            'intents': [
                "create a REST API for user management",
                "debug the authentication middleware",
                "optimize database queries for better performance",
                "refactor the payment processing module",
            ]
        },
        {
            'name': 'Data Analysis',
            'intents': [
                "analyze customer churn patterns",
                "create a dashboard for sales metrics",
                "identify outliers in the dataset",
                "predict future revenue trends",
            ]
        },
        {
            'name': 'System Administration',
            'intents': [
                "deploy the application to production",
                "monitor server performance",
                "backup the database",
                "configure load balancing",
            ]
        },
        {
            'name': 'User Support',
            'intents': [
                "help me understand this error message",
                "how do I reset my password?",
                "explain the new features",
                "troubleshoot connection issues",
            ]
        },
    ]
    
    for scenario in scenarios:
        print(f"\n🎭 Scenario: {scenario['name']}")
        print("-" * 50)
        
        scenario_stats = {
            'total_time': 0,
            'avg_confidence': 0,
            'early_stops': 0,
            'total_stages': 0,
        }
        
        for intent in scenario['intents']:
            action = ExecuteAlgorithmAction(
                algorithm_name="adaptive_intent_processor",
                input_data=intent,
            )
            
            start_time = time.time()
            observation = await engine.execute_action(action)
            execution_time = time.time() - start_time
            
            if observation.success:
                output = observation.output_data
                
                scenario_stats['total_time'] += execution_time
                scenario_stats['avg_confidence'] += output.get('final_confidence', 0)
                scenario_stats['total_stages'] += output.get('stages_executed', 0)
                
                if output.get('early_stopped', False):
                    scenario_stats['early_stops'] += 1
                
                print(f"  📝 '{intent[:40]}{'...' if len(intent) > 40 else ''}'")
                print(f"    ⏱️  {execution_time:.3f}s | "
                      f"🎯 {output.get('final_confidence', 0):.2f} | "
                      f"📊 {output.get('stages_executed', 0)} stages | "
                      f"⚡ {'Early' if output.get('early_stopped') else 'Full'}")
        
        # Scenario summary
        num_intents = len(scenario['intents'])
        print(f"\n  📊 Scenario Summary:")
        print(f"    ⏱️  Total time: {scenario_stats['total_time']:.3f}s")
        print(f"    🎯 Avg confidence: {scenario_stats['avg_confidence'] / num_intents:.2f}")
        print(f"    📊 Avg stages: {scenario_stats['total_stages'] / num_intents:.1f}")
        print(f"    ⚡ Early stop rate: {scenario_stats['early_stops'] / num_intents:.1%}")


async def main():
    """Run all adaptive intent processor demos."""
    print("🧠 ADAPTIVE INTENT PROCESSOR - COMPREHENSIVE DEMO")
    print("🚀 Revolutionary Intent Processing with Mathematical Optimization")
    print("⚡ O(1) to O(n³) Adaptive Complexity | 🎯 Confidence-Driven | 📊 Performance Optimized")
    
    try:
        # Run all demos
        await demo_basic_intent_processing()
        await demo_processing_strategies()
        await demo_confidence_optimization()
        await demo_performance_monitoring()
        await demo_real_world_scenarios()
        
        print("\n" + "="*70)
        print("🎉 ADAPTIVE INTENT PROCESSOR DEMO COMPLETED!")
        print("="*70)
        print("✨ The AdaptiveIntentProcessor demonstrates revolutionary capabilities:")
        print("  🧠 Intelligent 5-stage processing pipeline")
        print("  ⚡ Adaptive early stopping optimization")
        print("  🎯 Mathematical confidence thresholding")
        print("  📊 Real-time performance monitoring")
        print("  🔧 Multiple processing strategies")
        print("  🌍 Production-ready for real-world scenarios")
        print("\n🚀 Ready to revolutionize AI intent understanding!")
        
    except Exception as e:
        print(f"\n💥 Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
