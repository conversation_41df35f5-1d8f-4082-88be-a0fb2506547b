#!/usr/bin/env python3
"""
Revolutionary Multi-Agent Consensus System Demo

This demo showcases the cutting-edge multi-agent communication and consensus
capabilities of Agent Swarm, including:

- Virtual agent swarm with specialized roles
- Emergent communication protocols
- Byzantine fault tolerant consensus
- Conflict resolution mechanisms
- Real-time performance monitoring

Run this demo to see the revolutionary multi-agent system in action!
"""

import asyncio
import time
import json
from pathlib import Path
from typing import Dict, Any, List
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.tree import Tree
from rich.json import JSON

# Add src to path for development
try:
    import agent_swarm
except ImportError:
    import sys
    sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_swarm.algorithms.algorithms.stages.multi_agent_consensus_stage import (
    MultiAgentConsensusStage
)
from agent_swarm.algorithms.core import AlgorithmContext
from agent_swarm.core.multi_agent_communication import (
    initialize_communication_hub,
    get_communication_hub,
    AgentRole,
    MessageType
)

console = Console()


class MultiAgentDemo:
    """Interactive demo of the multi-agent consensus system."""
    
    def __init__(self):
        self.consensus_stage = MultiAgentConsensusStage()
        self.communication_hub = None
        self.demo_scenarios = self._create_demo_scenarios()
    
    def _create_demo_scenarios(self) -> List[Dict[str, Any]]:
        """Create diverse demo scenarios to showcase capabilities."""
        return [
            {
                "name": "🏗️ Software Architecture Decision",
                "description": "Choose between microservices and monolith architecture",
                "input": {
                    "domain": "technology",
                    "problem": "Our e-commerce platform is growing rapidly. Should we migrate from monolith to microservices?",
                    "constraints": [
                        "Current team size: 12 developers",
                        "Timeline: 6 months for migration",
                        "Budget: $200k allocated",
                        "Must maintain 99.9% uptime during transition"
                    ],
                    "requirements": [
                        "Support 10x traffic growth",
                        "Enable faster feature deployment",
                        "Improve system maintainability",
                        "Reduce deployment risks"
                    ],
                    "current_state": {
                        "architecture": "monolith",
                        "tech_stack": ["Python", "Django", "PostgreSQL", "Redis"],
                        "traffic": "100k requests/day",
                        "team_experience": "monolith-focused"
                    }
                },
                "expected_agents": ["specialist_tech", "critic_security", "validator_quality", "coordinator_main"]
            },
            {
                "name": "🔒 Security Risk Assessment",
                "description": "Evaluate security implications of cloud migration",
                "input": {
                    "domain": "security",
                    "problem": "Assess the security risks and mitigation strategies for migrating our on-premise infrastructure to AWS cloud",
                    "assets": [
                        "Customer PII database (10M records)",
                        "Payment processing system",
                        "Internal business applications",
                        "Development and testing environments"
                    ],
                    "compliance_requirements": [
                        "PCI DSS Level 1",
                        "GDPR compliance",
                        "SOX compliance",
                        "Industry-specific regulations"
                    ],
                    "threat_landscape": [
                        "Advanced persistent threats",
                        "Insider threats",
                        "Cloud misconfigurations",
                        "Supply chain attacks"
                    ]
                },
                "expected_agents": ["critic_security", "specialist_tech", "validator_quality", "synthesizer_main"]
            },
            {
                "name": "💼 Strategic Business Decision",
                "description": "Evaluate market expansion opportunity",
                "input": {
                    "domain": "business",
                    "problem": "Should we expand our SaaS product to the European market?",
                    "market_analysis": {
                        "market_size": "$2.5B addressable market",
                        "competition": "3 major players, fragmented market",
                        "growth_rate": "15% YoY",
                        "regulatory_complexity": "High (GDPR, local laws)"
                    },
                    "financial_projections": {
                        "investment_required": "$5M over 2 years",
                        "break_even": "18 months",
                        "projected_revenue": "$15M in year 3",
                        "risk_factors": ["Currency fluctuation", "Regulatory changes", "Competition"]
                    },
                    "operational_considerations": [
                        "Need local sales team",
                        "Customer support in multiple languages",
                        "Data residency requirements",
                        "Local partnerships required"
                    ]
                },
                "expected_agents": ["specialist_business", "critic_security", "synthesizer_main", "coordinator_main"]
            },
            {
                "name": "🧪 AI/ML Implementation Strategy",
                "description": "Decide on AI-powered features for product",
                "input": {
                    "domain": "technology",
                    "problem": "Should we implement AI-powered recommendation engine and automated customer support?",
                    "technical_requirements": {
                        "data_volume": "50M user interactions/month",
                        "latency_requirements": "<100ms for recommendations",
                        "accuracy_targets": ">85% recommendation relevance",
                        "integration_complexity": "High - 15 existing systems"
                    },
                    "business_impact": {
                        "expected_revenue_increase": "20-30%",
                        "cost_reduction": "$2M/year in support costs",
                        "competitive_advantage": "Significant differentiation",
                        "customer_satisfaction": "Expected 25% improvement"
                    },
                    "risks_and_challenges": [
                        "Data privacy and bias concerns",
                        "Model drift and maintenance",
                        "Integration with legacy systems",
                        "Team skill gap in ML/AI"
                    ]
                },
                "expected_agents": ["specialist_tech", "specialist_business", "critic_security", "validator_quality"]
            },
            {
                "name": "🌍 Sustainability Initiative",
                "description": "Evaluate carbon-neutral infrastructure transition",
                "input": {
                    "domain": "business",
                    "problem": "Should we commit to carbon-neutral infrastructure by 2025?",
                    "current_footprint": {
                        "data_centers": "500 tons CO2/year",
                        "cloud_services": "200 tons CO2/year",
                        "employee_travel": "150 tons CO2/year",
                        "office_operations": "100 tons CO2/year"
                    },
                    "proposed_initiatives": [
                        "Migrate to renewable energy cloud providers",
                        "Implement edge computing to reduce data transfer",
                        "Carbon offset programs",
                        "Remote-first work policy"
                    ],
                    "financial_impact": {
                        "initial_investment": "$3M",
                        "annual_savings": "$500k (energy efficiency)",
                        "carbon_offset_costs": "$200k/year",
                        "potential_revenue": "$2M (green premium)"
                    }
                },
                "expected_agents": ["specialist_business", "specialist_tech", "synthesizer_main", "coordinator_main"]
            }
        ]
    
    async def initialize(self):
        """Initialize the demo system."""
        console.print("\n🚀 [bold blue]Initializing Revolutionary Multi-Agent System...[/bold blue]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Setting up communication hub...", total=None)
            
            # Initialize communication hub
            self.communication_hub = await initialize_communication_hub()
            
            progress.update(task, description="Communication hub ready ✅")
            await asyncio.sleep(0.5)
            
            progress.update(task, description="Virtual agents initialized ✅")
            await asyncio.sleep(0.5)
            
            progress.update(task, description="Consensus algorithms loaded ✅")
            await asyncio.sleep(0.5)
        
        console.print("✅ [bold green]Multi-Agent System Ready![/bold green]\n")
    
    async def cleanup(self):
        """Cleanup demo resources."""
        if self.communication_hub:
            await self.communication_hub.shutdown()
    
    def display_system_overview(self):
        """Display system overview and capabilities."""
        console.print(Panel.fit(
            "[bold cyan]🤖 Revolutionary Multi-Agent Consensus System[/bold cyan]\n\n"
            "[yellow]Key Features:[/yellow]\n"
            "• 6 Specialized Virtual Agents with unique personalities\n"
            "• O(n³) Consensus Algorithm with triple-wise validation\n"
            "• Byzantine Fault Tolerant consensus mechanisms\n"
            "• Emergent communication protocols\n"
            "• Automatic conflict detection and resolution\n"
            "• Real-time performance monitoring\n\n"
            "[yellow]Agent Roles:[/yellow]\n"
            "🔧 Technology Specialist - Technical deep-dive analysis\n"
            "💼 Business Specialist - Strategic and economic considerations\n"
            "🛡️ Security Critic - Risk identification and mitigation\n"
            "🔄 Main Synthesizer - Multi-perspective integration\n"
            "✅ Quality Validator - Correctness and quality validation\n"
            "👥 Main Coordinator - Consensus facilitation",
            title="System Overview"
        ))
    
    def display_virtual_agents(self):
        """Display virtual agent details."""
        table = Table(title="🤖 Virtual Agent Swarm Configuration")
        table.add_column("Agent ID", style="cyan", no_wrap=True)
        table.add_column("Role", style="magenta")
        table.add_column("Expertise", style="green")
        table.add_column("Personality", style="yellow")
        table.add_column("Decision Bias", style="red")
        
        for agent in self.consensus_stage.virtual_agents:
            personality_str = ", ".join([f"{k}:{v}" for k, v in list(agent.personality_traits.items())[:2]])
            bias_str = f"{agent.decision_bias:+.1f}"
            
            table.add_row(
                agent.id,
                agent.role.value,
                ", ".join(agent.expertise[:3]),
                personality_str,
                bias_str
            )
        
        console.print(table)
    
    def select_scenario(self) -> Dict[str, Any]:
        """Interactive scenario selection."""
        console.print("\n📋 [bold]Available Demo Scenarios:[/bold]\n")
        
        for i, scenario in enumerate(self.demo_scenarios, 1):
            console.print(f"[cyan]{i}.[/cyan] {scenario['name']}")
            console.print(f"   {scenario['description']}\n")
        
        while True:
            try:
                choice = console.input("Select scenario (1-{}): ".format(len(self.demo_scenarios)))
                choice_idx = int(choice) - 1
                
                if 0 <= choice_idx < len(self.demo_scenarios):
                    return self.demo_scenarios[choice_idx]
                else:
                    console.print("[red]Invalid choice. Please try again.[/red]")
            except ValueError:
                console.print("[red]Please enter a valid number.[/red]")
    
    def display_scenario_details(self, scenario: Dict[str, Any]):
        """Display detailed scenario information."""
        console.print(f"\n🎯 [bold]{scenario['name']}[/bold]")
        console.print(f"[italic]{scenario['description']}[/italic]\n")
        
        # Display scenario input as formatted JSON
        console.print(Panel(
            JSON.from_data(scenario['input']),
            title="📝 Scenario Details",
            expand=False
        ))
    
    async def run_consensus(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """Run consensus process for scenario."""
        console.print("\n🔄 [bold]Starting Multi-Agent Consensus Process...[/bold]")
        
        # Create algorithm context
        context = AlgorithmContext(
            request_id=f"demo_{int(time.time())}",
            user_id="demo_user",
            session_id="demo_session",
            metadata={"scenario": scenario['name']}
        )
        
        # Show progress
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Selecting agents for task...", total=None)
            
            # Simulate processing stages
            await asyncio.sleep(1)
            progress.update(task, description="Agents generating perspectives...")
            await asyncio.sleep(1)
            progress.update(task, description="Cross-agent communication...")
            await asyncio.sleep(1)
            progress.update(task, description="Detecting and resolving conflicts...")
            await asyncio.sleep(1)
            progress.update(task, description="Building consensus...")
            await asyncio.sleep(1)
            progress.update(task, description="Validating consensus (O(n³))...")
            
            # Run actual consensus
            result = await self.consensus_stage.process(scenario['input'], context)
            
            progress.update(task, description="Consensus complete! ✅")
        
        return result
    
    def display_consensus_result(self, result: Any, scenario: Dict[str, Any]):
        """Display comprehensive consensus results."""
        if not result.success:
            console.print(f"[red]❌ Consensus failed: {result.error}[/red]")
            return
        
        output = result.output
        
        # Overall result
        console.print(f"\n🎉 [bold green]Consensus Reached![/bold green]")
        console.print(f"[yellow]Type:[/yellow] {output['consensus_type']}")
        console.print(f"[yellow]Confidence:[/yellow] {output['overall_confidence']:.2%}")
        console.print(f"[yellow]Execution Time:[/yellow] {result.execution_time:.2f}s")
        console.print(f"[yellow]Participating Agents:[/yellow] {output['participating_agents']}")
        
        # Agent contributions
        self._display_agent_contributions(output['agent_contributions'])
        
        # Synthesized results
        self._display_synthesized_results(output['synthesized_result'])
        
        # Consensus metrics
        self._display_consensus_metrics(output['consensus_metrics'])
        
        # Revolutionary features used
        self._display_revolutionary_features(output)
    
    def _display_agent_contributions(self, contributions: List[Dict[str, Any]]):
        """Display agent contributions table."""
        console.print("\n👥 [bold]Agent Contributions:[/bold]")
        
        table = Table()
        table.add_column("Agent", style="cyan")
        table.add_column("Role", style="magenta")
        table.add_column("Confidence", style="green")
        table.add_column("Vote Weight", style="yellow")
        table.add_column("Perspective", style="blue")
        
        for contrib in contributions:
            table.add_row(
                contrib['agent_id'].replace('_', ' ').title(),
                contrib['role'].title(),
                f"{contrib['confidence']:.2%}",
                f"{contrib['vote_weight']:.2f}",
                contrib['perspective_summary'].replace('_', ' ').title()
            )
        
        console.print(table)
    
    def _display_synthesized_results(self, synthesized: Dict[str, Any]):
        """Display synthesized consensus results."""
        console.print("\n🔄 [bold]Synthesized Results:[/bold]")
        
        # Recommendations
        if synthesized['recommendations']:
            console.print("\n[green]✅ Recommendations:[/green]")
            for i, rec in enumerate(synthesized['recommendations'], 1):
                console.print(f"  {i}. {rec}")
        
        # Concerns
        if synthesized['concerns']:
            console.print("\n[yellow]⚠️ Identified Concerns:[/yellow]")
            for i, concern in enumerate(synthesized['concerns'], 1):
                console.print(f"  {i}. {concern}")
        
        # Reasoning chain
        if synthesized['reasoning_chain']:
            console.print("\n[blue]🧠 Collective Reasoning:[/blue]")
            for i, reason in enumerate(synthesized['reasoning_chain'], 1):
                console.print(f"  {i}. {reason}")
    
    def _display_consensus_metrics(self, metrics: Dict[str, Any]):
        """Display consensus quality metrics."""
        console.print("\n📊 [bold]Consensus Quality Metrics:[/bold]")
        
        metrics_table = Table()
        metrics_table.add_column("Metric", style="cyan")
        metrics_table.add_column("Value", style="green")
        metrics_table.add_column("Interpretation", style="yellow")
        
        # Confidence variance
        variance = metrics.get('confidence_variance', 0)
        variance_interp = "Low disagreement" if variance < 0.1 else "High disagreement" if variance > 0.3 else "Moderate disagreement"
        metrics_table.add_row("Confidence Variance", f"{variance:.3f}", variance_interp)
        
        # Role diversity
        role_diversity = metrics.get('role_diversity', 0)
        diversity_interp = "High diversity" if role_diversity >= 4 else "Moderate diversity" if role_diversity >= 3 else "Low diversity"
        metrics_table.add_row("Role Diversity", str(role_diversity), diversity_interp)
        
        # Expertise coverage
        expertise_coverage = metrics.get('expertise_coverage', 0)
        coverage_interp = "Comprehensive" if expertise_coverage >= 8 else "Good" if expertise_coverage >= 5 else "Limited"
        metrics_table.add_row("Expertise Coverage", str(expertise_coverage), coverage_interp)
        
        console.print(metrics_table)
    
    def _display_revolutionary_features(self, output: Dict[str, Any]):
        """Display revolutionary features used."""
        console.print("\n🚀 [bold]Revolutionary Features Demonstrated:[/bold]")
        
        features_tree = Tree("🌟 Advanced Capabilities")
        
        # Communication features
        comm_branch = features_tree.add("📡 Communication Features")
        for feature in output.get('communication_features', []):
            comm_branch.add(f"✅ {feature}")
        
        # Revolutionary features
        rev_branch = features_tree.add("🔬 Revolutionary Features")
        for feature in output.get('revolutionary_features', []):
            rev_branch.add(f"🚀 {feature}")
        
        console.print(features_tree)
    
    def display_communication_hub_stats(self):
        """Display communication hub statistics."""
        if not self.communication_hub:
            return
        
        stats = self.communication_hub.get_metrics()
        
        console.print("\n📈 [bold]Communication Hub Statistics:[/bold]")
        
        stats_table = Table()
        stats_table.add_column("Metric", style="cyan")
        stats_table.add_column("Value", style="green")
        
        stats_table.add_row("Registered Agents", str(stats['agents_registered']))
        stats_table.add_row("Messages Sent", str(stats['messages_sent']))
        stats_table.add_row("Messages Received", str(stats['messages_received']))
        stats_table.add_row("Consensus Reached", str(stats['consensus_reached']))
        stats_table.add_row("Success Rate", f"{stats['success_rate']:.2%}")
        stats_table.add_row("Topology Protocol", stats['topology_protocol'])
        
        console.print(stats_table)
    
    async def run_interactive_demo(self):
        """Run the interactive demo."""
        try:
            # Initialize system
            await self.initialize()
            
            # Display overview
            self.display_system_overview()
            
            # Show virtual agents
            console.print("\n")
            self.display_virtual_agents()
            
            while True:
                # Select scenario
                scenario = self.select_scenario()
                
                # Display scenario details
                self.display_scenario_details(scenario)
                
                # Confirm execution
                if not console.input("\nProceed with consensus? (y/n): ").lower().startswith('y'):
                    continue
                
                # Run consensus
                result = await self.run_consensus(scenario)
                
                # Display results
                self.display_consensus_result(result, scenario)
                
                # Show communication stats
                self.display_communication_hub_stats()
                
                # Continue or exit
                if not console.input("\nRun another scenario? (y/n): ").lower().startswith('y'):
                    break
            
        except KeyboardInterrupt:
            console.print("\n[yellow]Demo interrupted by user.[/yellow]")
        except Exception as e:
            console.print(f"\n[red]Demo error: {e}[/red]")
        finally:
            await self.cleanup()
            console.print("\n👋 [bold]Thank you for exploring the Revolutionary Multi-Agent System![/bold]")


async def main():
    """Main demo function."""
    console.print("""
[bold cyan]🚀 Revolutionary Multi-Agent Consensus System Demo[/bold cyan]

Welcome to the cutting-edge demonstration of Agent Swarm's multi-agent
communication and consensus capabilities!

This demo showcases:
• Virtual agent swarm with specialized roles and personalities
• O(n³) consensus algorithm with Byzantine fault tolerance
• Emergent communication protocols with attention-based routing
• Automatic conflict detection and resolution mechanisms
• Real-time performance monitoring and metrics

Get ready to witness the future of AI collaboration! 🤖✨
""")
    
    demo = MultiAgentDemo()
    await demo.run_interactive_demo()


if __name__ == "__main__":
    asyncio.run(main())