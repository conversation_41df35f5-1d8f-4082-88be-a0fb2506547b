# Agent Swarm .editorconfig
# EditorConfig helps maintain consistent coding styles across different editors and IDEs
# See https://editorconfig.org

# Top-most EditorConfig file
root = true

# ============================================================================
# Default settings for all files
# ============================================================================
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 4

# ============================================================================
# Python files
# ============================================================================
[*.py]
indent_style = space
indent_size = 4
max_line_length = 88  # Black formatter default
quote_type = double

[*.pyi]
indent_style = space
indent_size = 4
max_line_length = 88

# ============================================================================
# Configuration files
# ============================================================================

# TOML files (pyproject.toml, etc.)
[*.toml]
indent_style = space
indent_size = 4

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# INI files
[*.{ini,cfg}]
indent_style = space
indent_size = 4

# ============================================================================
# Documentation
# ============================================================================

# Markdown files
[*.md]
indent_style = space
indent_size = 2
trim_trailing_whitespace = false  # Preserve trailing spaces for line breaks

# reStructuredText files
[*.rst]
indent_style = space
indent_size = 3

# ============================================================================
# Web files
# ============================================================================

# HTML files
[*.html]
indent_style = space
indent_size = 2

# CSS files
[*.css]
indent_style = space
indent_size = 2

# JavaScript/TypeScript files
[*.{js,ts}]
indent_style = space
indent_size = 2

# ============================================================================
# Shell scripts
# ============================================================================

# Shell scripts
[*.{sh,bash,zsh}]
indent_style = space
indent_size = 2

# ============================================================================
# Docker files
# ============================================================================

[Dockerfile*]
indent_style = space
indent_size = 4

[docker-compose*.yml]
indent_style = space
indent_size = 2

# ============================================================================
# Makefiles
# ============================================================================

[Makefile]
indent_style = tab
indent_size = 4

[*.mk]
indent_style = tab
indent_size = 4

# ============================================================================
# CI/CD files
# ============================================================================

# GitHub Actions
[.github/workflows/*.yml]
indent_style = space
indent_size = 2

# GitLab CI
[.gitlab-ci.yml]
indent_style = space
indent_size = 2

# ============================================================================
# Special files
# ============================================================================

# Requirements files
[requirements*.txt]
indent_style = space
indent_size = 4

# License files
[LICENSE*]
indent_style = space
indent_size = 4

# Git files
[.git*]
indent_style = space
indent_size = 2
